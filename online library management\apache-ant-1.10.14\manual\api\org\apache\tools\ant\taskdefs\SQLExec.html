<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>SQLExec (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: SQLExec">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class SQLExec" class="title">Class SQLExec</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.JDBCTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.SQLExec</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SQLExec</span>
<span class="extends-implements">extends <a href="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></span></div>
<div class="block">Executes a series of SQL statements on a database using JDBC.

 <p>Statements can
 either be read in from a text file using the <i>src</i> attribute or from
 between the enclosing SQL tags.</p>

 <p>Multiple statements can be provided, separated by semicolons (or the
 defined <i>delimiter</i>). Individual lines within the statements can be
 commented using either --, // or REM at the start of the line.</p>

 <p>The <i>autocommit</i> attribute specifies whether auto-commit should be
 turned on or off whilst executing the statements. If auto-commit is turned
 on each statement will be executed and committed. If it is turned off the
 statements will all be executed as one transaction.</p>

 <p>The <i>onerror</i> attribute specifies how to proceed when an error occurs
 during the execution of one of the statements.
 The possible values are: <b>continue</b> execution, only show the error;
 <b>stop</b> execution and commit transaction;
 and <b>abort</b> execution and transaction and fail task.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="SQLExec.DelimiterType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a></code></div>
<div class="col-last even-row-color">
<div class="block">delimiters we support, "normal" and "row"</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="SQLExec.OnError.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The action a task should perform on an error,
 one of "continue", "stop" and "abort"</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="SQLExec.Transaction.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</a></code></div>
<div class="col-last even-row-color">
<div class="block">Contains the definition of a new transaction element.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SQLExec</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">add</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a collection of resources (nested element).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of files (nested fileset attribute).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addText(java.lang.String)" class="member-name-link">addText</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set an inline SQL command to execute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createTransaction()" class="member-name-link">createTransaction</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a SQL transaction to execute</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execSQL(java.lang.String,java.io.PrintStream)" class="member-name-link">execSQL</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Exec the sql statement.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Load the sql file and then execute it</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/Connection.html" title="class or interface in java.sql" class="external-link">Connection</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConnection()" class="member-name-link">getConnection</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Caches the connection returned by the base class's getConnection method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExpandProperties()" class="member-name-link">getExpandProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">is property expansion inside inline text enabled?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/Statement.html" title="class or interface in java.sql" class="external-link">Statement</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStatement()" class="member-name-link">getStatement</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates and configures a Statement instance which is then
 cached for subsequent calls.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#lastDelimiterPosition(java.lang.StringBuffer,java.lang.String)" class="member-name-link">lastDelimiterPosition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/StringBuffer.html" title="class or interface in java.lang" class="external-link">StringBuffer</a>&nbsp;buf,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;currentLine)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#printResults(java.io.PrintStream)" class="member-name-link">printResults</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#printResults(java.sql.ResultSet,java.io.PrintStream)" class="member-name-link">printResults</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/ResultSet.html" title="class or interface in java.sql" class="external-link">ResultSet</a>&nbsp;rs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">print any results in the result set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runStatements(java.io.Reader,java.io.PrintStream)" class="member-name-link">runStatements</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">read in lines and execute them</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAppend(boolean)" class="member-name-link">setAppend</a><wbr>(boolean&nbsp;append)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">whether output should be appended to or overwrite
 an existing file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCsvColumnSeparator(java.lang.String)" class="member-name-link">setCsvColumnSeparator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The column separator used when printing the results.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCsvQuoteCharacter(java.lang.String)" class="member-name-link">setCsvQuoteCharacter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The character used to quote column values.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDelimiter(java.lang.String)" class="member-name-link">setDelimiter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;delimiter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the delimiter that separates SQL statements.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDelimiterType(org.apache.tools.ant.taskdefs.SQLExec.DelimiterType)" class="member-name-link">setDelimiterType</a><wbr>(<a href="SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a>&nbsp;delimiterType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the delimiter type: "normal" or "row" (default "normal").</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the file encoding to use on the SQL files read in</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty()" class="member-name-link">setErrorProperty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty(java.lang.String)" class="member-name-link">setErrorProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property to set to "true" if a statement throws an error.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEscapeProcessing(boolean)" class="member-name-link">setEscapeProcessing</a><wbr>(boolean&nbsp;enable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set escape processing for statements.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExpandProperties(boolean)" class="member-name-link">setExpandProperties</a><wbr>(boolean&nbsp;expandProperties)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable property expansion inside nested text</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForceCsvQuoteChar(boolean)" class="member-name-link">setForceCsvQuoteChar</a><wbr>(boolean&nbsp;forceCsvQuoteChar)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Force the csv quote character</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepformat(boolean)" class="member-name-link">setKeepformat</a><wbr>(boolean&nbsp;keepformat)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">whether or not format should be preserved.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOnerror(org.apache.tools.ant.taskdefs.SQLExec.OnError)" class="member-name-link">setOnerror</a><wbr>(<a href="SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a>&nbsp;action)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Action to perform when statement fails: continue, stop, or abort
 optional; default &quot;abort&quot;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutput(java.io.File)" class="member-name-link">setOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output file;
 optional, defaults to the Ant log.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutput(org.apache.tools.ant.types.Resource)" class="member-name-link">setOutput</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output Resource;
 optional, defaults to the Ant log.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputEncoding(java.lang.String)" class="member-name-link">setOutputEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The encoding to use when writing the result to a resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrint(boolean)" class="member-name-link">setPrint</a><wbr>(boolean&nbsp;print)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Print result sets from the statements;
 optional, default false</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRawBlobs(boolean)" class="member-name-link">setRawBlobs</a><wbr>(boolean&nbsp;rawBlobs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to print raw BLOBs rather than their string (hex) representations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRowCountProperty(int)" class="member-name-link">setRowCountProperty</a><wbr>(int&nbsp;rowCount)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRowCountProperty(java.lang.String)" class="member-name-link">setRowCountProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowCountProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a given property to the number of rows in the first
 statement that returned a row count.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShowheaders(boolean)" class="member-name-link">setShowheaders</a><wbr>(boolean&nbsp;showheaders)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Print headers for result sets from the
 statements; optional, default true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShowtrailers(boolean)" class="member-name-link">setShowtrailers</a><wbr>(boolean&nbsp;showtrailers)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Print trailing info (rows affected) for the SQL
 Addresses Bug/Request #27446</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShowWarnings(boolean)" class="member-name-link">setShowWarnings</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">whether to show SQLWarnings as WARN messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrc(java.io.File)" class="member-name-link">setSrc</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the SQL file to be run.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictDelimiterMatching(boolean)" class="member-name-link">setStrictDelimiterMatching</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If false, delimiters will be searched for in a case-insensitive
 manner (i.e.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTreatWarningsAsErrors(boolean)" class="member-name-link">setTreatWarningsAsErrors</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether a warning is an error - in which case onError applies.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWarningProperty()" class="member-name-link">setWarningProperty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWarningProperty(java.lang.String)" class="member-name-link">setWarningProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;warningProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property to set to "true" if a statement produces a warning.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.JDBCTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></h3>
<code><a href="JDBCTask.html#addConnectionProperty(org.apache.tools.ant.taskdefs.Property)">addConnectionProperty</a>, <a href="JDBCTask.html#createClasspath()">createClasspath</a>, <a href="JDBCTask.html#getClasspath()">getClasspath</a>, <a href="JDBCTask.html#getLoader()">getLoader</a>, <a href="JDBCTask.html#getLoaderMap()">getLoaderMap</a>, <a href="JDBCTask.html#getPassword()">getPassword</a>, <a href="JDBCTask.html#getRdbms()">getRdbms</a>, <a href="JDBCTask.html#getUrl()">getUrl</a>, <a href="JDBCTask.html#getUserId()">getUserId</a>, <a href="JDBCTask.html#getVersion()">getVersion</a>, <a href="JDBCTask.html#isAutocommit()">isAutocommit</a>, <a href="JDBCTask.html#isCaching(boolean)">isCaching</a>, <a href="JDBCTask.html#isValidRdbms(java.sql.Connection)">isValidRdbms</a>, <a href="JDBCTask.html#setAutocommit(boolean)">setAutocommit</a>, <a href="JDBCTask.html#setCaching(boolean)">setCaching</a>, <a href="JDBCTask.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="JDBCTask.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</a>, <a href="JDBCTask.html#setDriver(java.lang.String)">setDriver</a>, <a href="JDBCTask.html#setFailOnConnectionError(boolean)">setFailOnConnectionError</a>, <a href="JDBCTask.html#setPassword(java.lang.String)">setPassword</a>, <a href="JDBCTask.html#setRdbms(java.lang.String)">setRdbms</a>, <a href="JDBCTask.html#setUrl(java.lang.String)">setUrl</a>, <a href="JDBCTask.html#setUserid(java.lang.String)">setUserid</a>, <a href="JDBCTask.html#setVersion(java.lang.String)">setVersion</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SQLExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SQLExec</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSrc(java.io.File)">
<h3>setSrc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrc</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile)</span></div>
<div class="block">Set the name of the SQL file to be run.
 Required unless statements are enclosed in the build file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcFile</code> - the file containing the SQL command.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExpandProperties(boolean)">
<h3>setExpandProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExpandProperties</span><wbr><span class="parameters">(boolean&nbsp;expandProperties)</span></div>
<div class="block">Enable property expansion inside nested text</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>expandProperties</code> - if true expand properties.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExpandProperties()">
<h3>getExpandProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getExpandProperties</span>()</div>
<div class="block">is property expansion inside inline text enabled?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if properties are to be expanded.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addText(java.lang.String)">
<h3>addText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql)</span></div>
<div class="block">Set an inline SQL command to execute.
 NB: Properties are not expanded in this text unless <code>expandProperties</code>
 is set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sql</code> - an inline string containing the SQL command.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">Adds a set of files (nested fileset attribute).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - a set of files contains SQL commands, each File is run in
            a separate transaction.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.ResourceCollection)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</span></div>
<div class="block">Adds a collection of resources (nested element).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rc</code> - a collection of resources containing SQL commands,
 each resource is run in a separate transaction.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTransaction()">
<h3>createTransaction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</a></span>&nbsp;<span class="element-name">createTransaction</span>()</div>
<div class="block">Add a SQL transaction to execute</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a Transaction to be configured.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Set the file encoding to use on the SQL files read in</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the encoding to use on the files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDelimiter(java.lang.String)">
<h3>setDelimiter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDelimiter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;delimiter)</span></div>
<div class="block">Set the delimiter that separates SQL statements. Defaults to &quot;;&quot;;
 optional

 <p>For example, set this to "go" and delimitertype to "ROW" for
 Sybase ASE or MS SQL Server.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delimiter</code> - the separator.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDelimiterType(org.apache.tools.ant.taskdefs.SQLExec.DelimiterType)">
<h3>setDelimiterType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDelimiterType</span><wbr><span class="parameters">(<a href="SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</a>&nbsp;delimiterType)</span></div>
<div class="block">Set the delimiter type: "normal" or "row" (default "normal").

 <p>The delimiter type takes two values - normal and row. Normal
 means that any occurrence of the delimiter terminate the SQL
 command whereas with row, only a line containing just the
 delimiter is recognized as the end of the command.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delimiterType</code> - the type of delimiter - "normal" or "row".</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPrint(boolean)">
<h3>setPrint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrint</span><wbr><span class="parameters">(boolean&nbsp;print)</span></div>
<div class="block">Print result sets from the statements;
 optional, default false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>print</code> - if true print result sets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShowheaders(boolean)">
<h3>setShowheaders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShowheaders</span><wbr><span class="parameters">(boolean&nbsp;showheaders)</span></div>
<div class="block">Print headers for result sets from the
 statements; optional, default true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>showheaders</code> - if true print headers of result sets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShowtrailers(boolean)">
<h3>setShowtrailers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShowtrailers</span><wbr><span class="parameters">(boolean&nbsp;showtrailers)</span></div>
<div class="block">Print trailing info (rows affected) for the SQL
 Addresses Bug/Request #27446</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>showtrailers</code> - if true prints the SQL rows affected</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutput(java.io.File)">
<h3>setOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;output)</span></div>
<div class="block">Set the output file;
 optional, defaults to the Ant log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - the output file to use for logging messages.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutput(org.apache.tools.ant.types.Resource)">
<h3>setOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutput</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;output)</span></div>
<div class="block">Set the output Resource;
 optional, defaults to the Ant log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - the output Resource to store results.</dd>
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputEncoding(java.lang.String)">
<h3>setOutputEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</span></div>
<div class="block">The encoding to use when writing the result to a resource.
 <p>Default's to the platform's default encoding</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputEncoding</code> - the name of the encoding or null for the
 platform's default encoding</dd>
<dt>Since:</dt>
<dd>Ant 1.9.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAppend(boolean)">
<h3>setAppend</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAppend</span><wbr><span class="parameters">(boolean&nbsp;append)</span></div>
<div class="block">whether output should be appended to or overwrite
 an existing file.  Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>append</code> - if true append to an existing file.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOnerror(org.apache.tools.ant.taskdefs.SQLExec.OnError)">
<h3>setOnerror</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOnerror</span><wbr><span class="parameters">(<a href="SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</a>&nbsp;action)</span></div>
<div class="block">Action to perform when statement fails: continue, stop, or abort
 optional; default &quot;abort&quot;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>action</code> - the action to perform on statement failure.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeepformat(boolean)">
<h3>setKeepformat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepformat</span><wbr><span class="parameters">(boolean&nbsp;keepformat)</span></div>
<div class="block">whether or not format should be preserved.
 Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keepformat</code> - The keepformat to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEscapeProcessing(boolean)">
<h3>setEscapeProcessing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEscapeProcessing</span><wbr><span class="parameters">(boolean&nbsp;enable)</span></div>
<div class="block">Set escape processing for statements.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enable</code> - if true enable escape processing, default is true.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRawBlobs(boolean)">
<h3>setRawBlobs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRawBlobs</span><wbr><span class="parameters">(boolean&nbsp;rawBlobs)</span></div>
<div class="block">Set whether to print raw BLOBs rather than their string (hex) representations.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rawBlobs</code> - whether to print raw BLOBs.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictDelimiterMatching(boolean)">
<h3>setStrictDelimiterMatching</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictDelimiterMatching</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">If false, delimiters will be searched for in a case-insensitive
 manner (i.e. delimiter="go" matches "GO") and surrounding
 whitespace will be ignored (delimiter="go" matches "GO ").</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShowWarnings(boolean)">
<h3>setShowWarnings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShowWarnings</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">whether to show SQLWarnings as WARN messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTreatWarningsAsErrors(boolean)">
<h3>setTreatWarningsAsErrors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTreatWarningsAsErrors</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether a warning is an error - in which case onError applies.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCsvColumnSeparator(java.lang.String)">
<h3>setCsvColumnSeparator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCsvColumnSeparator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</span></div>
<div class="block">The column separator used when printing the results.

 <p>Defaults to ","</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCsvQuoteCharacter(java.lang.String)">
<h3>setCsvQuoteCharacter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCsvQuoteCharacter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</span></div>
<div class="block">The character used to quote column values.

 <p>If set, columns that contain either the column separator or
 the quote character itself will be surrounded by the quote
 character.  The quote character itself will be doubled if it
 appears inside of the column's value.</p>

 <p>If this value is not set (the default), no column values
 will be quoted, not even if they contain the column
 separator.</p>

 <p><b>Note:</b> BLOB values will never be quoted.</p>

 <p>Defaults to "not set"</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty(java.lang.String)">
<h3>setErrorProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</span></div>
<div class="block">Property to set to "true" if a statement throws an error.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorProperty</code> - the name of the property to set in the
 event of an error.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWarningProperty(java.lang.String)">
<h3>setWarningProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWarningProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;warningProperty)</span></div>
<div class="block">Property to set to "true" if a statement produces a warning.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>warningProperty</code> - the name of the property to set in the
 event of a warning.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRowCountProperty(java.lang.String)">
<h3>setRowCountProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRowCountProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowCountProperty)</span></div>
<div class="block">Sets a given property to the number of rows in the first
 statement that returned a row count.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rowCountProperty</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForceCsvQuoteChar(boolean)">
<h3>setForceCsvQuoteChar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForceCsvQuoteChar</span><wbr><span class="parameters">(boolean&nbsp;forceCsvQuoteChar)</span></div>
<div class="block">Force the csv quote character</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>forceCsvQuoteChar</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Load the sql file and then execute it</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runStatements(java.io.Reader,java.io.PrintStream)">
<h3>runStatements</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">runStatements</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a>,
<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">read in lines and execute them</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>reader</code> - the reader contains sql lines.</dd>
<dd><code>out</code> - the place to output results.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></code> - on sql problems</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on io problems</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execSQL(java.lang.String,java.io.PrintStream)">
<h3>execSQL</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execSQL</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></span></div>
<div class="block">Exec the sql statement.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sql</code> - the SQL statement to execute</dd>
<dd><code>out</code> - the place to put output</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></code> - on SQL problems</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="printResults(java.io.PrintStream)">
<h3>printResults</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">printResults</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use <a href="#printResults(java.sql.ResultSet,java.io.PrintStream)"><code>the two arg version</code></a> instead.</div>
</div>
<div class="block">print any results in the statement</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - the place to print results</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></code> - on SQL problems.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="printResults(java.sql.ResultSet,java.io.PrintStream)">
<h3>printResults</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">printResults</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/ResultSet.html" title="class or interface in java.sql" class="external-link">ResultSet</a>&nbsp;rs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;out)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></span></div>
<div class="block">print any results in the result set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rs</code> - the resultset to print information about</dd>
<dd><code>out</code> - the place to print results</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></code> - on SQL problems.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getConnection()">
<h3>getConnection</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/Connection.html" title="class or interface in java.sql" class="external-link">Connection</a></span>&nbsp;<span class="element-name">getConnection</span>()</div>
<div class="block">Caches the connection returned by the base class's getConnection method.

 <p>Subclasses that need to provide a different connection than
 the base class would, should override this method but keep in
 mind that this class expects to get the same connection
 instance on consecutive calls.</p>

 <p>returns null if the connection does not connect to the
 expected RDBMS.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="JDBCTask.html#getConnection()">getConnection</a></code>&nbsp;in class&nbsp;<code><a href="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></code></dd>
<dt>Returns:</dt>
<dd>Connection the newly created connection or null if the
 connection failed and failOnConnectionError is false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStatement()">
<h3>getStatement</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/Statement.html" title="class or interface in java.sql" class="external-link">Statement</a></span>&nbsp;<span class="element-name">getStatement</span>()
                          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></span></div>
<div class="block">Creates and configures a Statement instance which is then
 cached for subsequent calls.

 <p>Subclasses that want to provide different Statement
 instances, should override this method but keep in mind that
 this class expects to get the same connection instance on
 consecutive calls.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Statement</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.sql/java/sql/SQLException.html" title="class or interface in java.sql" class="external-link">SQLException</a></code> - if statement creation or processing fails</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="lastDelimiterPosition(java.lang.StringBuffer,java.lang.String)">
<h3>lastDelimiterPosition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">lastDelimiterPosition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/StringBuffer.html" title="class or interface in java.lang" class="external-link">StringBuffer</a>&nbsp;buf,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;currentLine)</span></div>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty()">
<h3>setErrorProperty</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setWarningProperty()">
<h3>setWarningProperty</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWarningProperty</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setRowCountProperty(int)">
<h3>setRowCountProperty</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRowCountProperty</span><wbr><span class="parameters">(int&nbsp;rowCount)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
