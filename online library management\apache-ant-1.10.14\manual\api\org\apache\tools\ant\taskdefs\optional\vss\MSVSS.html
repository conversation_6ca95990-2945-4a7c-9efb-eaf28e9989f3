<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>MSVSS (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.vss, class: MSVSS">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.vss</a></div>
<h1 title="Class MSVSS" class="title">Class MSVSS</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.vss.MSVSS</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="MSVSSADD.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSADD</a></code>, <code><a href="MSVSSCHECKIN.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKIN</a></code>, <code><a href="MSVSSCHECKOUT.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCHECKOUT</a></code>, <code><a href="MSVSSCP.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCP</a></code>, <code><a href="MSVSSCREATE.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSCREATE</a></code>, <code><a href="MSVSSGET.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSGET</a></code>, <code><a href="MSVSSHISTORY.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSHISTORY</a></code>, <code><a href="MSVSSLABEL.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSSLABEL</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">MSVSS</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>
implements <a href="MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></span></div>
<div class="block">A base class for creating tasks for executing commands on Visual SourceSafe.
 <p>
 The class extends the 'exec' task as it operates by executing the ss.exe program
 supplied with SourceSafe. By default the task expects ss.exe to be in the path,
 you can override this be specifying the ssdir attribute.
 </p>
 <p>
 This class provides set and get methods for 'login' and 'vsspath' attributes. It
 also contains constants for the flags that can be passed to SS.
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="MSVSS.CurrentModUpdated.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a></code></div>
<div class="col-last even-row-color">
<div class="block">Extension of EnumeratedAttribute to hold the values for file time stamp.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="MSVSS.WritableFiles.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Extension of EnumeratedAttribute to hold the values for writable filess.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.vss.<a href="MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</a></h3>
<code><a href="MSVSSConstants.html#COMMAND_ADD">COMMAND_ADD</a>, <a href="MSVSSConstants.html#COMMAND_CHECKIN">COMMAND_CHECKIN</a>, <a href="MSVSSConstants.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</a>, <a href="MSVSSConstants.html#COMMAND_CP">COMMAND_CP</a>, <a href="MSVSSConstants.html#COMMAND_CREATE">COMMAND_CREATE</a>, <a href="MSVSSConstants.html#COMMAND_GET">COMMAND_GET</a>, <a href="MSVSSConstants.html#COMMAND_HISTORY">COMMAND_HISTORY</a>, <a href="MSVSSConstants.html#COMMAND_LABEL">COMMAND_LABEL</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_DEF">FLAG_AUTORESPONSE_DEF</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_NO">FLAG_AUTORESPONSE_NO</a>, <a href="MSVSSConstants.html#FLAG_AUTORESPONSE_YES">FLAG_AUTORESPONSE_YES</a>, <a href="MSVSSConstants.html#FLAG_BRIEF">FLAG_BRIEF</a>, <a href="MSVSSConstants.html#FLAG_CODEDIFF">FLAG_CODEDIFF</a>, <a href="MSVSSConstants.html#FLAG_COMMENT">FLAG_COMMENT</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_DEF">FLAG_FILETIME_DEF</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_MODIFIED">FLAG_FILETIME_MODIFIED</a>, <a href="MSVSSConstants.html#FLAG_FILETIME_UPDATED">FLAG_FILETIME_UPDATED</a>, <a href="MSVSSConstants.html#FLAG_LABEL">FLAG_LABEL</a>, <a href="MSVSSConstants.html#FLAG_LOGIN">FLAG_LOGIN</a>, <a href="MSVSSConstants.html#FLAG_NO_FILE">FLAG_NO_FILE</a>, <a href="MSVSSConstants.html#FLAG_NO_GET">FLAG_NO_GET</a>, <a href="MSVSSConstants.html#FLAG_OUTPUT">FLAG_OUTPUT</a>, <a href="MSVSSConstants.html#FLAG_OVERRIDE_WORKING_DIR">FLAG_OVERRIDE_WORKING_DIR</a>, <a href="MSVSSConstants.html#FLAG_QUIET">FLAG_QUIET</a>, <a href="MSVSSConstants.html#FLAG_RECURSION">FLAG_RECURSION</a>, <a href="MSVSSConstants.html#FLAG_REPLACE_WRITABLE">FLAG_REPLACE_WRITABLE</a>, <a href="MSVSSConstants.html#FLAG_SKIP_WRITABLE">FLAG_SKIP_WRITABLE</a>, <a href="MSVSSConstants.html#FLAG_USER">FLAG_USER</a>, <a href="MSVSSConstants.html#FLAG_VERSION">FLAG_VERSION</a>, <a href="MSVSSConstants.html#FLAG_VERSION_DATE">FLAG_VERSION_DATE</a>, <a href="MSVSSConstants.html#FLAG_VERSION_LABEL">FLAG_VERSION_LABEL</a>, <a href="MSVSSConstants.html#FLAG_WRITABLE">FLAG_WRITABLE</a>, <a href="MSVSSConstants.html#PROJECT_PREFIX">PROJECT_PREFIX</a>, <a href="MSVSSConstants.html#SS_EXE">SS_EXE</a>, <a href="MSVSSConstants.html#STYLE_BRIEF">STYLE_BRIEF</a>, <a href="MSVSSConstants.html#STYLE_CODEDIFF">STYLE_CODEDIFF</a>, <a href="MSVSSConstants.html#STYLE_DEFAULT">STYLE_DEFAULT</a>, <a href="MSVSSConstants.html#STYLE_NOFILE">STYLE_NOFILE</a>, <a href="MSVSSConstants.html#TIME_CURRENT">TIME_CURRENT</a>, <a href="MSVSSConstants.html#TIME_MODIFIED">TIME_MODIFIED</a>, <a href="MSVSSConstants.html#TIME_UPDATED">TIME_UPDATED</a>, <a href="MSVSSConstants.html#VALUE_FROMDATE">VALUE_FROMDATE</a>, <a href="MSVSSConstants.html#VALUE_FROMLABEL">VALUE_FROMLABEL</a>, <a href="MSVSSConstants.html#VALUE_NO">VALUE_NO</a>, <a href="MSVSSConstants.html#VALUE_YES">VALUE_YES</a>, <a href="MSVSSConstants.html#WRITABLE_FAIL">WRITABLE_FAIL</a>, <a href="MSVSSConstants.html#WRITABLE_REPLACE">WRITABLE_REPLACE</a>, <a href="MSVSSConstants.html#WRITABLE_SKIP">WRITABLE_SKIP</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">MSVSS</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAutoresponse()" class="member-name-link">getAutoresponse</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the auto response string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComment()" class="member-name-link">getComment</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the comment string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileTimeStamp()" class="member-name-link">getFileTimeStamp</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the value set for the FileTimeStamp.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGetLocalCopy()" class="member-name-link">getGetLocalCopy</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds and returns the -G- flag if required.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLabel()" class="member-name-link">getLabel</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the label string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalpath()" class="member-name-link">getLocalpath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the localpath string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLogin()" class="member-name-link">getLogin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the login string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutput()" class="member-name-link">getOutput</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the output file string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getQuiet()" class="member-name-link">getQuiet</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the quiet string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRecursive()" class="member-name-link">getRecursive</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the recursive string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSSCommand()" class="member-name-link">getSSCommand</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the sscommand string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStyle()" class="member-name-link">getStyle</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the style string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUser()" class="member-name-link">getUser</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the user string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersion()" class="member-name-link">getVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the version string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersionDate()" class="member-name-link">getVersionDate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the Version date string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersionDateLabel()" class="member-name-link">getVersionDateLabel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the version string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersionLabel()" class="member-name-link">getVersionLabel</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the version string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVsspath()" class="member-name-link">getVsspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the vssserverpath string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWritable()" class="member-name-link">getWritable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the writable string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWritableFiles()" class="member-name-link">getWritableFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the value to determine the behaviour when encountering writable files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnError(boolean)" class="member-name-link">setFailOnError</a><wbr>(boolean&nbsp;failOnError)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates if the build should fail if the Sourcesafe command does.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalAutoResponse(java.lang.String)" class="member-name-link">setInternalAutoResponse</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;autoResponse)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the auto response attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalComment(java.lang.String)" class="member-name-link">setInternalComment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the internal comment attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalDate(java.lang.String)" class="member-name-link">setInternalDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;date)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the date attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalDateFormat(java.text.DateFormat)" class="member-name-link">setInternalDateFormat</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/text/DateFormat.html" title="class or interface in java.text" class="external-link">DateFormat</a>&nbsp;dateFormat)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the date format attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalFailOnError(boolean)" class="member-name-link">setInternalFailOnError</a><wbr>(boolean&nbsp;failOnError)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the failOnError attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalFileTimeStamp(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.CurrentModUpdated)" class="member-name-link">setInternalFileTimeStamp</a><wbr>(<a href="MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a>&nbsp;timestamp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timestamp attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalFromDate(java.lang.String)" class="member-name-link">setInternalFromDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fromDate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the from date attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalFromLabel(java.lang.String)" class="member-name-link">setInternalFromLabel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fromLabel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the from label attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalGetLocalCopy(boolean)" class="member-name-link">setInternalGetLocalCopy</a><wbr>(boolean&nbsp;getLocalCopy)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the getLocalCopy attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalLabel(java.lang.String)" class="member-name-link">setInternalLabel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;label)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the label attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalLocalPath(java.lang.String)" class="member-name-link">setInternalLocalPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;localPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the local path comment attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalNumDays(int)" class="member-name-link">setInternalNumDays</a><wbr>(int&nbsp;numDays)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the num days attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalOutputFilename(java.lang.String)" class="member-name-link">setInternalOutputFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputFileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the outputFileName comment attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalQuiet(boolean)" class="member-name-link">setInternalQuiet</a><wbr>(boolean&nbsp;quiet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the quiet attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalRecursive(boolean)" class="member-name-link">setInternalRecursive</a><wbr>(boolean&nbsp;recursive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the recursive attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalStyle(java.lang.String)" class="member-name-link">setInternalStyle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;style)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the style attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalToDate(java.lang.String)" class="member-name-link">setInternalToDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toDate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the to date attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalToLabel(java.lang.String)" class="member-name-link">setInternalToLabel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toLabel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the to label attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalUser(java.lang.String)" class="member-name-link">setInternalUser</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;user)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the user attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalVersion(java.lang.String)" class="member-name-link">setInternalVersion</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the version attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalWritable(boolean)" class="member-name-link">setInternalWritable</a><wbr>(boolean&nbsp;writable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the writable attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalWritableFiles(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.WritableFiles)" class="member-name-link">setInternalWritableFiles</a><wbr>(<a href="MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a>&nbsp;writableFiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the writableFiles attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogin(java.lang.String)" class="member-name-link">setLogin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vssLogin)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Login to use when accessing VSS, formatted as "username,password".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setServerpath(java.lang.String)" class="member-name-link">setServerpath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;serverPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Directory where <code>srssafe.ini</code> resides.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSsdir(java.lang.String)" class="member-name-link">setSsdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Directory where <code>ss.exe</code> resides.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVsspath(java.lang.String)" class="member-name-link">setVsspath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vssPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SourceSafe path which specifies the project/file(s) you wish to perform
 the action on.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>MSVSS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MSVSS</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSsdir(java.lang.String)">
<h3>setSsdir</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSsdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</span></div>
<div class="block">Directory where <code>ss.exe</code> resides.
 By default the task expects it to be in the PATH.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - The directory containing ss.exe.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogin(java.lang.String)">
<h3>setLogin</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vssLogin)</span></div>
<div class="block">Login to use when accessing VSS, formatted as "username,password".
 <p>
 You can omit the password if your database is not password protected.
 If you have a password and omit it, Ant will hang.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vssLogin</code> - The login string to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVsspath(java.lang.String)">
<h3>setVsspath</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVsspath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vssPath)</span></div>
<div class="block">SourceSafe path which specifies the project/file(s) you wish to perform
 the action on.
 <p>
 A prefix of 'vss://' will be removed if specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vssPath</code> - The VSS project path.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setServerpath(java.lang.String)">
<h3>setServerpath</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setServerpath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;serverPath)</span></div>
<div class="block">Directory where <code>srssafe.ini</code> resides.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>serverPath</code> - The path to the VSS server.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnError(boolean)">
<h3>setFailOnError</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnError</span><wbr><span class="parameters">(boolean&nbsp;failOnError)</span></div>
<div class="block">Indicates if the build should fail if the Sourcesafe command does. Defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failOnError</code> - True if task should fail on any error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the task.
 <p>
 Builds a command line to execute ss.exe and then calls Exec's run method
 to execute the command line.
 </p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the command cannot execute.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalComment(java.lang.String)">
<h3>setInternalComment</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalComment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</span></div>
<div class="block">Set the internal comment attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comment</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalAutoResponse(java.lang.String)">
<h3>setInternalAutoResponse</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalAutoResponse</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;autoResponse)</span></div>
<div class="block">Set the auto response attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>autoResponse</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalDate(java.lang.String)">
<h3>setInternalDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;date)</span></div>
<div class="block">Set the date attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalDateFormat(java.text.DateFormat)">
<h3>setInternalDateFormat</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalDateFormat</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/text/DateFormat.html" title="class or interface in java.text" class="external-link">DateFormat</a>&nbsp;dateFormat)</span></div>
<div class="block">Set the date format attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dateFormat</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalFailOnError(boolean)">
<h3>setInternalFailOnError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalFailOnError</span><wbr><span class="parameters">(boolean&nbsp;failOnError)</span></div>
<div class="block">Set the failOnError attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failOnError</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalFromDate(java.lang.String)">
<h3>setInternalFromDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalFromDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fromDate)</span></div>
<div class="block">Set the from date attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fromDate</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalFromLabel(java.lang.String)">
<h3>setInternalFromLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalFromLabel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fromLabel)</span></div>
<div class="block">Set the from label attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fromLabel</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalLabel(java.lang.String)">
<h3>setInternalLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalLabel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;label)</span></div>
<div class="block">Set the label attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>label</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalLocalPath(java.lang.String)">
<h3>setInternalLocalPath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalLocalPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;localPath)</span></div>
<div class="block">Set the local path comment attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>localPath</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalNumDays(int)">
<h3>setInternalNumDays</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalNumDays</span><wbr><span class="parameters">(int&nbsp;numDays)</span></div>
<div class="block">Set the num days attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>numDays</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalOutputFilename(java.lang.String)">
<h3>setInternalOutputFilename</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalOutputFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputFileName)</span></div>
<div class="block">Set the outputFileName comment attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputFileName</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalQuiet(boolean)">
<h3>setInternalQuiet</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalQuiet</span><wbr><span class="parameters">(boolean&nbsp;quiet)</span></div>
<div class="block">Set the quiet attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>quiet</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalRecursive(boolean)">
<h3>setInternalRecursive</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalRecursive</span><wbr><span class="parameters">(boolean&nbsp;recursive)</span></div>
<div class="block">Set the recursive attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>recursive</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalStyle(java.lang.String)">
<h3>setInternalStyle</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalStyle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;style)</span></div>
<div class="block">Set the style attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>style</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalToDate(java.lang.String)">
<h3>setInternalToDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalToDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toDate)</span></div>
<div class="block">Set the to date attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>toDate</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalToLabel(java.lang.String)">
<h3>setInternalToLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalToLabel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;toLabel)</span></div>
<div class="block">Set the to label attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>toLabel</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalUser(java.lang.String)">
<h3>setInternalUser</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalUser</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;user)</span></div>
<div class="block">Set the user attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>user</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalVersion(java.lang.String)">
<h3>setInternalVersion</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalVersion</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</span></div>
<div class="block">Set the version attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>version</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalWritable(boolean)">
<h3>setInternalWritable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalWritable</span><wbr><span class="parameters">(boolean&nbsp;writable)</span></div>
<div class="block">Set the writable attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>writable</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalFileTimeStamp(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.CurrentModUpdated)">
<h3>setInternalFileTimeStamp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalFileTimeStamp</span><wbr><span class="parameters">(<a href="MSVSS.CurrentModUpdated.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.CurrentModUpdated</a>&nbsp;timestamp)</span></div>
<div class="block">Set the timestamp attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timestamp</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalWritableFiles(org.apache.tools.ant.taskdefs.optional.vss.MSVSS.WritableFiles)">
<h3>setInternalWritableFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalWritableFiles</span><wbr><span class="parameters">(<a href="MSVSS.WritableFiles.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS.WritableFiles</a>&nbsp;writableFiles)</span></div>
<div class="block">Set the writableFiles attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>writableFiles</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalGetLocalCopy(boolean)">
<h3>setInternalGetLocalCopy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalGetLocalCopy</span><wbr><span class="parameters">(boolean&nbsp;getLocalCopy)</span></div>
<div class="block">Set the getLocalCopy attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>getLocalCopy</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSSCommand()">
<h3>getSSCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSSCommand</span>()</div>
<div class="block">Gets the sscommand string. "ss" or "c:\path\to\ss"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The path to ss.exe or just ss if sscommand is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVsspath()">
<h3>getVsspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVsspath</span>()</div>
<div class="block">Gets the vssserverpath string.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>null if vssserverpath is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getQuiet()">
<h3>getQuiet</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getQuiet</span>()</div>
<div class="block">Gets the quiet string. -O-</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if quiet is not set or is false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRecursive()">
<h3>getRecursive</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRecursive</span>()</div>
<div class="block">Gets the recursive string. "-R"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if recursive is not set or is false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWritable()">
<h3>getWritable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWritable</span>()</div>
<div class="block">Gets the writable string. "-W"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if writable is not set or is false.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLabel()">
<h3>getLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLabel</span>()</div>
<div class="block">Gets the label string. "-Lbuild1"
 Max label length is 32 chars</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if label is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStyle()">
<h3>getStyle</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStyle</span>()</div>
<div class="block">Gets the style string. "-Lbuild1"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if label is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVersionDateLabel()">
<h3>getVersionDateLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVersionDateLabel</span>()</div>
<div class="block">Gets the version string. Returns the first specified of version "-V1.0",
 date "-Vd01.01.01", label "-Vlbuild1".</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if a version, date and label are not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVersion()">
<h3>getVersion</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVersion</span>()</div>
<div class="block">Gets the version string.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if a version is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocalpath()">
<h3>getLocalpath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLocalpath</span>()</div>
<div class="block">Gets the localpath string. "-GLc:\source" <p>
 The localpath is created if it didn't exist.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if localpath is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getComment()">
<h3>getComment</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getComment</span>()</div>
<div class="block">Gets the comment string. "-Ccomment text"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A comment of "-" if comment is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAutoresponse()">
<h3>getAutoresponse</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getAutoresponse</span>()</div>
<div class="block">Gets the auto response string. This can be Y "-I-Y" or N "-I-N".</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The default value "-I-" if autoresponse is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLogin()">
<h3>getLogin</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLogin</span>()</div>
<div class="block">Gets the login string. This can be user and password, "-Yuser,password"
 or just user "-Yuser".</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if login is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutput()">
<h3>getOutput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOutput</span>()</div>
<div class="block">Gets the output file string. "-Ooutput.file"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if user is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUser()">
<h3>getUser</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUser</span>()</div>
<div class="block">Gets the user string. "-Uusername"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if user is not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVersionLabel()">
<h3>getVersionLabel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVersionLabel</span>()</div>
<div class="block">Gets the version string. This can be to-from "-VLbuild2~Lbuild1", from
 "~Lbuild1" or to "-VLbuild2".</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if neither tolabel or fromlabel are set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVersionDate()">
<h3>getVersionDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVersionDate</span>()
                         throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Gets the Version date string.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if neither Todate or from date are set.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getGetLocalCopy()">
<h3>getGetLocalCopy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getGetLocalCopy</span>()</div>
<div class="block">Builds and returns the -G- flag if required.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty string if get local copy is true.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileTimeStamp()">
<h3>getFileTimeStamp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileTimeStamp</span>()</div>
<div class="block">Gets the value set for the FileTimeStamp.
 if it equals "current" then we return -GTC
 if it equals "modified" then we return -GTM
 if it equals "updated" then we return -GTU
 otherwise we return -GTC</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The default file time flag, if not set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWritableFiles()">
<h3>getWritableFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWritableFiles</span>()</div>
<div class="block">Gets the value to determine the behaviour when encountering writable files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An empty String, if not set.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
