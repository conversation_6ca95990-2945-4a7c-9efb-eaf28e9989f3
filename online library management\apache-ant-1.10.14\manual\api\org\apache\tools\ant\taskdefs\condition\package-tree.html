<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.condition Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="tree: package: org.apache.tools.ant.taskdefs.condition">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.taskdefs.condition</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Contains.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="../../types/EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsLastModified.CompareMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified.CompareMode</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Equals.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="FilesMatch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="HasFreeSpace.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">HasFreeSpace</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsFailure.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFailure</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="JavaVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">JavaVersion</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Os.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Os</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="ConditionBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="And.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">And</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Not.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Not</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Or.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Or</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Xor.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="../../types/DataType.html" class="type-name-link" title="class in org.apache.tools.ant.types">DataType</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="../../types/selectors/AbstractSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="../../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsFileSelected.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsSigned.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="HasMethod.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Http.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Http</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsFalse.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsLastModified.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsReachable.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsReference.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsSet.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="IsTrue.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Matches.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="ParserSupports.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="ResourceExists.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Socket.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../../Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="AntVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="TypeFound.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="ResourceContains.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourceContains</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="ResourcesMatch.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.condition">ResourcesMatch</a> (implements org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.condition.<a href="Condition.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
