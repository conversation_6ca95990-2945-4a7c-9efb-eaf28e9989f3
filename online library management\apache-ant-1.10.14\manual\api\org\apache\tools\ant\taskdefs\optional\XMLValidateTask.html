<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>XMLValidateTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional, class: XMLValidateTask">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<h1 title="Class XMLValidateTask" class="title">Class XMLValidateTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.XMLValidateTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="SchemaValidate.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">XMLValidateTask</span>
<span class="extends-implements">extends <a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Checks XML files are valid (or only well formed). The
 task uses the SAX2 parser implementation provided by JAXP by default
 (probably the one that is used by Ant itself), but one can specify any
 SAX1/2 parser if needed.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="XMLValidateTask.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></code></div>
<div class="col-last even-row-color">
<div class="block">The class to create to set a feature of the parser.</div>
</div>
<div class="col-first odd-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="XMLValidateTask.Property.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A Parser property.</div>
</div>
<div class="col-first even-row-color"><code>protected class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="XMLValidateTask.ValidatorErrorHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a></code></div>
<div class="col-last even-row-color">
<div class="block">ValidatorErrorHandler role :
 
  log SAX parse exceptions,
  remember if an error occurred
 </div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#classpath" class="member-name-link">classpath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a></code></div>
<div class="col-second odd-row-color"><code><a href="#errorHandler" class="member-name-link">errorHandler</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#failOnError" class="member-name-link">failOnError</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color"><code><a href="#file" class="member-name-link">file</a></code></div>
<div class="col-last odd-row-color">
<div class="block">file to be validated</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#filesets" class="member-name-link">filesets</a></code></div>
<div class="col-last even-row-color">
<div class="block">sets of file to be validated</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#INIT_FAILED_MSG" class="member-name-link">INIT_FAILED_MSG</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#lenient" class="member-name-link">lenient</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#MESSAGE_FILES_VALIDATED" class="member-name-link">MESSAGE_FILES_VALIDATED</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Message for successful validation</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#readerClassName" class="member-name-link">readerClassName</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#warn" class="member-name-link">warn</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></code></div>
<div class="col-second even-row-color"><code><a href="#xmlReader" class="member-name-link">xmlReader</a></code></div>
<div class="col-last even-row-color">
<div class="block">the parser is viewed as a SAX2 XMLReader.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">XMLValidateTask</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)" class="member-name-link">addConfiguredXMLCatalog</a><wbr>(<a href="../../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a>&nbsp;catalog)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an XMLCatalog as a nested element; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">specify a set of file to be checked</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanup()" class="member-name-link">cleanup</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans up resources.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createAttribute()" class="member-name-link">createAttribute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an attribute nested element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDefaultReader()" class="member-name-link">createDefaultReader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a reader if the use of the class did not specify another one.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/DTDLocation.html" title="class in org.apache.tools.ant.types">DTDLocation</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDTD()" class="member-name-link">createDTD</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a DTD location record; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createProperty()" class="member-name-link">createProperty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createXmlReader()" class="member-name-link">createXmlReader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">create the XML reader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#doValidate(java.io.File)" class="member-name-link">doValidate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;afile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">parse the file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">execute the task</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEntityResolver()" class="member-name-link">getEntityResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">accessor to the xmlCatalog used in the task</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getXmlReader()" class="member-name-link">getXmlReader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the XML reader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called by the project to let the task initialize properly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initValidator()" class="member-name-link">initValidator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">init the parser :
 load the parser class, and set features if necessary
 It is only after this that the reader is valid</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSax1Parser()" class="member-name-link">isSax1Parser</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">test that returns true if we are using a SAX1 parser.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onSuccessfulValidation(int)" class="member-name-link">onSuccessfulValidation</a><wbr>(int&nbsp;fileProcessed)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">handler called on successful file validation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassName(java.lang.String)" class="member-name-link">setClassName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify the class name of the SAX parser to be used.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify the classpath to be searched to load the parser (optional)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Where to find the parser class; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnError(boolean)" class="member-name-link">setFailOnError</a><wbr>(boolean&nbsp;fail)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how parser error are to be handled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFeature(java.lang.String,boolean)" class="member-name-link">setFeature</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;feature,
 boolean&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a feature on the parser.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFile(java.io.File)" class="member-name-link">setFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">specify the file to be checked; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLenient(boolean)" class="member-name-link">setLenient</a><wbr>(boolean&nbsp;bool)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify whether the parser should be validating.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProperty(java.lang.String,java.lang.String)" class="member-name-link">setProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a property.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWarn(boolean)" class="member-name-link">setWarn</a><wbr>(boolean&nbsp;bool)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how parser error are to be handled.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="INIT_FAILED_MSG">
<h3>INIT_FAILED_MSG</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">INIT_FAILED_MSG</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.XMLValidateTask.INIT_FAILED_MSG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="failOnError">
<h3>failOnError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">failOnError</span></div>
</section>
</li>
<li>
<section class="detail" id="warn">
<h3>warn</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">warn</span></div>
</section>
</li>
<li>
<section class="detail" id="lenient">
<h3>lenient</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">lenient</span></div>
</section>
</li>
<li>
<section class="detail" id="readerClassName">
<h3>readerClassName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">readerClassName</span></div>
</section>
</li>
<li>
<section class="detail" id="file">
<h3>file</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">file</span></div>
<div class="block">file to be validated</div>
</section>
</li>
<li>
<section class="detail" id="filesets">
<h3>filesets</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&gt;</span>&nbsp;<span class="element-name">filesets</span></div>
<div class="block">sets of file to be validated</div>
</section>
</li>
<li>
<section class="detail" id="classpath">
<h3>classpath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">classpath</span></div>
</section>
</li>
<li>
<section class="detail" id="xmlReader">
<h3>xmlReader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></span>&nbsp;<span class="element-name">xmlReader</span></div>
<div class="block">the parser is viewed as a SAX2 XMLReader. If a SAX1 parser is specified,
 it's wrapped in an adapter that make it behave as a XMLReader.
 a more 'standard' way of doing this would be to use the JAXP1.1 SAXParser
 interface.</div>
</section>
</li>
<li>
<section class="detail" id="errorHandler">
<h3>errorHandler</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a></span>&nbsp;<span class="element-name">errorHandler</span></div>
</section>
</li>
<li>
<section class="detail" id="MESSAGE_FILES_VALIDATED">
<h3>MESSAGE_FILES_VALIDATED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">MESSAGE_FILES_VALIDATED</span></div>
<div class="block">Message for successful validation</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.XMLValidateTask.MESSAGE_FILES_VALIDATED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>XMLValidateTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">XMLValidateTask</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setFailOnError(boolean)">
<h3>setFailOnError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnError</span><wbr><span class="parameters">(boolean&nbsp;fail)</span></div>
<div class="block">Specify how parser error are to be handled.
 Optional, default is <code>true</code>.
 <p>
 If set to <code>true</code> (default), throw a buildException if the
 parser yields an error.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fail</code> - if set to <code>false</code> do not fail on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWarn(boolean)">
<h3>setWarn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWarn</span><wbr><span class="parameters">(boolean&nbsp;bool)</span></div>
<div class="block">Specify how parser error are to be handled.
 <p>
 If set to <code>true</code> (default), log a warn message for each SAX warn event.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bool</code> - if set to <code>false</code> do not send warnings</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLenient(boolean)">
<h3>setLenient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLenient</span><wbr><span class="parameters">(boolean&nbsp;bool)</span></div>
<div class="block">Specify whether the parser should be validating. Default
 is <code>true</code>.
 <p>
 If set to false, the validation will fail only if the parsed document
 is not well formed XML.
 <p>
 this option is ignored if the specified class
 with <a href="#setClassName(java.lang.String)"><code>setClassName(String)</code></a> is not a SAX2 XMLReader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bool</code> - if set to <code>false</code> only fail on malformed XML</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassName(java.lang.String)">
<h3>setClassName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span></div>
<div class="block">Specify the class name of the SAX parser to be used. (optional)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>className</code> - should be an implementation of SAX2
 <code>org.xml.sax.XMLReader</code> or SAX2 <code>org.xml.sax.Parser</code>.
 <p>If className is an implementation of
 <code>org.xml.sax.Parser</code>, <a href="#setLenient(boolean)"><code>setLenient(boolean)</code></a>,
 will be ignored.</p>
 <p>If not set, the default will be used.</p></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link"><code>XMLReader</code></a></li>
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Parser.html" title="class or interface in org.xml.sax" class="external-link"><code>Parser</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Specify the classpath to be searched to load the parser (optional)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to load the parser</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classpath created</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setClasspath(org.apache.tools.ant.types.Path)"><code>setClasspath(org.apache.tools.ant.types.Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Where to find the parser class; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - reference to a classpath defined elsewhere</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setClasspath(org.apache.tools.ant.types.Path)"><code>setClasspath(org.apache.tools.ant.types.Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFile(java.io.File)">
<h3>setFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">specify the file to be checked; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to be checked</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">
<h3>addConfiguredXMLCatalog</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredXMLCatalog</span><wbr><span class="parameters">(<a href="../../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a>&nbsp;catalog)</span></div>
<div class="block">add an XMLCatalog as a nested element; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>catalog</code> - XMLCatalog to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">specify a set of file to be checked</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the fileset to check</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAttribute()">
<h3>createAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></span>&nbsp;<span class="element-name">createAttribute</span>()</div>
<div class="block">Add an attribute nested element. This is used for setting arbitrary
 features of the SAX parser.
 Valid attributes
 <a href="http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description">include</a></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>attribute created</dd>
<dt>Since:</dt>
<dd>ant1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createProperty()">
<h3>createProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></span>&nbsp;<span class="element-name">createProperty</span>()</div>
<div class="block">Creates a property.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a property.</dd>
<dt>Since:</dt>
<dd>ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()
          throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called by the project to let the task initialize properly.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#init()">init</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong with the build</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createDTD()">
<h3>createDTD</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/DTDLocation.html" title="class in org.apache.tools.ant.types">DTDLocation</a></span>&nbsp;<span class="element-name">createDTD</span>()</div>
<div class="block">Create a DTD location record; optional.
 This stores the location of a DTD. The DTD is identified
 by its public Id.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>created DTD location</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEntityResolver()">
<h3>getEntityResolver</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></span>&nbsp;<span class="element-name">getEntityResolver</span>()</div>
<div class="block">accessor to the xmlCatalog used in the task</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>xmlCatalog reference</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getXmlReader()">
<h3>getXmlReader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></span>&nbsp;<span class="element-name">getXmlReader</span>()</div>
<div class="block">get the XML reader. Non-null only after <a href="#initValidator()"><code>initValidator()</code></a>.
 If the reader is an instance of  <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/ParserAdapter.html" title="class or interface in org.xml.sax.helpers" class="external-link"><code>ParserAdapter</code></a> then
 the parser is a SAX1 parser, and you cannot call
 <a href="#setFeature(java.lang.String,boolean)"><code>setFeature(String, boolean)</code></a> or <a href="#setProperty(java.lang.String,java.lang.String)"><code>setProperty(String, String)</code></a>
 on it.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the XML reader or null.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">execute the task</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if <code>failonerror</code> is true and an error happens</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onSuccessfulValidation(int)">
<h3>onSuccessfulValidation</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onSuccessfulValidation</span><wbr><span class="parameters">(int&nbsp;fileProcessed)</span></div>
<div class="block">handler called on successful file validation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileProcessed</code> - number of files processed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initValidator()">
<h3>initValidator</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initValidator</span>()</div>
<div class="block">init the parser :
 load the parser class, and set features if necessary
 It is only after this that the reader is valid</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something went wrong</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSax1Parser()">
<h3>isSax1Parser</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSax1Parser</span>()</div>
<div class="block">test that returns true if we are using a SAX1 parser.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true when a SAX1 parser is in use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createXmlReader()">
<h3>createXmlReader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></span>&nbsp;<span class="element-name">createXmlReader</span>()</div>
<div class="block">create the XML reader.
 This is one by instantiating anything specified by <a href="#readerClassName"><code>readerClassName</code></a>,
 falling back to a default reader if not.
 If the returned reader is an instance of <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/ParserAdapter.html" title="class or interface in org.xml.sax.helpers" class="external-link"><code>ParserAdapter</code></a> then
 we have created and wrapped a SAX1 parser.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the new XMLReader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanup()">
<h3>cleanup</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanup</span>()</div>
<div class="block">Cleans up resources.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createDefaultReader()">
<h3>createDefaultReader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/XMLReader.html" title="class or interface in org.xml.sax" class="external-link">XMLReader</a></span>&nbsp;<span class="element-name">createDefaultReader</span>()</div>
<div class="block">Create a reader if the use of the class did not specify another one.
 If a BuildException is thrown, the caller may revert to an alternate
 reader.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a new reader.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something went wrong</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFeature(java.lang.String,boolean)">
<h3>setFeature</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFeature</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;feature,
 boolean&nbsp;value)</span>
                   throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set a feature on the parser.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>feature</code> - the name of the feature to set</dd>
<dd><code>value</code> - the value of the feature</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the feature was not supported</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProperty(java.lang.String,java.lang.String)">
<h3>setProperty</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                    throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sets a property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - a property name</dd>
<dd><code>value</code> - a property value.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs.</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the property was not supported</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doValidate(java.io.File)">
<h3>doValidate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">doValidate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;afile)</span></div>
<div class="block">parse the file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>afile</code> - the file to validate.</dd>
<dt>Returns:</dt>
<dd>true if the file validates.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
