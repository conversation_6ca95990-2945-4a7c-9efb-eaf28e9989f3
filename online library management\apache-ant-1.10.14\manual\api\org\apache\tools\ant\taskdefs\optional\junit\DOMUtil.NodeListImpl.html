<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DOMUtil.NodeListImpl (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: DOMUtil, class: NodeListImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class DOMUtil.NodeListImpl" class="title">Class DOMUtil.NodeListImpl</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/AbstractCollection.html" title="class or interface in java.util" class="external-link">java.util.AbstractCollection</a>&lt;E&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">java.util.AbstractList</a>&lt;E&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">java.util.Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt;
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="DOMUtil.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">DOMUtil.NodeListImpl</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt;
implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a></span></div>
<div class="block">custom implementation of a nodelist</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../serialized-form.html#org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.Vector">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#capacityIncrement" title="class or interface in java.util" class="external-link">capacityIncrement</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#elementCount" title="class or interface in java.util" class="external-link">elementCount</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#elementData" title="class or interface in java.util" class="external-link">elementData</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.AbstractList">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">AbstractList</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/AbstractList.html#modCount" title="class or interface in java.util" class="external-link">modCount</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">NodeListImpl</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLength()" class="member-name-link">getLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of nodes in the list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#item(int)" class="member-name-link">item</a><wbr>(int&nbsp;i)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a particular node.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Vector">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#add(int,E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#add(E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#addAll(int,java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#addAll(java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#addElement(E)" title="class or interface in java.util" class="external-link">addElement</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#capacity()" title="class or interface in java.util" class="external-link">capacity</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#clear()" title="class or interface in java.util" class="external-link">clear</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#clone()" title="class or interface in java.util" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link">contains</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#copyInto(java.lang.Object%5B%5D)" title="class or interface in java.util" class="external-link">copyInto</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#elementAt(int)" title="class or interface in java.util" class="external-link">elementAt</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#elements()" title="class or interface in java.util" class="external-link">elements</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#ensureCapacity(int)" title="class or interface in java.util" class="external-link">ensureCapacity</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#equals(java.lang.Object)" title="class or interface in java.util" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#firstElement()" title="class or interface in java.util" class="external-link">firstElement</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#forEach(java.util.function.Consumer)" title="class or interface in java.util" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#get(int)" title="class or interface in java.util" class="external-link">get</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#hashCode()" title="class or interface in java.util" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">indexOf</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link">indexOf</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#insertElementAt(E,int)" title="class or interface in java.util" class="external-link">insertElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#isEmpty()" title="class or interface in java.util" class="external-link">isEmpty</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#iterator()" title="class or interface in java.util" class="external-link">iterator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#lastElement()" title="class or interface in java.util" class="external-link">lastElement</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#lastIndexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">lastIndexOf</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#lastIndexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link">lastIndexOf</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#listIterator()" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#listIterator(int)" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#remove(int)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#remove(java.lang.Object)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeAll(java.util.Collection)" title="class or interface in java.util" class="external-link">removeAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeAllElements()" title="class or interface in java.util" class="external-link">removeAllElements</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeElement(java.lang.Object)" title="class or interface in java.util" class="external-link">removeElement</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeElementAt(int)" title="class or interface in java.util" class="external-link">removeElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeIf(java.util.function.Predicate)" title="class or interface in java.util" class="external-link">removeIf</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#removeRange(int,int)" title="class or interface in java.util" class="external-link">removeRange</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#replaceAll(java.util.function.UnaryOperator)" title="class or interface in java.util" class="external-link">replaceAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#retainAll(java.util.Collection)" title="class or interface in java.util" class="external-link">retainAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#set(int,E)" title="class or interface in java.util" class="external-link">set</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#setElementAt(E,int)" title="class or interface in java.util" class="external-link">setElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#setSize(int)" title="class or interface in java.util" class="external-link">setSize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#size()" title="class or interface in java.util" class="external-link">size</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#sort(java.util.Comparator)" title="class or interface in java.util" class="external-link">sort</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#spliterator()" title="class or interface in java.util" class="external-link">spliterator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#subList(int,int)" title="class or interface in java.util" class="external-link">subList</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#toArray()" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#toArray(T%5B%5D)" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#toString()" title="class or interface in java.util" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html#trimToSize()" title="class or interface in java.util" class="external-link">trimToSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Collection">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html#parallelStream()" title="class or interface in java.util" class="external-link">parallelStream</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html#stream()" title="class or interface in java.util" class="external-link">stream</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html#toArray(java.util.function.IntFunction)" title="class or interface in java.util" class="external-link">toArray</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>NodeListImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NodeListImpl</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getLength()">
<h3>getLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getLength</span>()</div>
<div class="block">Get the number of nodes in the list.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html#getLength()" title="class or interface in org.w3c.dom" class="external-link">getLength</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a></code></dd>
<dt>Returns:</dt>
<dd>the length of the list.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="item(int)">
<h3>item</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a></span>&nbsp;<span class="element-name">item</span><wbr><span class="parameters">(int&nbsp;i)</span></div>
<div class="block">Get a particular node.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html#item(int)" title="class or interface in org.w3c.dom" class="external-link">item</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/NodeList.html" title="class or interface in org.w3c.dom" class="external-link">NodeList</a></code></dd>
<dt>Parameters:</dt>
<dd><code>i</code> - the index of the node to get.</dd>
<dt>Returns:</dt>
<dd>the node if the index is in bounds, null otherwise.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
