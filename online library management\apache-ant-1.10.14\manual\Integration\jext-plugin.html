<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Apache AntWork Plugin for the Jext Java Text Editor</title>
</head>
<body>

<h1 id="authors">AntWork Plugin for the Jext Java Text Editor</h1>
by
<ul>
  <li><PERSON> (<a href="mailto:<PERSON>H<PERSON><EMAIL>"><EMAIL></a>)</li>
</ul>
<hr/>

<p>You can download the plugin
at: <a href="https://sourceforge.net/projects/jext/files/OldFiles/antwork_plugin.zip/download"
target="_top">https://sourceforge.net/projects/jext/files/OldFiles/antwork_plugin.zip/download</a></p>

<h2>Installation instructions from the Readme.txt</h2>

<p>You have to enable the Jext Console to see the Apache Ant output (menu:
Edit&rarr;Options&hellip;&ndash;General Panel), because Ant messages are redirected to the Jext
console.</p>

<p>You can configure the Ant call in the Jext menu: Edit&rarr;Options&hellip;&ndash; Plugin
Options&ndash;Antwork Plugin Panel; here you can set Ant home directory and the path to your build
file.</p>

<p>You can start AntWork in the menu: Plugins&rarr;Ant&rarr;Work Now!  In the appearing dialog box
you can enter the target which you want to compile.</p>

<p>If a <code>javac</code> error occurs in the Ant run, an error list opens within Jext. With a
double click on the error message you jump to the error in the specified Java source file.</p>

</body>
</html>
