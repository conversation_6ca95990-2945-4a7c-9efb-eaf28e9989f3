<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Link (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.modules, class: Link">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.modules</a></div>
<h1 title="Class Link" class="title">Class Link</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.modules.Link</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Link</span>
<span class="extends-implements">extends <a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Assembles jmod files into an executable image.  Equivalent to the
 JDK <code>jlink</code> command.
 <p>
 Supported attributes:
 <dl>
 <dt><code>destDir</code>
 <dd>Root directory of created image. (required)
 <dt><code>modulePath</code>
 <dd>Path of modules.  Should be a list of .jmod files.  Required, unless
     nested module path or modulepathref is present.
 <dt><code>modulePathRef</code>
 <dd>Reference to path of modules.  Referenced path should be
     a list of .jmod files.
 <dt><code>modules</code>
 <dd>Comma-separated list of modules to assemble.  Required, unless
     one or more nested <code>&lt;module&gt;</code> elements are present.
 <dt><code>observableModules</code>
 <dd>Comma-separated list of explicit modules that comprise
     "universe" visible to tool while linking.
 <dt><code>launchers</code>
 <dd>Comma-separated list of commands, each of the form
     <var>name</var><code>=</code><var>module</var> or
     <var>name</var><code>=</code><var>module</var><code>/</code><var>mainclass</var>
 <dt><code>excludeFiles</code>
 <dd>Comma-separated list of patterns specifying files to exclude from
     linked image.
     Each is either a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     or <code>@</code><var>filename</var>.
 <dt><code>excludeResources</code>
 <dd>Comma-separated list of patterns specifying resources to exclude from jmods.
     Each is either a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     or <code>@</code><var>filename</var>.
 <dt><code>locales</code>
 <dd>Comma-separated list of extra locales to include,
     requires <code>jdk.localedata</code> module
 <dt><code>resourceOrder</code>
 <dt>Comma-separated list of patterns specifying resource search order.
     Each is either a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     or <code>@</code><var>filename</var>.
 <dt><code>bindServices</code>
 <dd>boolean, whether to link service providers; default is false
 <dt><code>ignoreSigning</code>
 <dd>boolean, whether to allow signed jar files; default is false
 <dt><code>includeHeaders</code>
 <dd>boolean, whether to include header files; default is true
 <dt><code>includeManPages</code>
 <dd>boolean, whether to include man pages; default is true
 <dt><code>includeNativeCommands</code>
 <dd>boolean, whether to include native executables normally generated
     for image; default is true
 <dt><code>debug</code>
 <dd>boolean, whether to include debug information; default is true
 <dt><code>verboseLevel</code>
 <dd>If set, jlink will produce verbose output, which will be logged at
     the specified Ant log level (<code>DEBUG</code>, <code>VERBOSE</code>,
     <code>INFO</code>}, <code>WARN</code>, or <code>ERR</code>).
 <dt><code>compress</code>
 <dd>compression level, one of:
     <dl>
     <dt><code>0</code>
     <dt><code>none</code>
     <dd>no compression (default)
     <dt><code>1</code>
     <dt><code>strings</code>
     <dd>constant string sharing
     <dt><code>2</code>
     <dt><code>zip</code>
     <dd>zip compression
     </dl>
 <dt><code>endianness</code>
 <dd>Must be <code>little</code> or <code>big</code>, default is native endianness
 <dt><code>checkDuplicateLegal</code>
 <dd>Boolean.  When merging legal notices from different modules
     because they have the same name, verify that their contents
     are identical.  Default is false, which means any license files
     with the same name are assumed to have the same content, and no
     checking is done.
 <dt><code>vmType</code>
 <dd>Hotspot VM in image, one of:
     <ul>
     <li><code>client</code>
     <li><code>server</code>
     <li><code>minimal</code>
     <li><code>all</code> (default)
     </ul>
 </dl>

 <p>
 Supported nested elements
 <dl>
 <dt><code>&lt;modulepath&gt;</code>
 <dd>path element
 <dt><code>&lt;module&gt;</code>
 <dd>May be specified multiple times.
     Only attribute is required <code>name</code> attribute.
 <dt><code>&lt;observableModule&gt;</code>
 <dd>May be specified multiple times.
     Only attribute is required <code>name</code> attribute.
 <dt><code>&lt;launcher&gt;</code>
 <dd>May be specified multiple times.  Attributes:
     <ul>
     <li><code>name</code> (required)
     <li><code>module</code> (required)
     <li><code>mainClass</code> (optional)
     </ul>
 <dt><code>&lt;locale&gt;</code>
 <dd>May be specified multiple times.
     Only attribute is required <code>name</code> attribute.
 <dt><code>&lt;resourceOrder&gt;</code>
 <dd>Explicit resource search order in image.  May be specified multiple
     times.  Exactly one of these attributes must be specified:
     <dl>
     <dt><code>pattern</code>
     <dd>A <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     <dt><code>listFile</code>
     <dd>Text file containing list of resource names (not patterns),
         one per line
     </dl>
     If the <code>resourceOrder</code> attribute is also present on the task, its
     patterns are treated as if they occur before patterns in nested
     <code>&lt;resourceOrder&gt;</code> elements.
 <dt><code>&lt;excludeFiles&gt;</code>
 <dd>Excludes files from linked image tree.  May be specified multiple times.
     Exactly one of these attributes is required:
     <dl>
     <dt><code>pattern</code>
     <dd>A <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     <dt><code>listFile</code>
     <dd>Text file containing list of file names (not patterns),
         one per line
     </dl>
 <dt><code>&lt;excludeResources&gt;</code>
 <dd>Excludes resources from jmods.  May be specified multiple times.
     Exactly one of these attributes is required:
     <dl>
     <dt><code>pattern</code>
     <dd>A <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/nio/file/FileSystem.html#getPathMatcher%28java.lang.String%29">standard PathMatcher pattern</a>
     <dt><code>listFile</code>
     <dd>Text file containing list of resource names (not patterns),
         one per line
     </dl>
 <dt><code>&lt;compress&gt;</code>
 <dd>Must have <code>level</code> attribute, whose permitted values are the same
     as the <code>compress</code> task attribute described above.
     May also have a <code>files</code> attribute, which is a comma-separated
     list of patterns, and/or nested <code>&lt;files&gt;</code> elements, each with
     either a <code>pattern</code> attribute or <code>listFile</code> attribute.
 <dt><code>&lt;releaseInfo&gt;</code>
 <dd>Replaces, augments, or trims the image's release info properties.
     This may specify any of the following:
     <ul>
     <li>A <code>file</code> attribute, pointing to a Java properties file
         containing new release info properties that will entirely replace
         the current ones.
     <li>A <code>delete</code> attribute, containing comma-separated property keys
         to remove from application's release info, and/or any number of
         nested <code>&lt;delete&gt;</code> elements, each with a required <code>key</code>
         attribute.
     <li>One or more nested <code>&lt;add&gt;</code> elements, containing either
         <code>key</code> and <code>value</code> attributes, or a <code>file</code>
         attribute and an optional <code>charset</code> attribute.
     </ul>
 </dl></div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.10.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://docs.oracle.com/en/java/javase/11/tools/jlink.html"><code>jlink</code> tool reference</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.Compression.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Compression</a></code></div>
<div class="col-last even-row-color">
<div class="block">Child element fully describing compression of a linked image.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Link.CompressionLevel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Possible attribute values for compression level of a linked image:
 
 <code>0</code>
 <code>none</code>
 no compression (default)
 <code>1</code>
 <code>strings</code>
 constant string sharing
 <code>2</code>
 <code>zip</code>
 zip compression
 </div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.Endianness.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a></code></div>
<div class="col-last even-row-color">
<div class="block">Possible values for linked image endianness:
 <code>little</code> and <code>big</code>.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Link.Launcher.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.Launcher</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Child element representing a custom launcher command in a linked image.</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.LocaleSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.LocaleSpec</a></code></div>
<div class="col-last even-row-color">
<div class="block">Child element that contains a pattern matching Java locales.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Link.ModuleSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Child element that explicitly names a Java module.</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.PatternListEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></code></div>
<div class="col-last even-row-color">
<div class="block">Child element type which specifies a jlink files pattern.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Link.ReleaseInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfo</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Child element describing changes to the default release properties
 of a linked image.</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.ReleaseInfoEntry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfoEntry</a></code></div>
<div class="col-last even-row-color">
<div class="block">Grandchild element describing additional release info properties for a
 linked image.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Link.ReleaseInfoKey.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfoKey</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Grandchild element representing deletable key in a linked image's
 release properties.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Link.VMType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a></code></div>
<div class="col-last even-row-color">
<div class="block">Possible values for JVM type in linked image:
 <code>client</code>, <code>server</code>, <code>minimal</code>, or <code>all</code>.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Link</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.Compression.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Compression</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompress()" class="member-name-link">createCompress</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;compress&gt;</code> element that specifies the level of
 compression the linker will apply, and optionally, which files in the
 image will be compressed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExcludeFiles()" class="member-name-link">createExcludeFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;excludeFiles&gt;</code> element that specifies
 files to exclude from linked modules when assembling linked image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExcludeResources()" class="member-name-link">createExcludeResources</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;excludeResources&gt;</code> element that specifies
 resources in linked modules that will be excluded from linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.Launcher.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Launcher</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createLauncher()" class="member-name-link">createLauncher</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;launcher&gt;</code> element that can contain information
 on additional executable in the linked image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.LocaleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.LocaleSpec</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createLocale()" class="member-name-link">createLocale</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;locale&gt;</code> element that specifies a Java locale,
 or set of locales, to include from the <code>jdk.localedata</code> module
 in the linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.ModuleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModule()" class="member-name-link">createModule</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds child <code>&lt;module&gt;</code> element, specifying a module to link.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModulePath()" class="member-name-link">createModulePath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds child <code>&lt;modulePath&gt;</code> element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.ModuleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createObservableModule()" class="member-name-link">createObservableModule</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;observableModule&gt;</code> element that represents
 one of the modules the linker is permitted to know about.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.ReleaseInfo.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfo</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createReleaseInfo()" class="member-name-link">createReleaseInfo</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;releaseInfo&gt;</code> element that modifies the default
 release properties of the linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createResourceOrder()" class="member-name-link">createResourceOrder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child <code>&lt;resourceOrder</code> element that specifies
 explicit ordering of resources in linked image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Invokes the jlink tool to create a new linked image, unless the
 output directory exists and all of its files are files are newer
 than all files in the module path.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBindServices()" class="member-name-link">getBindServices</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether linked image should pull in providers
 in the module path of services used by explicitly linked modules.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCheckDuplicateLegal()" class="member-name-link">getCheckDuplicateLegal</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether linker should check legal notices with
 duplicate names, and refuse to merge them (usually using symbolic links)
 if their respective content is not identical.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.CompressionLevel.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompress()" class="member-name-link">getCompress</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating level of compression linker will apply to image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDebug()" class="member-name-link">getDebug</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether linker should keep or strip
 debug information in classes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestDir()" class="member-name-link">getDestDir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Required attribute containing directory where linked image will be
 created.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.Endianness.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEndianness()" class="member-name-link">getEndianness</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute which indicates whether certain files in the linked image
 will be big-endian or little-endian.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIgnoreSigning()" class="member-name-link">getIgnoreSigning</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether linker should allow modules made from
 signed jars.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeHeaders()" class="member-name-link">getIncludeHeaders</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether to include header files from linked modules
 in image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeManPages()" class="member-name-link">getIncludeManPages</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether to include man pages from linked modules
 in image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeNativeCommands()" class="member-name-link">getIncludeNativeCommands</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether to include generated native commands,
 and native commands from linked modules, in image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulePath()" class="member-name-link">getModulePath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing path of directories containing linkable modules.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/LogLevel.html" title="class in org.apache.tools.ant.types">LogLevel</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVerboseLevel()" class="member-name-link">getVerboseLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether linker should produce verbose output,
 and at what logging level that output should be shown.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Link.VMType.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVmType()" class="member-name-link">getVmType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating what type of JVM the linked image should have.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBindServices(boolean)" class="member-name-link">setBindServices</a><wbr>(boolean&nbsp;bind)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether linked image should pull in providers
 in the module path of services used by explicitly linked modules.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCheckDuplicateLegal(boolean)" class="member-name-link">setCheckDuplicateLegal</a><wbr>(boolean&nbsp;check)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether linker should check legal notices with
 duplicate names, and refuse to merge them (usually using symbolic links)
 if their respective content is not identical.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompress(org.apache.tools.ant.taskdefs.modules.Link.CompressionLevel)" class="member-name-link">setCompress</a><wbr>(<a href="Link.CompressionLevel.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a>&nbsp;level)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating level of compression linker will apply
 to image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether linker should keep or strip
 debug information in classes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestDir(java.io.File)" class="member-name-link">setDestDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating directory where linked image will be created.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEndianness(org.apache.tools.ant.taskdefs.modules.Link.Endianness)" class="member-name-link">setEndianness</a><wbr>(<a href="Link.Endianness.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a>&nbsp;endianness)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute which indicates whether certain files in the linked image
 will be big-endian or little-endian.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludeFiles(java.lang.String)" class="member-name-link">setExcludeFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a list of patterns denoting files
 to exclude from linked modules when assembling linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludeResources(java.lang.String)" class="member-name-link">setExcludeResources</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a list of patterns denoting resources
 to exclude from linked modules in linked image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnoreSigning(boolean)" class="member-name-link">setIgnoreSigning</a><wbr>(boolean&nbsp;ignore)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether linker should allow modules made from
 signed jars.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeHeaders(boolean)" class="member-name-link">setIncludeHeaders</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether to include header files from
 linked modules in image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeManPages(boolean)" class="member-name-link">setIncludeManPages</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether to include man pages from
 linked modules in image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeNativeCommands(boolean)" class="member-name-link">setIncludeNativeCommands</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether to include generated native commands,
 and native commands from linked modules, in image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLaunchers(java.lang.String)" class="member-name-link">setLaunchers</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;launcherList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing comma-separated list of information needed for
 additional executables in the linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLocales(java.lang.String)" class="member-name-link">setLocales</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;localeList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a list of locale patterns, to specify
 Java locales to include from <code>jdk.localedata</code> module in
 linked image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulePath(org.apache.tools.ant.types.Path)" class="member-name-link">setModulePath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing path of directories containing
 linkable modules.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulePathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setModulePathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets module path as a reference.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModules(java.lang.String)" class="member-name-link">setModules</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;moduleList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing list of modules to link.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setObservableModules(java.lang.String)" class="member-name-link">setObservableModules</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;moduleList)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing modules linker is permitted to know about.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResourceOrder(java.lang.String)" class="member-name-link">setResourceOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a list of patterns that explicitly
 order resources in the linked image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerboseLevel(org.apache.tools.ant.types.LogLevel)" class="member-name-link">setVerboseLevel</a><wbr>(<a href="../../types/LogLevel.html" title="class in org.apache.tools.ant.types">LogLevel</a>&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether linker should produce verbose output,
 and at what logging level that output should be shown.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVmType(org.apache.tools.ant.taskdefs.modules.Link.VMType)" class="member-name-link">setVmType</a><wbr>(<a href="Link.VMType.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set attribute indicating what type of JVM the linked image should have.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#init()">init</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Link</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Link</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createModulePath()">
<h3>createModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createModulePath</span>()</div>
<div class="block">Adds child <code>&lt;modulePath&gt;</code> element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, empty child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModulePath(org.apache.tools.ant.types.Path)"><code>setModulePath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModulePath()">
<h3>getModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulePath</span>()</div>
<div class="block">Attribute containing path of directories containing linkable modules.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>current module path, possibly <code>null</code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModulePath(org.apache.tools.ant.types.Path)"><code>setModulePath(Path)</code></a></li>
<li><a href="#createModulePath()"><code>createModulePath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModulePath(org.apache.tools.ant.types.Path)">
<h3>setModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulePath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing path of directories containing
 linkable modules.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - new module path</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getModulePath()"><code>getModulePath()</code></a></li>
<li><a href="#setModulePathRef(org.apache.tools.ant.types.Reference)"><code>setModulePathRef(Reference)</code></a></li>
<li><a href="#createModulePath()"><code>createModulePath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModulePathRef(org.apache.tools.ant.types.Reference)">
<h3>setModulePathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulePathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets module path as a reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - path reference</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModulePath(org.apache.tools.ant.types.Path)"><code>setModulePath(Path)</code></a></li>
<li><a href="#createModulePath()"><code>createModulePath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createModule()">
<h3>createModule</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.ModuleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></span>&nbsp;<span class="element-name">createModule</span>()</div>
<div class="block">Adds child <code>&lt;module&gt;</code> element, specifying a module to link.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModules(java.lang.String)"><code>setModules(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModules(java.lang.String)">
<h3>setModules</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModules</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;moduleList)</span></div>
<div class="block">Sets attribute containing list of modules to link.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>moduleList</code> - comma-separated list of module names</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createObservableModule()">
<h3>createObservableModule</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.ModuleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ModuleSpec</a></span>&nbsp;<span class="element-name">createObservableModule</span>()</div>
<div class="block">Creates child <code>&lt;observableModule&gt;</code> element that represents
 one of the modules the linker is permitted to know about.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setObservableModules(java.lang.String)">
<h3>setObservableModules</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setObservableModules</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;moduleList)</span></div>
<div class="block">Sets attribute containing modules linker is permitted to know about.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>moduleList</code> - comma-separated list of module names</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createLauncher()">
<h3>createLauncher</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.Launcher.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Launcher</a></span>&nbsp;<span class="element-name">createLauncher</span>()</div>
<div class="block">Creates child <code>&lt;launcher&gt;</code> element that can contain information
 on additional executable in the linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setLaunchers(java.lang.String)"><code>setLaunchers(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLaunchers(java.lang.String)">
<h3>setLaunchers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLaunchers</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;launcherList)</span></div>
<div class="block">Sets attribute containing comma-separated list of information needed for
 additional executables in the linked image.  Each item must be of the
 form * <var>name</var><code>=</code><var>module</var> or
 <var>name</var><code>=</code><var>module</var><code>/</code><var>mainclass</var>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>launcherList</code> - comma-separated list of launcher data</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createLocale()">
<h3>createLocale</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.LocaleSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.LocaleSpec</a></span>&nbsp;<span class="element-name">createLocale</span>()</div>
<div class="block">Creates child <code>&lt;locale&gt;</code> element that specifies a Java locale,
 or set of locales, to include from the <code>jdk.localedata</code> module
 in the linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLocales(java.lang.String)">
<h3>setLocales</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLocales</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;localeList)</span></div>
<div class="block">Sets attribute containing a list of locale patterns, to specify
 Java locales to include from <code>jdk.localedata</code> module in
 linked image.  Asterisks (<code>*</code>) are permitted for wildcard
 matches.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>localeList</code> - comma-separated list of locale patterns</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExcludeFiles()">
<h3>createExcludeFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></span>&nbsp;<span class="element-name">createExcludeFiles</span>()</div>
<div class="block">Creates child <code>&lt;excludeFiles&gt;</code> element that specifies
 files to exclude from linked modules when assembling linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setExcludeFiles(java.lang.String)"><code>setExcludeFiles(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludeFiles(java.lang.String)">
<h3>setExcludeFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludeFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</span></div>
<div class="block">Sets attribute containing a list of patterns denoting files
 to exclude from linked modules when assembling linked image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>patternList</code> - comman-separated list of patterns</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules"><code>Link.PatternListEntry</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExcludeResources()">
<h3>createExcludeResources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></span>&nbsp;<span class="element-name">createExcludeResources</span>()</div>
<div class="block">Creates child <code>&lt;excludeResources&gt;</code> element that specifies
 resources in linked modules that will be excluded from linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setExcludeResources(java.lang.String)"><code>setExcludeResources(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludeResources(java.lang.String)">
<h3>setExcludeResources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludeResources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</span></div>
<div class="block">Sets attribute containing a list of patterns denoting resources
 to exclude from linked modules in linked image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>patternList</code> - comma-separated list of patterns</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createExcludeResources()"><code>createExcludeResources()</code></a></li>
<li><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules"><code>Link.PatternListEntry</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createResourceOrder()">
<h3>createResourceOrder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.PatternListEntry</a></span>&nbsp;<span class="element-name">createResourceOrder</span>()</div>
<div class="block">Creates child <code>&lt;resourceOrder</code> element that specifies
 explicit ordering of resources in linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setResourceOrder(java.lang.String)"><code>setResourceOrder(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setResourceOrder(java.lang.String)">
<h3>setResourceOrder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResourceOrder</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;patternList)</span></div>
<div class="block">Sets attribute containing a list of patterns that explicitly
 order resources in the linked image.  Any patterns specified here
 will be placed before any patterns specified as
 <a href="#createResourceOrder()">child elements</a>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>patternList</code> - comma-separated list of patterns</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createResourceOrder()"><code>createResourceOrder()</code></a></li>
<li><a href="Link.PatternListEntry.html" title="class in org.apache.tools.ant.taskdefs.modules"><code>Link.PatternListEntry</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBindServices()">
<h3>getBindServices</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getBindServices</span>()</div>
<div class="block">Attribute indicating whether linked image should pull in providers
 in the module path of services used by explicitly linked modules.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if linked will pull in service provides, false if not</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setBindServices(boolean)"><code>setBindServices(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBindServices(boolean)">
<h3>setBindServices</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBindServices</span><wbr><span class="parameters">(boolean&nbsp;bind)</span></div>
<div class="block">Sets attribute indicating whether linked image should pull in providers
 in the module path of services used by explicitly linked modules.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bind</code> - whether to include service providers</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getBindServices()"><code>getBindServices()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIgnoreSigning()">
<h3>getIgnoreSigning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIgnoreSigning</span>()</div>
<div class="block">Attribute indicating whether linker should allow modules made from
 signed jars.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if signed jars are allowed, false if modules based on
         signed jars cause an error</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setIgnoreSigning(boolean)"><code>setIgnoreSigning(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnoreSigning(boolean)">
<h3>setIgnoreSigning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnoreSigning</span><wbr><span class="parameters">(boolean&nbsp;ignore)</span></div>
<div class="block">Sets attribute indicating whether linker should allow modules made from
 signed jars.
 <p>
 Note: As of Java 11, this attribute is internally forced to true.  See
 <a href="https://github.com/AdoptOpenJDK/openjdk-jdk11/blob/master/src/jdk.jlink/share/classes/jdk/tools/jlink/internal/JlinkTask.java#L80">the source</a>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ignore</code> - true to have linker allow signed jars,
               false to have linker emit an error for signed jars</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getIgnoreSigning()"><code>getIgnoreSigning()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeHeaders()">
<h3>getIncludeHeaders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeHeaders</span>()</div>
<div class="block">Attribute indicating whether to include header files from linked modules
 in image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if header files should be included, false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setIncludeHeaders(boolean)"><code>setIncludeHeaders(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludeHeaders(boolean)">
<h3>setIncludeHeaders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeHeaders</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">Sets attribute indicating whether to include header files from
 linked modules in image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - true if header files should be included,
                false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getIncludeHeaders()"><code>getIncludeHeaders()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeManPages()">
<h3>getIncludeManPages</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeManPages</span>()</div>
<div class="block">Attribute indicating whether to include man pages from linked modules
 in image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if man pages should be included, false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setIncludeManPages(boolean)"><code>setIncludeManPages(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludeManPages(boolean)">
<h3>setIncludeManPages</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeManPages</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">Sets attribute indicating whether to include man pages from
 linked modules in image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - true if man pages should be included,
                false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getIncludeManPages()"><code>getIncludeManPages()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeNativeCommands()">
<h3>getIncludeNativeCommands</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeNativeCommands</span>()</div>
<div class="block">Attribute indicating whether to include generated native commands,
 and native commands from linked modules, in image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if native commands should be included, false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setIncludeNativeCommands(boolean)"><code>setIncludeNativeCommands(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludeNativeCommands(boolean)">
<h3>setIncludeNativeCommands</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeNativeCommands</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">Sets attribute indicating whether to include generated native commands,
 and native commands from linked modules, in image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - true if native commands should be included,
                false to exclude them</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getIncludeNativeCommands()"><code>getIncludeNativeCommands()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDebug()">
<h3>getDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDebug</span>()</div>
<div class="block">Attribute indicating whether linker should keep or strip
 debug information in classes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if debug information will be retained,
         false if it will be stripped</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDebug(boolean)"><code>setDebug(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">Sets attribute indicating whether linker should keep or strip
 debug information in classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - true if debug information should be retained,
              false if it should be stripped</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getDebug()"><code>getDebug()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVerboseLevel()">
<h3>getVerboseLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/LogLevel.html" title="class in org.apache.tools.ant.types">LogLevel</a></span>&nbsp;<span class="element-name">getVerboseLevel</span>()</div>
<div class="block">Attribute indicating whether linker should produce verbose output,
 and at what logging level that output should be shown.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>logging level at which to show linker's verbose output,
         or <code>null</code> to disable verbose output</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setVerboseLevel(org.apache.tools.ant.types.LogLevel)"><code>setVerboseLevel(LogLevel)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerboseLevel(org.apache.tools.ant.types.LogLevel)">
<h3>setVerboseLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerboseLevel</span><wbr><span class="parameters">(<a href="../../types/LogLevel.html" title="class in org.apache.tools.ant.types">LogLevel</a>&nbsp;level)</span></div>
<div class="block">Sets attribute indicating whether linker should produce verbose output,
 and at what logging level that output should be shown.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>level</code> - level logging level at which to show linker's
              verbose output, or <code>null</code> to disable verbose output</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getVerboseLevel()"><code>getVerboseLevel()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDestDir()">
<h3>getDestDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestDir</span>()</div>
<div class="block">Required attribute containing directory where linked image will be
 created.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>directory where linked image will reside</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDestDir(java.io.File)"><code>setDestDir(File)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestDir(java.io.File)">
<h3>setDestDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span></div>
<div class="block">Sets attribute indicating directory where linked image will be created.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - directory in which image will be created by linker</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getDestDir()"><code>getDestDir()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompress()">
<h3>getCompress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.CompressionLevel.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a></span>&nbsp;<span class="element-name">getCompress</span>()</div>
<div class="block">Attribute indicating level of compression linker will apply to image.
 This is exclusive with regard to <a href="#createCompress()"><code>createCompress()</code></a>:  only one
 of the two may be specified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>compression level to apply, or <code>null</code> for none</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setCompress(org.apache.tools.ant.taskdefs.modules.Link.CompressionLevel)"><code>setCompress(Link.CompressionLevel)</code></a></li>
<li><a href="#createCompress()"><code>createCompress()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompress(org.apache.tools.ant.taskdefs.modules.Link.CompressionLevel)">
<h3>setCompress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompress</span><wbr><span class="parameters">(<a href="Link.CompressionLevel.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.CompressionLevel</a>&nbsp;level)</span></div>
<div class="block">Sets attribute indicating level of compression linker will apply
 to image. This is exclusive with regard to <a href="#createCompress()"><code>createCompress()</code></a>:
 only one of the two may be specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>level</code> - compression level to apply, or <code>null</code> for none</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getCompress()"><code>getCompress()</code></a></li>
<li><a href="#createCompress()"><code>createCompress()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCompress()">
<h3>createCompress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.Compression.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Compression</a></span>&nbsp;<span class="element-name">createCompress</span>()</div>
<div class="block">Creates child <code>&lt;compress&gt;</code> element that specifies the level of
 compression the linker will apply, and optionally, which files in the
 image will be compressed.  This is exclusive with regard to the
 <a href="#setCompress(org.apache.tools.ant.taskdefs.modules.Link.CompressionLevel)"><code>compress</code></a> attribute:  only one of the two may be
 specified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setCompress(org.apache.tools.ant.taskdefs.modules.Link.CompressionLevel)"><code>setCompress(Link.CompressionLevel)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEndianness()">
<h3>getEndianness</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.Endianness.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a></span>&nbsp;<span class="element-name">getEndianness</span>()</div>
<div class="block">Attribute which indicates whether certain files in the linked image
 will be big-endian or little-endian.  If <code>null</code>, the underlying
 platform's endianness is used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>endianness to apply, or <code>null</code> to platform default</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setEndianness(org.apache.tools.ant.taskdefs.modules.Link.Endianness)"><code>setEndianness(Link.Endianness)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEndianness(org.apache.tools.ant.taskdefs.modules.Link.Endianness)">
<h3>setEndianness</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEndianness</span><wbr><span class="parameters">(<a href="Link.Endianness.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.Endianness</a>&nbsp;endianness)</span></div>
<div class="block">Sets attribute which indicates whether certain files in the linked image
 will be big-endian or little-endian.  If <code>null</code>, the underlying
 platform's endianness is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>endianness</code> - endianness to apply, or <code>null</code> to use
                   platform default</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getEndianness()"><code>getEndianness()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCheckDuplicateLegal()">
<h3>getCheckDuplicateLegal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getCheckDuplicateLegal</span>()</div>
<div class="block">Attribute indicating whether linker should check legal notices with
 duplicate names, and refuse to merge them (usually using symbolic links)
 if their respective content is not identical.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if legal notice files with same name should be checked
         for identical content, false to suppress check</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setCheckDuplicateLegal(boolean)"><code>setCheckDuplicateLegal(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCheckDuplicateLegal(boolean)">
<h3>setCheckDuplicateLegal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCheckDuplicateLegal</span><wbr><span class="parameters">(boolean&nbsp;check)</span></div>
<div class="block">Sets attribute indicating whether linker should check legal notices with
 duplicate names, and refuse to merge them (usually using symbolic links)
 if their respective content is not identical.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>check</code> - true if legal notice files with same name should be checked
         for identical content, false to suppress check</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getCheckDuplicateLegal()"><code>getCheckDuplicateLegal()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVmType()">
<h3>getVmType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.VMType.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a></span>&nbsp;<span class="element-name">getVmType</span>()</div>
<div class="block">Attribute indicating what type of JVM the linked image should have.
 If <code>null</code>, all JVM types are included.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>type of JVM linked image will have</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setVmType(org.apache.tools.ant.taskdefs.modules.Link.VMType)"><code>setVmType(Link.VMType)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVmType(org.apache.tools.ant.taskdefs.modules.Link.VMType)">
<h3>setVmType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVmType</span><wbr><span class="parameters">(<a href="Link.VMType.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.VMType</a>&nbsp;type)</span></div>
<div class="block">Set attribute indicating what type of JVM the linked image should have.
 If <code>null</code>, all JVM types are included.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - type of JVM linked image will have</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getVmType()"><code>getVmType()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createReleaseInfo()">
<h3>createReleaseInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Link.ReleaseInfo.html" title="class in org.apache.tools.ant.taskdefs.modules">Link.ReleaseInfo</a></span>&nbsp;<span class="element-name">createReleaseInfo</span>()</div>
<div class="block">Creates child <code>&lt;releaseInfo&gt;</code> element that modifies the default
 release properties of the linked image.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Invokes the jlink tool to create a new linked image, unless the
 output directory exists and all of its files are files are newer
 than all files in the module path.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if destDir is not set</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if module path is unset or empty</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if module list is empty</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if compressionLevel attribute and compression
                        child element are both specified</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
