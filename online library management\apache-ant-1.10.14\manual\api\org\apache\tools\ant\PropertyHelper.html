<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>PropertyHelper (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: PropertyHelper">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class PropertyHelper" class="title">Class PropertyHelper</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.PropertyHelper</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">PropertyHelper</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a></span></div>
<div class="block">Deals with properties - substitution, dynamic properties, etc.

 <p>This code has been heavily restructured for Ant 1.8.0.  It is
 expected that custom PropertyHelper implementation that used the
 older chaining mechanism of Ant 1.6 won't work in all cases, and
 its usage is deprecated.  The preferred way to customize Ant's
 property handling is by <a href="#add(org.apache.tools.ant.PropertyHelper.Delegate)"><code>adding</code></a> <a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant"><code>delegates</code></a> of the appropriate subinterface
 and have this implementation use them.</p>

 <p>When <a href="#parseProperties(java.lang.String)"><code>expanding a string that may contain
 properties</code></a> this class will delegate the actual parsing to <a href="property/ParseProperties.html#parseProperties(java.lang.String)"><code>parseProperties</code></a> inside the ParseProperties class which in turn
 uses the <a href="property/PropertyExpander.html" title="interface in org.apache.tools.ant.property"><code>PropertyExpander delegates</code></a> to find properties inside the string
 and this class to expand the property names found into the
 corresponding values.</p>

 <p>When <a href="#getProperty(org.apache.tools.ant.Project,java.lang.String)"><code>looking up a property value</code></a> this class
 will first consult all <a href="PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant"><code>PropertyEvaluator</code></a> delegates and fall back to an internal map of
 "project properties" if no evaluator matched the property name.</p>

 <p>When <a href="#setProperty(org.apache.tools.ant.Project,java.lang.String,java.lang.Object)"><code>setting a property value</code></a> this class
 will first consult all <a href="PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant"><code>PropertySetter</code></a> delegates and fall back to an internal map of
 "project properties" if no setter matched the property name.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="PropertyHelper.Delegate.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a></code></div>
<div class="col-last even-row-color">
<div class="block">Marker interface for a PropertyHelper delegate.</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="PropertyHelper.PropertyEnumerator.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Obtains the names of all known properties.</div>
</div>
<div class="col-first even-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="PropertyHelper.PropertyEvaluator.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></code></div>
<div class="col-last even-row-color">
<div class="block">Looks up a property's value based on its name.</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="PropertyHelper.PropertySetter.html" class="type-name-link" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Sets or overrides a property.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">PropertyHelper</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.PropertyHelper.Delegate)" class="member-name-link">add</a><wbr>(<a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&nbsp;delegate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the specified delegate object to this PropertyHelper.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#containsProperties(java.lang.String)" class="member-name-link">containsProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Learn whether a String contains replaceable properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyInheritedProperties(org.apache.tools.ant.Project)" class="member-name-link">copyInheritedProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies all user properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyUserProperties(org.apache.tools.ant.Project)" class="member-name-link">copyUserProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies all user properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDelegateInterfaces(org.apache.tools.ant.PropertyHelper.Delegate)" class="member-name-link">getDelegateInterfaces</a><wbr>(<a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&nbsp;d)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get all Delegate interfaces (excluding Delegate itself) from the specified Delegate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected &lt;D extends <a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&gt;<br><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;D&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDelegates(java.lang.Class)" class="member-name-link">getDelegates</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;D&gt;&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Collection of delegates of the specified type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="property/PropertyExpander.html" title="interface in org.apache.tools.ant.property">PropertyExpander</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExpanders()" class="member-name-link">getExpanders</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the <a href="property/PropertyExpander.html" title="interface in org.apache.tools.ant.property"><code>expanders</code></a>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInheritedProperties()" class="member-name-link">getInheritedProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a copy of the inherited property hashtable</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInternalInheritedProperties()" class="member-name-link">getInternalInheritedProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">special back door for subclasses, internal access to the hashtables</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInternalProperties()" class="member-name-link">getInternalProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">special back door for subclasses, internal access to the hashtables</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInternalUserProperties()" class="member-name-link">getInternalUserProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">special back door for subclasses, internal access to the hashtables</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getNext()" class="member-name-link">getNext</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the delegate mechanism instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get this PropertyHelper's Project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProperties()" class="member-name-link">getProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a copy of the properties table.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProperty(java.lang.String)" class="member-name-link">getProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the value of a property, if it is set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getProperty(java.lang.String,java.lang.String)" class="member-name-link">getProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getProperty(org.apache.tools.ant.Project,java.lang.String)" class="member-name-link">getProperty</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">A helper static method to get a property
 from a particular project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getPropertyHelper(org.apache.tools.ant.Project)" class="member-name-link">getPropertyHelper</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Factory method to create a property processor.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getPropertyHook(java.lang.String,java.lang.String,boolean)" class="member-name-link">getPropertyHook</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;user)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">PropertyHelper chaining is deprecated.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPropertyNames()" class="member-name-link">getPropertyNames</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the names of all known properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserProperties()" class="member-name-link">getUserProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a copy of the user property hashtable</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserProperty(java.lang.String)" class="member-name-link">getUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the value of a user property, if it is set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getUserProperty(java.lang.String,java.lang.String)" class="member-name-link">getUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseProperties(java.lang.String)" class="member-name-link">parseProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decode properties from a String representation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)" class="member-name-link">parsePropertyString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fragments,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;propertyRefs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the other mechanisms of this class instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#replaceProperties(java.lang.String)" class="member-name-link">replaceProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#replaceProperties(java.lang.String,java.lang.String,java.util.Hashtable)" class="member-name-link">replaceProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;keys)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInheritedProperty(java.lang.String,java.lang.Object)" class="member-name-link">setInheritedProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets an inherited user property, which cannot be overwritten by set/unset
 property calls.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setInheritedProperty(java.lang.String,java.lang.String,java.lang.Object)" class="member-name-link">setInheritedProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewProperty(java.lang.String,java.lang.Object)" class="member-name-link">setNewProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a property if no value currently exists.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setNewProperty(java.lang.String,java.lang.String,java.lang.Object)" class="member-name-link">setNewProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setNewProperty(org.apache.tools.ant.Project,java.lang.String,java.lang.Object)" class="member-name-link">setNewProperty</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">A helper static method to set a new property
 from a particular project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setNext(org.apache.tools.ant.PropertyHelper)" class="member-name-link">setNext</a><wbr>(<a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;next)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the delegate mechanism instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProject(org.apache.tools.ant.Project)" class="member-name-link">setProject</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the project for which this helper is performing property resolution.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProperty(java.lang.String,java.lang.Object,boolean)" class="member-name-link">setProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;verbose)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Default implementation of setProperty.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setProperty(java.lang.String,java.lang.String,java.lang.Object,boolean)" class="member-name-link">setProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;verbose)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setProperty(org.apache.tools.ant.Project,java.lang.String,java.lang.Object)" class="member-name-link">setProperty</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">A helper static method to set a property
 from a particular project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setPropertyHook(java.lang.String,java.lang.String,java.lang.Object,boolean,boolean,boolean)" class="member-name-link">setPropertyHook</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;inherited,
 boolean&nbsp;user,
 boolean&nbsp;isNew)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">PropertyHelper chaining is deprecated.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserProperty(java.lang.String,java.lang.Object)" class="member-name-link">setUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a user property, which cannot be overwritten by
 set/unset property calls.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setUserProperty(java.lang.String,java.lang.String,java.lang.Object)" class="member-name-link">setUserProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#testIfCondition(java.lang.Object)" class="member-name-link">testIfCondition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the value is null or an empty string, can be
 interpreted as a true value or cannot be interpreted as a false
 value and a property of the value's name exists.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#testUnlessCondition(java.lang.Object)" class="member-name-link">testUnlessCondition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the value is null or an empty string, can be
 interpreted as a false value or cannot be interpreted as a true
 value and a property of the value's name doesn't exist.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#toBoolean(java.lang.Object)" class="member-name-link">toBoolean</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">If the given object can be interpreted as a true/false value,
 turn it into a matching Boolean - otherwise return null.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>PropertyHelper</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">PropertyHelper</span>()</div>
<div class="block">Default constructor.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getProperty(org.apache.tools.ant.Project,java.lang.String)">
<h3>getProperty</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getProperty</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">A helper static method to get a property
 from a particular project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project in question.</dd>
<dd><code>name</code> - the property name</dd>
<dt>Returns:</dt>
<dd>the value of the property if present, null otherwise.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProperty(org.apache.tools.ant.Project,java.lang.String,java.lang.Object)">
<h3>setProperty</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProperty</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">A helper static method to set a property
 from a particular project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project in question.</dd>
<dd><code>name</code> - the property name</dd>
<dd><code>value</code> - the value to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNewProperty(org.apache.tools.ant.Project,java.lang.String,java.lang.Object)">
<h3>setNewProperty</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewProperty</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">A helper static method to set a new property
 from a particular project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project in question.</dd>
<dd><code>name</code> - the property name</dd>
<dd><code>value</code> - the value to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProject(org.apache.tools.ant.Project)">
<h3>setProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProject</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Set the project for which this helper is performing property resolution.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">Get this PropertyHelper's Project.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Project</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNext(org.apache.tools.ant.PropertyHelper)">
<h3>setNext</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNext</span><wbr><span class="parameters">(<a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;next)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the delegate mechanism instead</div>
</div>
<div class="block">Prior to Ant 1.8.0 there have been 2 ways to hook into property handling:

  - you can replace the main PropertyHelper. The replacement is required
 to support the same semantics (of course :-)

  - you can chain a property helper capable of storing some properties.
  Again, you are required to respect the immutability semantics (at
  least for non-dynamic properties)

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>next</code> - the next property helper in the chain.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNext()">
<h3>getNext</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></span>&nbsp;<span class="element-name">getNext</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the delegate mechanism instead</div>
</div>
<div class="block">Get the next property helper in the chain.

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself except the <a href="#setPropertyHook(java.lang.String,java.lang.String,java.lang.Object,boolean,boolean,boolean)"><code>setPropertyHook</code></a> and <a href="#getPropertyHook(java.lang.String,java.lang.String,boolean)"><code>getPropertyHook</code></a>
 methods in this class.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the next property helper.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPropertyHelper(org.apache.tools.ant.Project)">
<h3>getPropertyHelper</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a></span>&nbsp;<span class="element-name">getPropertyHelper</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Factory method to create a property processor.
 Users can provide their own or replace it using "ant.PropertyHelper"
 reference. User tasks can also add themselves to the chain, and provide
 dynamic properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project for which the property helper is required.</dd>
<dt>Returns:</dt>
<dd>the project's property helper.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExpanders()">
<h3>getExpanders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="property/PropertyExpander.html" title="interface in org.apache.tools.ant.property">PropertyExpander</a>&gt;</span>&nbsp;<span class="element-name">getExpanders</span>()</div>
<div class="block">Get the <a href="property/PropertyExpander.html" title="interface in org.apache.tools.ant.property"><code>expanders</code></a>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the expanders.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPropertyHook(java.lang.String,java.lang.String,java.lang.Object,boolean,boolean,boolean)">
<h3>setPropertyHook</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setPropertyHook</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;inherited,
 boolean&nbsp;user,
 boolean&nbsp;isNew)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">PropertyHelper chaining is deprecated.</div>
</div>
<div class="block">Sets a property. Any existing property of the same name
 is overwritten, unless it is a user property.

 If all helpers return false, the property will be saved in
 the default properties table by setProperty.

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace that the property is in (currently
             not used.</dd>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dd><code>inherited</code> - True if this property is inherited (an [sub]ant[call] property).</dd>
<dd><code>user</code> - True if this property is a user property.</dd>
<dd><code>isNew</code> - True is this is a new property.</dd>
<dt>Returns:</dt>
<dd>true if this helper has stored the property, false if it
    couldn't. Each helper should delegate to the next one (unless it
    has a good reason not to).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPropertyHook(java.lang.String,java.lang.String,boolean)">
<h3>getPropertyHook</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getPropertyHook</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;user)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">PropertyHelper chaining is deprecated.</div>
</div>
<div class="block">Get a property. If all hooks return null, the default
 tables will be used.

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - namespace of the sought property.</dd>
<dd><code>name</code> - name of the sought property.</dd>
<dd><code>user</code> - True if this is a user property.</dd>
<dt>Returns:</dt>
<dd>The property, if returned by a hook, or null if none.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)">
<h3>parsePropertyString</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parsePropertyString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fragments,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;propertyRefs)</span>
                         throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use the other mechanisms of this class instead</div>
</div>
<div class="block">Parses a string containing <code>${xxx}</code> style property
 references into two lists. The first list is a collection
 of text fragments, while the other is a set of string property names.
 <code>null</code> entries in the first list indicate a property
 reference from the second list.

 <p>Delegates to <code>parsePropertyStringDefault</code>.</p>

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself except {ProjectHelper#parsePropertyString
 ProjectHelper.parsePropertyString}.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - Text to parse. Must not be <code>null</code>.</dd>
<dd><code>fragments</code> - List to add text fragments to.
                  Must not be <code>null</code>.</dd>
<dd><code>propertyRefs</code> - List to add property names to.
                     Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="replaceProperties(java.lang.String,java.lang.String,java.util.Hashtable)">
<h3>replaceProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">replaceProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;keys)</span>
                         throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.

 <p>Delegates to the one-arg version, completely ignoring the ns
 and keys parameters.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property.</dd>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.</dd>
<dd><code>keys</code> - Mapping (String to Object) of property names to their
              values. If <code>null</code>, only project properties will
              be used.</dd>
<dt>Returns:</dt>
<dd>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="replaceProperties(java.lang.String)">
<h3>replaceProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">replaceProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                         throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.</dd>
<dt>Returns:</dt>
<dd>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseProperties(java.lang.String)">
<h3>parseProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">parseProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                       throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Decode properties from a String representation.  If the entire
 contents of the String resolve to a single property, that value
 is returned.  Otherwise a String is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.</dd>
<dt>Returns:</dt>
<dd>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="containsProperties(java.lang.String)">
<h3>containsProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">containsProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Learn whether a String contains replaceable properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the String to check.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if <code>value</code> contains property notation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProperty(java.lang.String,java.lang.String,java.lang.Object,boolean)">
<h3>setProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;verbose)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Default implementation of setProperty. Will be called from Project.
 This is the original 1.5 implementation, with calls to the hook
 added.

 <p>Delegates to the three-arg version, completely ignoring the
 ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of the property.</dd>
<dd><code>value</code> - The value to set the property to.</dd>
<dd><code>verbose</code> - If this is true output extra log messages.</dd>
<dt>Returns:</dt>
<dd>true if the property is set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProperty(java.lang.String,java.lang.Object,boolean)">
<h3>setProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 boolean&nbsp;verbose)</span></div>
<div class="block">Default implementation of setProperty. Will be called from Project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the property.</dd>
<dd><code>value</code> - The value to set the property to.</dd>
<dd><code>verbose</code> - If this is true output extra log messages.</dd>
<dt>Returns:</dt>
<dd>true if the property is set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNewProperty(java.lang.String,java.lang.String,java.lang.Object)">
<h3>setNewProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Sets a property if no value currently exists. If the property
 exists already, a message is logged and the method returns with
 no other effect.

 <p>Delegates to the two-arg version, completely ignoring the
 ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNewProperty(java.lang.String,java.lang.Object)">
<h3>setNewProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Sets a property if no value currently exists. If the property
 exists already, a message is logged and the method returns with
 no other effect.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUserProperty(java.lang.String,java.lang.String,java.lang.Object)">
<h3>setUserProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Sets a user property, which cannot be overwritten by
 set/unset property calls. Any previous value is overwritten.

 <p>Delegates to the two-arg version, completely ignoring the
 ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUserProperty(java.lang.String,java.lang.Object)">
<h3>setUserProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Sets a user property, which cannot be overwritten by
 set/unset property calls. Any previous value is overwritten.

 <p>Does <code>not</code> consult any delegates.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInheritedProperty(java.lang.String,java.lang.String,java.lang.Object)">
<h3>setInheritedProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInheritedProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Sets an inherited user property, which cannot be overwritten by set/unset
 property calls. Any previous value is overwritten. Also marks
 these properties as properties that have not come from the
 command line.

 <p>Delegates to the two-arg version, completely ignoring the
 ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInheritedProperty(java.lang.String,java.lang.Object)">
<h3>setInheritedProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInheritedProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Sets an inherited user property, which cannot be overwritten by set/unset
 property calls. Any previous value is overwritten. Also marks
 these properties as properties that have not come from the
 command line.

 <p>Does <code>not</code> consult any delegates.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of property to set.
             Must not be <code>null</code>.</dd>
<dd><code>value</code> - The new value of the property.
              Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProperty(java.lang.String,java.lang.String)">
<h3>getProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Returns the value of a property, if it is set.  You can override
 this method in order to plug your own storage.

 <p>Delegates to the one-arg version ignoring the ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProperty(java.lang.String)">
<h3>getProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns the value of a property, if it is set.

 <p>This is the method that is invoked by {Project#getProperty
 Project.getProperty}.</p>

 <p>You can override this method in order to plug your own
 storage but the recommended approach is to add your own
 implementation of <a href="PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant"><code>PropertyEvaluator</code></a>
 instead.</p></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="property/GetProperty.html#getProperty(java.lang.String)">getProperty</a></code>&nbsp;in interface&nbsp;<code><a href="property/GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPropertyNames()">
<h3>getPropertyNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getPropertyNames</span>()</div>
<div class="block">Returns the names of all known properties.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the names of all known properties.</dd>
<dt>Since:</dt>
<dd>1.10.9</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUserProperty(java.lang.String,java.lang.String)">
<h3>getUserProperty</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">namespaces are unnecessary.</div>
</div>
<div class="block">Returns the value of a user property, if it is set.

 <p>Delegates to the one-arg version ignoring the ns parameter.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - The namespace for the property (currently not used).</dd>
<dd><code>name</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUserProperty(java.lang.String)">
<h3>getUserProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getUserProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns the value of a user property, if it is set.

 <p>Does <code>not</code> consult any delegates.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProperties()">
<h3>getProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getProperties</span>()</div>
<div class="block">Returns a copy of the properties table.

 <p>Does not contain properties held by implementations of
 delegates (like local properties).</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing all properties (including user properties).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUserProperties()">
<h3>getUserProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getUserProperties</span>()</div>
<div class="block">Returns a copy of the user property hashtable

 <p>Does not contain properties held by implementations of
 delegates (like local properties).</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing just the user properties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInheritedProperties()">
<h3>getInheritedProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getInheritedProperties</span>()</div>
<div class="block">Returns a copy of the inherited property hashtable

 <p>Does not contain properties held by implementations of
 delegates (like local properties).</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a hashtable containing just the inherited properties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInternalProperties()">
<h3>getInternalProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getInternalProperties</span>()</div>
<div class="block">special back door for subclasses, internal access to the hashtables</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the live hashtable of all properties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInternalUserProperties()">
<h3>getInternalUserProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getInternalUserProperties</span>()</div>
<div class="block">special back door for subclasses, internal access to the hashtables</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the live hashtable of user properties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInternalInheritedProperties()">
<h3>getInternalInheritedProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getInternalInheritedProperties</span>()</div>
<div class="block">special back door for subclasses, internal access to the hashtables</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the live hashtable inherited properties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyInheritedProperties(org.apache.tools.ant.Project)">
<h3>copyInheritedProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyInheritedProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</span></div>
<div class="block">Copies all user properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.

 <p>To copy all "user" properties, you will also have to call
 <a href="#copyUserProperties(org.apache.tools.ant.Project)"><code>copyUserProperties</code></a>.</p>

 <p>Does not copy properties held by implementations of
 delegates (like local properties).</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the project to copy the properties to.  Must not be null.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyUserProperties(org.apache.tools.ant.Project)">
<h3>copyUserProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyUserProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;other)</span></div>
<div class="block">Copies all user properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.

 <p>To copy all "user" properties, you will also have to call
 <a href="#copyInheritedProperties(org.apache.tools.ant.Project)"><code>copyInheritedProperties</code></a>.</p>

 <p>Does not copy properties held by implementations of
 delegates (like local properties).</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the project to copy the properties to.  Must not be null.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.PropertyHelper.Delegate)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&nbsp;delegate)</span></div>
<div class="block">Add the specified delegate object to this PropertyHelper.
 Delegates are processed in LIFO order.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delegate</code> - the delegate to add.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDelegates(java.lang.Class)">
<h3>getDelegates</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="type-parameters">&lt;D extends <a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&gt;</span>
<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;D&gt;</span>&nbsp;<span class="element-name">getDelegates</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;D&gt;&nbsp;type)</span></div>
<div class="block">Get the Collection of delegates of the specified type.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>D</code> - desired type.</dd>
<dt>Parameters:</dt>
<dd><code>type</code> - delegate type.</dd>
<dt>Returns:</dt>
<dd>Collection.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDelegateInterfaces(org.apache.tools.ant.PropertyHelper.Delegate)">
<h3>getDelegateInterfaces</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;? extends <a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&gt;&gt;</span>&nbsp;<span class="element-name">getDelegateInterfaces</span><wbr><span class="parameters">(<a href="PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a>&nbsp;d)</span></div>
<div class="block">Get all Delegate interfaces (excluding Delegate itself) from the specified Delegate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the Delegate to inspect.</dd>
<dt>Returns:</dt>
<dd>Set&lt;Class&gt;</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toBoolean(java.lang.Object)">
<h3>toBoolean</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">toBoolean</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">If the given object can be interpreted as a true/false value,
 turn it into a matching Boolean - otherwise return null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - Object</dd>
<dt>Returns:</dt>
<dd>Boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="testIfCondition(java.lang.Object)">
<h3>testIfCondition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">testIfCondition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Returns true if the value is null or an empty string, can be
 interpreted as a true value or cannot be interpreted as a false
 value and a property of the value's name exists.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - Object</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="testUnlessCondition(java.lang.Object)">
<h3>testUnlessCondition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">testUnlessCondition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Returns true if the value is null or an empty string, can be
 interpreted as a false value or cannot be interpreted as a true
 value and a property of the value's name doesn't exist.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - Object</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
