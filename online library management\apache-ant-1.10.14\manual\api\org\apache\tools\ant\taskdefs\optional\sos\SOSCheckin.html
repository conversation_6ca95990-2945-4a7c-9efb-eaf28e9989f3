<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title><PERSON><PERSON><PERSON><PERSON>ckin (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.sos, class: SOSCheckin">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.sos</a></div>
<h1 title="Class SOSCheckin" class="title">Class SOSCheckin</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">org.apache.tools.ant.taskdefs.optional.sos.SOS</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.sos.SOSCheckin</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SOSCheckin</span>
<span class="extends-implements">extends <a href="SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></span></div>
<div class="block">Commits and unlocks files in Visual SourceSafe via a SourceOffSite server.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.sos.SOS">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.sos.<a href="SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></h3>
<code><a href="SOS.html#commandLine">commandLine</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.sos.SOSCmd">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.sos.<a href="SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</a></h3>
<code><a href="SOSCmd.html#COMMAND_CHECKIN_FILE">COMMAND_CHECKIN_FILE</a>, <a href="SOSCmd.html#COMMAND_CHECKIN_PROJECT">COMMAND_CHECKIN_PROJECT</a>, <a href="SOSCmd.html#COMMAND_CHECKOUT_FILE">COMMAND_CHECKOUT_FILE</a>, <a href="SOSCmd.html#COMMAND_CHECKOUT_PROJECT">COMMAND_CHECKOUT_PROJECT</a>, <a href="SOSCmd.html#COMMAND_GET_FILE">COMMAND_GET_FILE</a>, <a href="SOSCmd.html#COMMAND_GET_PROJECT">COMMAND_GET_PROJECT</a>, <a href="SOSCmd.html#COMMAND_HISTORY">COMMAND_HISTORY</a>, <a href="SOSCmd.html#COMMAND_LABEL">COMMAND_LABEL</a>, <a href="SOSCmd.html#COMMAND_SOS_EXE">COMMAND_SOS_EXE</a>, <a href="SOSCmd.html#FLAG_COMMAND">FLAG_COMMAND</a>, <a href="SOSCmd.html#FLAG_COMMENT">FLAG_COMMENT</a>, <a href="SOSCmd.html#FLAG_FILE">FLAG_FILE</a>, <a href="SOSCmd.html#FLAG_LABEL">FLAG_LABEL</a>, <a href="SOSCmd.html#FLAG_NO_CACHE">FLAG_NO_CACHE</a>, <a href="SOSCmd.html#FLAG_NO_COMPRESSION">FLAG_NO_COMPRESSION</a>, <a href="SOSCmd.html#FLAG_PASSWORD">FLAG_PASSWORD</a>, <a href="SOSCmd.html#FLAG_PROJECT">FLAG_PROJECT</a>, <a href="SOSCmd.html#FLAG_RECURSION">FLAG_RECURSION</a>, <a href="SOSCmd.html#FLAG_SOS_HOME">FLAG_SOS_HOME</a>, <a href="SOSCmd.html#FLAG_SOS_SERVER">FLAG_SOS_SERVER</a>, <a href="SOSCmd.html#FLAG_USERNAME">FLAG_USERNAME</a>, <a href="SOSCmd.html#FLAG_VERBOSE">FLAG_VERBOSE</a>, <a href="SOSCmd.html#FLAG_VERSION">FLAG_VERSION</a>, <a href="SOSCmd.html#FLAG_VSS_SERVER">FLAG_VSS_SERVER</a>, <a href="SOSCmd.html#FLAG_WORKING_DIR">FLAG_WORKING_DIR</a>, <a href="SOSCmd.html#PROJECT_PREFIX">PROJECT_PREFIX</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SOSCheckin</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildCmdLine()" class="member-name-link">buildCmdLine</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Build the command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComment(java.lang.String)" class="member-name-link">setComment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The comment to apply to all files being labelled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFile(java.lang.String)" class="member-name-link">setFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The filename to act upon.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRecursive(boolean)" class="member-name-link">setRecursive</a><wbr>(boolean&nbsp;recursive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to recursively apply the action.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.sos.SOS">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.sos.<a href="SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></h3>
<code><a href="SOS.html#execute()">execute</a>, <a href="SOS.html#getComment()">getComment</a>, <a href="SOS.html#getFilename()">getFilename</a>, <a href="SOS.html#getLabel()">getLabel</a>, <a href="SOS.html#getLocalPath()">getLocalPath</a>, <a href="SOS.html#getNoCache()">getNoCache</a>, <a href="SOS.html#getNoCompress()">getNoCompress</a>, <a href="SOS.html#getOptionalAttributes()">getOptionalAttributes</a>, <a href="SOS.html#getPassword()">getPassword</a>, <a href="SOS.html#getProjectPath()">getProjectPath</a>, <a href="SOS.html#getRecursive()">getRecursive</a>, <a href="SOS.html#getRequiredAttributes()">getRequiredAttributes</a>, <a href="SOS.html#getSosCommand()">getSosCommand</a>, <a href="SOS.html#getSosHome()">getSosHome</a>, <a href="SOS.html#getSosServerPath()">getSosServerPath</a>, <a href="SOS.html#getUsername()">getUsername</a>, <a href="SOS.html#getVerbose()">getVerbose</a>, <a href="SOS.html#getVersion()">getVersion</a>, <a href="SOS.html#getVssServerPath()">getVssServerPath</a>, <a href="SOS.html#run(org.apache.tools.ant.types.Commandline)">run</a>, <a href="SOS.html#setInternalComment(java.lang.String)">setInternalComment</a>, <a href="SOS.html#setInternalFilename(java.lang.String)">setInternalFilename</a>, <a href="SOS.html#setInternalLabel(java.lang.String)">setInternalLabel</a>, <a href="SOS.html#setInternalRecursive(boolean)">setInternalRecursive</a>, <a href="SOS.html#setInternalVersion(java.lang.String)">setInternalVersion</a>, <a href="SOS.html#setLocalPath(org.apache.tools.ant.types.Path)">setLocalPath</a>, <a href="SOS.html#setNoCache(boolean)">setNoCache</a>, <a href="SOS.html#setNoCompress(boolean)">setNoCompress</a>, <a href="SOS.html#setPassword(java.lang.String)">setPassword</a>, <a href="SOS.html#setProjectPath(java.lang.String)">setProjectPath</a>, <a href="SOS.html#setSosCmd(java.lang.String)">setSosCmd</a>, <a href="SOS.html#setSosHome(java.lang.String)">setSosHome</a>, <a href="SOS.html#setSosServerPath(java.lang.String)">setSosServerPath</a>, <a href="SOS.html#setUsername(java.lang.String)">setUsername</a>, <a href="SOS.html#setVerbose(boolean)">setVerbose</a>, <a href="SOS.html#setVssServerPath(java.lang.String)">setVssServerPath</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SOSCheckin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SOSCheckin</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setFile(java.lang.String)">
<h3>setFile</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">The filename to act upon.
 If no file is specified then the task
 acts upon the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - The new file value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRecursive(boolean)">
<h3>setRecursive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRecursive</span><wbr><span class="parameters">(boolean&nbsp;recursive)</span></div>
<div class="block">Flag to recursively apply the action. Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>recursive</code> - True for recursive operation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setComment(java.lang.String)">
<h3>setComment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</span></div>
<div class="block">The comment to apply to all files being labelled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comment</code> - The new comment value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildCmdLine()">
<h3>buildCmdLine</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">buildCmdLine</span>()</div>
<div class="block">Build the command line.
 <p>
 CheckInFile required parameters: -server -name -password -database -project
  -file<br>
 CheckInFile optional parameters: -workdir -log -verbose -nocache -nocompression
  -soshome<br>
 CheckInProject required parameters: -server -name -password -database
  -project<br>
 CheckInProject optional parameters: workdir -recursive -log -verbose
  -nocache -nocompression -soshome
 </p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Commandline the generated command to be executed</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
