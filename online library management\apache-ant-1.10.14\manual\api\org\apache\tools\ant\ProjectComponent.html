<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectComponent (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: ProjectComponent">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class ProjectComponent" class="title">Class ProjectComponent</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.ProjectComponent</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="types/optional/AbstractScriptComponent.html" title="class in org.apache.tools.ant.types.optional">AbstractScriptComponent</a></code>, <code><a href="attribute/BaseIfAttribute.html" title="class in org.apache.tools.ant.attribute">BaseIfAttribute</a></code>, <code><a href="types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></code>, <code><a href="taskdefs/Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</a></code>, <code><a href="taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></code>, <code><a href="types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code>, <code><a href="taskdefs/optional/junit/FailureRecorder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a></code>, <code><a href="util/FileTokenizer.html" title="class in org.apache.tools.ant.util">FileTokenizer</a></code>, <code><a href="taskdefs/condition/HasMethod.html" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a></code>, <code><a href="taskdefs/condition/Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a></code>, <code><a href="taskdefs/condition/IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a></code>, <code><a href="taskdefs/condition/IsLastModified.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a></code>, <code><a href="taskdefs/condition/IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a></code>, <code><a href="taskdefs/condition/IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a></code>, <code><a href="taskdefs/condition/IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a></code>, <code><a href="taskdefs/condition/IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a></code>, <code><a href="taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</a></code>, <code><a href="util/LineTokenizer.html" title="class in org.apache.tools.ant.util">LineTokenizer</a></code>, <code><a href="taskdefs/condition/Matches.html" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a></code>, <code><a href="taskdefs/email/Message.html" title="class in org.apache.tools.ant.taskdefs.email">Message</a></code>, <code><a href="taskdefs/condition/ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a></code>, <code><a href="types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a></code>, <code><a href="types/spi/Provider.html" title="class in org.apache.tools.ant.types.spi">Provider</a></code>, <code><a href="taskdefs/condition/ResourceExists.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a></code>, <code><a href="types/spi/Service.html" title="class in org.apache.tools.ant.types.spi">Service</a></code>, <code><a href="taskdefs/condition/Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a></code>, <code><a href="util/StringTokenizer.html" title="class in org.apache.tools.ant.util">StringTokenizer</a></code>, <code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code>, <code><a href="filters/TokenFilter.ChainableReaderFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a></code>, <code><a href="filters/TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a></code>, <code><a href="filters/TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a></code>, <code><a href="taskdefs/condition/TypeFound.html" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a></code>, <code><a href="util/XMLFragment.html" title="class in org.apache.tools.ant.util">XMLFragment</a></code>, <code><a href="taskdefs/XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Attribute</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ProjectComponent</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></span></div>
<div class="block">Base class for components of a project, including tasks and data types.
 Provides common facilities.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#description" class="member-name-link">description</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="Location.html" title="class in org.apache.tools.ant">Location</a></code></div>
<div class="col-second odd-row-color"><code><a href="#location" class="member-name-link">location</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color"><code><a href="#project" class="member-name-link">project</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ProjectComponent</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Sole constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDescription()" class="member-name-link">getDescription</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the description of the current action.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Location.html" title="class in org.apache.tools.ant">Location</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocation()" class="member-name-link">getLocation</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the file/location where this task was defined.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the project to which this component belongs.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the default (INFO) priority.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the given priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDescription(java.lang.String)" class="member-name-link">setDescription</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;desc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a description of the current action.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLocation(org.apache.tools.ant.Location)" class="member-name-link">setLocation</a><wbr>(<a href="Location.html" title="class in org.apache.tools.ant">Location</a>&nbsp;location)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the file/location where this task was defined.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProject(org.apache.tools.ant.Project)" class="member-name-link">setProject</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the project object of this component.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="project">
<h3>project</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">project</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be directly accessing this variable directly.
             You should access project object via the getProject()
             or setProject() accessor/mutators.</div>
</div>
<div class="block">Project object of this component.</div>
</section>
</li>
<li>
<section class="detail" id="location">
<h3>location</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Location.html" title="class in org.apache.tools.ant">Location</a></span>&nbsp;<span class="element-name">location</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="#getLocation()"><code>getLocation()</code></a> method.</div>
</div>
<div class="block">Location within the build file of this task definition.</div>
</section>
</li>
<li>
<section class="detail" id="description">
<h3>description</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">description</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.</div>
</div>
<div class="block">Description of this component, if any.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ProjectComponent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ProjectComponent</span>()</div>
<div class="block">Sole constructor.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setProject(org.apache.tools.ant.Project)">
<h3>setProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProject</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Sets the project object of this component. This method is used by
 Project when a component is added to it so that the component has
 access to the functions of the project. It should not be used
 for any other purpose.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - Project in whose scope this component belongs.
                Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">Returns the project to which this component belongs.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the components's project.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocation()">
<h3>getLocation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Location.html" title="class in org.apache.tools.ant">Location</a></span>&nbsp;<span class="element-name">getLocation</span>()</div>
<div class="block">Returns the file/location where this task was defined.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the file/location where this task was defined.
         Should not return <code>null</code>. Location.UNKNOWN_LOCATION
         is used for unknown locations.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Location.html#UNKNOWN_LOCATION"><code>Location.UNKNOWN_LOCATION</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLocation(org.apache.tools.ant.Location)">
<h3>setLocation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLocation</span><wbr><span class="parameters">(<a href="Location.html" title="class in org.apache.tools.ant">Location</a>&nbsp;location)</span></div>
<div class="block">Sets the file/location where this task was defined.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>location</code> - The file/location where this task was defined.
                 Should not be <code>null</code>--use
                 Location.UNKNOWN_LOCATION if the location isn't known.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Location.html#UNKNOWN_LOCATION"><code>Location.UNKNOWN_LOCATION</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDescription(java.lang.String)">
<h3>setDescription</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDescription</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;desc)</span></div>
<div class="block">Sets a description of the current action. This may be used for logging
 purposes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>desc</code> - Description of the current action.
             May be <code>null</code>, indicating that no description is
             available.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDescription()">
<h3>getDescription</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDescription</span>()</div>
<div class="block">Returns the description of the current action.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the description of the current action, or <code>null</code> if
         no description is available.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</span></div>
<div class="block">Logs a message with the default (INFO) priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msg</code> - The message to be logged. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;msgLevel)</span></div>
<div class="block">Logs a message with the given priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msg</code> - The message to be logged. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - the message priority at which this message is
                 to be logged.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()
             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/CloneNotSupportedException.html" title="class or interface in java.lang" class="external-link">CloneNotSupportedException</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>a shallow copy of this projectcomponent.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/CloneNotSupportedException.html" title="class or interface in java.lang" class="external-link">CloneNotSupportedException</a></code> - does not happen,
                                    but is declared to allow subclasses to do so.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
