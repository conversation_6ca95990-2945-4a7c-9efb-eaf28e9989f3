<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>SignJar (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: SignJar">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class SignJar" class="title">Class SignJar</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.AbstractJarSignerTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.SignJar</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SignJar</span>
<span class="extends-implements">extends <a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></span></div>
<div class="block">Signs JAR or ZIP files with the javasign command line tool. The tool detailed
 dependency checking: files are only signed if they are not signed. The
 <code>signjar</code> attribute can point to the file to generate; if this file
 exists then its modification date is used as a cue as to whether to resign
 any JAR file.

 Timestamp signature support is based on Java 8</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://docs.oracle.com/javase/8/docs/technotes/guides/security/time-of-signing.html">
 documentation</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#destDir" class="member-name-link">destDir</a></code></div>
<div class="col-last even-row-color">
<div class="block">the output directory when using paths.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_BAD_MAP" class="member-name-link">ERROR_BAD_MAP</a></code></div>
<div class="col-last odd-row-color">
<div class="block">error string for unit test verification: "Cannot map source file to anything sensible: "</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_MAPPER_WITHOUT_DEST" class="member-name-link">ERROR_MAPPER_WITHOUT_DEST</a></code></div>
<div class="col-last even-row-color">
<div class="block">error string for unit test verification: "The destDir attribute is required if a mapper is set"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NO_ALIAS" class="member-name-link">ERROR_NO_ALIAS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">error string for unit test verification: "alias attribute must be set"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_NO_STOREPASS" class="member-name-link">ERROR_NO_STOREPASS</a></code></div>
<div class="col-last even-row-color">
<div class="block">error string for unit test verification: "storepass attribute must be set"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_SIGNEDJAR_AND_PATHS" class="member-name-link">ERROR_SIGNEDJAR_AND_PATHS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">error string for unit test verification "You cannot specify the signed JAR when using paths or filesets"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_TODIR_AND_SIGNEDJAR" class="member-name-link">ERROR_TODIR_AND_SIGNEDJAR</a></code></div>
<div class="col-last even-row-color">
<div class="block">error string for unit test verification: "\'destdir\' and \'signedjar\' cannot both be set"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_TOO_MANY_MAPPERS" class="member-name-link">ERROR_TOO_MANY_MAPPERS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">error string for unit test verification: "Too many mappers"</div>
</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#internalsf" class="member-name-link">internalsf</a></code></div>
<div class="col-last even-row-color">
<div class="block">flag for internal sf signing</div>
</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#lazy" class="member-name-link">lazy</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Whether to assume a jar which has an appropriate .SF file in is already
 signed.</div>
</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#sectionsonly" class="member-name-link">sectionsonly</a></code></div>
<div class="col-last even-row-color">
<div class="block">sign sections only?</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#sigfile" class="member-name-link">sigfile</a></code></div>
<div class="col-last odd-row-color">
<div class="block">name to a signature file</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#signedjar" class="member-name-link">signedjar</a></code></div>
<div class="col-last even-row-color">
<div class="block">name of a single jar</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#tsacert" class="member-name-link">tsacert</a></code></div>
<div class="col-last odd-row-color">
<div class="block">alias for the TSA in the keystore</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#tsaproxyhost" class="member-name-link">tsaproxyhost</a></code></div>
<div class="col-last even-row-color">
<div class="block">Proxy host to be used when connecting to TSA server</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#tsaproxyport" class="member-name-link">tsaproxyport</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Proxy port to be used when connecting to TSA server</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#tsaurl" class="member-name-link">tsaurl</a></code></div>
<div class="col-last even-row-color">
<div class="block">URL for a tsa; null implies no tsa support</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractJarSignerTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></h3>
<code><a href="AbstractJarSignerTask.html#alias">alias</a>, <a href="AbstractJarSignerTask.html#ERROR_NO_SOURCE">ERROR_NO_SOURCE</a>, <a href="AbstractJarSignerTask.html#filesets">filesets</a>, <a href="AbstractJarSignerTask.html#jar">jar</a>, <a href="AbstractJarSignerTask.html#JARSIGNER_COMMAND">JARSIGNER_COMMAND</a>, <a href="AbstractJarSignerTask.html#keypass">keypass</a>, <a href="AbstractJarSignerTask.html#keystore">keystore</a>, <a href="AbstractJarSignerTask.html#maxMemory">maxMemory</a>, <a href="AbstractJarSignerTask.html#storepass">storepass</a>, <a href="AbstractJarSignerTask.html#storetype">storetype</a>, <a href="AbstractJarSignerTask.html#strict">strict</a>, <a href="AbstractJarSignerTask.html#verbose">verbose</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SignJar</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.util.FileNameMapper)" class="member-name-link">add</a><wbr>(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;newMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a mapper to determine file naming policy.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sign the jar(s)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDigestAlg()" class="member-name-link">getDigestAlg</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Digest Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMapper()" class="member-name-link">getMapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the active mapper; may be null</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSigAlg()" class="member-name-link">getSigAlg</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signature Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTsacert()" class="member-name-link">getTsacert</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the -tsacert option</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTSADigestAlg()" class="member-name-link">getTSADigestAlg</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">TSA Digest Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTsaproxyhost()" class="member-name-link">getTsaproxyhost</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the proxy host to be used when connecting to the TSA url</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTsaproxyport()" class="member-name-link">getTsaproxyport</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the proxy host to be used when connecting to the TSA url</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTsaurl()" class="member-name-link">getTsaurl</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the -tsaurl url</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isForce()" class="member-name-link">isForce</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Should the task force signing of a jar even it is already
 signed?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSigned(java.io.File)" class="member-name-link">isSigned</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">test for a file being signed, by looking for a signature in the META-INF
 directory with our alias/sigfile.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isUpToDate(java.io.File,java.io.File)" class="member-name-link">isUpToDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;signedjarFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Compare a jar file with its corresponding signed jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestDir(java.io.File)" class="member-name-link">setDestDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Optionally sets the output directory to be used.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDigestAlg(java.lang.String)" class="member-name-link">setDigestAlg</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;digestAlg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Digest Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForce(boolean)" class="member-name-link">setForce</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to force signing of a jar even it is already signed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInternalsf(boolean)" class="member-name-link">setInternalsf</a><wbr>(boolean&nbsp;internalsf)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to include the .SF file inside the signature; optional; default
 false</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLazy(boolean)" class="member-name-link">setLazy</a><wbr>(boolean&nbsp;lazy)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">flag to control whether the presence of a signature file means a JAR is
 signed; optional, default false</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreserveLastModified(boolean)" class="member-name-link">setPreserveLastModified</a><wbr>(boolean&nbsp;preserveLastModified)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">true to indicate that the signed jar modification date remains the same
 as the original.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSectionsonly(boolean)" class="member-name-link">setSectionsonly</a><wbr>(boolean&nbsp;sectionsonly)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">flag to compute hash of entire manifest; optional, default false</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSigAlg(java.lang.String)" class="member-name-link">setSigAlg</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sigAlg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signature Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSigfile(java.lang.String)" class="member-name-link">setSigfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sigfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">name of .SF/.DSA file; optional</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSignedjar(java.io.File)" class="member-name-link">setSignedjar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;signedjar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">name of signed JAR file; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTsacert(java.lang.String)" class="member-name-link">setTsacert</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsacert)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the alias in the keystore of the TSA to use;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTSADigestAlg(java.lang.String)" class="member-name-link">setTSADigestAlg</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;digestAlg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">TSA Digest Algorithm; optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTsaproxyhost(java.lang.String)" class="member-name-link">setTsaproxyhost</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaproxyhost)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTsaproxyport(java.lang.String)" class="member-name-link">setTsaproxyport</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaproxyport)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTsaurl(java.lang.String)" class="member-name-link">setTsaurl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaurl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractJarSignerTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></h3>
<code><a href="AbstractJarSignerTask.html#addArg(org.apache.tools.ant.types.Commandline.Argument)">addArg</a>, <a href="AbstractJarSignerTask.html#addArgument(org.apache.tools.ant.taskdefs.ExecTask,org.apache.tools.ant.types.Commandline.Argument)">addArgument</a>, <a href="AbstractJarSignerTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="AbstractJarSignerTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</a>, <a href="AbstractJarSignerTask.html#addValue(org.apache.tools.ant.taskdefs.ExecTask,java.lang.String)">addValue</a>, <a href="AbstractJarSignerTask.html#beginExecution()">beginExecution</a>, <a href="AbstractJarSignerTask.html#bindToKeystore(org.apache.tools.ant.taskdefs.ExecTask)">bindToKeystore</a>, <a href="AbstractJarSignerTask.html#createJarSigner()">createJarSigner</a>, <a href="AbstractJarSignerTask.html#createPath()">createPath</a>, <a href="AbstractJarSignerTask.html#createUnifiedSourcePath()">createUnifiedSourcePath</a>, <a href="AbstractJarSignerTask.html#createUnifiedSources()">createUnifiedSources</a>, <a href="AbstractJarSignerTask.html#declareSysProperty(org.apache.tools.ant.taskdefs.ExecTask,org.apache.tools.ant.types.Environment.Variable)">declareSysProperty</a>, <a href="AbstractJarSignerTask.html#endExecution()">endExecution</a>, <a href="AbstractJarSignerTask.html#getRedirector()">getRedirector</a>, <a href="AbstractJarSignerTask.html#hasResources()">hasResources</a>, <a href="AbstractJarSignerTask.html#setAlias(java.lang.String)">setAlias</a>, <a href="AbstractJarSignerTask.html#setCommonOptions(org.apache.tools.ant.taskdefs.ExecTask)">setCommonOptions</a>, <a href="AbstractJarSignerTask.html#setExecutable(java.lang.String)">setExecutable</a>, <a href="AbstractJarSignerTask.html#setJar(java.io.File)">setJar</a>, <a href="AbstractJarSignerTask.html#setKeypass(java.lang.String)">setKeypass</a>, <a href="AbstractJarSignerTask.html#setKeystore(java.lang.String)">setKeystore</a>, <a href="AbstractJarSignerTask.html#setMaxmemory(java.lang.String)">setMaxmemory</a>, <a href="AbstractJarSignerTask.html#setProviderArg(java.lang.String)">setProviderArg</a>, <a href="AbstractJarSignerTask.html#setProviderClass(java.lang.String)">setProviderClass</a>, <a href="AbstractJarSignerTask.html#setProviderName(java.lang.String)">setProviderName</a>, <a href="AbstractJarSignerTask.html#setStorepass(java.lang.String)">setStorepass</a>, <a href="AbstractJarSignerTask.html#setStoretype(java.lang.String)">setStoretype</a>, <a href="AbstractJarSignerTask.html#setStrict(boolean)">setStrict</a>, <a href="AbstractJarSignerTask.html#setVerbose(boolean)">setVerbose</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ERROR_TODIR_AND_SIGNEDJAR">
<h3>ERROR_TODIR_AND_SIGNEDJAR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_TODIR_AND_SIGNEDJAR</span></div>
<div class="block">error string for unit test verification: "\'destdir\' and \'signedjar\' cannot both be set"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_TODIR_AND_SIGNEDJAR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_TOO_MANY_MAPPERS">
<h3>ERROR_TOO_MANY_MAPPERS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_TOO_MANY_MAPPERS</span></div>
<div class="block">error string for unit test verification: "Too many mappers"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_TOO_MANY_MAPPERS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_SIGNEDJAR_AND_PATHS">
<h3>ERROR_SIGNEDJAR_AND_PATHS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_SIGNEDJAR_AND_PATHS</span></div>
<div class="block">error string for unit test verification "You cannot specify the signed JAR when using paths or filesets"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_SIGNEDJAR_AND_PATHS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_BAD_MAP">
<h3>ERROR_BAD_MAP</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_BAD_MAP</span></div>
<div class="block">error string for unit test verification: "Cannot map source file to anything sensible: "</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_BAD_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_MAPPER_WITHOUT_DEST">
<h3>ERROR_MAPPER_WITHOUT_DEST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_MAPPER_WITHOUT_DEST</span></div>
<div class="block">error string for unit test verification: "The destDir attribute is required if a mapper is set"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_MAPPER_WITHOUT_DEST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_ALIAS">
<h3>ERROR_NO_ALIAS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_ALIAS</span></div>
<div class="block">error string for unit test verification: "alias attribute must be set"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_ALIAS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_STOREPASS">
<h3>ERROR_NO_STOREPASS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_STOREPASS</span></div>
<div class="block">error string for unit test verification: "storepass attribute must be set"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_STOREPASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sigfile">
<h3>sigfile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">sigfile</span></div>
<div class="block">name to a signature file</div>
</section>
</li>
<li>
<section class="detail" id="signedjar">
<h3>signedjar</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">signedjar</span></div>
<div class="block">name of a single jar</div>
</section>
</li>
<li>
<section class="detail" id="internalsf">
<h3>internalsf</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">internalsf</span></div>
<div class="block">flag for internal sf signing</div>
</section>
</li>
<li>
<section class="detail" id="sectionsonly">
<h3>sectionsonly</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">sectionsonly</span></div>
<div class="block">sign sections only?</div>
</section>
</li>
<li>
<section class="detail" id="lazy">
<h3>lazy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">lazy</span></div>
<div class="block">Whether to assume a jar which has an appropriate .SF file in is already
 signed.</div>
</section>
</li>
<li>
<section class="detail" id="destDir">
<h3>destDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">destDir</span></div>
<div class="block">the output directory when using paths.</div>
</section>
</li>
<li>
<section class="detail" id="tsaurl">
<h3>tsaurl</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">tsaurl</span></div>
<div class="block">URL for a tsa; null implies no tsa support</div>
</section>
</li>
<li>
<section class="detail" id="tsaproxyhost">
<h3>tsaproxyhost</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">tsaproxyhost</span></div>
<div class="block">Proxy host to be used when connecting to TSA server</div>
</section>
</li>
<li>
<section class="detail" id="tsaproxyport">
<h3>tsaproxyport</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">tsaproxyport</span></div>
<div class="block">Proxy port to be used when connecting to TSA server</div>
</section>
</li>
<li>
<section class="detail" id="tsacert">
<h3>tsacert</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">tsacert</span></div>
<div class="block">alias for the TSA in the keystore</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SignJar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SignJar</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSigfile(java.lang.String)">
<h3>setSigfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSigfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sigfile)</span></div>
<div class="block">name of .SF/.DSA file; optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sigfile</code> - the name of the .SF/.DSA file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSignedjar(java.io.File)">
<h3>setSignedjar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSignedjar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;signedjar)</span></div>
<div class="block">name of signed JAR file; optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>signedjar</code> - the name of the signed jar file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInternalsf(boolean)">
<h3>setInternalsf</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInternalsf</span><wbr><span class="parameters">(boolean&nbsp;internalsf)</span></div>
<div class="block">Flag to include the .SF file inside the signature; optional; default
 false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>internalsf</code> - if true include the .SF file inside the signature</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSectionsonly(boolean)">
<h3>setSectionsonly</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSectionsonly</span><wbr><span class="parameters">(boolean&nbsp;sectionsonly)</span></div>
<div class="block">flag to compute hash of entire manifest; optional, default false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sectionsonly</code> - flag to compute hash of entire manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLazy(boolean)">
<h3>setLazy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLazy</span><wbr><span class="parameters">(boolean&nbsp;lazy)</span></div>
<div class="block">flag to control whether the presence of a signature file means a JAR is
 signed; optional, default false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>lazy</code> - flag to control whether the presence of a signature</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestDir(java.io.File)">
<h3>setDestDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</span></div>
<div class="block">Optionally sets the output directory to be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destDir</code> - the directory in which to place signed jars</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.util.FileNameMapper)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;newMapper)</span></div>
<div class="block">add a mapper to determine file naming policy. Only used with toDir
 processing.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newMapper</code> - the mapper to add.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMapper()">
<h3>getMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></span>&nbsp;<span class="element-name">getMapper</span>()</div>
<div class="block">get the active mapper; may be null</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>mapper or null</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTsaurl()">
<h3>getTsaurl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTsaurl</span>()</div>
<div class="block">get the -tsaurl url</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>url or null</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTsaurl(java.lang.String)">
<h3>setTsaurl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTsaurl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaurl)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tsaurl</code> - the tsa url.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTsaproxyhost()">
<h3>getTsaproxyhost</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTsaproxyhost</span>()</div>
<div class="block">Get the proxy host to be used when connecting to the TSA url</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>url or null</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTsaproxyhost(java.lang.String)">
<h3>setTsaproxyhost</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTsaproxyhost</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaproxyhost)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tsaproxyhost</code> - the proxy host to be used when connecting to the TSA.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTsaproxyport()">
<h3>getTsaproxyport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTsaproxyport</span>()</div>
<div class="block">Get the proxy host to be used when connecting to the TSA url</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>url or null</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTsaproxyport(java.lang.String)">
<h3>setTsaproxyport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTsaproxyport</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsaproxyport)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tsaproxyport</code> - the proxy port to be used when connecting to the TSA.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTsacert()">
<h3>getTsacert</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTsacert</span>()</div>
<div class="block">get the -tsacert option</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a certificate alias or null</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTsacert(java.lang.String)">
<h3>setTsacert</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTsacert</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tsacert)</span></div>
<div class="block">set the alias in the keystore of the TSA to use;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tsacert</code> - the cert alias.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForce(boolean)">
<h3>setForce</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForce</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to force signing of a jar even it is already signed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isForce()">
<h3>isForce</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isForce</span>()</div>
<div class="block">Should the task force signing of a jar even it is already
 signed?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSigAlg(java.lang.String)">
<h3>setSigAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSigAlg</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sigAlg)</span></div>
<div class="block">Signature Algorithm; optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sigAlg</code> - the signature algorithm</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSigAlg()">
<h3>getSigAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSigAlg</span>()</div>
<div class="block">Signature Algorithm; optional</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDigestAlg(java.lang.String)">
<h3>setDigestAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDigestAlg</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;digestAlg)</span></div>
<div class="block">Digest Algorithm; optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>digestAlg</code> - the digest algorithm</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDigestAlg()">
<h3>getDigestAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDigestAlg</span>()</div>
<div class="block">Digest Algorithm; optional</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTSADigestAlg(java.lang.String)">
<h3>setTSADigestAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTSADigestAlg</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;digestAlg)</span></div>
<div class="block">TSA Digest Algorithm; optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>digestAlg</code> - the tsa digest algorithm</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTSADigestAlg()">
<h3>getTSADigestAlg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTSADigestAlg</span>()</div>
<div class="block">TSA Digest Algorithm; optional</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">sign the jar(s)</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isUpToDate(java.io.File,java.io.File)">
<h3>isUpToDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isUpToDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;signedjarFile)</span></div>
<div class="block"><p>Compare a jar file with its corresponding signed jar. The logic for this
 is complex, and best explained in the source itself. Essentially if
 either file doesn't exist, or the destfile has an out of date timestamp,
 then the return value is false.</p>

 <p>If we are signing ourself, the check <a href="#isSigned(java.io.File)"><code>isSigned(File)</code></a> is used to
 trigger the process.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jarFile</code> - the unsigned jar file</dd>
<dd><code>signedjarFile</code> - the result signed jar file</dd>
<dt>Returns:</dt>
<dd>true if the signedjarFile is considered up to date</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSigned(java.io.File)">
<h3>isSigned</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSigned</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">test for a file being signed, by looking for a signature in the META-INF
 directory with our alias/sigfile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to be checked</dd>
<dt>Returns:</dt>
<dd>true if the file is signed</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="condition/IsSigned.html#isSigned(java.io.File,java.lang.String)"><code>IsSigned.isSigned(File, String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreserveLastModified(boolean)">
<h3>setPreserveLastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreserveLastModified</span><wbr><span class="parameters">(boolean&nbsp;preserveLastModified)</span></div>
<div class="block">true to indicate that the signed jar modification date remains the same
 as the original. Defaults to false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>preserveLastModified</code> - if true preserve the last modified time</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
