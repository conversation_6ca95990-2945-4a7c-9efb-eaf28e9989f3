<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>XSLTLiaison4 (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, interface: XSLTLiaison4">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Interface XSLTLiaison4" class="title">Interface XSLTLiaison4</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code>, <code><a href="XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></code>, <code><a href="XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="optional/TraXLiaison.html" title="class in org.apache.tools.ant.taskdefs.optional">TraXLiaison</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">XSLTLiaison4</span><span class="extends-implements">
extends <a href="XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></span></div>
<div class="block">Extends Proxy interface for XSLT processors: adds support for XSLT parameters
 of various types (not only String)</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.3</dd>
<dt>Author:</dt>
<dd>Frantisek Kucera (xkucf03)</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs"><code>XSLTProcess</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLiaison">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></h3>
<code><a href="XSLTLiaison.html#FILE_PROTOCOL_PREFIX">FILE_PROTOCOL_PREFIX</a></code></div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#addParam(java.lang.String,java.lang.Object)" class="member-name-link">addParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Add a parameter to be set during the XSL transformation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLiaison">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></h3>
<code><a href="XSLTLiaison.html#addParam(java.lang.String,java.lang.String)">addParam</a>, <a href="XSLTLiaison.html#setStylesheet(java.io.File)">setStylesheet</a>, <a href="XSLTLiaison.html#transform(java.io.File,java.io.File)">transform</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLiaison2">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></h3>
<code><a href="XSLTLiaison2.html#configure(org.apache.tools.ant.taskdefs.XSLTProcess)">configure</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLiaison3">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></h3>
<code><a href="XSLTLiaison3.html#setStylesheet(org.apache.tools.ant.types.Resource)">setStylesheet</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addParam(java.lang.String,java.lang.Object)">
<h3>addParam</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">addParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span>
       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Add a parameter to be set during the XSL transformation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the parameter name.</dd>
<dd><code>value</code> - the parameter value as String, Boolean, int, etc.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - thrown if any problems happens.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.3</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/Transformer.html#setParameter(java.lang.String,java.lang.Object)" title="class or interface in javax.xml.transform" class="external-link"><code>Transformer.setParameter(java.lang.String, java.lang.Object)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
