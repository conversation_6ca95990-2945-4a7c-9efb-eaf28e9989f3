<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ChangeLogTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.cvslib, class: ChangeLogTask">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.cvslib</a></div>
<h1 title="Class ChangeLogTask" class="title">Class ChangeLogTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.AbstractCvsTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.cvslib.ChangeLogTask</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ChangeLogTask</span>
<span class="extends-implements">extends <a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></span></div>
<div class="block">Examines the output of cvs log and group related changes together.

 It produces an XML output representing the list of changes.
 <pre>
 &lt;!-- Root element --&gt;
 &lt;!ELEMENT changelog (entry+)&gt;
 &lt;!-- CVS Entry --&gt;
 &lt;!ELEMENT entry (date,author,file+,msg)&gt;
 &lt;!-- Date of cvs entry --&gt;
 &lt;!ELEMENT date (#PCDATA)&gt;
 &lt;!-- Author of change --&gt;
 &lt;!ELEMENT author (#PCDATA)&gt;
 &lt;!-- List of files affected --&gt;
 &lt;!ELEMENT msg (#PCDATA)&gt;
 &lt;!-- File changed --&gt;
 &lt;!ELEMENT file (name,revision,prevrevision?)&gt;
 &lt;!-- Name of the file --&gt;
 &lt;!ELEMENT name (#PCDATA)&gt;
 &lt;!-- Revision number --&gt;
 &lt;!ELEMENT revision (#PCDATA)&gt;
 &lt;!-- Previous revision number --&gt;
 &lt;!ELEMENT prevrevision (#PCDATA)&gt;
 </pre></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractCvsTask">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></h2>
<code><a href="../AbstractCvsTask.Module.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask.Module</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractCvsTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></h3>
<code><a href="../AbstractCvsTask.html#DEFAULT_COMPRESSION_LEVEL">DEFAULT_COMPRESSION_LEVEL</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ChangeLogTask</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileSet)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of files about which cvs logs will be generated.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addUser(org.apache.tools.ant.taskdefs.cvslib.CvsUser)" class="member-name-link">addUser</a><wbr>(<a href="CvsUser.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsUser</a>&nbsp;user)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a user to list changelog knows about.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Execute task</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDaysinpast(int)" class="member-name-link">setDaysinpast</a><wbr>(int&nbsp;days)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the number of days worth of log entries to process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestfile(java.io.File)" class="member-name-link">setDestfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output file for the log.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDir(java.io.File)" class="member-name-link">setDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base dir for cvs.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnd(java.util.Date)" class="member-name-link">setEnd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;endDate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the date at which the changelog should stop.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEndTag(java.lang.String)" class="member-name-link">setEndTag</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;end)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the tag at which the changelog should stop.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRemote(boolean)" class="member-name-link">setRemote</a><wbr>(boolean&nbsp;remote)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to use rlog against a remote repository instead of log
 in a working copy's directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStart(java.util.Date)" class="member-name-link">setStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;start)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the date at which the changelog should start.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStartTag(java.lang.String)" class="member-name-link">setStartTag</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;start)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the tag at which the changelog should start.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUsersfile(java.io.File)" class="member-name-link">setUsersfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;usersFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a lookup list of user names &amp; addresses</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractCvsTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></h3>
<code><a href="../AbstractCvsTask.html#addCommandArgument(java.lang.String)">addCommandArgument</a>, <a href="../AbstractCvsTask.html#addCommandArgument(org.apache.tools.ant.types.Commandline,java.lang.String)">addCommandArgument</a>, <a href="../AbstractCvsTask.html#addConfiguredCommandline(org.apache.tools.ant.types.Commandline)">addConfiguredCommandline</a>, <a href="../AbstractCvsTask.html#addConfiguredCommandline(org.apache.tools.ant.types.Commandline,boolean)">addConfiguredCommandline</a>, <a href="../AbstractCvsTask.html#addModule(org.apache.tools.ant.taskdefs.AbstractCvsTask.Module)">addModule</a>, <a href="../AbstractCvsTask.html#configureCommandline(org.apache.tools.ant.types.Commandline)">configureCommandline</a>, <a href="../AbstractCvsTask.html#getCommand()">getCommand</a>, <a href="../AbstractCvsTask.html#getCvsRoot()">getCvsRoot</a>, <a href="../AbstractCvsTask.html#getCvsRsh()">getCvsRsh</a>, <a href="../AbstractCvsTask.html#getDest()">getDest</a>, <a href="../AbstractCvsTask.html#getErrorStream()">getErrorStream</a>, <a href="../AbstractCvsTask.html#getExecuteStreamHandler()">getExecuteStreamHandler</a>, <a href="../AbstractCvsTask.html#getModules()">getModules</a>, <a href="../AbstractCvsTask.html#getOutputStream()">getOutputStream</a>, <a href="../AbstractCvsTask.html#getPackage()">getPackage</a>, <a href="../AbstractCvsTask.html#getPassFile()">getPassFile</a>, <a href="../AbstractCvsTask.html#getPort()">getPort</a>, <a href="../AbstractCvsTask.html#getTag()">getTag</a>, <a href="../AbstractCvsTask.html#removeCommandline(org.apache.tools.ant.types.Commandline)">removeCommandline</a>, <a href="../AbstractCvsTask.html#runCommand(org.apache.tools.ant.types.Commandline)">runCommand</a>, <a href="../AbstractCvsTask.html#setAppend(boolean)">setAppend</a>, <a href="../AbstractCvsTask.html#setCommand(java.lang.String)">setCommand</a>, <a href="../AbstractCvsTask.html#setCompression(boolean)">setCompression</a>, <a href="../AbstractCvsTask.html#setCompressionLevel(int)">setCompressionLevel</a>, <a href="../AbstractCvsTask.html#setCvsRoot(java.lang.String)">setCvsRoot</a>, <a href="../AbstractCvsTask.html#setCvsRsh(java.lang.String)">setCvsRsh</a>, <a href="../AbstractCvsTask.html#setDate(java.lang.String)">setDate</a>, <a href="../AbstractCvsTask.html#setDest(java.io.File)">setDest</a>, <a href="../AbstractCvsTask.html#setError(java.io.File)">setError</a>, <a href="../AbstractCvsTask.html#setErrorStream(java.io.OutputStream)">setErrorStream</a>, <a href="../AbstractCvsTask.html#setExecuteStreamHandler(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)">setExecuteStreamHandler</a>, <a href="../AbstractCvsTask.html#setFailOnError(boolean)">setFailOnError</a>, <a href="../AbstractCvsTask.html#setNoexec(boolean)">setNoexec</a>, <a href="../AbstractCvsTask.html#setOutput(java.io.File)">setOutput</a>, <a href="../AbstractCvsTask.html#setOutputStream(java.io.OutputStream)">setOutputStream</a>, <a href="../AbstractCvsTask.html#setPackage(java.lang.String)">setPackage</a>, <a href="../AbstractCvsTask.html#setPassfile(java.io.File)">setPassfile</a>, <a href="../AbstractCvsTask.html#setPort(int)">setPort</a>, <a href="../AbstractCvsTask.html#setQuiet(boolean)">setQuiet</a>, <a href="../AbstractCvsTask.html#setReallyquiet(boolean)">setReallyquiet</a>, <a href="../AbstractCvsTask.html#setTag(java.lang.String)">setTag</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#init()">init</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ChangeLogTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ChangeLogTask</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDir(java.io.File)">
<h3>setDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputDir)</span></div>
<div class="block">Set the base dir for cvs.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputDir</code> - The new dir value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestfile(java.io.File)">
<h3>setDestfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</span></div>
<div class="block">Set the output file for the log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destFile</code> - The new destfile value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUsersfile(java.io.File)">
<h3>setUsersfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUsersfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;usersFile)</span></div>
<div class="block">Set a lookup list of user names &amp; addresses</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>usersFile</code> - The file containing the users info.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addUser(org.apache.tools.ant.taskdefs.cvslib.CvsUser)">
<h3>addUser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addUser</span><wbr><span class="parameters">(<a href="CvsUser.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsUser</a>&nbsp;user)</span></div>
<div class="block">Add a user to list changelog knows about.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>user</code> - the user</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStart(java.util.Date)">
<h3>setStart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;start)</span></div>
<div class="block">Set the date at which the changelog should start.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>start</code> - The date at which the changelog should start.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEnd(java.util.Date)">
<h3>setEnd</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEnd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;endDate)</span></div>
<div class="block">Set the date at which the changelog should stop.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>endDate</code> - The date at which the changelog should stop.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDaysinpast(int)">
<h3>setDaysinpast</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDaysinpast</span><wbr><span class="parameters">(int&nbsp;days)</span></div>
<div class="block">Set the number of days worth of log entries to process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>days</code> - the number of days of log to process.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRemote(boolean)">
<h3>setRemote</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRemote</span><wbr><span class="parameters">(boolean&nbsp;remote)</span></div>
<div class="block">Whether to use rlog against a remote repository instead of log
 in a working copy's directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>remote</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStartTag(java.lang.String)">
<h3>setStartTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStartTag</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;start)</span></div>
<div class="block">Set the tag at which the changelog should start.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>start</code> - The date at which the changelog should start.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEndTag(java.lang.String)">
<h3>setEndTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEndTag</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;end)</span></div>
<div class="block">Set the tag at which the changelog should stop.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>end</code> - The date at which the changelog should stop.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileSet)</span></div>
<div class="block">Adds a set of files about which cvs logs will be generated.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileSet</code> - a set of files about which cvs logs will be generated.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Execute task</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AbstractCvsTask.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong executing the
            cvs command</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
