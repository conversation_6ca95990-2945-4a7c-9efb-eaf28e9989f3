<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JUnitTest (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: JUnitTest">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class JUnitTest" class="title">Class JUnitTest</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.BaseTest</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.JUnitTest</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JUnitTest</span>
<span class="extends-implements">extends <a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a>
implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></span></div>
<div class="block">Run a single JUnit test.

 <p>The JUnit test is actually run by <a href="JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTestRunner</code></a>.
 So read the doc comments for that class :)</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTask</code></a></li>
<li><a href="JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTestRunner</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junit.BaseTest">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.junit.<a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a></h3>
<code><a href="BaseTest.html#destDir">destDir</a>, <a href="BaseTest.html#errorProperty">errorProperty</a>, <a href="BaseTest.html#failureProperty">failureProperty</a>, <a href="BaseTest.html#filtertrace">filtertrace</a>, <a href="BaseTest.html#fork">fork</a>, <a href="BaseTest.html#formatters">formatters</a>, <a href="BaseTest.html#haltOnError">haltOnError</a>, <a href="BaseTest.html#haltOnFail">haltOnFail</a>, <a href="BaseTest.html#ifProperty">ifProperty</a>, <a href="BaseTest.html#unlessProperty">unlessProperty</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JUnitTest</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">No arg constructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">JUnitTest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor with name.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,boolean,boolean,boolean)" class="member-name-link">JUnitTest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor with options.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,boolean,boolean,boolean,java.lang.String%5B%5D)" class="member-name-link">JUnitTest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor with options.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,boolean,boolean,boolean,java.lang.String%5B%5D,int)" class="member-name-link">JUnitTest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 int&nbsp;thread)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor with options.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#errorCount()" class="member-name-link">errorCount</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of errors.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#failureCount()" class="member-name-link">failureCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of failures.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFormatters()" class="member-name-link">getFormatters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the formatters set for this test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of the test class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutfile()" class="member-name-link">getOutfile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of the output file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProperties()" class="member-name-link">getProperties</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the properties used in the test.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRunTime()" class="member-name-link">getRunTime</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the run time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getThread()" class="member-name-link">getThread</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Ant id of the thread running the test.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#parseTestMethodNamesList(java.lang.String)" class="member-name-link">parseTestMethodNamesList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methodNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Parses a comma-separated list of method names and check their validity.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runCount()" class="member-name-link">runCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of runs.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCounts(long,long,long)" class="member-name-link">setCounts</a><wbr>(long&nbsp;runs,
 long&nbsp;failures,
 long&nbsp;errors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the number of runs, failures, errors, and skipped tests.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCounts(long,long,long,long)" class="member-name-link">setCounts</a><wbr>(long&nbsp;runs,
 long&nbsp;failures,
 long&nbsp;errors,
 long&nbsp;skips)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the number of runs, failures, errors, and skipped tests.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMethods(java.lang.String)" class="member-name-link">setMethods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets names of individual test methods to be executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the test class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutfile(java.lang.String)" class="member-name-link">setOutfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the output file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProperties(java.util.Hashtable)" class="member-name-link">setProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;?,<wbr>?&gt;&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the properties to be used in the test.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRunTime(long)" class="member-name-link">setRunTime</a><wbr>(long&nbsp;runTime)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the runtime.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setThread(int)" class="member-name-link">setThread</a><wbr>(int&nbsp;thread)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the thread id</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shouldRun(org.apache.tools.ant.Project)" class="member-name-link">shouldRun</a><wbr>(<a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this test should run based on the if and unless
 attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#skipCount()" class="member-name-link">skipCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of skipped tests.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junit.BaseTest">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.junit.<a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a></h3>
<code><a href="BaseTest.html#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)">addFormatter</a>, <a href="BaseTest.html#getErrorProperty()">getErrorProperty</a>, <a href="BaseTest.html#getFailureProperty()">getFailureProperty</a>, <a href="BaseTest.html#getFiltertrace()">getFiltertrace</a>, <a href="BaseTest.html#getFork()">getFork</a>, <a href="BaseTest.html#getHaltonerror()">getHaltonerror</a>, <a href="BaseTest.html#getHaltonfailure()">getHaltonfailure</a>, <a href="BaseTest.html#getIfCondition()">getIfCondition</a>, <a href="BaseTest.html#getTodir()">getTodir</a>, <a href="BaseTest.html#getUnlessCondition()">getUnlessCondition</a>, <a href="BaseTest.html#isSkipNonTests()">isSkipNonTests</a>, <a href="BaseTest.html#setErrorProperty(java.lang.String)">setErrorProperty</a>, <a href="BaseTest.html#setFailureProperty(java.lang.String)">setFailureProperty</a>, <a href="BaseTest.html#setFiltertrace(boolean)">setFiltertrace</a>, <a href="BaseTest.html#setFork(boolean)">setFork</a>, <a href="BaseTest.html#setHaltonerror(boolean)">setHaltonerror</a>, <a href="BaseTest.html#setHaltonfailure(boolean)">setHaltonfailure</a>, <a href="BaseTest.html#setIf(java.lang.Object)">setIf</a>, <a href="BaseTest.html#setIf(java.lang.String)">setIf</a>, <a href="BaseTest.html#setSkipNonTests(boolean)">setSkipNonTests</a>, <a href="BaseTest.html#setTodir(java.io.File)">setTodir</a>, <a href="BaseTest.html#setUnless(java.lang.Object)">setUnless</a>, <a href="BaseTest.html#setUnless(java.lang.String)">setUnless</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JUnitTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTest</span>()</div>
<div class="block">No arg constructor.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>JUnitTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Constructor with name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,boolean,boolean,boolean)">
<h3>JUnitTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace)</span></div>
<div class="block">Constructor with options.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the test.</dd>
<dd><code>haltOnError</code> - if true halt the tests if there is an error.</dd>
<dd><code>haltOnFailure</code> - if true halt the tests if there is a failure.</dd>
<dd><code>filtertrace</code> - if true filter stack traces.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,boolean,boolean,boolean,java.lang.String[])">
<h3>JUnitTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods)</span></div>
<div class="block">Constructor with options.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the test.</dd>
<dd><code>haltOnError</code> - if true halt the tests if there is an error.</dd>
<dd><code>haltOnFailure</code> - if true halt the tests if there is a failure.</dd>
<dd><code>filtertrace</code> - if true filter stack traces.</dd>
<dd><code>methods</code> - if non-null run only these test methods</dd>
<dt>Since:</dt>
<dd>1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,boolean,boolean,boolean,java.lang.String[],int)">
<h3>JUnitTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;haltOnError,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;filtertrace,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 int&nbsp;thread)</span></div>
<div class="block">Constructor with options.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the test.</dd>
<dd><code>haltOnError</code> - if true halt the tests if there is an error.</dd>
<dd><code>haltOnFailure</code> - if true halt the tests if there is a failure.</dd>
<dd><code>filtertrace</code> - if true filter stack traces.</dd>
<dd><code>methods</code> - if non-null run only these test methods</dd>
<dd><code>thread</code> - Ant thread ID in which test is currently running</dd>
<dt>Since:</dt>
<dd>1.9.4</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMethods(java.lang.String)">
<h3>setMethods</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMethods</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Sets names of individual test methods to be executed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - comma-separated list of names of individual test methods
              to be executed,
              or <code>null</code> if all test methods should be executed</dd>
<dt>Since:</dt>
<dd>1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set the name of the test class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the name to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setThread(int)">
<h3>setThread</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setThread</span><wbr><span class="parameters">(int&nbsp;thread)</span></div>
<div class="block">Set the thread id</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thread</code> - the Ant id of the thread running this test
 (this is not the system process or thread id)
 (this will be 0 in single-threaded mode).</dd>
<dt>Since:</dt>
<dd>Ant 1.9.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutfile(java.lang.String)">
<h3>setOutfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set the name of the output file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the name of the output file to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseTestMethodNamesList(java.lang.String)">
<h3>parseTestMethodNamesList</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">parseTestMethodNamesList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methodNames)</span>
                                         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></span></div>
<div class="block">Parses a comma-separated list of method names and check their validity.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>methodNames</code> - comma-separated list of method names to be parsed</dd>
<dt>Returns:</dt>
<dd>array of individual test method names</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if the given string is <code>null</code> or if it is not
             a comma-separated list of valid Java identifiers;
             an empty string is acceptable and is handled as an empty
             list</dd>
<dt>Since:</dt>
<dd>1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">Get the name of the test class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getThread()">
<h3>getThread</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getThread</span>()</div>
<div class="block">Get the Ant id of the thread running the test.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the thread id</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutfile()">
<h3>getOutfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOutfile</span>()</div>
<div class="block">Get the name of the output file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the output file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCounts(long,long,long)">
<h3>setCounts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCounts</span><wbr><span class="parameters">(long&nbsp;runs,
 long&nbsp;failures,
 long&nbsp;errors)</span></div>
<div class="block">Set the number of runs, failures, errors, and skipped tests.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>runs</code> - the number of runs.</dd>
<dd><code>failures</code> - the number of failures.</dd>
<dd><code>errors</code> - the number of errors.
 Kept for backward compatibility with Ant 1.8.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCounts(long,long,long,long)">
<h3>setCounts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCounts</span><wbr><span class="parameters">(long&nbsp;runs,
 long&nbsp;failures,
 long&nbsp;errors,
 long&nbsp;skips)</span></div>
<div class="block">Set the number of runs, failures, errors, and skipped tests.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>runs</code> - the number of runs.</dd>
<dd><code>failures</code> - the number of failures.</dd>
<dd><code>errors</code> - the number of errors.</dd>
<dd><code>skips</code> - the number of skipped tests.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRunTime(long)">
<h3>setRunTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRunTime</span><wbr><span class="parameters">(long&nbsp;runTime)</span></div>
<div class="block">Set the runtime.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>runTime</code> - the time in milliseconds.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runCount()">
<h3>runCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">runCount</span>()</div>
<div class="block">Get the number of runs.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of runs.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="failureCount()">
<h3>failureCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">failureCount</span>()</div>
<div class="block">Get the number of failures.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of failures.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="errorCount()">
<h3>errorCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">errorCount</span>()</div>
<div class="block">Get the number of errors.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of errors.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="skipCount()">
<h3>skipCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">skipCount</span>()</div>
<div class="block">Get the number of skipped tests.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of skipped tests.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRunTime()">
<h3>getRunTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getRunTime</span>()</div>
<div class="block">Get the run time.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the run time in milliseconds.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProperties()">
<h3>getProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a></span>&nbsp;<span class="element-name">getProperties</span>()</div>
<div class="block">Get the properties used in the test.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the properties.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProperties(java.util.Hashtable)">
<h3>setProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;?,<wbr>?&gt;&nbsp;p)</span></div>
<div class="block">Set the properties to be used in the test.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the properties.
          This is a copy of the projects ant properties.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shouldRun(org.apache.tools.ant.Project)">
<h3>shouldRun</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">shouldRun</span><wbr><span class="parameters">(<a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Check if this test should run based on the if and unless
 attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project to use to check if the if and unless
          properties exist in.</dd>
<dt>Returns:</dt>
<dd>true if this test or testsuite should be run.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFormatters()">
<h3>getFormatters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a>[]</span>&nbsp;<span class="element-name">getFormatters</span>()</div>
<div class="block">Get the formatters set for this test.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the formatters as an array.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>a clone of this test.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
