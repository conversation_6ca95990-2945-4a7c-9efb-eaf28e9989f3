<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="tree: package: org.apache.tools.ant.taskdefs.optional">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.taskdefs.optional</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../../types/EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="EchoProperties.FormatAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="NetRexxC.TraceAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="NetRexxC.VerboseAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="PropertyFile.Entry.Operation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Operation</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="PropertyFile.Entry.Type.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Type</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="PropertyFile.Unit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Unit</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Javah.ClassArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Javah.ClassArgument</a></li>
<li class="circle">org.apache.tools.ant.<a href="../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.<a href="../../Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="ANTLR.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="EchoProperties.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Javah.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Javah</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.<a href="../MatchingTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a> (implements org.apache.tools.ant.types.selectors.<a href="../../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Cab.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Cab</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Native2Ascii.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="NetRexxC.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="RenameExtensions.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="PropertyFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="ReplaceRegExp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Rpm.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Script.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Script</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="SchemaValidate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="PropertyFile.Entry.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="SchemaValidate.SchemaLocation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="TraXLiaison.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">TraXLiaison</a> (implements javax.xml.transform.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a>, org.apache.tools.ant.taskdefs.<a href="../XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a>, org.apache.tools.ant.taskdefs.<a href="../XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="Xalan2TraceSupport.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">Xalan2TraceSupport</a> (implements org.apache.tools.ant.taskdefs.optional.<a href="XSLTTraceSupport.html" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.Property.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="XMLValidateTask.ValidatorErrorHandler.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</a> (implements org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.<a href="XSLTTraceSupport.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
