<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>LoadResource Task</title>
</head>

<body>


<h2 id="loadresource">LoadResource</h2>

<p><em>Since Apache Ant 1.7</em></p>

<h3>Description</h3>
<p>Load a text resource into a single property. Unless an encoding is specified, the encoding of the
current locale is used.  Resources to load are specified as
nested <a href="../Types/resources.html">resource</a> elements or single element resource
collections. If the resource content is empty (maybe after processing a <code>filterchain</code>)
the property is not set.</p>

<p>Since properties are immutable, the task will not change the value of an existing property.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>property to save to</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>encoding to use when loading the resource</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Whether to halt the build on failure</td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>quiet</td>
    <td>Do not display a diagnostic message (unless Ant has been invoked with
      the <kbd>-verbose</kbd> or <kbd>-debug</kbd> switches) or modify the exit status to
      reflect an error. Setting this to <q>true</q> implies setting <var>failonerror</var>
      to <q>false</q>.</td>
    <td>No; default is <q>false</q></td>
  </tr>
</table>
<p>The LoadResource task supports nested <a href="../Types/filterchain.html">FilterChain</a>s.</p>

<h3>Examples</h3>

<p>Load the entry point of Ant's homepage into property <code>homepage</code>;
an <code>&lt;echo&gt;</code> can print this.</p>
<pre>
&lt;loadresource property="homepage"&gt;
    &lt;url url="https://ant.apache.org/index.html"/&gt;
&lt;/loadresource&gt;</pre>

<p>For more examples see the <a href="loadfile.html">loadfile</a> task.</p>

</body>
</html>
