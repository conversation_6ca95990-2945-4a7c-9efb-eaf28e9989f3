<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>AntXMLContext (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.helper, class: AntXMLContext">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.helper</a></div>
<h1 title="Class AntXMLContext" class="title">Class AntXMLContext</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.helper.AntXMLContext</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AntXMLContext</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Context information for the ant processing.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Project)" class="member-name-link">AntXMLContext</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color">
<div class="block">constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTarget(org.apache.tools.ant.Target)" class="member-name-link">addTarget</a><wbr>(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a new target</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configureId(java.lang.Object,org.xml.sax.Attributes)" class="member-name-link">configureId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scans an attribute list for the <code>id</code> attribute and
 stores a reference to the target object in the project if an
 id is found.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#currentWrapper()" class="member-name-link">currentWrapper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the current runtime configurable wrapper
 can return null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endPrefixMapping(java.lang.String)" class="member-name-link">endPrefixMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">End of prefix to uri mapping.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFile()" class="member-name-link">getBuildFile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the build file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFileParent()" class="member-name-link">getBuildFileParent</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the parent build file of this build file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFileParentURL()" class="member-name-link">getBuildFileParentURL</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the parent build file of this build file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFileURL()" class="member-name-link">getBuildFileURL</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the build file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentProjectName()" class="member-name-link">getCurrentProjectName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the current project name</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Target.html" title="class in org.apache.tools.ant">Target</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentTarget()" class="member-name-link">getCurrentTarget</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the current target</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentTargets()" class="member-name-link">getCurrentTargets</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the targets in the current source file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Target.html" title="class in org.apache.tools.ant">Target</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplicitTarget()" class="member-name-link">getImplicitTarget</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the implicit target</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocator()" class="member-name-link">getLocator</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">access the locator</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPrefixMapping(java.lang.String)" class="member-name-link">getPrefixMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">prefix to namespace uri mapping</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the project to which this antxml context belongs</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTargets()" class="member-name-link">getTargets</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">access the vector of targets</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWrapperStack()" class="member-name-link">getWrapperStack</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">access the stack of wrappers</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isIgnoringProjectTag()" class="member-name-link">isIgnoringProjectTag</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">tells whether the project tag is being ignored</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parentWrapper()" class="member-name-link">parentWrapper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the runtime configurable wrapper of the parent project
 can return null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#popWrapper()" class="member-name-link">popWrapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">remove a runtime configurable wrapper from the stack</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pushWrapper(org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">pushWrapper</a><wbr>(<a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;wrapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a runtime configurable wrapper to the internal stack</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBuildFile(java.io.File)" class="member-name-link">setBuildFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;buildFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the build file to which the XML context belongs</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBuildFile(java.net.URL)" class="member-name-link">setBuildFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;buildFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the build file to which the XML context belongs</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurrentProjectName(java.lang.String)" class="member-name-link">setCurrentProjectName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the name of the current project</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurrentTarget(org.apache.tools.ant.Target)" class="member-name-link">setCurrentTarget</a><wbr>(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the current target</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurrentTargets(java.util.Map)" class="member-name-link">setCurrentTargets</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;currentTargets)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the map of the targets in the current source file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnoreProjectTag(boolean)" class="member-name-link">setIgnoreProjectTag</a><wbr>(boolean&nbsp;flag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the flag to ignore the project tag</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setImplicitTarget(org.apache.tools.ant.Target)" class="member-name-link">setImplicitTarget</a><wbr>(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the implicit target</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLocator(org.xml.sax.Locator)" class="member-name-link">setLocator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a>&nbsp;locator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the locator</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startPrefixMapping(java.lang.String,java.lang.String)" class="member-name-link">startPrefixMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called during parsing, stores the prefix to uri mapping.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Project)">
<h3>AntXMLContext</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntXMLContext</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project to which this antxml context belongs to</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setBuildFile(java.io.File)">
<h3>setBuildFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBuildFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;buildFile)</span></div>
<div class="block">sets the build file to which the XML context belongs</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buildFile</code> - ant build file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBuildFile(java.net.URL)">
<h3>setBuildFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBuildFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&nbsp;buildFile)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/MalformedURLException.html" title="class or interface in java.net" class="external-link">MalformedURLException</a></span></div>
<div class="block">sets the build file to which the XML context belongs</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buildFile</code> - Ant build file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/MalformedURLException.html" title="class or interface in java.net" class="external-link">MalformedURLException</a></code> - if parent URL cannot be constructed</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildFile()">
<h3>getBuildFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBuildFile</span>()</div>
<div class="block">find out the build file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the build file to which the XML context belongs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildFileParent()">
<h3>getBuildFileParent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBuildFileParent</span>()</div>
<div class="block">find out the parent build file of this build file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the parent build file of this build file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildFileURL()">
<h3>getBuildFileURL</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></span>&nbsp;<span class="element-name">getBuildFileURL</span>()</div>
<div class="block">find out the build file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the build file to which the xml context belongs</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildFileParentURL()">
<h3>getBuildFileParentURL</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></span>&nbsp;<span class="element-name">getBuildFileParentURL</span>()</div>
<div class="block">find out the parent build file of this build file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the parent build file of this build file</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">find out the project to which this antxml context belongs</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>project</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentProjectName()">
<h3>getCurrentProjectName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentProjectName</span>()</div>
<div class="block">find out the current project name</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>current project name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentProjectName(java.lang.String)">
<h3>setCurrentProjectName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentProjectName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">set the name of the current project</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - name of the current project</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="currentWrapper()">
<h3>currentWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">currentWrapper</span>()</div>
<div class="block">get the current runtime configurable wrapper
 can return null</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>runtime configurable wrapper</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parentWrapper()">
<h3>parentWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">parentWrapper</span>()</div>
<div class="block">get the runtime configurable wrapper of the parent project
 can return null</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>runtime configurable wrapper  of the parent project</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pushWrapper(org.apache.tools.ant.RuntimeConfigurable)">
<h3>pushWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pushWrapper</span><wbr><span class="parameters">(<a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;wrapper)</span></div>
<div class="block">add a runtime configurable wrapper to the internal stack</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>wrapper</code> - runtime configurable wrapper</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="popWrapper()">
<h3>popWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">popWrapper</span>()</div>
<div class="block">remove a runtime configurable wrapper from the stack</div>
</section>
</li>
<li>
<section class="detail" id="getWrapperStack()">
<h3>getWrapperStack</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&gt;</span>&nbsp;<span class="element-name">getWrapperStack</span>()</div>
<div class="block">access the stack of wrappers</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the stack of wrappers</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTarget(org.apache.tools.ant.Target)">
<h3>addTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTarget</span><wbr><span class="parameters">(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">add a new target</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - target to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentTarget()">
<h3>getCurrentTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Target.html" title="class in org.apache.tools.ant">Target</a></span>&nbsp;<span class="element-name">getCurrentTarget</span>()</div>
<div class="block">get the current target</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>current target</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImplicitTarget()">
<h3>getImplicitTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Target.html" title="class in org.apache.tools.ant">Target</a></span>&nbsp;<span class="element-name">getImplicitTarget</span>()</div>
<div class="block">get the implicit target</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>implicit target</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentTarget(org.apache.tools.ant.Target)">
<h3>setCurrentTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentTarget</span><wbr><span class="parameters">(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">sets the current target</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - current target</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setImplicitTarget(org.apache.tools.ant.Target)">
<h3>setImplicitTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setImplicitTarget</span><wbr><span class="parameters">(<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">sets the implicit target</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - the implicit target</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTargets()">
<h3>getTargets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">getTargets</span>()</div>
<div class="block">access the vector of targets</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>vector of targets</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configureId(java.lang.Object,org.xml.sax.Attributes)">
<h3>configureId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureId</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attr)</span></div>
<div class="block">Scans an attribute list for the <code>id</code> attribute and
 stores a reference to the target object in the project if an
 id is found.
 <p>
 This method was moved out of the configure method to allow
 it to be executed at parse time.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>element</code> - the current element</dd>
<dd><code>attr</code> - attributes of the current element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocator()">
<h3>getLocator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a></span>&nbsp;<span class="element-name">getLocator</span>()</div>
<div class="block">access the locator</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>locator</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLocator(org.xml.sax.Locator)">
<h3>setLocator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLocator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a>&nbsp;locator)</span></div>
<div class="block">sets the locator</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>locator</code> - locator</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isIgnoringProjectTag()">
<h3>isIgnoringProjectTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isIgnoringProjectTag</span>()</div>
<div class="block">tells whether the project tag is being ignored</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether the project tag is being ignored</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnoreProjectTag(boolean)">
<h3>setIgnoreProjectTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnoreProjectTag</span><wbr><span class="parameters">(boolean&nbsp;flag)</span></div>
<div class="block">sets the flag to ignore the project tag</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - to ignore the project tag</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startPrefixMapping(java.lang.String,java.lang.String)">
<h3>startPrefixMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startPrefixMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</span></div>
<div class="block">Called during parsing, stores the prefix to uri mapping.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - a namespace prefix</dd>
<dd><code>uri</code> - a namespace uri</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endPrefixMapping(java.lang.String)">
<h3>endPrefixMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endPrefixMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">End of prefix to uri mapping.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - the namespace prefix</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPrefixMapping(java.lang.String)">
<h3>getPrefixMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPrefixMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">prefix to namespace uri mapping</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - the prefix to map</dd>
<dt>Returns:</dt>
<dd>the uri for this prefix, null if not present</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentTargets()">
<h3>getCurrentTargets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;</span>&nbsp;<span class="element-name">getCurrentTargets</span>()</div>
<div class="block">Get the targets in the current source file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the current targets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentTargets(java.util.Map)">
<h3>setCurrentTargets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentTargets</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../Target.html" title="class in org.apache.tools.ant">Target</a>&gt;&nbsp;currentTargets)</span></div>
<div class="block">Set the map of the targets in the current source file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>currentTargets</code> - a map of targets.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
