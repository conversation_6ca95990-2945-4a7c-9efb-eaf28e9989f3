<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>LocalProperties (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.property, class: LocalProperties">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.property</a></div>
<h1 title="Class LocalProperties" class="title">Class LocalProperties</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html" title="class or interface in java.lang" class="external-link">java.lang.ThreadLocal</a>&lt;T&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InheritableThreadLocal.html" title="class or interface in java.lang" class="external-link">java.lang.InheritableThreadLocal</a>&lt;<a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a>&gt;
<div class="inheritance">org.apache.tools.ant.property.LocalProperties</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../PropertyHelper.Delegate.html" title="interface in org.apache.tools.ant">PropertyHelper.Delegate</a></code>, <code><a href="../PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></code>, <code><a href="../PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></code>, <code><a href="../PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">LocalProperties</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InheritableThreadLocal.html" title="class or interface in java.lang" class="external-link">InheritableThreadLocal</a>&lt;<a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a>&gt;
implements <a href="../PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a>, <a href="../PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a>, <a href="../PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></span></div>
<div class="block">Thread local class containing local properties.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addLocal(java.lang.String)" class="member-name-link">addLocal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a local property to the current scope.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copy()" class="member-name-link">copy</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copy the stack for a parallel thread.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enterScope()" class="member-name-link">enterScope</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">enter the scope</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#evaluate(java.lang.String,org.apache.tools.ant.PropertyHelper)" class="member-name-link">evaluate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;helper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Evaluate a property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exitScope()" class="member-name-link">exitScope</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">exit the scope</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="LocalProperties.html" title="class in org.apache.tools.ant.property">LocalProperties</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#get(org.apache.tools.ant.Project)" class="member-name-link">get</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get a localproperties for the given project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPropertyNames()" class="member-name-link">getPropertyNames</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the names of all properties known to this delegate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initialValue()" class="member-name-link">initialValue</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the initial value.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)" class="member-name-link">set</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;propertyHelper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a property.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNew(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)" class="member-name-link">setNew</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;propertyHelper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a *new" property.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.InheritableThreadLocal">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InheritableThreadLocal.html" title="class or interface in java.lang" class="external-link">InheritableThreadLocal</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InheritableThreadLocal.html#childValue(T)" title="class or interface in java.lang" class="external-link">childValue</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.ThreadLocal">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html" title="class or interface in java.lang" class="external-link">ThreadLocal</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html#get()" title="class or interface in java.lang" class="external-link">get</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html#remove()" title="class or interface in java.lang" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html#set(T)" title="class or interface in java.lang" class="external-link">set</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html#withInitial(java.util.function.Supplier)" title="class or interface in java.lang" class="external-link">withInitial</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="get(org.apache.tools.ant.Project)">
<h3>get</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="LocalProperties.html" title="class in org.apache.tools.ant.property">LocalProperties</a></span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Get a localproperties for the given project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project to retrieve the localproperties for.</dd>
<dt>Returns:</dt>
<dd>the localproperties.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initialValue()">
<h3>initialValue</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a></span>&nbsp;<span class="element-name">initialValue</span>()</div>
<div class="block">Get the initial value.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html#initialValue()" title="class or interface in java.lang" class="external-link">initialValue</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ThreadLocal.html" title="class or interface in java.lang" class="external-link">ThreadLocal</a>&lt;<a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>a new localproperties stack.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addLocal(java.lang.String)">
<h3>addLocal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addLocal</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property)</span></div>
<div class="block">Add a local property to the current scope.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>property</code> - the property name to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="enterScope()">
<h3>enterScope</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">enterScope</span>()</div>
<div class="block">enter the scope</div>
</section>
</li>
<li>
<section class="detail" id="exitScope()">
<h3>exitScope</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">exitScope</span>()</div>
<div class="block">exit the scope</div>
</section>
</li>
<li>
<section class="detail" id="copy()">
<h3>copy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copy</span>()</div>
<div class="block">Copy the stack for a parallel thread.
 To be called from the parallel thread itself.</div>
</section>
</li>
<li>
<section class="detail" id="evaluate(java.lang.String,org.apache.tools.ant.PropertyHelper)">
<h3>evaluate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">evaluate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;helper)</span></div>
<div class="block">Evaluate a property.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../PropertyHelper.PropertyEvaluator.html#evaluate(java.lang.String,org.apache.tools.ant.PropertyHelper)">evaluate</a></code>&nbsp;in interface&nbsp;<code><a href="../PropertyHelper.PropertyEvaluator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEvaluator</a></code></dd>
<dt>Parameters:</dt>
<dd><code>property</code> - the property's String "identifier".</dd>
<dd><code>helper</code> - the invoking PropertyHelper.</dd>
<dt>Returns:</dt>
<dd>Object value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNew(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)">
<h3>setNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">setNew</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;propertyHelper)</span></div>
<div class="block">Set a *new" property.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../PropertyHelper.PropertySetter.html#setNew(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)">setNew</a></code>&nbsp;in interface&nbsp;<code><a href="../PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>property</code> - the property's String "identifier".</dd>
<dd><code>value</code> - the value to set.</dd>
<dd><code>propertyHelper</code> - the invoking PropertyHelper.</dd>
<dt>Returns:</dt>
<dd>true if this entity 'owns' the property.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="set(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)">
<h3>set</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">set</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;property,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value,
 <a href="../PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</a>&nbsp;propertyHelper)</span></div>
<div class="block">Set a property.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../PropertyHelper.PropertySetter.html#set(java.lang.String,java.lang.Object,org.apache.tools.ant.PropertyHelper)">set</a></code>&nbsp;in interface&nbsp;<code><a href="../PropertyHelper.PropertySetter.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertySetter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>property</code> - the property's String "identifier".</dd>
<dd><code>value</code> - the value to set.</dd>
<dd><code>propertyHelper</code> - the invoking PropertyHelper.</dd>
<dt>Returns:</dt>
<dd>true if this entity 'owns' the property.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPropertyNames()">
<h3>getPropertyNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getPropertyNames</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="../PropertyHelper.PropertyEnumerator.html#getPropertyNames()">PropertyHelper.PropertyEnumerator</a></code></span></div>
<div class="block">Returns the names of all properties known to this delegate.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../PropertyHelper.PropertyEnumerator.html#getPropertyNames()">getPropertyNames</a></code>&nbsp;in interface&nbsp;<code><a href="../PropertyHelper.PropertyEnumerator.html" title="interface in org.apache.tools.ant">PropertyHelper.PropertyEnumerator</a></code></dd>
<dt>Returns:</dt>
<dd>the names of all properties known to this delegate.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
