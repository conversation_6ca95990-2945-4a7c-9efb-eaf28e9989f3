<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Deprecated List (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="deprecated elements">
<meta name="generator" content="javadoc/DeprecatedListWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="deprecated-list-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="nav-bar-cell1-rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#deprecated">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
</div>
<h2 title="Contents">Contents</h2>
<ul class="contents-list">
<li id="contents-class"><a href="#class">Classes</a></li>
<li id="contents-field"><a href="#field">Fields</a></li>
<li id="contents-method"><a href="#method">Methods</a></li>
<li id="contents-constructor"><a href="#constructor">Constructors</a></li>
</ul>
<ul class="block-list">
<li>
<div id="class">
<div class="caption"><span>Deprecated Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/listener/Log4jListener.html" title="class in org.apache.tools.ant.listener">org.apache.tools.ant.listener.Log4jListener</a></div>
<div class="col-last even-row-color">
<div class="block">Apache Log4j (1) is not developed any more. Last
     release is 1.2.17 from 26-May-2012 and contains vulnerability issues.
     Use the standard listener or your own custom listener instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">org.apache.tools.ant.loader.AntClassLoader2</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7
             Just use <a href="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><code>AntClassLoader</code></a> itself.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/loader/AntClassLoader5.html" title="class in org.apache.tools.ant.loader">org.apache.tools.ant.loader.AntClassLoader5</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.9.7
             Just use <a href="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><code>AntClassLoader</code></a> itself.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/Javac12.html" title="class in org.apache.tools.ant.taskdefs.compilers">org.apache.tools.ant.taskdefs.compilers.Javac12</a></div>
<div class="col-last odd-row-color">
<div class="block">Use <a href="org/apache/tools/ant/taskdefs/compilers/Javac13.html" title="class in org.apache.tools.ant.taskdefs.compilers"><code>Javac13</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copydir</a></div>
<div class="col-last even-row-color">
<div class="block">The copydir task is deprecated since Ant 1.2.  Use copy instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copyfile</a></div>
<div class="col-last odd-row-color">
<div class="block">The copyfile task is deprecated since Ant 1.2.  Use
 copy instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.CopyPath</a></div>
<div class="col-last even-row-color">
<div class="block">this task should have never been released and was
 obsoleted by ResourceCollection support in Copy available since Ant
 1.7.0.  Don't use it.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Deltree</a></div>
<div class="col-last odd-row-color">
<div class="block">The deltree task is deprecated since Ant 1.2.  Use
 delete instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/email/MimeMailer.html" title="class in org.apache.tools.ant.taskdefs.email">org.apache.tools.ant.taskdefs.email.MimeMailer</a></div>
<div class="col-last even-row-color">
<div class="block">see org.apache.tools.ant.taskdefs.email.JakartaMimeMailer</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Exec</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.2.
             delegate to <a href="org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><code>Execute</code></a>
             instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/FixCRLF.OneLiner.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.FixCRLF.OneLiner</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.0.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Jikes.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Jikes</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.2.
             Merged into the class Javac.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.JikesOutputParser</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.2.
             Use Jikes' exit value to detect compilation failure.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.KeySubst</a></div>
<div class="col-last odd-row-color">
<div class="block">KeySubst is deprecated since Ant 1.1. Use Filter + Copy
 instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/extension/DeweyDecimal.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">org.apache.tools.ant.taskdefs.optional.extension.DeweyDecimal</a></div>
<div class="col-last even-row-color">
<div class="block">use org.apache.tools.ant.util.DeweyDecimal instead.
 Deprecated since ant 1.8</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/image/Image.html" title="class in org.apache.tools.ant.taskdefs.optional.image">org.apache.tools.ant.taskdefs.optional.image.Image</a></div>
<div class="col-last odd-row-color">
<div class="block">JAI is not developed any more. Internal APIs that JAI depends on were
 scheduled for removal in Java 7 and finally removed in Java 9.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/junit/Enumerations.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.Enumerations</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/net/MimeMail.html" title="class in org.apache.tools.ant.taskdefs.optional.net">org.apache.tools.ant.taskdefs.optional.net.MimeMail</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use <a href="org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email"><code>EmailTask</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">org.apache.tools.ant.taskdefs.optional.RenameExtensions</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use &lt;move&gt; instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Rename</a></div>
<div class="col-last odd-row-color">
<div class="block">The rename task is deprecated since Ant 1.2.  Use move instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.TaskOutputStream</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.2.x.
 Use LogOutputStream instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.CollectionUtils</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.EmptyEnumeration.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.CollectionUtils.EmptyEnumeration</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/LazyHashtable.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LazyHashtable</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/optional/WeakishReference12.html" title="class in org.apache.tools.ant.util.optional">org.apache.tools.ant.util.optional.WeakishReference12</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             Just use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ref/WeakReference.html" title="class or interface in java.lang.ref" class="external-link"><code>WeakReference</code></a> directly.
 Note that in ant1.7 is parent was changed to extend HardReference.
 This is because the latter has access to the (package scoped)
 WeakishReference(Object) constructor, and both that and this are thin
 facades on the underlying no-longer-abstract base class.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/ScriptRunner.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.ScriptRunner</a></div>
<div class="col-last odd-row-color">
<div class="block">Implementation moved to another location. Use
             org.apache.tools.ant.util.optional.ScriptRunner instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/SymbolicLinkUtils.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.SymbolicLinkUtils</a></div>
<div class="col-last even-row-color">
<div class="block">Starting Ant 1.10.2, this class is now deprecated in favour
              of the Java <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/file/Files.html" title="class or interface in java.nio.file" class="external-link"><code>Files</code></a> APIs introduced in
              Java 7, for dealing with symbolic links</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/WeakishReference.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.WeakishReference</a></div>
<div class="col-last odd-row-color">
<div class="block">deprecated 1.7; will be removed in Ant1.8
             Just use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ref/WeakReference.html" title="class or interface in java.lang.ref" class="external-link"><code>WeakReference</code></a> directly.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/WeakishReference.HardReference.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.WeakishReference.HardReference</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             Hopefully nobody is using this.</div>
</div>
</div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div id="field">
<div class="caption"><span>Deprecated Fields</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/DefaultLogger.html#lSep">org.apache.tools.ant.DefaultLogger.lSep</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/DirectoryScanner.html#DEFAULTEXCLUDES">org.apache.tools.ant.DirectoryScanner.DEFAULTEXCLUDES</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use the <a href="org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()"><code>getDefaultExcludes</code></a>
             method instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/listener/Log4jListener.html#LOG_ANT">org.apache.tools.ant.listener.Log4jListener.LOG_ANT</a></div>
<div class="col-last even-row-color">
<div class="block">use MagicNames.ANT_CORE_PACKAGE</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#JAVA_1_0">org.apache.tools.ant.Project.JAVA_1_0</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_0"><code>JavaEnvUtils.JAVA_1_0</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#JAVA_1_1">org.apache.tools.ant.Project.JAVA_1_1</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_1"><code>JavaEnvUtils.JAVA_1_1</code></a> instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#JAVA_1_2">org.apache.tools.ant.Project.JAVA_1_2</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_2"><code>JavaEnvUtils.JAVA_1_2</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#JAVA_1_3">org.apache.tools.ant.Project.JAVA_1_3</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_3"><code>JavaEnvUtils.JAVA_1_3</code></a> instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#JAVA_1_4">org.apache.tools.ant.Project.JAVA_1_4</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_4"><code>JavaEnvUtils.JAVA_1_4</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectComponent.html#description">org.apache.tools.ant.ProjectComponent.description</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectComponent.html#location">org.apache.tools.ant.ProjectComponent.location</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="org/apache/tools/ant/ProjectComponent.html#getLocation()"><code>ProjectComponent.getLocation()</code></a> method.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectComponent.html#project">org.apache.tools.ant.ProjectComponent.project</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             You should not be directly accessing this variable directly.
             You should access project object via the getProject()
             or setProject() accessor/mutators.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#ANTLIB_URI">org.apache.tools.ant.ProjectHelper.ANTLIB_URI</a></div>
<div class="col-last odd-row-color">
<div class="block">use MagicNames.ANTLIB_PREFIX</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#HELPER_PROPERTY">org.apache.tools.ant.ProjectHelper.HELPER_PROPERTY</a></div>
<div class="col-last even-row-color">
<div class="block">use MagicNames.PROJECT_HELPER_CLASS</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#PROJECTHELPER_REFERENCE">org.apache.tools.ant.ProjectHelper.PROJECTHELPER_REFERENCE</a></div>
<div class="col-last odd-row-color">
<div class="block">use MagicNames.REFID_PROJECT_HELPER</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#SERVICE_ID">org.apache.tools.ant.ProjectHelper.SERVICE_ID</a></div>
<div class="col-last even-row-color">
<div class="block">use MagicNames.PROJECT_HELPER_SERVICE</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Task.html#target">org.apache.tools.ant.Task.target</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="org/apache/tools/ant/Task.html#getOwningTarget()"><code>Task.getOwningTarget()</code></a> method.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Task.html#taskName">org.apache.tools.ant.Task.taskName</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="org/apache/tools/ant/Task.html#getTaskName()"><code>Task.getTaskName()</code></a> method.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Task.html#taskType">org.apache.tools.ant.Task.taskType</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="org/apache/tools/ant/Task.html#getTaskType()"><code>Task.getTaskType()</code></a> method.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Task.html#wrapper">org.apache.tools.ant.Task.wrapper</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="org/apache/tools/ant/Task.html#getWrapper()"><code>Task.getWrapper()</code></a> method.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#lSep">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.lSep</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/condition/IsReachable.html#METHOD_NAME">org.apache.tools.ant.taskdefs.condition.IsReachable.METHOD_NAME</a></div>
<div class="col-last even-row-color">
<div class="block">Since 1.10.6</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Manifest.html#JAR_ENCODING">org.apache.tools.ant.taskdefs.Manifest.JAR_ENCODING</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#FAIL">org.apache.tools.ant.taskdefs.Tar.FAIL</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Tar.FAIL is deprecated and is replaced with
             Tar.TarLongFileMode.FAIL</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#GNU">org.apache.tools.ant.taskdefs.Tar.GNU</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Tar.GNU is deprecated and is replaced with
             Tar.TarLongFileMode.GNU</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#OMIT">org.apache.tools.ant.taskdefs.Tar.OMIT</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Tar.OMIT is deprecated and is replaced with
             Tar.TarLongFileMode.OMIT</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#TRUNCATE">org.apache.tools.ant.taskdefs.Tar.TRUNCATE</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Tar.TRUNCATE is deprecated and is replaced with
             Tar.TarLongFileMode.TRUNCATE</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#WARN">org.apache.tools.ant.taskdefs.Tar.WARN</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Tar.WARN is deprecated and is replaced with
             Tar.TarLongFileMode.WARN</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/DataType.html#checked">org.apache.tools.ant.types.DataType.checked</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.
             The user should not be directly referencing
             variable. Please use <a href="org/apache/tools/ant/types/DataType.html#setChecked(boolean)"><code>DataType.setChecked(boolean)</code></a> or
             <a href="org/apache/tools/ant/types/DataType.html#isChecked()"><code>DataType.isChecked()</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/DataType.html#ref">org.apache.tools.ant.types.DataType.ref</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             The user should not be directly referencing
             variable. Please use <a href="org/apache/tools/ant/types/DataType.html#getRefid()"><code>DataType.getRefid()</code></a> instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#EMPTY_LIST">org.apache.tools.ant.util.CollectionUtils.EMPTY_LIST</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/DateUtils.html#DATE_HEADER_FORMAT">org.apache.tools.ant.util.DateUtils.DATE_HEADER_FORMAT</a></div>
<div class="col-last even-row-color">
<div class="block">DateFormat is not thread safe, and we cannot guarantee that
 some other code is using the format in parallel.
 Deprecated since ant 1.8</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_9">org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_9</a></div>
<div class="col-last odd-row-color">
<div class="block">use #JAVA_9 instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/JavaEnvUtils.html#VERSION_1_9">org.apache.tools.ant.util.JavaEnvUtils.VERSION_1_9</a></div>
<div class="col-last even-row-color">
<div class="block">use #VERSION_9 instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/ResourceUtils.html#ISO_8859_1">org.apache.tools.ant.util.ResourceUtils.ISO_8859_1</a></div>
<div class="col-last odd-row-color">
<div class="block">use StandardCharsets.ISO_8859_1</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/StringUtils.html#LINE_SEP">org.apache.tools.ant.util.StringUtils.LINE_SEP</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/zip/ZipOutputStream.html#EFS_FLAG">org.apache.tools.zip.ZipOutputStream.EFS_FLAG</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="org/apache/tools/zip/GeneralPurposeBit.html#UFT8_NAMES_FLAG"><code>GeneralPurposeBit.UFT8_NAMES_FLAG</code></a> instead</div>
</div>
</div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div id="method">
<div class="caption"><span>Deprecated Methods</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/AntClassLoader.html#initializeClass(java.lang.Class)">org.apache.tools.ant.AntClassLoader.initializeClass<wbr>(Class&lt;?&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             Use Class.forName with initialize=true instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/BuildException.html#getException()">org.apache.tools.ant.BuildException.getException()</a></div>
<div class="col-last odd-row-color">
<div class="block">Use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html#getCause()" title="class or interface in java.lang" class="external-link"><code>Throwable.getCause()</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Diagnostics.html#isOptionalAvailable()">org.apache.tools.ant.Diagnostics.isOptionalAvailable()</a></div>
<div class="col-last even-row-color">
<div class="block">Obsolete since Ant 1.8.2</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Diagnostics.html#validateVersion()">org.apache.tools.ant.Diagnostics.validateVersion()</a></div>
<div class="col-last odd-row-color">
<div class="block">Obsolete since Ant 1.8.2</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/IntrospectionHelper.html#createElement(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">org.apache.tools.ant.IntrospectionHelper.createElement<wbr>(Project, Object, String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             This is not a namespace aware method.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/launch/Locator.html#fileToURL(java.io.File)">org.apache.tools.ant.launch.Locator.fileToURL<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.9, use <code>FileUtils.getFileURL(File)</code></div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#addFilter(java.lang.String,java.lang.String)">org.apache.tools.ant.Project.addFilter<wbr>(String, String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x.
             Use getGlobalFilterSet().addFilter(token,value)</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.io.File,java.io.File)">org.apache.tools.ant.Project.copyFile<wbr>(File, File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.io.File,java.io.File,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(File, File, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.io.File,java.io.File,boolean,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(File, File, boolean, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.io.File,java.io.File,boolean,boolean,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(File, File, boolean, boolean, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.lang.String,java.lang.String)">org.apache.tools.ant.Project.copyFile<wbr>(String, String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.lang.String,java.lang.String,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(String, String, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.lang.String,java.lang.String,boolean,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(String, String, boolean, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#copyFile(java.lang.String,java.lang.String,boolean,boolean,boolean)">org.apache.tools.ant.Project.copyFile<wbr>(String, String, boolean, boolean, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#getFilters()">org.apache.tools.ant.Project.getFilters()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#getJavaVersion()">org.apache.tools.ant.Project.getJavaVersion()</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.util.JavaEnvUtils instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#resolveFile(java.lang.String,java.io.File)">org.apache.tools.ant.Project.resolveFile<wbr>(String, File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#setDefaultTarget(java.lang.String)">org.apache.tools.ant.Project.setDefaultTarget<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use setDefault.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Project.html#setFileLastModified(java.io.File,long)">org.apache.tools.ant.Project.setFileLastModified<wbr>(File, long)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/Project.html#translatePath(java.lang.String)">org.apache.tools.ant.Project.translatePath<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7
             Use FileUtils.translatePath instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#configure(java.lang.Object,org.xml.sax.AttributeList,org.apache.tools.ant.Project)">org.apache.tools.ant.ProjectHelper.configure<wbr>(Object, AttributeList, Project)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use IntrospectionHelper for each property.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#getContextClassLoader()">org.apache.tools.ant.ProjectHelper.getContextClassLoader()</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             Use LoaderUtils.getContextClassLoader()</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)">org.apache.tools.ant.ProjectHelper.parsePropertyString<wbr>(String, Vector&lt;String&gt;, Vector&lt;String&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use PropertyHelper.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project,java.lang.String)">org.apache.tools.ant.ProjectHelper.replaceProperties<wbr>(Project, String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             Use project.replaceProperties().</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project,java.lang.String,java.util.Hashtable)">org.apache.tools.ant.ProjectHelper.replaceProperties<wbr>(Project, String, Hashtable&lt;String, Object&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use PropertyHelper.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/property/ResolvePropertyMap.html#resolveAllProperties(java.util.Map)">org.apache.tools.ant.property.ResolvePropertyMap.resolveAllProperties<wbr>(Map&lt;String, Object&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.8.2, use the three-arg method instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/property/ResolvePropertyMap.html#resolveAllProperties(java.util.Map,java.lang.String)">org.apache.tools.ant.property.ResolvePropertyMap.resolveAllProperties<wbr>(Map&lt;String, Object&gt;, String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.8.2, use the three-arg method instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#getNext()">org.apache.tools.ant.PropertyHelper.getNext()</a></div>
<div class="col-last even-row-color">
<div class="block">use the delegate mechanism instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#getProperty(java.lang.String,java.lang.String)">org.apache.tools.ant.PropertyHelper.getProperty<wbr>(String, String)</a></div>
<div class="col-last odd-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#getPropertyHook(java.lang.String,java.lang.String,boolean)">org.apache.tools.ant.PropertyHelper.getPropertyHook<wbr>(String, String, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">PropertyHelper chaining is deprecated.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#getUserProperty(java.lang.String,java.lang.String)">org.apache.tools.ant.PropertyHelper.getUserProperty<wbr>(String, String)</a></div>
<div class="col-last odd-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)">org.apache.tools.ant.PropertyHelper.parsePropertyString<wbr>(String, Vector&lt;String&gt;, Vector&lt;String&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">use the other mechanisms of this class instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setInheritedProperty(java.lang.String,java.lang.String,java.lang.Object)">org.apache.tools.ant.PropertyHelper.setInheritedProperty<wbr>(String, String, Object)</a></div>
<div class="col-last odd-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setNewProperty(java.lang.String,java.lang.String,java.lang.Object)">org.apache.tools.ant.PropertyHelper.setNewProperty<wbr>(String, String, Object)</a></div>
<div class="col-last even-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setNext(org.apache.tools.ant.PropertyHelper)">org.apache.tools.ant.PropertyHelper.setNext<wbr>(PropertyHelper)</a></div>
<div class="col-last odd-row-color">
<div class="block">use the delegate mechanism instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setProperty(java.lang.String,java.lang.String,java.lang.Object,boolean)">org.apache.tools.ant.PropertyHelper.setProperty<wbr>(String, String, Object, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setPropertyHook(java.lang.String,java.lang.String,java.lang.Object,boolean,boolean,boolean)">org.apache.tools.ant.PropertyHelper.setPropertyHook<wbr>(String, String, Object, boolean, boolean, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">PropertyHelper chaining is deprecated.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/PropertyHelper.html#setUserProperty(java.lang.String,java.lang.String,java.lang.Object)">org.apache.tools.ant.PropertyHelper.setUserProperty<wbr>(String, String, Object)</a></div>
<div class="col-last even-row-color">
<div class="block">namespaces are unnecessary.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/RuntimeConfigurable.html#getAttributes()">org.apache.tools.ant.RuntimeConfigurable.getAttributes()</a></div>
<div class="col-last odd-row-color">
<div class="block">Deprecated since Ant 1.6 in favor of <a href="org/apache/tools/ant/RuntimeConfigurable.html#getAttributeMap()"><code>RuntimeConfigurable.getAttributeMap()</code></a>.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/RuntimeConfigurable.html#setAttributes(org.xml.sax.AttributeList)">org.apache.tools.ant.RuntimeConfigurable.setAttributes<wbr>(AttributeList)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Available.html#setType(java.lang.String)">org.apache.tools.ant.taskdefs.Available.setType<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             setType(String) is deprecated and is replaced with
             setType(Available.FileDir) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Classloader.html#setReverse(boolean)">org.apache.tools.ant.taskdefs.Classloader.setReverse<wbr>(boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">use setParentFirst with a negated argument instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#addExtdirsToClasspath(org.apache.tools.ant.types.Path)">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.addExtdirsToClasspath<wbr>(Path)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.types.Path#addExtdirs instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava11()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava11()</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_1Plus, if necessary combined with !assumeJava1_2Plus</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava12()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava12()</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_2Plus, if necessary combined with !assumeJava1_3Plus</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava13()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava13()</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_3Plus, if necessary combined with !assumeJava1_4Plus</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava14()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava14()</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_4Plus, if necessary combined with !assumeJava1_5Plus</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava15()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava15()</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_5Plus, if necessary combined with !assumeJava1_6Plus</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava16()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava16()</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_6Plus, if necessary combined with !assumeJava1_7Plus</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava17()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava17()</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_7Plus, if necessary combined with !assumeJava1_8Plus</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava18()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava18()</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.10.7, use assumeJava1_8Plus, if necessary combined with !assumeJava9Plus</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava19()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava19()</a></div>
<div class="col-last even-row-color">
<div class="block">use #assumeJava9 instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava9()">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.assumeJava9()</a></div>
<div class="col-last odd-row-color">
<div class="block">since Ant 1.10.7, use assumeJava9Plus, in the future if necessary combined with !assumeJava10Plus</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Concat.html#setForce(boolean)">org.apache.tools.ant.taskdefs.Concat.setForce<wbr>(boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">use #setOverwrite instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Copy.html#setPreserveLastModified(java.lang.String)">org.apache.tools.ant.taskdefs.Copy.setPreserveLastModified<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             setPreserveLastModified(String) has been deprecated and
             replaced with setPreserveLastModified(boolean) to
             consistently let the Introspection mechanism work.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/DefBase.html#setReverseLoader(boolean)">org.apache.tools.ant.taskdefs.DefBase.setReverseLoader<wbr>(boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             stop using this attribute</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Ear.html#setEarfile(java.io.File)">org.apache.tools.ant.taskdefs.Ear.setEarfile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use setDestFile(destfile) instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Execute.html#getProcEnvironment()">org.apache.tools.ant.taskdefs.Execute.getProcEnvironment()</a></div>
<div class="col-last even-row-color">
<div class="block">use #getEnvironmentVariables instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Execute.html#setSpawn(boolean)">org.apache.tools.ant.taskdefs.Execute.setSpawn<wbr>(boolean)</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/ExecuteJava.html#setOutput(java.io.PrintStream)">org.apache.tools.ant.taskdefs.ExecuteJava.setOutput<wbr>(PrintStream)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.4.x.
             manage output at the task level.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/FixCRLF.html#setCr(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)">org.apache.tools.ant.taskdefs.FixCRLF.setCr<wbr>(FixCRLF.AddAsisRemove)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.4.x.
             Use <a href="org/apache/tools/ant/taskdefs/FixCRLF.html#setEol(org.apache.tools.ant.taskdefs.FixCRLF.CrLf)"><code>setEol</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Get.html#doGet(int,org.apache.tools.ant.taskdefs.Get.DownloadProgress)">org.apache.tools.ant.taskdefs.Get.doGet<wbr>(int, Get.DownloadProgress)</a></div>
<div class="col-last even-row-color">
<div class="block">only gets the first configured resource</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Jar.html#setJarfile(java.io.File)">org.apache.tools.ant.taskdefs.Jar.setJarfile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(java.lang.String)">org.apache.tools.ant.taskdefs.Javadoc.setExtdirs<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use the <a href="org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(org.apache.tools.ant.types.Path)"><code>Javadoc.setExtdirs(Path)</code></a> version.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/MacroInstance.html#createDynamicElement(java.lang.String)">org.apache.tools.ant.taskdefs.MacroInstance.createDynamicElement<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/ANTLR.html#setGlib(java.lang.String)">org.apache.tools.ant.taskdefs.optional.ANTLR.setGlib<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since ant 1.6</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#runS(org.apache.tools.ant.types.Commandline)">org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.runS<wbr>(Commandline)</a></div>
<div class="col-last odd-row-color">
<div class="block">use the two arg version instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/image/Image.html#processFile(java.io.File)">org.apache.tools.ant.taskdefs.optional.image.Image.processFile<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">this method isn't used anymore</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/image/ImageIOTask.html#processFile(java.io.File)">org.apache.tools.ant.taskdefs.optional.image.ImageIOTask.processFile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">this method isn't used anymore</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html#createSourcespath()">org.apache.tools.ant.taskdefs.optional.jdepend.JDependTask.createSourcespath()</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html#getSourcespath()">org.apache.tools.ant.taskdefs.optional.jdepend.JDependTask.getSourcespath()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/junit/Enumerations.html#fromArray(T...)">org.apache.tools.ant.taskdefs.optional.junit.Enumerations.fromArray<wbr>(T...)</a></div>
<div class="col-last even-row-color">
<div class="block">use Collections.enumeration(Arrays.asList(array))</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/junit/Enumerations.html#fromCompound(java.util.Enumeration...)">org.apache.tools.ant.taskdefs.optional.junit.Enumerations.fromCompound<wbr>(Enumeration&lt;? extends T&gt;...)</a></div>
<div class="col-last odd-row-color">
<div class="block">use Stream.concat(Collections.list(one).stream(), Collections.list(two).stream())
                 .collect(Collectors.collectingAndThen(Collectors.toList(), Collections::enumeration))</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.addSysproperty<wbr>(Environment.Variable)</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.6</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/net/FTP.html#setAction(java.lang.String)">org.apache.tools.ant.taskdefs.optional.net.FTP.setAction<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/net/FTPTask.html#setAction(java.lang.String)">org.apache.tools.ant.taskdefs.optional.net.FTPTask.setAction<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/ReplaceRegExp.html#setByLine(java.lang.String)">org.apache.tools.ant.taskdefs.optional.ReplaceRegExp.setByLine<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.
             Use setByLine(boolean).</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.html#executeScript(java.util.Map,java.util.Map)">org.apache.tools.ant.taskdefs.optional.script.ScriptDef.executeScript<wbr>(Map&lt;String, String&gt;, Map&lt;String, List&lt;Object&gt;&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             Use executeScript(attribute, elements, instance) instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.html#setManager(java.lang.String)">org.apache.tools.ant.taskdefs.optional.script.ScriptDef.setManager<wbr>(String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/Script.html#setManager(java.lang.String)">org.apache.tools.ant.taskdefs.optional.Script.setManager<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setPassword(java.lang.String)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setPassword<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.net.SetProxy</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setPort(java.lang.String)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setPort<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.net.SetProxy</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setProxy(java.lang.String)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setProxy<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.net.SetProxy</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setUseproxy(boolean)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setUseproxy<wbr>(boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.net.SetProxy</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setUser(java.lang.String)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setUser<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.net.SetProxy</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/TraXLiaison.html#getSystemId(java.io.File)">org.apache.tools.ant.taskdefs.optional.TraXLiaison.getSystemId<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use org.apache.tools.ant.util.JAXPUtils#getSystemId instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/optional/unix/Symlink.html#deleteSymlink(java.io.File)">org.apache.tools.ant.taskdefs.optional.unix.Symlink.deleteSymlink<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/file/Files.html#delete(java.nio.file.Path)" title="class or interface in java.nio.file" class="external-link"><code>Files.delete(Path)</code></a> instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/optional/unix/Symlink.html#deleteSymlink(java.lang.String)">org.apache.tools.ant.taskdefs.optional.unix.Symlink.deleteSymlink<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/file/Files.html#delete(java.nio.file.Path)" title="class or interface in java.nio.file" class="external-link"><code>Files.delete(Path)</code></a> instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/PathConvert.html#setTargetos(java.lang.String)">org.apache.tools.ant.taskdefs.PathConvert.setTargetos<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use the method taking a TargetOs argument instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Property.html#setUserProperty(boolean)">org.apache.tools.ant.taskdefs.Property.setUserProperty<wbr>(boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             This was never a supported feature and has been
             deprecated without replacement.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/SendEmail.html#setMailport(java.lang.Integer)">org.apache.tools.ant.taskdefs.SendEmail.setMailport<wbr>(Integer)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use <a href="org/apache/tools/ant/taskdefs/email/EmailTask.html#setMailport(int)"><code>EmailTask.setMailport(int)</code></a> instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.io.PrintStream)">org.apache.tools.ant.taskdefs.SQLExec.printResults<wbr>(PrintStream)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.
             Use <a href="org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.sql.ResultSet,java.io.PrintStream)"><code>the two arg version</code></a> instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#archiveIsUpToDate(java.lang.String%5B%5D)">org.apache.tools.ant.taskdefs.Tar.archiveIsUpToDate<wbr>(String[])</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             use the two-arg version instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#setLongfile(java.lang.String)">org.apache.tools.ant.taskdefs.Tar.setLongfile<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             setLongFile(String) is deprecated and is replaced with
             setLongFile(Tar.TarLongFileMode) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the mode in its own class.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Tar.html#setTarfile(java.io.File)">org.apache.tools.ant.taskdefs.Tar.setTarfile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             For consistency with other tasks, please use setDestFile().</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Touch.html#touch(java.io.File)">org.apache.tools.ant.taskdefs.Touch.touch<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Tstamp.CustomFormat.html#setUnit(java.lang.String)">org.apache.tools.ant.taskdefs.Tstamp.CustomFormat.setUnit<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             setUnit(String) is deprecated and is replaced with
             setUnit(Tstamp.Unit) to make Ant's
             Introspection mechanism do the work and also to
             encapsulate operations on the unit in its own
             class.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Unpack.html#setDest(java.lang.String)">org.apache.tools.ant.taskdefs.Unpack.setDest<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             setDest(String) is deprecated and is replaced with
             setDest(File) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Unpack.html#setSrc(java.lang.String)">org.apache.tools.ant.taskdefs.Unpack.setSrc<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             setSrc(String) is deprecated and is replaced with
             setSrc(File) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/War.html#setWarfile(java.io.File)">org.apache.tools.ant.taskdefs.War.setWarfile<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use setDestFile(File) instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/XmlProperty.html#getIncludeSementicAttribute()">org.apache.tools.ant.taskdefs.XmlProperty.getIncludeSementicAttribute()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/XSLTProcess.html#configureLiaison(java.io.File)">org.apache.tools.ant.taskdefs.XSLTProcess.configureLiaison<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.7</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/taskdefs/Zip.html#setFile(java.io.File)">org.apache.tools.ant.taskdefs.Zip.setFile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/Zip.html#setZipfile(java.io.File)">org.apache.tools.ant.taskdefs.Zip.setZipfile<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode()">org.apache.tools.ant.types.ArchiveFileSet.getDirMode()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode()">org.apache.tools.ant.types.ArchiveFileSet.getFileMode()</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath()">org.apache.tools.ant.types.ArchiveFileSet.getFullpath()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix()">org.apache.tools.ant.types.ArchiveFileSet.getPrefix()</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/CommandlineJava.html#size()">org.apache.tools.ant.types.CommandlineJava.size()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.
             Please don't use this, it effectively creates the
             entire command.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/DataType.html#getCheckedRef()">org.apache.tools.ant.types.DataType.getCheckedRef()</a></div>
<div class="col-last even-row-color">
<div class="block">use getCheckedRef(Class)</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">org.apache.tools.ant.types.DataType.getCheckedRef<wbr>(Project)</a></div>
<div class="col-last odd-row-color">
<div class="block">use getCheckedRef(Class)</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/Mapper.html#getRef()">org.apache.tools.ant.types.Mapper.getRef()</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.7.1 because a mapper might ref a
             FileNameMapper implementation directly.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/optional/AbstractScriptComponent.html#setManager(java.lang.String)">org.apache.tools.ant.types.optional.AbstractScriptComponent.setManager<wbr>(String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/optional/ScriptFilter.html#setManager(java.lang.String)">org.apache.tools.ant.types.optional.ScriptFilter.setManager<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/optional/ScriptSelector.html#setManager(java.lang.String)">org.apache.tools.ant.types.optional.ScriptSelector.setManager<wbr>(String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/resources/TarResource.html#getGid()">org.apache.tools.ant.types.resources.TarResource.getGid()</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/resources/TarResource.html#getUid()">org.apache.tools.ant.types.resources.TarResource.getUid()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/resources/Union.html#getCollection(boolean)">org.apache.tools.ant.types.resources.Union.getCollection<wbr>(boolean)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#append(java.util.Enumeration,java.util.Enumeration)">org.apache.tools.ant.util.CollectionUtils.append<wbr>(Enumeration&lt;E&gt;, Enumeration&lt;E&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">use Stream.concat(Collections.list(e1).stream(), Collections.list(e2).stream())
                 .collect(Collectors.collectingAndThen(Collectors.toList(), Collections::enumeration))</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#asCollection(java.util.Iterator)">org.apache.tools.ant.util.CollectionUtils.asCollection<wbr>(Iterator&lt;? extends T&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">instantiate a list and use forEachRemaining(list::add)</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#asEnumeration(java.util.Iterator)">org.apache.tools.ant.util.CollectionUtils.asEnumeration<wbr>(Iterator&lt;E&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">use Collections.enumeration()</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#asIterator(java.util.Enumeration)">org.apache.tools.ant.util.CollectionUtils.asIterator<wbr>(Enumeration&lt;E&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">use Collections.list(e).iterator()</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#equals(java.util.Dictionary,java.util.Dictionary)">org.apache.tools.ant.util.CollectionUtils.equals<wbr>(Dictionary&lt;?, ?&gt;, Dictionary&lt;?, ?&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#equals(java.util.Vector,java.util.Vector)">org.apache.tools.ant.util.CollectionUtils.equals<wbr>(Vector&lt;?&gt;, Vector&lt;?&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#flattenToString(java.util.Collection)">org.apache.tools.ant.util.CollectionUtils.flattenToString<wbr>(Collection&lt;?&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">use stream().collect(Collectors.joining(","))</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#frequency(java.util.Collection,java.lang.Object)">org.apache.tools.ant.util.CollectionUtils.frequency<wbr>(Collection&lt;?&gt;, Object)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/CollectionUtils.html#putAll(java.util.Dictionary,java.util.Dictionary)">org.apache.tools.ant.util.CollectionUtils.putAll<wbr>(Dictionary&lt;? super K, ? super V&gt;, Dictionary&lt;? extends K, ? extends V&gt;)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#createTempFile(java.lang.String,java.lang.String,java.io.File)">org.apache.tools.ant.util.FileUtils.createTempFile<wbr>(String, String, File)</a></div>
<div class="col-last even-row-color">
<div class="block">since ant 1.7.1 use createTempFile(Project, String, String, File,
 boolean, boolean) instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#createTempFile(java.lang.String,java.lang.String,java.io.File,boolean)">org.apache.tools.ant.util.FileUtils.createTempFile<wbr>(String, String, File, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">since ant 1.7.1 use createTempFile(Project, String, String, File,
 boolean, boolean) instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#createTempFile(java.lang.String,java.lang.String,java.io.File,boolean,boolean)">org.apache.tools.ant.util.FileUtils.createTempFile<wbr>(String, String, File, boolean, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">since Ant 1.10.8 use createTempFile(Project, String, String, File,
 boolean, boolean) instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#getParentFile(java.io.File)">org.apache.tools.ant.util.FileUtils.getParentFile<wbr>(File)</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7. Just use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getParentFile()" title="class or interface in java.io" class="external-link"><code>File.getParentFile()</code></a> directly.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#isSymbolicLink(java.io.File,java.lang.String)">org.apache.tools.ant.util.FileUtils.isSymbolicLink<wbr>(File, String)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/file/Files.html#isSymbolicLink(java.nio.file.Path)" title="class or interface in java.nio.file" class="external-link"><code>Files.isSymbolicLink(Path)</code></a> instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/FileUtils.html#newFileUtils()">org.apache.tools.ant.util.FileUtils.newFileUtils()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.
             Use getFileUtils instead,
 FileUtils do not have state.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/JavaEnvUtils.html#getJavaVersionNumber()">org.apache.tools.ant.util.JavaEnvUtils.getJavaVersionNumber()</a></div>
<div class="col-last even-row-color">
<div class="block">use #getParsedJavaVersion instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/ScriptRunnerCreator.html#createRunner(java.lang.String,java.lang.String,java.lang.ClassLoader)">org.apache.tools.ant.util.ScriptRunnerCreator.createRunner<wbr>(String, String, ClassLoader)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use <a href="org/apache/tools/ant/util/ScriptRunnerCreator.html#createRunner(org.apache.tools.ant.util.ScriptManager,java.lang.String,java.lang.ClassLoader)"><code>ScriptRunnerCreator.createRunner(ScriptManager,String,ClassLoader)</code></a> instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/util/ScriptRunnerHelper.html#setManager(java.lang.String)">org.apache.tools.ant.util.ScriptRunnerHelper.setManager<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/util/StringUtils.html#replace(java.lang.String,java.lang.String,java.lang.String)">org.apache.tools.ant.util.StringUtils.replace<wbr>(String, String, String)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html#replace(java.lang.CharSequence,java.lang.CharSequence)" title="class or interface in java.lang" class="external-link"><code>String.replace(CharSequence, CharSequence)</code></a> now.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/tar/TarEntry.html#getGroupId()">org.apache.tools.tar.TarEntry.getGroupId()</a></div>
<div class="col-last even-row-color">
<div class="block">use #getLongGroupId instead as group ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/tar/TarEntry.html#getUserId()">org.apache.tools.tar.TarEntry.getUserId()</a></div>
<div class="col-last odd-row-color">
<div class="block">use #getLongUserId instead as user ids can be
 bigger than <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Integer.html#MAX_VALUE" title="class or interface in java.lang" class="external-link"><code>Integer.MAX_VALUE</code></a></div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/zip/ZipEntry.html#setComprSize(long)">org.apache.tools.zip.ZipEntry.setComprSize<wbr>(long)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             Use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipEntry.html#setCompressedSize(long)" title="class or interface in java.util.zip" class="external-link"><code>ZipEntry.setCompressedSize(long)</code></a> directly.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/zip/ZipOutputStream.html#adjustToLong(int)">org.apache.tools.zip.ZipOutputStream.adjustToLong<wbr>(int)</a></div>
<div class="col-last odd-row-color">
<div class="block">use ZipUtil#adjustToLong</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/zip/ZipOutputStream.html#toDosTime(long)">org.apache.tools.zip.ZipOutputStream.toDosTime<wbr>(long)</a></div>
<div class="col-last even-row-color">
<div class="block">use ZipUtil#toDosTime</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/zip/ZipOutputStream.html#toDosTime(java.util.Date)">org.apache.tools.zip.ZipOutputStream.toDosTime<wbr>(Date)</a></div>
<div class="col-last odd-row-color">
<div class="block">use ZipUtil#toDosTime</div>
</div>
</div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div id="constructor">
<div class="caption"><span>Deprecated Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/input/MultipleChoiceInputRequest.html#%3Cinit%3E(java.lang.String,java.util.Vector)">org.apache.tools.ant.input.MultipleChoiceInputRequest<wbr>(String, Vector&lt;String&gt;)</a></div>
<div class="col-last even-row-color">
<div class="block">Use <a href="org/apache/tools/ant/input/MultipleChoiceInputRequest.html#%3Cinit%3E(java.lang.String,java.util.Collection)"><code>MultipleChoiceInputRequest(String,Collection)</code></a> instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/Main.html#%3Cinit%3E(java.lang.String%5B%5D)">org.apache.tools.ant.Main<wbr>(String[])</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.6.x</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/taskdefs/ExecuteWatchdog.html#%3Cinit%3E(int)">org.apache.tools.ant.taskdefs.ExecuteWatchdog<wbr>(int)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.5.x.
             Use constructor with a long type instead.
 (1.4.x compatibility)</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/Reference.html#%3Cinit%3E()">org.apache.tools.ant.types.Reference()</a></div>
<div class="col-last odd-row-color">
<div class="block">since 1.7.
             Please use <a href="org/apache/tools/ant/types/Reference.html#%3Cinit%3E(org.apache.tools.ant.Project,java.lang.String)"><code>Reference(Project,String)</code></a>
             instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/Reference.html#%3Cinit%3E(java.lang.String)">org.apache.tools.ant.types.Reference<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">since 1.7.
             Please use <a href="org/apache/tools/ant/types/Reference.html#%3Cinit%3E(org.apache.tools.ant.Project,java.lang.String)"><code>Reference(Project,String)</code></a>
             instead.</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E()">org.apache.tools.ant.types.resources.FileResourceIterator()</a></div>
<div class="col-last odd-row-color">
<div class="block">in favor of <a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E(org.apache.tools.ant.Project)"><code>FileResourceIterator(Project)</code></a></div>
</div>
<div class="col-summary-item-name even-row-color"><a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E(java.io.File)">org.apache.tools.ant.types.resources.FileResourceIterator<wbr>(File)</a></div>
<div class="col-last even-row-color">
<div class="block">in favor of <a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E(org.apache.tools.ant.Project,java.io.File)"><code>FileResourceIterator(Project, File)</code></a></div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E(java.io.File,java.lang.String%5B%5D)">org.apache.tools.ant.types.resources.FileResourceIterator<wbr>(File, String[])</a></div>
<div class="col-last odd-row-color">
<div class="block">in favor of <a href="org/apache/tools/ant/types/resources/FileResourceIterator.html#%3Cinit%3E(org.apache.tools.ant.Project,java.io.File,java.lang.String%5B%5D)"><code>FileResourceIterator(Project, File, String[])</code></a></div>
</div>
</div>
</div>
</li>
</ul>
<script type="text/javascript">document.addEventListener("DOMContentLoaded", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.disabled = false;
            c.onclick();
        });
    });
window.addEventListener("load", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.onclick();
        });
    });
</script>
</main>
</div>
</div>
</body>
</html>
