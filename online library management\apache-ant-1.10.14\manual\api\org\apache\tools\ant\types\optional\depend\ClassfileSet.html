<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ClassfileSet (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.optional.depend, class: ClassfileSet">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.optional.depend</a></div>
<h1 title="Class ClassfileSet" class="title">Class ClassfileSet</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../../AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</a>
<div class="inheritance"><a href="../../FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</a>
<div class="inheritance">org.apache.tools.ant.types.optional.depend.ClassfileSet</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code>, <code><a href="../../selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ClassfileSet</span>
<span class="extends-implements">extends <a href="../../FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></span></div>
<div class="block">A ClassfileSet is a FileSet that enlists all classes that depend on a
 certain set of root classes.

 ClassfileSet extends FileSet, its inherited properties
 defining the domain searched for dependent classes.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ClassfileSet.ClassRoot.html" class="type-name-link" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet.ClassRoot</a></code></div>
<div class="col-last even-row-color">
<div class="block">Inner class used to contain info about root classes.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checked">checked</a>, <a href="../../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ClassfileSet</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.optional.depend.ClassfileSet)" class="member-name-link">ClassfileSet</a><wbr>(<a href="ClassfileSet.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create a ClassfileSet from another ClassfileSet.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredRoot(org.apache.tools.ant.types.optional.depend.ClassfileSet.ClassRoot)" class="member-name-link">addConfiguredRoot</a><wbr>(<a href="ClassfileSet.ClassRoot.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet.ClassRoot</a>&nbsp;root)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested root class definition to this class file set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addRootFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addRootFileset</a><wbr>(<a href="../../FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;rootFileSet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a fileset to which contains a collection of root classes used to
 drive the search from classes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clone this data type.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirectoryScanner(org.apache.tools.ant.Project)" class="member-name-link">getDirectoryScanner</a><wbr>(<a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the DirectoryScanner associated with this FileSet.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRootClass(java.lang.String)" class="member-name-link">setRootClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rootClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the root class attribute.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.FileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></h3>
<code><a href="../../FileSet.html#getRef(org.apache.tools.ant.Project)">getRef</a>, <a href="../../FileSet.html#isFilesystemOnly()">isFilesystemOnly</a>, <a href="../../FileSet.html#iterator()">iterator</a>, <a href="../../FileSet.html#size()">size</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.AbstractFileSet">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></h3>
<code><a href="../../AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../../AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../../AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../../AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../../AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../../AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../../AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../../AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../../AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../../AbstractFileSet.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="../../AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../../AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../../AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../../AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../../AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../../AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../../AbstractFileSet.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="../../AbstractFileSet.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="../../AbstractFileSet.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="../../AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../../AbstractFileSet.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="../../AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../../AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../../AbstractFileSet.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="../../AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../../AbstractFileSet.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="../../AbstractFileSet.html#appendExcludes(java.lang.String%5B%5D)">appendExcludes</a>, <a href="../../AbstractFileSet.html#appendIncludes(java.lang.String%5B%5D)">appendIncludes</a>, <a href="../../AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="../../AbstractFileSet.html#createExclude()">createExclude</a>, <a href="../../AbstractFileSet.html#createExcludesFile()">createExcludesFile</a>, <a href="../../AbstractFileSet.html#createInclude()">createInclude</a>, <a href="../../AbstractFileSet.html#createIncludesFile()">createIncludesFile</a>, <a href="../../AbstractFileSet.html#createPatternSet()">createPatternSet</a>, <a href="../../AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</a>, <a href="../../AbstractFileSet.html#getDir()">getDir</a>, <a href="../../AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</a>, <a href="../../AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</a>, <a href="../../AbstractFileSet.html#getErrorOnMissingDir()">getErrorOnMissingDir</a>, <a href="../../AbstractFileSet.html#getMaxLevelsOfSymlinks()">getMaxLevelsOfSymlinks</a>, <a href="../../AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../../AbstractFileSet.html#hasPatterns()">hasPatterns</a>, <a href="../../AbstractFileSet.html#hasSelectors()">hasSelectors</a>, <a href="../../AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</a>, <a href="../../AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="../../AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</a>, <a href="../../AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</a>, <a href="../../AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</a>, <a href="../../AbstractFileSet.html#selectorCount()">selectorCount</a>, <a href="../../AbstractFileSet.html#selectorElements()">selectorElements</a>, <a href="../../AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="../../AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="../../AbstractFileSet.html#setDir(java.io.File)">setDir</a>, <a href="../../AbstractFileSet.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="../../AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="../../AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="../../AbstractFileSet.html#setFile(java.io.File)">setFile</a>, <a href="../../AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../../AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="../../AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="../../AbstractFileSet.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="../../AbstractFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../../AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</a>, <a href="../../AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner,org.apache.tools.ant.Project)">setupDirectoryScanner</a>, <a href="../../AbstractFileSet.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../../DataType.html#circularReference()">circularReference</a>, <a href="../../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../../DataType.html#getRefid()">getRefid</a>, <a href="../../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../../DataType.html#isChecked()">isChecked</a>, <a href="../../DataType.html#isReference()">isReference</a>, <a href="../../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../../ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../../ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ClassfileSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ClassfileSet</span>()</div>
<div class="block">Default constructor.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.optional.depend.ClassfileSet)">
<h3>ClassfileSet</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ClassfileSet</span><wbr><span class="parameters">(<a href="ClassfileSet.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet</a>&nbsp;s)</span></div>
<div class="block">Create a ClassfileSet from another ClassfileSet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - the other classfileset.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addRootFileset(org.apache.tools.ant.types.FileSet)">
<h3>addRootFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addRootFileset</span><wbr><span class="parameters">(<a href="../../FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;rootFileSet)</span></div>
<div class="block">Add a fileset to which contains a collection of root classes used to
 drive the search from classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rootFileSet</code> - a root file set to be used to search for dependent
 classes.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRootClass(java.lang.String)">
<h3>setRootClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRootClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rootClass)</span></div>
<div class="block">Set the root class attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rootClass</code> - the name of the root class.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDirectoryScanner(org.apache.tools.ant.Project)">
<h3>getDirectoryScanner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></span>&nbsp;<span class="element-name">getDirectoryScanner</span><wbr><span class="parameters">(<a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Return the DirectoryScanner associated with this FileSet.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../AbstractFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</a></code>&nbsp;in class&nbsp;<code><a href="../../AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the project used to resolve dirs, etc.</dd>
<dt>Returns:</dt>
<dd>a dependency scanner.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredRoot(org.apache.tools.ant.types.optional.depend.ClassfileSet.ClassRoot)">
<h3>addConfiguredRoot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredRoot</span><wbr><span class="parameters">(<a href="ClassfileSet.ClassRoot.html" title="class in org.apache.tools.ant.types.optional.depend">ClassfileSet.ClassRoot</a>&nbsp;root)</span></div>
<div class="block">Add a nested root class definition to this class file set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>root</code> - the configured class root.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Clone this data type.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../FileSet.html#clone()">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></dd>
<dt>Returns:</dt>
<dd>a clone of the class file set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">DataType</a></code></span></div>
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).

 <p>If one is included, throw a BuildException created by <a href="../../DataType.html#circularReference()"><code>circularReference</code></a>.</p>

 <p>This implementation is appropriate only for a DataType that
 cannot hold other DataTypes as children.</p>

 <p>The general contract of this method is that it shouldn't do
 anything if <a href="../../DataType.html#checked"><code>DataType.checked</code></a> is true and
 set it to true on exit.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../AbstractFileSet.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a></code>&nbsp;in class&nbsp;<code><a href="../../AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stk</code> - the stack of references to check.</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
