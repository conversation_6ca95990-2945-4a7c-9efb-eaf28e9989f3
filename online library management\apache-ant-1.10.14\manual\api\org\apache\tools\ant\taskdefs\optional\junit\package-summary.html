<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional.junit (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.optional.junit" class="title">Package org.apache.tools.ant.taskdefs.optional.junit</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.optional.junit</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Transform a JUnit xml report.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AggregateTransformer.Format.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer.Format</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">defines acceptable formats.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Baseclass for BatchTest and JUnitTest.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Create then run <code>JUnitTest</code>'s based on the list of files
 given by the fileset attribute.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BriefJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BriefJUnitResultFormatter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Prints plain text output of the test to a specified Writer.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Constants.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">Constants</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Constants, like filenames shared between various classes in this package.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CustomJUnit4TestAdapterCache.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">CustomJUnit4TestAdapterCache</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Provides a custom implementation of the notifier for a JUnit4TestAdapter
 so that skipped and ignored tests can be reported to the existing
 <code>TestListener</code>s.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DOMUtil.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Some utilities that might be useful when manipulating DOM trees.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="DOMUtil.NodeFilter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Filter interface to be applied when iterating over a DOM tree.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DOMUtil.NodeListImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">DOMUtil.NodeListImpl</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">custom implementation of a nodelist</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Enumerations.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">Enumerations</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FailureRecorder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Collects all failing test <i>cases</i> and creates a new JUnit test class containing
 a suite() method which calls these failed tests.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FailureRecorder.TestInfos.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder.TestInfos</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">TestInfos holds information about a given test for later use.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A wrapper for the implementations of <code>JUnitResultFormatter</code>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FormatterElement.TypeAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement.TypeAttribute</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "plain", "xml", "brief" and "failure".</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="IgnoredTestListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Provides the functionality for TestListeners to be able to be notified of
 the necessary JUnit4 events for test being ignored (@Ignore annotation)
 or skipped (Assume failures).</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IgnoredTestResult.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">IgnoredTestResult</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Records ignored and skipped tests reported as part of the execution of
 JUnit 4 tests.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnit4TestMethodAdapter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnit4TestMethodAdapter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adapter between JUnit 3.8.x API and JUnit 4.x API for execution of tests
 and listening of events (test start, test finish, test failure, test skipped).</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">This Interface describes classes that format the results of a JUnit
 testrun.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Runs JUnit tests.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">These are the different forking options</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnitTask.JUnitLogOutputStream.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A stream handler for handling the junit task.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JUnitTask.JUnitLogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A log stream handler for junit.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Print summary enumeration values.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A value class that contains the result of a test.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Handles the portions of <a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTask</code></a> which need to directly access
 actual JUnit classes, so that junit.jar need not be on Ant's startup classpath.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">The interface that JUnitResultFormatter extends.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Interface that test runners implement.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">The interface that SummaryJUnitResultFormatter extends.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnitTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirrorImpl</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Implementation of the part of the junit task which can directly refer to junit.* classes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Run a single JUnit test.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTestRunner</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Simple Testrunner for JUnit that runs all tests of a testsuite.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JUnitVersionHelper.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitVersionHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Work around for some changes to the public JUnit API between
 different JUnit releases.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="OutErrSummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">OutErrSummaryJUnitResultFormatter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Used instead of SummaryJUnitResultFormatter in forked tests if
 withOutAndErr is requested.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PlainJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">PlainJUnitResultFormatter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Prints plain text output of the test to a specified Writer.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">SummaryJUnitResultFormatter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Prints short summary output of the test to Ant's logging system.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TearDownOnVmCrash.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TearDownOnVmCrash</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Formatter that doesn't create any output but tries to invoke the
 tearDown method on a testcase if that test was forked and caused a
 timeout or VM crash.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TestIgnored.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestIgnored</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TestListenerWrapper.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">TestListenerWrapper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Interface groups XML constants.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XMLJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLJUnitResultFormatter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Prints XML output of the test to a specified Writer.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Aggregates all &lt;junit&gt; XML formatter testsuite data under
 a specific directory and transforms the results via XSLT.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
