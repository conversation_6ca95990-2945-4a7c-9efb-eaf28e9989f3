<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>BigProjectLogger (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.listener, class: BigProjectLogger">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.listener</a></div>
<h1 title="Class BigProjectLogger" class="title">Class BigProjectLogger</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../DefaultLogger.html" title="class in org.apache.tools.ant">org.apache.tools.ant.DefaultLogger</a>
<div class="inheritance"><a href="../NoBannerLogger.html" title="class in org.apache.tools.ant">org.apache.tools.ant.NoBannerLogger</a>
<div class="inheritance"><a href="SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">org.apache.tools.ant.listener.SimpleBigProjectLogger</a>
<div class="inheritance">org.apache.tools.ant.listener.BigProjectLogger</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="../BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code>, <code><a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">BigProjectLogger</span>
<span class="extends-implements">extends <a href="SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a>
implements <a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></span></div>
<div class="block">This is a special logger that is designed to make it easier to work
 with big projects, those that use imports and
 subant to build complex systems.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant1.7.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FOOTER" class="member-name-link">FOOTER</a></code></div>
<div class="col-last even-row-color">
<div class="block">Footer string for the log.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#HEADER" class="member-name-link">HEADER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Header string for the log.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.NoBannerLogger">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></h3>
<code><a href="../NoBannerLogger.html#targetName">targetName</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.DefaultLogger">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></h3>
<code><a href="../DefaultLogger.html#emacsMode">emacsMode</a>, <a href="../DefaultLogger.html#err">err</a>, <a href="../DefaultLogger.html#LEFT_COLUMN_SIZE">LEFT_COLUMN_SIZE</a>, <a href="../DefaultLogger.html#lSep">lSep</a>, <a href="../DefaultLogger.html#msgOutputLevel">msgOutputLevel</a>, <a href="../DefaultLogger.html#out">out</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">BigProjectLogger</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#extractNameOrDefault(org.apache.tools.ant.BuildEvent)" class="member-name-link">extractNameOrDefault</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of an event</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFailedMessage()" class="member-name-link">getBuildFailedMessage</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point: the message that indicates whether
 a build failed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildSuccessfulMessage()" class="member-name-link">getBuildSuccessfulMessage</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point: the message that indicates that
 a build succeeded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFooter()" class="member-name-link">getFooter</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override point: return the footer string for the entry/exit message</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeader()" class="member-name-link">getHeader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override point: return the header string for the entry/exit message</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message for a target if it is of an appropriate
 priority, also logging the name of the target if this
 is the first message which needs to be logged for the
 target.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subBuildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">subBuildFinished</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that the last target has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subBuildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">subBuildStarted</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Signals that a subbuild has started.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Notes the name of the target so it can be logged
 if it generates any messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">No-op implementation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.listener.SimpleBigProjectLogger">Methods inherited from class&nbsp;org.apache.tools.ant.listener.<a href="SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a></h3>
<code><a href="SimpleBigProjectLogger.html#extractTargetName(org.apache.tools.ant.BuildEvent)">extractTargetName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.NoBannerLogger">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></h3>
<code><a href="../NoBannerLogger.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.DefaultLogger">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></h3>
<code><a href="../DefaultLogger.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a>, <a href="../DefaultLogger.html#extractProjectName(org.apache.tools.ant.BuildEvent)">extractProjectName</a>, <a href="../DefaultLogger.html#formatTime(long)">formatTime</a>, <a href="../DefaultLogger.html#getMessageOutputLevel()">getMessageOutputLevel</a>, <a href="../DefaultLogger.html#getTimestamp()">getTimestamp</a>, <a href="../DefaultLogger.html#log(java.lang.String)">log</a>, <a href="../DefaultLogger.html#printMessage(java.lang.String,java.io.PrintStream,int)">printMessage</a>, <a href="../DefaultLogger.html#setEmacsMode(boolean)">setEmacsMode</a>, <a href="../DefaultLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</a>, <a href="../DefaultLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</a>, <a href="../DefaultLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</a>, <a href="../DefaultLogger.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.BuildListener">Methods inherited from interface&nbsp;org.apache.tools.ant.<a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></h3>
<code><a href="../BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a>, <a href="../BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a>, <a href="../BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="HEADER">
<h3>HEADER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">HEADER</span></div>
<div class="block">Header string for the log.
 "======================================================================"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.listener.BigProjectLogger.HEADER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="FOOTER">
<h3>FOOTER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FOOTER</span></div>
<div class="block">Footer string for the log.
 "======================================================================"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.listener.BigProjectLogger.FOOTER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>BigProjectLogger</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BigProjectLogger</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getBuildFailedMessage()">
<h3>getBuildFailedMessage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBuildFailedMessage</span>()</div>
<div class="block">This is an override point: the message that indicates whether
 a build failed. Subclasses can change/enhance the
 message.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DefaultLogger.html#getBuildFailedMessage()">getBuildFailedMessage</a></code>&nbsp;in class&nbsp;<code><a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code></dd>
<dt>Returns:</dt>
<dd>The classic "BUILD FAILED" plus a timestamp</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildSuccessfulMessage()">
<h3>getBuildSuccessfulMessage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBuildSuccessfulMessage</span>()</div>
<div class="block">This is an override point: the message that indicates that
 a build succeeded. Subclasses can change/enhance the
 message.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DefaultLogger.html#getBuildSuccessfulMessage()">getBuildSuccessfulMessage</a></code>&nbsp;in class&nbsp;<code><a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code></dd>
<dt>Returns:</dt>
<dd>The classic "BUILD SUCCESSFUL" plus a timestamp</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Notes the name of the target so it can be logged
 if it generates any messages.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../NoBannerLogger.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in class&nbsp;<code><a href="../NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">No-op implementation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../DefaultLogger.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in class&nbsp;<code><a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../DefaultLogger.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in class&nbsp;<code><a href="../DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Logs a message for a target if it is of an appropriate
 priority, also logging the name of the target if this
 is the first message which needs to be logged for the
 target.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in interface&nbsp;<code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../NoBannerLogger.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in class&nbsp;<code><a href="../NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - BuildEvent</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
<li><a href="../BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subBuildStarted(org.apache.tools.ant.BuildEvent)">
<h3>subBuildStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">subBuildStarted</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a subbuild has started. This event
 is fired before any targets have started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../SubBuildListener.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information. Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extractNameOrDefault(org.apache.tools.ant.BuildEvent)">
<h3>extractNameOrDefault</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">extractNameOrDefault</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Get the name of an event</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - the event name</dd>
<dt>Returns:</dt>
<dd>the name or a default string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subBuildFinished(org.apache.tools.ant.BuildEvent)">
<h3>subBuildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">subBuildFinished</span><wbr><span class="parameters">(<a href="../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that the last target has finished. This event
 will still be fired if an error occurred during the build.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../SubBuildListener.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getHeader()">
<h3>getHeader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getHeader</span>()</div>
<div class="block">Override point: return the header string for the entry/exit message</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the header string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFooter()">
<h3>getFooter</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFooter</span>()</div>
<div class="block">Override point: return the footer string for the entry/exit message</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the footer string</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
