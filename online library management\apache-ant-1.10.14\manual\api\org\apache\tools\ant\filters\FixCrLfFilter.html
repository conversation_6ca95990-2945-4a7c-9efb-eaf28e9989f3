<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>FixCrLfFilter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters, class: FixCrLfFilter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.filters</a></div>
<h1 title="Class FixCrLfFilter" class="title">Class FixCrLfFilter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">java.io.Reader</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">java.io.FilterReader</a>
<div class="inheritance"><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseFilterReader</a>
<div class="inheritance"><a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseParamFilterReader</a>
<div class="inheritance">org.apache.tools.ant.filters.FixCrLfFilter</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Readable.html" title="class or interface in java.lang" class="external-link">Readable</a></code>, <code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code>, <code><a href="../types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">FixCrLfFilter</span>
<span class="extends-implements">extends <a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a>
implements <a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></span></div>
<div class="block">Converts text to local OS formatting conventions, as well as repair text
 damaged by misconfigured or misguided editors or file transfer programs.
 <p>
 This filter can take the following arguments:
 </p>
 <ul>
 <li>eof</li>
 <li>eol</li>
 <li>fixlast</li>
 <li>javafiles</li>
 <li>tab</li>
 <li>tablength</li>
 </ul>
 None of which are required.
 <p>
 This version generalises the handling of EOL characters, and allows for
 CR-only line endings (the standard on Mac systems prior to OS X). Tab
 handling has also been generalised to accommodate any tabwidth from 2 to 80,
 inclusive. Importantly, it can leave untouched any literal TAB characters
 embedded within Java string or character constants.
 </p>
 <p>
 <em>Caution:</em> run with care on carefully formatted files. This may
 sound obvious, but if you don't specify asis, presume that your files are
 going to be modified. If "tabs" is "add" or "remove", whitespace characters
 may be added or removed as necessary. Similarly, for EOLs, eol="asis"
 actually means convert to your native O/S EOL convention while eol="crlf" or
 cr="add" can result in CR characters being removed in one special case
 accommodated, i.e., CRCRLF is regarded as a single EOL to handle cases where
 other programs have converted CRLF into CRCRLF.
</p>
 <p>
 Example:
 </p>
 <pre>
 &lt;&lt;fixcrlf tab=&quot;add&quot; eol=&quot;crlf&quot; eof=&quot;asis&quot;/&gt;
 </pre>
 Or:
 <pre>
 &lt;filterreader classname=&quot;org.apache.tools.ant.filters.FixCrLfFilter&quot;&gt;
   &lt;param eol=&quot;crlf&quot; tab=&quot;asis&quot;/&gt;
  &lt;/filterreader&gt;
 </pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FixCrLfFilter.AddAsisRemove.html" class="type-name-link" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FixCrLfFilter.CrLf.html" class="type-name-link" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf" and "crlf".</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterReader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#in" title="class or interface in java.io" class="external-link">in</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.Reader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#lock" title="class or interface in java.io" class="external-link">lock</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">FixCrLfFilter</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for "dummy" instances.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.Reader)" class="member-name-link">FixCrLfFilter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create a new filtered reader.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new FixCrLfFilter using the passed in Reader for instantiation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEof()" class="member-name-link">getEof</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get how DOS EOF (control-z) characters are being handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEol()" class="member-name-link">getEol</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get how EndOfLine characters are being handled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFixlast()" class="member-name-link">getFixlast</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get whether a missing EOL be added to the final line of the stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJavafiles()" class="member-name-link">getJavafiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get whether the stream is to be treated as though it contains Java
 source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTab()" class="member-name-link">getTab</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return how tab characters are being handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTablength()" class="member-name-link">getTablength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the tab length to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read()" class="member-name-link">read</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the next character in the filtered stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEof(org.apache.tools.ant.filters.FixCrLfFilter.AddAsisRemove)" class="member-name-link">setEof</a><wbr>(<a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a>&nbsp;attr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how DOS EOF (control-z) characters are to be handled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEol(org.apache.tools.ant.filters.FixCrLfFilter.CrLf)" class="member-name-link">setEol</a><wbr>(<a href="FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a>&nbsp;attr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how end of line (EOL) characters are to be handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFixlast(boolean)" class="member-name-link">setFixlast</a><wbr>(boolean&nbsp;fixlast)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify whether a missing EOL will be added to the final line of input.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavafiles(boolean)" class="member-name-link">setJavafiles</a><wbr>(boolean&nbsp;javafiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate whether this stream contains Java source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTab(org.apache.tools.ant.filters.FixCrLfFilter.AddAsisRemove)" class="member-name-link">setTab</a><wbr>(<a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a>&nbsp;attr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how tab characters are to be handled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTablength(int)" class="member-name-link">setTablength</a><wbr>(int&nbsp;tabLength)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify tab length in characters.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseParamFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a></h3>
<code><a href="BaseParamFilterReader.html#getParameters()">getParameters</a>, <a href="BaseParamFilterReader.html#setParameters(org.apache.tools.ant.types.Parameter...)">setParameters</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></h3>
<code><a href="BaseFilterReader.html#getInitialized()">getInitialized</a>, <a href="BaseFilterReader.html#getProject()">getProject</a>, <a href="BaseFilterReader.html#read(char%5B%5D,int,int)">read</a>, <a href="BaseFilterReader.html#readFully()">readFully</a>, <a href="BaseFilterReader.html#readLine()">readLine</a>, <a href="BaseFilterReader.html#setInitialized(boolean)">setInitialized</a>, <a href="BaseFilterReader.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="BaseFilterReader.html#skip(long)">skip</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterReader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#close()" title="class or interface in java.io" class="external-link">close</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#mark(int)" title="class or interface in java.io" class="external-link">mark</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#markSupported()" title="class or interface in java.io" class="external-link">markSupported</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#ready()" title="class or interface in java.io" class="external-link">ready</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#reset()" title="class or interface in java.io" class="external-link">reset</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.Reader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#nullReader()" title="class or interface in java.io" class="external-link">nullReader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(char%5B%5D)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(java.nio.CharBuffer)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#transferTo(java.io.Writer)" title="class or interface in java.io" class="external-link">transferTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>FixCrLfFilter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FixCrLfFilter</span>()</div>
<div class="block">Constructor for "dummy" instances.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BaseFilterReader.html#%3Cinit%3E()"><code>BaseFilterReader()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.Reader)">
<h3>FixCrLfFilter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FixCrLfFilter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Create a new filtered reader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - A Reader object providing the underlying stream. Must not be
            <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</span></div>
<div class="block">Create a new FixCrLfFilter using the passed in Reader for instantiation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ChainableReader.html#chain(java.io.Reader)">chain</a></code>&nbsp;in interface&nbsp;<code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rdr</code> - A Reader object providing the underlying stream. Must not be
            <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a new filter based on this configuration, but filtering the
         specified reader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEof()">
<h3>getEof</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></span>&nbsp;<span class="element-name">getEof</span>()</div>
<div class="block">Get how DOS EOF (control-z) characters are being handled.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>values:
         <ul>
         <li>add: ensure that there is an eof at the end of the file
         <li>asis: leave eof characters alone
         <li>remove: remove any eof character found at the end
         </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEol()">
<h3>getEol</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></span>&nbsp;<span class="element-name">getEol</span>()</div>
<div class="block">Get how EndOfLine characters are being handled.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>values:
         <ul>
         <li>asis: convert line endings to your O/S convention
         <li>cr: convert line endings to CR
         <li>lf: convert line endings to LF
         <li>crlf: convert line endings to CRLF
         </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFixlast()">
<h3>getFixlast</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getFixlast</span>()</div>
<div class="block">Get whether a missing EOL be added to the final line of the stream.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if a filtered file will always end with an EOL</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJavafiles()">
<h3>getJavafiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getJavafiles</span>()</div>
<div class="block">Get whether the stream is to be treated as though it contains Java
 source.
 <P>
 This attribute is only used in association with the &quot;<i><b>tab</b></i>&quot;
 attribute. Tabs found in Java literals are protected from changes by this
 filter.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if whitespace in Java character and string literals is
         ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTab()">
<h3>getTab</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></span>&nbsp;<span class="element-name">getTab</span>()</div>
<div class="block">Return how tab characters are being handled.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>values:
         <ul>
         <li>add: convert sequences of spaces which span a tab stop to
         tabs
         <li>asis: leave tab and space characters alone
         <li>remove: convert tabs to spaces
         </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTablength()">
<h3>getTablength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getTablength</span>()</div>
<div class="block">Get the tab length to use.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the length of tab in spaces</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="read()">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span>()
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Return the next character in the filtered stream.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#read()" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></code></dd>
<dt>Returns:</dt>
<dd>the next character in the resulting stream, or -1 if the end of
         the resulting stream has been reached.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the underlying stream throws an IOException during
                reading.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEof(org.apache.tools.ant.filters.FixCrLfFilter.AddAsisRemove)">
<h3>setEof</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEof</span><wbr><span class="parameters">(<a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a>&nbsp;attr)</span></div>
<div class="block">Specify how DOS EOF (control-z) characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
            <ul>
            <li>add: ensure that there is an eof at the end of the file
            <li>asis: leave eof characters alone
            <li>remove: remove any eof character found at the end
            </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEol(org.apache.tools.ant.filters.FixCrLfFilter.CrLf)">
<h3>setEol</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEol</span><wbr><span class="parameters">(<a href="FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a>&nbsp;attr)</span></div>
<div class="block">Specify how end of line (EOL) characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
            <ul>
            <li>asis: convert line endings to your O/S convention
            <li>cr: convert line endings to CR
            <li>lf: convert line endings to LF
            <li>crlf: convert line endings to CRLF
            </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFixlast(boolean)">
<h3>setFixlast</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFixlast</span><wbr><span class="parameters">(boolean&nbsp;fixlast)</span></div>
<div class="block">Specify whether a missing EOL will be added to the final line of input.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fixlast</code> - if true a missing EOL will be appended.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavafiles(boolean)">
<h3>setJavafiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavafiles</span><wbr><span class="parameters">(boolean&nbsp;javafiles)</span></div>
<div class="block">Indicate whether this stream contains Java source.

 This attribute is only used in association with the &quot;<i><b>tab</b></i>&quot;
 attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>javafiles</code> - set to true to prevent this filter from changing tabs found in
            Java literals.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTab(org.apache.tools.ant.filters.FixCrLfFilter.AddAsisRemove)">
<h3>setTab</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTab</span><wbr><span class="parameters">(<a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a>&nbsp;attr)</span></div>
<div class="block">Specify how tab characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
            <ul>
            <li>add: convert sequences of spaces which span a tab stop to
            tabs
            <li>asis: leave tab and space characters alone
            <li>remove: convert tabs to spaces
            </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTablength(int)">
<h3>setTablength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTablength</span><wbr><span class="parameters">(int&nbsp;tabLength)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Specify tab length in characters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tabLength</code> - specify the length of tab in spaces. Valid values are between
            2 and 80 inclusive. The default for this parameter is 8.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
