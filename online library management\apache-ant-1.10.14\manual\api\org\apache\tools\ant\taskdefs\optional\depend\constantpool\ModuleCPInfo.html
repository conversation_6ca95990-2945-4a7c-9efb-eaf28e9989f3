<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ModuleCPInfo (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.depend.constantpool, class: ModuleCPInfo">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.depend.constantpool</a></div>
<h1 title="Class ModuleCPInfo" class="title">Class ModuleCPInfo</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry</a>
<div class="inheritance"><a href="ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantCPInfo</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.depend.constantpool.ModuleCPInfo</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ModuleCPInfo</span>
<span class="extends-implements">extends <a href="ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantCPInfo</a></span></div>
<div class="block">Represents the module info constant pool entry</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></h3>
<code><a href="ConstantPoolEntry.html#CONSTANT_CLASS">CONSTANT_CLASS</a>, <a href="ConstantPoolEntry.html#CONSTANT_DOUBLE">CONSTANT_DOUBLE</a>, <a href="ConstantPoolEntry.html#CONSTANT_FIELDREF">CONSTANT_FIELDREF</a>, <a href="ConstantPoolEntry.html#CONSTANT_FLOAT">CONSTANT_FLOAT</a>, <a href="ConstantPoolEntry.html#CONSTANT_INTEGER">CONSTANT_INTEGER</a>, <a href="ConstantPoolEntry.html#CONSTANT_INTERFACEMETHODREF">CONSTANT_INTERFACEMETHODREF</a>, <a href="ConstantPoolEntry.html#CONSTANT_INVOKEDYNAMIC">CONSTANT_INVOKEDYNAMIC</a>, <a href="ConstantPoolEntry.html#CONSTANT_LONG">CONSTANT_LONG</a>, <a href="ConstantPoolEntry.html#CONSTANT_METHODHANDLE">CONSTANT_METHODHANDLE</a>, <a href="ConstantPoolEntry.html#CONSTANT_METHODREF">CONSTANT_METHODREF</a>, <a href="ConstantPoolEntry.html#CONSTANT_METHODTYPE">CONSTANT_METHODTYPE</a>, <a href="ConstantPoolEntry.html#CONSTANT_MODULEINFO">CONSTANT_MODULEINFO</a>, <a href="ConstantPoolEntry.html#CONSTANT_NAMEANDTYPE">CONSTANT_NAMEANDTYPE</a>, <a href="ConstantPoolEntry.html#CONSTANT_PACKAGEINFO">CONSTANT_PACKAGEINFO</a>, <a href="ConstantPoolEntry.html#CONSTANT_STRING">CONSTANT_STRING</a>, <a href="ConstantPoolEntry.html#CONSTANT_UTF8">CONSTANT_UTF8</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ModuleCPInfo</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read(java.io.DataInputStream)" class="member-name-link">read</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">read a constant pool entry from a class stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)" class="member-name-link">resolve</a><wbr>(<a href="ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a>&nbsp;constantPool)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resolve this constant pool entry with respect to its dependents in
 the constant pool.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantCPInfo">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantCPInfo</a></h3>
<code><a href="ConstantCPInfo.html#getValue()">getValue</a>, <a href="ConstantCPInfo.html#setValue(java.lang.Object)">setValue</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.depend.constantpool.<a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></h3>
<code><a href="ConstantPoolEntry.html#getNumEntries()">getNumEntries</a>, <a href="ConstantPoolEntry.html#getTag()">getTag</a>, <a href="ConstantPoolEntry.html#isResolved()">isResolved</a>, <a href="ConstantPoolEntry.html#readEntry(java.io.DataInputStream)">readEntry</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ModuleCPInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ModuleCPInfo</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="read(java.io.DataInputStream)">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</span>
          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="ConstantPoolEntry.html#read(java.io.DataInputStream)">ConstantPoolEntry</a></code></span></div>
<div class="block">read a constant pool entry from a class stream.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ConstantPoolEntry.html#read(java.io.DataInputStream)">read</a></code>&nbsp;in class&nbsp;<code><a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></code></dd>
<dt>Parameters:</dt>
<dd><code>cpStream</code> - the DataInputStream which contains the constant pool
      entry to be read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading the entry from
      the stream.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">
<h3>resolve</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resolve</span><wbr><span class="parameters">(<a href="ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a>&nbsp;constantPool)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="ConstantPoolEntry.html#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">ConstantPoolEntry</a></code></span></div>
<div class="block">Resolve this constant pool entry with respect to its dependents in
 the constant pool.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ConstantPoolEntry.html#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">resolve</a></code>&nbsp;in class&nbsp;<code><a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></code></dd>
<dt>Parameters:</dt>
<dd><code>constantPool</code> - the constant pool of which this entry is a member
      and against which this entry is to be resolved.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
