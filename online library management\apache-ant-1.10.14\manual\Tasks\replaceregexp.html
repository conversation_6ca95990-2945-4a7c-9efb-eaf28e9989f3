<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ReplaceRegExp Task</title>
</head>
<body>

<h2 id="replaceregexp">ReplaceRegExp</h2>
<h3>Description</h3>
<p><code>ReplaceRegExp</code> is a directory based task for replacing the occurrence of a given
regular expression with a substitution pattern in a selected file or set of files.</p>

<p>The output file is only written if it differs from the existing
file.  This prevents spurious rebuilds based on unchanged files which
have been regenerated by this task. In order to assess whether a file
has changed, this task will create a pre-processed version of the
source file inside of the <a href="../running.html#tmpdir">temporary
directory</a>.</p>

<p>Similar to <a href="../Types/mapper.html#regexp-mapper">regexp type mappers</a> this task needs a
supporting regular expression library and an implementation
of <code class="code">org.apache.tools.ant.util.regexp.Regexp</code>.  See details in the
documentation of the <a href="../Types/regexp.html#implementation">Regexp Type</a>.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>file for which the regular expression should be replaced.</td>
    <td>Yes, unless nested <code>&lt;fileset&gt;</code> is used</td>
  </tr>
  <tr>
    <td>match</td>
    <td>The regular expression pattern to match in the file(s)</td>
    <td>Yes, unless nested <code>&lt;regexp&gt;</code> is used</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>The substitution pattern to place in the file(s) in place of the regular expression.</td>
    <td>Yes, unless nested <code>&lt;substitution&gt;</code> is used</td>
  </tr>
  <tr>
    <td>flags</td>
    <td>The flags to use when matching the regular expression.  For more information, consult the
      Perl 5 syntax<br/>
      <q>g</q> : Global replacement.  Replace all occurrences found<br/>
      <q>i</q> : Case Insensitive.  Do not consider case in the match<br/>
      <q>m</q> : Multiline.  Treat the string as multiple lines of input, using <q>^</q>
      and <q>$</q> as the start or end of any line, respectively, rather than start or end of
      string.<br/>
      <q>s</q> : Singleline.  Treat the string as a single line of input, using <q>.</q> to match
      any character, including a newline, which normally, it would not match.
    </td>
    <td>No</td>
  </tr>
  <tr>
    <td>byline</td>
    <td>Process the file(s) one line at a time, executing the replacement on one line at a time
      (<q>true|false</q>).  This is useful if you want to only replace the first occurrence of a
      regular expression on each line, which is not easy to do when processing the file as a
      whole.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the file. <em>since Apache Ant 1.6</em></td>
    <td>No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>preserveLastModified</td>
    <td>Keep the file timestamp(s) even if the file(s) is(are) modified.  <em>since Ant
      1.8.0</em>.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
</table>
<h3>Examples</h3>
<pre>
&lt;replaceregexp file=&quot;${src}/build.properties&quot;
               match=&quot;OldProperty=(.*)&quot;
               replace=&quot;NewProperty=\1&quot;
               byline=&quot;true&quot;/&gt;</pre>
<p>replaces occurrences of the property name <q>OldProperty</q> with <q>NewProperty</q> in a
properties file, preserving the existing value, in the file <samp>${src}/build.properties</samp></p>

<h3>Parameters specified as nested elements</h3>
<p>This task supports a nested <a href="../Types/fileset.html">FileSet</a> element.</p>
<p><em>Since Ant 1.8.0</em>, this task supports any filesystem
based <a href="../Types/resources.html#collection">resource collections</a> as nested elements.</p>
<p>This task supports a nested <a href="../Types/regexp.html">Regexp</a> element to specify the
regular expression.  You can use this element to refer to a previously defined regular expression
datatype instance.</p>

<pre>
&lt;regexp id="id" pattern="alpha(.+)beta"/&gt;
&lt;regexp refid="id"/&gt;</pre>

<p>This task supports a nested <code>substitution</code> element to specify the substitution
pattern.  You can use this element to refer to a previously defined substitution pattern datatype
instance.</p>

<pre>
&lt;substitution id="id" expression="beta\1alpha"/&gt;<br/>
&lt;substitution refid="id"/&gt;</pre>

<h3>Examples</h3>

<p>Replace occurrences of the property name <q>OldProperty</q> with <q>NewProperty</q> in a
properties file, preserving the existing value, in all files ending in <samp>.properties</samp> in
the current directory:</p>
<pre>
&lt;replaceregexp byline=&quot;true&quot;&gt;
    &lt;regexp pattern=&quot;OldProperty=(.*)&quot;/&gt;
    &lt;substitution expression=&quot;NewProperty=\1&quot;/&gt;
    &lt;fileset dir=&quot;.&quot;&gt;
        &lt;include name=&quot;*.properties&quot;/&gt;
    &lt;/fileset&gt;
&lt;/replaceregexp&gt;</pre>

<p>Replace all whitespaces (blanks, tabs, etc) by one blank remaining the line separator:</p>
<pre>
&lt;replaceregexp match="\s+" replace=" " flags="g" byline="true"&gt;
    &lt;fileset dir="${html.dir}" includes="**/*.html"/&gt;
&lt;/replaceregexp&gt;</pre>
<p>Then, input</p>
<pre>
&lt;html&gt;    &lt;body&gt;
&lt;&lt;TAB&gt;&gt;&lt;h1&gt;    T E S T   &lt;/h1&gt;  &lt;&lt;TAB&gt;&gt;
&lt;&lt;TAB&gt;&gt; &lt;/body&gt;&lt;/html&gt;</pre>
<p>is converted to</p>
<pre>
&lt;html&gt; &lt;body&gt;
 &lt;h1&gt; T E S T &lt;/h1&gt; &lt;/body&gt;&lt;/html&gt;</pre>

<p>The task</p>
<pre>
&lt;replaceregexp match="\\n" replace="${line.separator}" flags="g" byline="true"&gt;
    &lt;fileset dir="${dir}"/&gt;
&lt;/replaceregexp&gt;</pre>
<p>replaces all <q>\n</q> markers (beware the quoting of the backslash) by a line break. Then,
input</p>
<pre>one\ntwo\nthree</pre>
<p>is converted to</p>
<pre>
one
two
three</pre>

<p>Beware that inserting line breaks could break file syntax. For example in XML:</p>
<pre>
&lt;root&gt;
    &lt;text&gt;line breaks \n should work in text&lt;/text&gt;
    &lt;attribute value=&quot;but breaks \n attributes&quot;/&gt;
&lt;/root&gt;</pre>

</body>
</html>
