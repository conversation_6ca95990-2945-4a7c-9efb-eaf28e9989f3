<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectHelper2.TargetHandler (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.helper, class: ProjectHelper2, class: TargetHandler">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.helper</a></div>
<h1 title="Class ProjectHelper2.TargetHandler" class="title">Class ProjectHelper2.TargetHandler</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">org.apache.tools.ant.helper.ProjectHelper2.AntHandler</a>
<div class="inheritance">org.apache.tools.ant.helper.ProjectHelper2.TargetHandler</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">ProjectHelper2.TargetHandler</span>
<span class="extends-implements">extends <a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></span></div>
<div class="block">Handler for "target" and "extension-point" elements.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TargetHandler</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onEndElement(java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onEndElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle the end of the project, sets the current target of the
 context to be the implicit target.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStartChild(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onStartChild</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the start of an element within a target.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStartElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onStartElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialisation routine called after handler creation
 with the element name and attributes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.helper.ProjectHelper2.AntHandler">Methods inherited from class&nbsp;org.apache.tools.ant.helper.<a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></h3>
<code><a href="ProjectHelper2.AntHandler.html#characters(char%5B%5D,int,int,org.apache.tools.ant.helper.AntXMLContext)">characters</a>, <a href="ProjectHelper2.AntHandler.html#checkNamespace(java.lang.String)">checkNamespace</a>, <a href="ProjectHelper2.AntHandler.html#onEndChild(java.lang.String,java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)">onEndChild</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TargetHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TargetHandler</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="onStartElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onStartElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onStartElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Initialisation routine called after handler creation
 with the element name and attributes. The attributes which
 this handler can deal with are: <code>"name"</code>,
 <code>"depends"</code>, <code>"if"</code>,
 <code>"unless"</code>, <code>"id"</code> and
 <code>"description"</code>.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProjectHelper2.AntHandler.html#onStartElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">onStartElement</a></code>&nbsp;in class&nbsp;<code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace URI for this element.</dd>
<dd><code>tag</code> - Name of the element which caused this handler
            to be created. Should not be <code>null</code>.
            Ignored in this implementation.</dd>
<dd><code>qname</code> - The qualified name for this element.</dd>
<dd><code>attrs</code> - Attributes of the element which caused this
              handler to be created. Must not be <code>null</code>.</dd>
<dd><code>context</code> - The current context.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if an unexpected attribute is encountered
            or if the <code>"name"</code> attribute is missing.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onStartChild(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onStartChild</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></span>&nbsp;<span class="element-name">onStartChild</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handles the start of an element within a target.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProjectHelper2.AntHandler.html#onStartChild(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">onStartChild</a></code>&nbsp;in class&nbsp;<code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace URI for this element.</dd>
<dd><code>name</code> - The name of the element being started.
            Will not be <code>null</code>.</dd>
<dd><code>qname</code> - The qualified name for this element.</dd>
<dd><code>attrs</code> - Attributes of the element being started.
              Will not be <code>null</code>.</dd>
<dd><code>context</code> - The current context.</dd>
<dt>Returns:</dt>
<dd>an element handler.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if an error occurs when initialising
                              the appropriate child handler</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onEndElement(java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onEndElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onEndElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span></div>
<div class="block">Handle the end of the project, sets the current target of the
 context to be the implicit target.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProjectHelper2.AntHandler.html#onEndElement(java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)">onEndElement</a></code>&nbsp;in class&nbsp;<code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace URI of the element.</dd>
<dd><code>tag</code> - The name of the element.</dd>
<dd><code>context</code> - The current context.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
