<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>NetRexxC (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional, class: NetRexxC">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<h1 title="Class NetRexxC" class="title">Class NetRexxC</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.NetRexxC</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">NetRexxC</span>
<span class="extends-implements">extends <a href="../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></span></div>
<div class="block">Compiles NetRexx source files.
 This task can take the following
 arguments:
 <ul>
 <li>binary</li>
 <li>classpath</li>
 <li>comments</li>
 <li>compile</li>
 <li>console</li>
 <li>crossref</li>
 <li>decimal</li>
 <li>destdir</li>
 <li>diag</li>
 <li>explicit</li>
 <li>format</li>
 <li>keep</li>
 <li>logo</li>
 <li>replace</li>
 <li>savelog</li>
 <li>srcdir</li>
 <li>sourcedir</li>
 <li>strictargs</li>
 <li>strictassign</li>
 <li>strictcase</li>
 <li>strictimport</li>
 <li>symbols</li>
 <li>time</li>
 <li>trace</li>
 <li>utf8</li>
 <li>verbose</li>
 <li>suppressMethodArgumentNotUsed</li>
 <li>suppressPrivatePropertyNotUsed</li>
 <li>suppressVariableNotUsed</li>
 <li>suppressExceptionNotSignalled</li>
 <li>suppressDeprecation</li>
 <li>removeKeepExtension</li>
 </ul>
 Of these arguments, the <b>srcdir</b> argument is required.

 <p>When this task executes, it will recursively scan the srcdir
 looking for NetRexx source files to compile. This task makes its
 compile decision based on timestamp.</p>
 <p>Before files are compiled they and any other file in the
 srcdir will be copied to the destdir allowing support files to be
 located properly in the classpath. The reason for copying the source files
 before the compile is that NetRexxC has only two destinations for classfiles:</p>
 <ol>
 <li>The current directory, and,</li>
 <li>The directory the source is in (see sourcedir option)
 </ol></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="NetRexxC.TraceAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerated class corresponding to the trace attribute.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="NetRexxC.VerboseAttr.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enumerated class corresponding to the verbose attribute.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="../MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">NetRexxC</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the task - performs the actual compiler call.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">init-Method sets defaults from Properties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBinary(boolean)" class="member-name-link">setBinary</a><wbr>(boolean&nbsp;binary)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether literals are treated as binary, rather than NetRexx types.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(java.lang.String)" class="member-name-link">setClasspath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath used for NetRexx compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComments(boolean)" class="member-name-link">setComments</a><wbr>(boolean&nbsp;comments)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether comments are passed through to the generated java source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompact(boolean)" class="member-name-link">setCompact</a><wbr>(boolean&nbsp;compact)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether error messages come out in compact or verbose format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompile(boolean)" class="member-name-link">setCompile</a><wbr>(boolean&nbsp;compile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether the NetRexx compiler should compile the generated java code.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConsole(boolean)" class="member-name-link">setConsole</a><wbr>(boolean&nbsp;console)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not compiler messages should be displayed on the 'console'.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCrossref(boolean)" class="member-name-link">setCrossref</a><wbr>(boolean&nbsp;crossref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether variable cross references are generated.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDecimal(boolean)" class="member-name-link">setDecimal</a><wbr>(boolean&nbsp;decimal)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether decimal arithmetic should be used for the netrexx code.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestDir(java.io.File)" class="member-name-link">setDestDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDirName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory into which the NetRexx source files
 should be copied and then compiled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDiag(boolean)" class="member-name-link">setDiag</a><wbr>(boolean&nbsp;diag)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether diagnostic information about the compile is generated</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExplicit(boolean)" class="member-name-link">setExplicit</a><wbr>(boolean&nbsp;explicit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether variables must be declared explicitly before use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFormat(boolean)" class="member-name-link">setFormat</a><wbr>(boolean&nbsp;format)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the generated java code is formatted nicely or left to match
 NetRexx line numbers for call stack debugging.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJava(boolean)" class="member-name-link">setJava</a><wbr>(boolean&nbsp;java)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the generated java code is produced.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeep(boolean)" class="member-name-link">setKeep</a><wbr>(boolean&nbsp;keep)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether the generated java source file should be kept after
 compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogo(boolean)" class="member-name-link">setLogo</a><wbr>(boolean&nbsp;logo)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the compiler text logo is displayed when compiling.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRemoveKeepExtension(boolean)" class="member-name-link">setRemoveKeepExtension</a><wbr>(boolean&nbsp;removeKeepExtension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells whether the trailing .keep in nocompile-mode should be removed
 so that the resulting java source really ends on .java.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReplace(boolean)" class="member-name-link">setReplace</a><wbr>(boolean&nbsp;replace)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the generated .java file should be replaced when compiling.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSavelog(boolean)" class="member-name-link">setSavelog</a><wbr>(boolean&nbsp;savelog)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether the compiler messages will be written to NetRexxC.log as
 well as to the console.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSourcedir(boolean)" class="member-name-link">setSourcedir</a><wbr>(boolean&nbsp;sourcedir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells the NetRexx compiler to store the class files in the same
 directory as the source files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcDir(java.io.File)" class="member-name-link">setSrcDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDirName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source dir to find the source Java files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictargs(boolean)" class="member-name-link">setStrictargs</a><wbr>(boolean&nbsp;strictargs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells the NetRexx compiler that method calls always need parentheses,
 even if no arguments are needed, e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictassign(boolean)" class="member-name-link">setStrictassign</a><wbr>(boolean&nbsp;strictassign)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells the NetRexx compile that assignments must match exactly on type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictcase(boolean)" class="member-name-link">setStrictcase</a><wbr>(boolean&nbsp;strictcase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies whether the NetRexx compiler should be case sensitive or not.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictimport(boolean)" class="member-name-link">setStrictimport</a><wbr>(boolean&nbsp;strictimport)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether classes need to be imported explicitly using an <code>import</code>
 statement.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictprops(boolean)" class="member-name-link">setStrictprops</a><wbr>(boolean&nbsp;strictprops)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether local properties need to be qualified explicitly using
 <code>this</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrictsignal(boolean)" class="member-name-link">setStrictsignal</a><wbr>(boolean&nbsp;strictsignal)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the compiler should force catching of exceptions by explicitly
 named types.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressDeprecation(boolean)" class="member-name-link">setSuppressDeprecation</a><wbr>(boolean&nbsp;suppressDeprecation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells whether we should filter out any deprecation-messages
 of the compiler out.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressExceptionNotSignalled(boolean)" class="member-name-link">setSuppressExceptionNotSignalled</a><wbr>(boolean&nbsp;suppressExceptionNotSignalled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the task should suppress the "FooException is in SIGNALS list
 but is not signalled within the method", which is sometimes rather
 useless.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressMethodArgumentNotUsed(boolean)" class="member-name-link">setSuppressMethodArgumentNotUsed</a><wbr>(boolean&nbsp;suppressMethodArgumentNotUsed)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the task should suppress the "Method argument is not used" in
 strictargs-Mode, which can not be suppressed by the compiler itself.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressPrivatePropertyNotUsed(boolean)" class="member-name-link">setSuppressPrivatePropertyNotUsed</a><wbr>(boolean&nbsp;suppressPrivatePropertyNotUsed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the task should suppress the "Private property is defined but
 not used" in strictargs-Mode, which can be quite annoying while
 developing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressVariableNotUsed(boolean)" class="member-name-link">setSuppressVariableNotUsed</a><wbr>(boolean&nbsp;suppressVariableNotUsed)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the task should suppress the "Variable is set but not used" in
 strictargs-Mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSymbols(boolean)" class="member-name-link">setSymbols</a><wbr>(boolean&nbsp;symbols)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether debug symbols should be generated into the class file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTime(boolean)" class="member-name-link">setTime</a><wbr>(boolean&nbsp;time)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Asks the NetRexx compiler to print compilation times to the console
 Valid true values are "yes", "on" or "true".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrace(java.lang.String)" class="member-name-link">setTrace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;trace)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrace(org.apache.tools.ant.taskdefs.optional.NetRexxC.TraceAttr)" class="member-name-link">setTrace</a><wbr>(<a href="NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a>&nbsp;trace)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUtf8(boolean)" class="member-name-link">setUtf8</a><wbr>(boolean&nbsp;utf8)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells the NetRexx compiler that the source is in UTF8.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(java.lang.String)" class="member-name-link">setVerbose</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;verbose)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether lots of warnings and error messages should be generated</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(org.apache.tools.ant.taskdefs.optional.NetRexxC.VerboseAttr)" class="member-name-link">setVerbose</a><wbr>(<a href="NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a>&nbsp;verbose)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether lots of warnings and error messages should be generated</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="../MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="../MatchingTask.html#createExclude()">createExclude</a>, <a href="../MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="../MatchingTask.html#createInclude()">createInclude</a>, <a href="../MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="../MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="../MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="../MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="../MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="../MatchingTask.html#selectorCount()">selectorCount</a>, <a href="../MatchingTask.html#selectorElements()">selectorElements</a>, <a href="../MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="../MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="../MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="../MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="../MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="../MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="../MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="../MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="../MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>NetRexxC</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">NetRexxC</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setBinary(boolean)">
<h3>setBinary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBinary</span><wbr><span class="parameters">(boolean&nbsp;binary)</span></div>
<div class="block">Set whether literals are treated as binary, rather than NetRexx types.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>binary</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(java.lang.String)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath used for NetRexx compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setComments(boolean)">
<h3>setComments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComments</span><wbr><span class="parameters">(boolean&nbsp;comments)</span></div>
<div class="block">Set whether comments are passed through to the generated java source.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comments</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompact(boolean)">
<h3>setCompact</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompact</span><wbr><span class="parameters">(boolean&nbsp;compact)</span></div>
<div class="block">Set whether error messages come out in compact or verbose format.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compact</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompile(boolean)">
<h3>setCompile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompile</span><wbr><span class="parameters">(boolean&nbsp;compile)</span></div>
<div class="block">Set whether the NetRexx compiler should compile the generated java code.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is true.
 Setting this flag to false, will automatically set the keep flag to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compile</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setConsole(boolean)">
<h3>setConsole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setConsole</span><wbr><span class="parameters">(boolean&nbsp;console)</span></div>
<div class="block">Set whether or not compiler messages should be displayed on the 'console'.
 Note that this task will rely on the default value for filtering compile messages.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>console</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCrossref(boolean)">
<h3>setCrossref</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCrossref</span><wbr><span class="parameters">(boolean&nbsp;crossref)</span></div>
<div class="block">Whether variable cross references are generated.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>crossref</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDecimal(boolean)">
<h3>setDecimal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDecimal</span><wbr><span class="parameters">(boolean&nbsp;decimal)</span></div>
<div class="block">Set whether decimal arithmetic should be used for the netrexx code.
 Setting this to off will report decimal arithmetic as an error, for
 performance critical applications.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>decimal</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestDir(java.io.File)">
<h3>setDestDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDirName)</span></div>
<div class="block">Set the destination directory into which the NetRexx source files
 should be copied and then compiled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destDirName</code> - the destination directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDiag(boolean)">
<h3>setDiag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDiag</span><wbr><span class="parameters">(boolean&nbsp;diag)</span></div>
<div class="block">Whether diagnostic information about the compile is generated</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>diag</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExplicit(boolean)">
<h3>setExplicit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExplicit</span><wbr><span class="parameters">(boolean&nbsp;explicit)</span></div>
<div class="block">Sets whether variables must be declared explicitly before use.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>explicit</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFormat(boolean)">
<h3>setFormat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFormat</span><wbr><span class="parameters">(boolean&nbsp;format)</span></div>
<div class="block">Whether the generated java code is formatted nicely or left to match
 NetRexx line numbers for call stack debugging.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>format</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJava(boolean)">
<h3>setJava</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJava</span><wbr><span class="parameters">(boolean&nbsp;java)</span></div>
<div class="block">Whether the generated java code is produced.
 This is not implemented yet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>java</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeep(boolean)">
<h3>setKeep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeep</span><wbr><span class="parameters">(boolean&nbsp;keep)</span></div>
<div class="block">Sets whether the generated java source file should be kept after
 compilation. The generated files will have an extension of .java.keep,
 <b>not</b> .java. See setRemoveKeepExtension
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keep</code> - a <code>boolean</code> value.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setRemoveKeepExtension(boolean)"><code>setRemoveKeepExtension(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogo(boolean)">
<h3>setLogo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogo</span><wbr><span class="parameters">(boolean&nbsp;logo)</span></div>
<div class="block">Whether the compiler text logo is displayed when compiling.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>logo</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setReplace(boolean)">
<h3>setReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReplace</span><wbr><span class="parameters">(boolean&nbsp;replace)</span></div>
<div class="block">Whether the generated .java file should be replaced when compiling.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>replace</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSavelog(boolean)">
<h3>setSavelog</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSavelog</span><wbr><span class="parameters">(boolean&nbsp;savelog)</span></div>
<div class="block">Sets whether the compiler messages will be written to NetRexxC.log as
 well as to the console.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>savelog</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSourcedir(boolean)">
<h3>setSourcedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSourcedir</span><wbr><span class="parameters">(boolean&nbsp;sourcedir)</span></div>
<div class="block">Tells the NetRexx compiler to store the class files in the same
 directory as the source files. The alternative is the working directory.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourcedir</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSrcDir(java.io.File)">
<h3>setSrcDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDirName)</span></div>
<div class="block">Set the source dir to find the source Java files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDirName</code> - the source directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictargs(boolean)">
<h3>setStrictargs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictargs</span><wbr><span class="parameters">(boolean&nbsp;strictargs)</span></div>
<div class="block">Tells the NetRexx compiler that method calls always need parentheses,
 even if no arguments are needed, e.g. <code>aStringVar.getBytes</code>
 vs. <code>aStringVar.getBytes()</code>.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictargs</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictassign(boolean)">
<h3>setStrictassign</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictassign</span><wbr><span class="parameters">(boolean&nbsp;strictassign)</span></div>
<div class="block">Tells the NetRexx compile that assignments must match exactly on type.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictassign</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictcase(boolean)">
<h3>setStrictcase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictcase</span><wbr><span class="parameters">(boolean&nbsp;strictcase)</span></div>
<div class="block">Specifies whether the NetRexx compiler should be case sensitive or not.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictcase</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictimport(boolean)">
<h3>setStrictimport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictimport</span><wbr><span class="parameters">(boolean&nbsp;strictimport)</span></div>
<div class="block">Sets whether classes need to be imported explicitly using an <code>import</code>
 statement. By default the NetRexx compiler will import certain packages
 automatically.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictimport</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictprops(boolean)">
<h3>setStrictprops</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictprops</span><wbr><span class="parameters">(boolean&nbsp;strictprops)</span></div>
<div class="block">Sets whether local properties need to be qualified explicitly using
 <code>this</code>.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictprops</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrictsignal(boolean)">
<h3>setStrictsignal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrictsignal</span><wbr><span class="parameters">(boolean&nbsp;strictsignal)</span></div>
<div class="block">Whether the compiler should force catching of exceptions by explicitly
 named types.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strictsignal</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSymbols(boolean)">
<h3>setSymbols</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSymbols</span><wbr><span class="parameters">(boolean&nbsp;symbols)</span></div>
<div class="block">Sets whether debug symbols should be generated into the class file.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>symbols</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTime(boolean)">
<h3>setTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTime</span><wbr><span class="parameters">(boolean&nbsp;time)</span></div>
<div class="block">Asks the NetRexx compiler to print compilation times to the console
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrace(org.apache.tools.ant.taskdefs.optional.NetRexxC.TraceAttr)">
<h3>setTrace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrace</span><wbr><span class="parameters">(<a href="NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a>&nbsp;trace)</span></div>
<div class="block">Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace". "trace" and
 "trace2".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>trace</code> - the value to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTrace(java.lang.String)">
<h3>setTrace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;trace)</span></div>
<div class="block">Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace". "trace" and
 "trace2".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>trace</code> - the value to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUtf8(boolean)">
<h3>setUtf8</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUtf8</span><wbr><span class="parameters">(boolean&nbsp;utf8)</span></div>
<div class="block">Tells the NetRexx compiler that the source is in UTF8.
 Valid true values are "yes", "on" or "true". Anything else sets the flag to false.
 The default value is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>utf8</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerbose(org.apache.tools.ant.taskdefs.optional.NetRexxC.VerboseAttr)">
<h3>setVerbose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(<a href="NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a>&nbsp;verbose)</span></div>
<div class="block">Whether lots of warnings and error messages should be generated</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - the value to set - verbose&lt;level&gt; or noverbose.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerbose(java.lang.String)">
<h3>setVerbose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;verbose)</span></div>
<div class="block">Whether lots of warnings and error messages should be generated</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - the value to set - verbose&lt;level&gt; or noverbose.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressMethodArgumentNotUsed(boolean)">
<h3>setSuppressMethodArgumentNotUsed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressMethodArgumentNotUsed</span><wbr><span class="parameters">(boolean&nbsp;suppressMethodArgumentNotUsed)</span></div>
<div class="block">Whether the task should suppress the "Method argument is not used" in
 strictargs-Mode, which can not be suppressed by the compiler itself.
 The warning is logged as verbose message, though.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>suppressMethodArgumentNotUsed</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressPrivatePropertyNotUsed(boolean)">
<h3>setSuppressPrivatePropertyNotUsed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressPrivatePropertyNotUsed</span><wbr><span class="parameters">(boolean&nbsp;suppressPrivatePropertyNotUsed)</span></div>
<div class="block">Whether the task should suppress the "Private property is defined but
 not used" in strictargs-Mode, which can be quite annoying while
 developing. The warning is logged as verbose message, though.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>suppressPrivatePropertyNotUsed</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressVariableNotUsed(boolean)">
<h3>setSuppressVariableNotUsed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressVariableNotUsed</span><wbr><span class="parameters">(boolean&nbsp;suppressVariableNotUsed)</span></div>
<div class="block">Whether the task should suppress the "Variable is set but not used" in
 strictargs-Mode. Be careful with this one! The warning is logged as
 verbose message, though.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>suppressVariableNotUsed</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressExceptionNotSignalled(boolean)">
<h3>setSuppressExceptionNotSignalled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressExceptionNotSignalled</span><wbr><span class="parameters">(boolean&nbsp;suppressExceptionNotSignalled)</span></div>
<div class="block">Whether the task should suppress the "FooException is in SIGNALS list
 but is not signalled within the method", which is sometimes rather
 useless. The warning is logged as verbose message, though.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>suppressExceptionNotSignalled</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressDeprecation(boolean)">
<h3>setSuppressDeprecation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressDeprecation</span><wbr><span class="parameters">(boolean&nbsp;suppressDeprecation)</span></div>
<div class="block">Tells whether we should filter out any deprecation-messages
 of the compiler out.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>suppressDeprecation</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRemoveKeepExtension(boolean)">
<h3>setRemoveKeepExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRemoveKeepExtension</span><wbr><span class="parameters">(boolean&nbsp;removeKeepExtension)</span></div>
<div class="block">Tells whether the trailing .keep in nocompile-mode should be removed
 so that the resulting java source really ends on .java.
 This facilitates the use of the javadoc tool later on.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>removeKeepExtension</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()</div>
<div class="block">init-Method sets defaults from Properties. That way, when ant is called
 with arguments like -Dant.netrexxc.verbose=verbose5 one can easily take
 control of all netrexxc-tasks.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#init()">init</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the task - performs the actual compiler call.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
