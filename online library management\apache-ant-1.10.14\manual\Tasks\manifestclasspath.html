<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ManifestClassPath Task</title>
</head>

<body>

<h2 id="manifestclasspath">Manifestclasspath</h2>
<p><em>Since Apache Ant 1.7</em></p>

<h3>Description</h3>
<p>Converts a <a href="../using.html#path">Path</a> into a property whose value is appropriate for
a <a href="manifest.html">Manifest</a>'s <code>Class-Path</code> attribute.</p>

<p>This task is often used to work around command line limitations on Windows when using very long
class paths when launching an application. The long class path normally specified on the command
line is replaced by a single (possibly empty) jar file which an in-manifest <code>Class-Path</code>
attribute whose value lists all the jar and zip files the classpath should contain. The files
referenced from this attribute must be found relatively to the jar file itself, usually in the same
directory. JVM automatically uses all file entries listed in the <code>Class-Path</code> attributes
of a jar to locate/load classes. Note though that it silently ignores entries for which it cannot
find any corresponding file.</p>

<p>Note that the property value created may be longer than a manifest's maximum 72 characters per
line, but will be properly wrapped as per the Jar specification by the <code>&lt;manifest&gt;</code>
element, where the defined property is re-referenced.</p>

<p>For this task to work properly the relative path from the file given in the <code>jarfile</code>
attribute to the elements of the nested <code>classpath</code> must be the same as you expect them
to be when deploying the jar.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>the name of the property to set. This property must not already be set.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>jarfile</td>
    <td>the filename for the Jar which will contain the manifest that will use the property this
      task will set. This file need not exist yet, but its parent directory must exist.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>maxParentLevels</td>
    <td>The maximum number of parent directories one is allowed to traverse to navigate from the jar
      file to the path entry. Put differently, the maximum number of <q>..</q> which is allowed in
      the relative path from the jar file to a given class path entry. Specify <q>0</q> to enforce a
      path entry to be in the same directory (or one of its sub-directories) as the jar file
      itself.</td>
    <td>No; defaults to <q>2</q></td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4>classpath</h4>
<p>A <a href="../using.html#path">path-like</a> element, which can be defined in-place, or refer to
a path defined elsewhere using the <code>&lt;classpath refid=&quot;pathid&quot;/&gt;</code> syntax.
This classpath must not be empty, and is required.</p>

<h3>Examples</h3>

<p>Assuming a path with <var>id</var>=<q>classpath</q> was already defined, convert this path
relatively to the <samp>build/</samp> directory that will contain <samp>acme.jar</samp>, which can
later be created with <code>&lt;jar&gt;</code> with a nested <code>&lt;manifest&gt;</code> element
that lists an <code>&lt;attribute name="Class-Path" value="${jar.classpath}"/&gt;</code>.</p>

<pre>
&lt;manifestclasspath property="jar.classpath"
                   jarfile="build/acme.jar"&gt;
    &lt;classpath refid="classpath"/&gt;
&lt;/manifestclasspath&gt;</pre>

</body>
</html>
