<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JavaCC (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.javacc, class: JavaCC">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.javacc</a></div>
<h1 title="Class JavaCC" class="title">Class JavaCC</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.javacc.JavaCC</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JavaCC</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">JavaCC compiler compiler task.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color"><code><a href="#ARCHIVE_LOCATIONS" class="member-name-link">ARCHIVE_LOCATIONS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int[]</code></div>
<div class="col-second odd-row-color"><code><a href="#ARCHIVE_LOCATIONS_VS_MAJOR_VERSION" class="member-name-link">ARCHIVE_LOCATIONS_VS_MAJOR_VERSION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COM_JAVACC_CLASS" class="member-name-link">COM_JAVACC_CLASS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COM_JJDOC_CLASS" class="member-name-link">COM_JJDOC_CLASS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COM_JJTREE_CLASS" class="member-name-link">COM_JJTREE_CLASS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COM_PACKAGE" class="member-name-link">COM_PACKAGE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ORG_JAVACC_CLASS" class="member-name-link">ORG_JAVACC_CLASS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ORG_JJDOC_CLASS" class="member-name-link">ORG_JJDOC_CLASS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ORG_JJTREE_CLASS" class="member-name-link">ORG_JJTREE_CLASS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ORG_PACKAGE_3_0" class="member-name-link">ORG_PACKAGE_3_0</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ORG_PACKAGE_3_1" class="member-name-link">ORG_PACKAGE_3_1</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#TASKDEF_TYPE_JAVACC" class="member-name-link">TASKDEF_TYPE_JAVACC</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#TASKDEF_TYPE_JJDOC" class="member-name-link">TASKDEF_TYPE_JJDOC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#TASKDEF_TYPE_JJTREE" class="member-name-link">TASKDEF_TYPE_JJTREE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JavaCC</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getArchiveFile(java.io.File)" class="member-name-link">getArchiveFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Helper method to retrieve the path used to store the JavaCC.zip
 or javacc.jar which is different from versions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMainClass(java.io.File,int)" class="member-name-link">getMainClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home,
 int&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Helper method to retrieve main class which is different from versions.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMainClass(org.apache.tools.ant.types.Path,int)" class="member-name-link">getMainClass</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 int&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Helper method to retrieve main class which is different from versions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMajorVersionNumber(java.io.File)" class="member-name-link">getMajorVersionNumber</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Helper method to determine the major version number of JavaCC.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBuildparser(boolean)" class="member-name-link">setBuildparser</a><wbr>(boolean&nbsp;buildParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the BUILD_PARSER grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBuildtokenmanager(boolean)" class="member-name-link">setBuildtokenmanager</a><wbr>(boolean&nbsp;buildTokenManager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the BUILD_TOKEN_MANAGER grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCachetokens(boolean)" class="member-name-link">setCachetokens</a><wbr>(boolean&nbsp;cacheTokens)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the CACHE_TOKENS grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setChoiceambiguitycheck(int)" class="member-name-link">setChoiceambiguitycheck</a><wbr>(int&nbsp;choiceAmbiguityCheck)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the CHOICE_AMBIGUITY_CHECK grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCommontokenaction(boolean)" class="member-name-link">setCommontokenaction</a><wbr>(boolean&nbsp;commonTokenAction)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the COMMON_TOKEN_ACTION grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebuglookahead(boolean)" class="member-name-link">setDebuglookahead</a><wbr>(boolean&nbsp;debugLookahead)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the DEBUG_LOOKAHEAD grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebugparser(boolean)" class="member-name-link">setDebugparser</a><wbr>(boolean&nbsp;debugParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the DEBUG_PARSER grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebugtokenmanager(boolean)" class="member-name-link">setDebugtokenmanager</a><wbr>(boolean&nbsp;debugTokenManager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the DEBUG_TOKEN_MANAGER grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorreporting(boolean)" class="member-name-link">setErrorreporting</a><wbr>(boolean&nbsp;errorReporting)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the ERROR_REPORTING grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForcelacheck(boolean)" class="member-name-link">setForcelacheck</a><wbr>(boolean&nbsp;forceLACheck)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the FORCE_LA_CHECK grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnorecase(boolean)" class="member-name-link">setIgnorecase</a><wbr>(boolean&nbsp;ignoreCase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the IGNORE_CASE grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavacchome(java.io.File)" class="member-name-link">setJavacchome</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;javaccHome)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The directory containing the JavaCC distribution.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavaunicodeescape(boolean)" class="member-name-link">setJavaunicodeescape</a><wbr>(boolean&nbsp;javaUnicodeEscape)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the JAVA_UNICODE_ESCAPE grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJDKversion(java.lang.String)" class="member-name-link">setJDKversion</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jdkVersion)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the JDK_VERSION option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeeplinecolumn(boolean)" class="member-name-link">setKeeplinecolumn</a><wbr>(boolean&nbsp;keepLineColumn)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the KEEP_LINE_COLUMN grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLookahead(int)" class="member-name-link">setLookahead</a><wbr>(int&nbsp;lookahead)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the LOOKAHEAD grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxmemory(java.lang.String)" class="member-name-link">setMaxmemory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;max)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Corresponds -Xmx.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOptimizetokenmanager(boolean)" class="member-name-link">setOptimizetokenmanager</a><wbr>(boolean&nbsp;optimizeTokenManager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the OPTIMIZE_TOKEN_MANAGER grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOtherambiguityCheck(int)" class="member-name-link">setOtherambiguityCheck</a><wbr>(int&nbsp;otherAmbiguityCheck)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the OTHER_AMBIGUITY_CHECK grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputdirectory(java.io.File)" class="member-name-link">setOutputdirectory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputDirectory)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The directory to write the generated files to.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSanitycheck(boolean)" class="member-name-link">setSanitycheck</a><wbr>(boolean&nbsp;sanityCheck)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the SANITY_CHECK grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStatic(boolean)" class="member-name-link">setStatic</a><wbr>(boolean&nbsp;staticParser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the STATIC grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTarget(java.io.File)" class="member-name-link">setTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The grammar file to process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUnicodeinput(boolean)" class="member-name-link">setUnicodeinput</a><wbr>(boolean&nbsp;unicodeInput)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the UNICODE_INPUT grammar option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUsercharstream(boolean)" class="member-name-link">setUsercharstream</a><wbr>(boolean&nbsp;userCharStream)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the USER_CHAR_STREAM grammar option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUsertokenmanager(boolean)" class="member-name-link">setUsertokenmanager</a><wbr>(boolean&nbsp;userTokenManager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the USER_TOKEN_MANAGER grammar option.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="TASKDEF_TYPE_JAVACC">
<h3>TASKDEF_TYPE_JAVACC</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TASKDEF_TYPE_JAVACC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JAVACC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TASKDEF_TYPE_JJTREE">
<h3>TASKDEF_TYPE_JJTREE</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TASKDEF_TYPE_JJTREE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JJTREE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TASKDEF_TYPE_JJDOC">
<h3>TASKDEF_TYPE_JJDOC</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">TASKDEF_TYPE_JJDOC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JJDOC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ARCHIVE_LOCATIONS">
<h3>ARCHIVE_LOCATIONS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">ARCHIVE_LOCATIONS</span></div>
</section>
</li>
<li>
<section class="detail" id="ARCHIVE_LOCATIONS_VS_MAJOR_VERSION">
<h3>ARCHIVE_LOCATIONS_VS_MAJOR_VERSION</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int[]</span>&nbsp;<span class="element-name">ARCHIVE_LOCATIONS_VS_MAJOR_VERSION</span></div>
</section>
</li>
<li>
<section class="detail" id="COM_PACKAGE">
<h3>COM_PACKAGE</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COM_PACKAGE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_PACKAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COM_JAVACC_CLASS">
<h3>COM_JAVACC_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COM_JAVACC_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JAVACC_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COM_JJTREE_CLASS">
<h3>COM_JJTREE_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COM_JJTREE_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JJTREE_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COM_JJDOC_CLASS">
<h3>COM_JJDOC_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COM_JJDOC_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JJDOC_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ORG_PACKAGE_3_0">
<h3>ORG_PACKAGE_3_0</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ORG_PACKAGE_3_0</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_PACKAGE_3_0">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ORG_PACKAGE_3_1">
<h3>ORG_PACKAGE_3_1</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ORG_PACKAGE_3_1</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_PACKAGE_3_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ORG_JAVACC_CLASS">
<h3>ORG_JAVACC_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ORG_JAVACC_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JAVACC_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ORG_JJTREE_CLASS">
<h3>ORG_JJTREE_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ORG_JJTREE_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JJTREE_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ORG_JJDOC_CLASS">
<h3>ORG_JJDOC_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ORG_JJDOC_CLASS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JJDOC_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JavaCC</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JavaCC</span>()</div>
<div class="block">Constructor</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setLookahead(int)">
<h3>setLookahead</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLookahead</span><wbr><span class="parameters">(int&nbsp;lookahead)</span></div>
<div class="block">Sets the LOOKAHEAD grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>lookahead</code> - an <code>int</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setChoiceambiguitycheck(int)">
<h3>setChoiceambiguitycheck</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setChoiceambiguitycheck</span><wbr><span class="parameters">(int&nbsp;choiceAmbiguityCheck)</span></div>
<div class="block">Sets the CHOICE_AMBIGUITY_CHECK grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>choiceAmbiguityCheck</code> - an <code>int</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOtherambiguityCheck(int)">
<h3>setOtherambiguityCheck</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOtherambiguityCheck</span><wbr><span class="parameters">(int&nbsp;otherAmbiguityCheck)</span></div>
<div class="block">Sets the OTHER_AMBIGUITY_CHECK grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>otherAmbiguityCheck</code> - an <code>int</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStatic(boolean)">
<h3>setStatic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStatic</span><wbr><span class="parameters">(boolean&nbsp;staticParser)</span></div>
<div class="block">Sets the STATIC grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>staticParser</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebugparser(boolean)">
<h3>setDebugparser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebugparser</span><wbr><span class="parameters">(boolean&nbsp;debugParser)</span></div>
<div class="block">Sets the DEBUG_PARSER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debugParser</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebuglookahead(boolean)">
<h3>setDebuglookahead</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebuglookahead</span><wbr><span class="parameters">(boolean&nbsp;debugLookahead)</span></div>
<div class="block">Sets the DEBUG_LOOKAHEAD grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debugLookahead</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebugtokenmanager(boolean)">
<h3>setDebugtokenmanager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebugtokenmanager</span><wbr><span class="parameters">(boolean&nbsp;debugTokenManager)</span></div>
<div class="block">Sets the DEBUG_TOKEN_MANAGER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debugTokenManager</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOptimizetokenmanager(boolean)">
<h3>setOptimizetokenmanager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOptimizetokenmanager</span><wbr><span class="parameters">(boolean&nbsp;optimizeTokenManager)</span></div>
<div class="block">Sets the OPTIMIZE_TOKEN_MANAGER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>optimizeTokenManager</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorreporting(boolean)">
<h3>setErrorreporting</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorreporting</span><wbr><span class="parameters">(boolean&nbsp;errorReporting)</span></div>
<div class="block">Sets the ERROR_REPORTING grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorReporting</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavaunicodeescape(boolean)">
<h3>setJavaunicodeescape</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavaunicodeescape</span><wbr><span class="parameters">(boolean&nbsp;javaUnicodeEscape)</span></div>
<div class="block">Sets the JAVA_UNICODE_ESCAPE grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>javaUnicodeEscape</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUnicodeinput(boolean)">
<h3>setUnicodeinput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUnicodeinput</span><wbr><span class="parameters">(boolean&nbsp;unicodeInput)</span></div>
<div class="block">Sets the UNICODE_INPUT grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unicodeInput</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnorecase(boolean)">
<h3>setIgnorecase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnorecase</span><wbr><span class="parameters">(boolean&nbsp;ignoreCase)</span></div>
<div class="block">Sets the IGNORE_CASE grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ignoreCase</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCommontokenaction(boolean)">
<h3>setCommontokenaction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCommontokenaction</span><wbr><span class="parameters">(boolean&nbsp;commonTokenAction)</span></div>
<div class="block">Sets the COMMON_TOKEN_ACTION grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>commonTokenAction</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUsertokenmanager(boolean)">
<h3>setUsertokenmanager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUsertokenmanager</span><wbr><span class="parameters">(boolean&nbsp;userTokenManager)</span></div>
<div class="block">Sets the USER_TOKEN_MANAGER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userTokenManager</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUsercharstream(boolean)">
<h3>setUsercharstream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUsercharstream</span><wbr><span class="parameters">(boolean&nbsp;userCharStream)</span></div>
<div class="block">Sets the USER_CHAR_STREAM grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userCharStream</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBuildparser(boolean)">
<h3>setBuildparser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBuildparser</span><wbr><span class="parameters">(boolean&nbsp;buildParser)</span></div>
<div class="block">Sets the BUILD_PARSER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buildParser</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBuildtokenmanager(boolean)">
<h3>setBuildtokenmanager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBuildtokenmanager</span><wbr><span class="parameters">(boolean&nbsp;buildTokenManager)</span></div>
<div class="block">Sets the BUILD_TOKEN_MANAGER grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buildTokenManager</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSanitycheck(boolean)">
<h3>setSanitycheck</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSanitycheck</span><wbr><span class="parameters">(boolean&nbsp;sanityCheck)</span></div>
<div class="block">Sets the SANITY_CHECK grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sanityCheck</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForcelacheck(boolean)">
<h3>setForcelacheck</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForcelacheck</span><wbr><span class="parameters">(boolean&nbsp;forceLACheck)</span></div>
<div class="block">Sets the FORCE_LA_CHECK grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>forceLACheck</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCachetokens(boolean)">
<h3>setCachetokens</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCachetokens</span><wbr><span class="parameters">(boolean&nbsp;cacheTokens)</span></div>
<div class="block">Sets the CACHE_TOKENS grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cacheTokens</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeeplinecolumn(boolean)">
<h3>setKeeplinecolumn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeeplinecolumn</span><wbr><span class="parameters">(boolean&nbsp;keepLineColumn)</span></div>
<div class="block">Sets the KEEP_LINE_COLUMN grammar option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keepLineColumn</code> - a <code>boolean</code> value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJDKversion(java.lang.String)">
<h3>setJDKversion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJDKversion</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jdkVersion)</span></div>
<div class="block">Sets the JDK_VERSION option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jdkVersion</code> - the version to use.</dd>
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputdirectory(java.io.File)">
<h3>setOutputdirectory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputdirectory</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputDirectory)</span></div>
<div class="block">The directory to write the generated files to.
 If not set, the files are written to the directory
 containing the grammar file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputDirectory</code> - the output directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTarget(java.io.File)">
<h3>setTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile)</span></div>
<div class="block">The grammar file to process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetFile</code> - the grammar file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavacchome(java.io.File)">
<h3>setJavacchome</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavacchome</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;javaccHome)</span></div>
<div class="block">The directory containing the JavaCC distribution.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>javaccHome</code> - the directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMaxmemory(java.lang.String)">
<h3>setMaxmemory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxmemory</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;max)</span></div>
<div class="block">Corresponds -Xmx.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max</code> - max memory parameter.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getArchiveFile(java.io.File)">
<h3>getArchiveFile</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getArchiveFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home)</span>
                              throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Helper method to retrieve the path used to store the JavaCC.zip
 or javacc.jar which is different from versions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>home</code> - the javacc home path directory.</dd>
<dt>Returns:</dt>
<dd>the file object pointing to the JavaCC archive.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown if the home directory is invalid
 or if the archive could not be found despite attempts to do so.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMainClass(java.io.File,int)">
<h3>getMainClass</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMainClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home,
 int&nbsp;type)</span>
                              throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Helper method to retrieve main class which is different from versions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>home</code> - the javacc home path directory.</dd>
<dd><code>type</code> - the taskdef.</dd>
<dt>Returns:</dt>
<dd>the main class for the taskdef.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown if the home directory is invalid
 or if the archive could not be found despite attempts to do so.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMainClass(org.apache.tools.ant.types.Path,int)">
<h3>getMainClass</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMainClass</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 int&nbsp;type)</span>
                              throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Helper method to retrieve main class which is different from versions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - classpath to search in.</dd>
<dd><code>type</code> - the taskdef.</dd>
<dt>Returns:</dt>
<dd>the main class for the taskdef.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown if the home directory is invalid
 or if the archive could not be found despite attempts to do so.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMajorVersionNumber(java.io.File)">
<h3>getMajorVersionNumber</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMajorVersionNumber</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;home)</span>
                                    throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Helper method to determine the major version number of JavaCC.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>home</code> - the javacc home path directory.</dd>
<dt>Returns:</dt>
<dd>a the major version number</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown if the home directory is invalid
 or if the archive could not be found despite attempts to do so.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
