<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>XMLValidate Task</title>
</head>

<body>

<h2 id="xmlvalidate">XMLValidate</h2>
<h3>Description</h3>

<p>This task checks that XML files are valid (or only well formed). The task uses the SAX2 parser
implementation provided by JAXP by default (probably the one that is used by Apache Ant itself), but
one can specify any SAX1/2 parser if needed.</p>

<p>This task supports the use of nested</p>
<ul>
  <li><a href="../Types/xmlcatalog.html"><code>&lt;xmlcatalog&gt;</code></a> elements</li>
  <li><code>&lt;dtd&gt;</code> elements which are used to resolve DTDs and entities</li>
  <li><code>&lt;attribute&gt;</code> elements which are used to set features on the parser.  These
    can be any number
    of <a href="http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description"
    target="_top"><code>http://xml.org/sax/features/</code></a> or other features that your parser
    may support.</li>
  <li><code>&lt;property&gt;</code> elements, containing string properties</li>
</ul>

<p><strong>Warning</strong>: By default, JAXP creates a non namespace aware parser.
The <var>http://xml.org/sax/features/namespaces</var> feature is set by default to <q>false</q> by
the JAXP implementation used by Ant. To validate a document containing namespaces, set the
namespaces feature to <q>true</q> explicitly by nesting the following element:</p>
<pre>&lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;</pre>
<p>If you are using for instance a <code>xsi:noNamespaceSchemaLocation</code> attribute in your XML
files, you will need this namespace support feature.</p>
<p>If you are using a parser not generated by JAXP, by using the <var>classname</var> attribute
of <code>xmlvalidate</code>, this warning may not apply.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>the file(s) you want to check.</td>
    <td>Yes, unless an embedded <code>&lt;fileset&gt;</code> is specified</td>
  </tr>
  <tr>
    <td>lenient</td>
    <td>if <q>true</q>, only check the XML document is well formed (ignored if the specified parser
      is a SAX1 parser).</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classname</td>
    <td>the parser to use.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>where to find the parser class.</td>
    <td>No, can use an embedded <code>&lt;classpath&gt;</code> element</td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>fails on a error if set to <q>true</q>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>warn</td>
    <td>log parser warn events.</td>
    <td>No</td>
  </tr>
</table>

<h3 id="nested">Parameters specified as nested elements</h3>
<h4>dtd</h4>
<p><code>&lt;dtd&gt;</code> is used to specify different locations for DTD resolution.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>publicId</td>
    <td>Public ID of the DTD to resolve</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>location</td>
    <td>Location of the DTD to use, which can be a file, a resource, or a URL</td>
    <td>Yes</td>
  </tr>
</table>
<h4>xmlcatalog</h4>
<p>The <a href="../Types/xmlcatalog.html"><code>&lt;xmlcatalog&gt;</code></a> element is used to
perform entity resolution.</p>
<h4>attribute</h4>
<p>The <code>&lt;attribute&gt;</code> element is used to set parser features.</p>
<p>Features usable with the Xerces parser are defined
here: <a href="https://xml.apache.org/xerces-j/features.html" target="_top">Setting Features</a></p>
<p>SAX features are defined
here: <a href="http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description"
target="_top">SAX2 Standard Feature Flags</a></p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the feature</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The boolean value of the feature</td>
    <td>Yes</td>
  </tr>
</table>

<h4>property</h4>
<p>The <code>&lt;property&gt;</code> element is used to set properties.  These properties are
defined here for the Xerces XML parser
implementation: <a href="https://xml.apache.org/xerces-j/properties.html" target="_top">XML Parser
properties</a>. Properties can be used to set the schema used to validate the XML file.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the feature</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The string value of the property</td>
    <td>Yes</td>
  </tr>
</table>

<h3>Examples</h3>
<p>Validate <samp>toto.xml</samp></p>
<pre>&lt;xmlvalidate file="toto.xml"/&gt;</pre>

<p>Validate all <samp>.xsl</samp> files in <samp>src/style</samp>, but only warn if there is an
error, rather than halt the build.</p>
<pre>
&lt;xmlvalidate failonerror="no" lenient="yes" warn="yes"
             classname="org.apache.xerces.parsers.SAXParser"&gt;
             classpath="lib/xerces.jar"&gt;
  &lt;fileset dir="src" includes="style/*.xsl"/&gt;
&lt;/xmlvalidate&gt;</pre>

<p>Validate a struts configuration, using a local copy of the DTD.</p>
<pre>
&lt;xmlvalidate file="struts-config.xml" warn="false"&gt;
  &lt;dtd publicId="-//Apache Software Foundation//DTD Struts Configuration 1.0//EN"
       location="struts-config_1_0.dtd"/&gt;
&lt;/xmlvalidate&gt;</pre>

<p>Scan all XML files in the project, using a predefined catalog to map URIs to local files.</p>
<pre>
&lt;xmlvalidate failonerror="no"&gt;
  &lt;fileset dir="${project.dir}" includes="**/*.xml"/&gt;
  &lt;xmlcatalog refid="mycatalog"/&gt;
&lt;/xmlvalidate&gt;</pre>

<p>Scan all XML files in the project, using the catalog defined inline.</p>
<pre>
&lt;xmlvalidate failonerror="no"&gt;
  &lt;fileset dir="${project.dir}" includes="**/*.xml"/&gt;
  &lt;xmlcatalog&gt;
    &lt;dtd publicId=&quot;-//ArielPartners//DTD XML Article V1.0//EN&quot;
         location=&quot;com/arielpartners/knowledgebase/dtd/article.dtd&quot;/&gt;
  &lt;/xmlcatalog&gt;
&lt;/xmlvalidate&gt;</pre>

<p>Validate all <samp>.xml</samp> files in <samp>xml</samp> directory with the parser configured to
perform XSD validation. Note: The parser must support the
feature <var>http://apache.org/xml/features/validation/schema</var>. The <a href="schemavalidate.html">schemavalidate</a>
task is better for validating W3C XML Schemas, as it extends this task with the right options
automatically enabled, and makes it easy to add a list of schema files/URLs to act as sources.</p>
<pre>
&lt;xmlvalidate failonerror="yes" lenient="no" warn="yes"&gt;
  &lt;fileset dir="xml" includes="**/*.xml"/&gt;
  &lt;attribute name="http://xml.org/sax/features/validation" value="true"/&gt;
  &lt;attribute name="http://apache.org/xml/features/validation/schema"  value="true"/&gt;
  &lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;
&lt;/xmlvalidate&gt;</pre>

<p>Validate the file <samp>xml/endpiece-noSchema.xml</samp> against the
schema <samp>xml/doc.xsd</samp>.</p>
<pre>
&lt;!-- Convert path to URL format --&gt;
&lt;pathconvert dirsep="/" property="xsd.file"&gt;
&lt;path&gt;
   &lt;pathelement location="xml/doc.xsd"/&gt;
&lt;/path&gt;
&lt;/pathconvert&gt;

&lt;xmlvalidate file="xml/endpiece-noSchema.xml" lenient="false" failonerror="true" warn="true"&gt;
  &lt;attribute name="http://apache.org/xml/features/validation/schema" value="true"/&gt;
  &lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;
  &lt;property name="http://apache.org/xml/properties/schema/external-noNamespaceSchemaLocation"
            value="${xsd.file}"/&gt;
&lt;/xmlvalidate&gt;</pre>
</body>
</html>
