<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Zip (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Zip">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Zip" class="title">Class Zip</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Zip</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Zip</span>
<span class="extends-implements">extends <a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></span></div>
<div class="block">Create a Zip file.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Zip.ArchiveState.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></code></div>
<div class="col-last even-row-color">
<div class="block">Holds the up-to-date status and the out-of-date resources of
 the original archive.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Zip.Duplicate.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Possible behaviors when a duplicate file is added:
 "add", "preserve" or "fail"</div>
</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Zip.UnicodeExtraField.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></code></div>
<div class="col-last even-row-color">
<div class="block">Policy for creation of Unicode extra fields: never, always or
 not-encodeable.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Zip.WhenEmpty.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Possible behaviors when there are no matching files for the task:
 "fail", "skip", or "create".</div>
</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Zip.Zip64ModeAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></code></div>
<div class="col-last even-row-color">
<div class="block">The choices for Zip64 extensions.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#addedDirs" class="member-name-link">addedDirs</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#archiveType" class="member-name-link">archiveType</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#doubleFilePass" class="member-name-link">doubleFilePass</a></code></div>
<div class="col-last even-row-color">
<div class="block">If this flag is true, execute() will run most operations twice,
 the first time with <a href="#skipWriting"><code>skipWriting</code></a> set to
 true and the second time with setting it to false.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#duplicate" class="member-name-link">duplicate</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#emptyBehavior" class="member-name-link">emptyBehavior</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#entries" class="member-name-link">entries</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#skipWriting" class="member-name-link">skipWriting</a></code></div>
<div class="col-last even-row-color">
<div class="block">whether the methods should just perform some sort of dry-run.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color"><code><a href="#zipFile" class="member-name-link">zipFile</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Zip</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">add</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a collection of resources to be archived.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addParentDirs(java.io.File,java.lang.String,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)" class="member-name-link">addParentDirs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;entry,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 int&nbsp;dirMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ensure all parent dirs of a given entry have been added.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addResources(org.apache.tools.ant.types.FileSet,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)" class="member-name-link">addResources</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset,
 <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the given resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addResources(org.apache.tools.ant.types.ResourceCollection,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)" class="member-name-link">addResources</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc,
 <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the given resources.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addZipfileset(org.apache.tools.ant.types.ZipFileSet)" class="member-name-link">addZipfileset</a><wbr>(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;set)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a set of files that can be
 read from an archive and be given a prefix/fullpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addZipGroupFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addZipGroupFileset</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a group of zip files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanUp()" class="member-name-link">cleanUp</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do any clean up necessary to allow this instance to be used again.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createEmptyZip(java.io.File)" class="member-name-link">createEmptyZip</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an empty zip file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">validate and build</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeMain()" class="member-name-link">executeMain</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Build the zip file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)" class="member-name-link">finalizeZipOutputStream</a><wbr>(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">method for subclasses to override</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComment()" class="member-name-link">getComment</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Comment of the archive</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCreateUnicodeExtraFields()" class="member-name-link">getCreateUnicodeExtraFields</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether Unicode extra fields will be created.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentExtraFields()" class="member-name-link">getCurrentExtraFields</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Provides the extra fields for the zip entry currently being
 added to the archive - if any.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestFile()" class="member-name-link">getDestFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The file to create.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEncoding()" class="member-name-link">getEncoding</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Encoding to use for filenames.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFallBackToUTF8()" class="member-name-link">getFallBackToUTF8</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to fall back to UTF-8 if a name cannot be encoded using
 the specified encoding.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLevel()" class="member-name-link">getLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the compression level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModificationtime()" class="member-name-link">getModificationtime</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The file modification time previously provided to
 <a href="#setModificationtime(java.lang.String)"><code>setModificationtime(String)</code></a> or <code>null</code> if unset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)" class="member-name-link">getNonFileSetResourcesToAdd</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreserve0Permissions()" class="member-name-link">getPreserve0Permissions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assume 0 Unix mode is intentional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourcesToAdd(org.apache.tools.ant.types.FileSet%5B%5D,java.io.File,boolean)" class="member-name-link">getResourcesToAdd</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>[]&nbsp;filesets,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)" class="member-name-link">getResourcesToAdd</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUseLanguageEnodingFlag()" class="member-name-link">getUseLanguageEnodingFlag</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the language encoding flag will be used.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getZip64Mode()" class="member-name-link">getZip64Mode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether Zip64 extensions will be used.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection%5B%5D)" class="member-name-link">grabNonFileSetResources</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fetch all included and not excluded resources from the collections.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#grabResources(org.apache.tools.ant.types.FileSet%5B%5D)" class="member-name-link">grabResources</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>[]&nbsp;filesets)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fetch all included and not excluded resources from the sets.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasUpdatedFile()" class="member-name-link">hasUpdatedFile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the value of the updatedFile attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)" class="member-name-link">initZipOutputStream</a><wbr>(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">method for subclasses to override</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAddingNewFiles()" class="member-name-link">isAddingNewFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates if the task is adding new files into the archive as opposed to
 copying back unchanged files from the backup copy</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCompress()" class="member-name-link">isCompress</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether we want to compress the files or only store them;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static final boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isEmpty(org.apache.tools.ant.types.Resource%5B%5D%5B%5D)" class="member-name-link">isEmpty</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Check is the resource arrays are empty.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFirstPass()" class="member-name-link">isFirstPass</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether this is the first time the archive building methods are invoked.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isInUpdateMode()" class="member-name-link">isInUpdateMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Are we updating an existing archive?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logWhenWriting(java.lang.String,int)" class="member-name-link">logWhenWriting</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message at the given output level, but only if this is
 the pass that will actually create the archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reset()" class="member-name-link">reset</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes this instance reset all attributes to their default
 values and forget all children.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectDirectoryResources(org.apache.tools.ant.types.Resource%5B%5D)" class="member-name-link">selectDirectoryResources</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Drops all non-directory resources from the given array.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectFileResources(org.apache.tools.ant.types.Resource%5B%5D)" class="member-name-link">selectFileResources</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Drops all non-file resources from the given array.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectResources(org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.ant.types.resources.selectors.ResourceSelector)" class="member-name-link">selectResources</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig,
 <a href="../types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Drops all resources from the given array that are not selected</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasedir(java.io.File)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Directory from which to archive files; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setComment(java.lang.String)" class="member-name-link">setComment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Comment to use for archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompress(boolean)" class="member-name-link">setCompress</a><wbr>(boolean&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether we want to compress the files or only store them;
 optional, default=true;</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCreateUnicodeExtraFields(org.apache.tools.ant.taskdefs.Zip.UnicodeExtraField)" class="member-name-link">setCreateUnicodeExtraFields</a><wbr>(<a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a>&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether Unicode extra fields will be created.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurrentExtraFields(org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">setCurrentExtraFields</a><wbr>(<a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the extra fields for the zip entry currently being
 added to the archive - if any.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestFile(java.io.File)" class="member-name-link">setDestFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The file to create; required.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)" class="member-name-link">setDuplicate</a><wbr>(<a href="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a>&nbsp;df)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets behavior for when a duplicate file is about to be added -
 one of <code>add</code>, <code>preserve</code> or <code>fail</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Encoding to use for filenames, defaults to the platform's
 default encoding.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFallBackToUTF8(boolean)" class="member-name-link">setFallBackToUTF8</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to fall back to UTF-8 if a name cannot be encoded using
 the specified encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setFile(java.io.File)" class="member-name-link">setFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFilesonly(boolean)" class="member-name-link">setFilesonly</a><wbr>(boolean&nbsp;f)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, emulate Sun's jar utility by not adding parent directories;
 optional, defaults to false.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepCompression(boolean)" class="member-name-link">setKeepCompression</a><wbr>(boolean&nbsp;keep)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the original compression of entries coming from a ZIP
 archive should be kept (for example when updating an archive).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLevel(int)" class="member-name-link">setLevel</a><wbr>(int&nbsp;level)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the compression level to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModificationtime(java.lang.String)" class="member-name-link">setModificationtime</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;time)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set all stored file modification times to <code>time</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreserve0Permissions(boolean)" class="member-name-link">setPreserve0Permissions</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assume 0 Unix mode is intentional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRoundUp(boolean)" class="member-name-link">setRoundUp</a><wbr>(boolean&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the file modification times will be rounded up to the
 next even number of seconds.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUpdate(boolean)" class="member-name-link">setUpdate</a><wbr>(boolean&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, updates an existing file, otherwise overwrite
 any existing one; optional defaults to false.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUseLanguageEncodingFlag(boolean)" class="member-name-link">setUseLanguageEncodingFlag</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to set the language encoding flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)" class="member-name-link">setWhenempty</a><wbr>(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets behavior of the task when no files match.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setZip64Mode(org.apache.tools.ant.taskdefs.Zip.Zip64ModeAttribute)" class="member-name-link">setZip64Mode</a><wbr>(<a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a>&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether Zip64 extensions should be used.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setZipfile(java.io.File)" class="member-name-link">setZipfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)" class="member-name-link">zipDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a directory to the zip stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">zipDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a directory to the zip stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipDir(org.apache.tools.ant.types.Resource,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">zipDir</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a directory to the zip stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)" class="member-name-link">zipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method that gets called when adding from <code>java.io.File</code> instances.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)" class="member-name-link">zipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a new entry to the archive, takes care of duplicates as well.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int,org.apache.tools.zip.ZipExtraField%5B%5D)" class="member-name-link">zipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a new entry to the archive, takes care of duplicates as well.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="zipFile">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">zipFile</span></div>
</section>
</li>
<li>
<section class="detail" id="entries">
<h3>entries</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">entries</span></div>
</section>
</li>
<li>
<section class="detail" id="duplicate">
<h3>duplicate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">duplicate</span></div>
</section>
</li>
<li>
<section class="detail" id="archiveType">
<h3>archiveType</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">archiveType</span></div>
</section>
</li>
<li>
<section class="detail" id="emptyBehavior">
<h3>emptyBehavior</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">emptyBehavior</span></div>
</section>
</li>
<li>
<section class="detail" id="addedDirs">
<h3>addedDirs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">addedDirs</span></div>
</section>
</li>
<li>
<section class="detail" id="doubleFilePass">
<h3>doubleFilePass</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">doubleFilePass</span></div>
<div class="block">If this flag is true, execute() will run most operations twice,
 the first time with <a href="#skipWriting"><code>skipWriting</code></a> set to
 true and the second time with setting it to false.

 <p>The only situation in Ant's current code base where this is
 ever going to be true is if the jar task has been configured
 with a filesetmanifest other than "skip".</p></div>
</section>
</li>
<li>
<section class="detail" id="skipWriting">
<h3>skipWriting</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">skipWriting</span></div>
<div class="block">whether the methods should just perform some sort of dry-run.

 <p>Will only ever be true in the first pass if the task
 performs two passes because <a href="#doubleFilePass"><code>doubleFilePass</code></a> is true.</p></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Zip</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Zip</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isFirstPass()">
<h3>isFirstPass</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFirstPass</span>()</div>
<div class="block">Whether this is the first time the archive building methods are invoked.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if either <a href="#doubleFilePass"><code>doubleFilePass</code></a>
 is false or <a href="#skipWriting"><code>skipWriting</code></a> is true.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setZipfile(java.io.File)">
<h3>setZipfile</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setZipfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="block">This is the name/location of where to
 create the .zip file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - the path of the zipFile</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFile(java.io.File)">
<h3>setFile</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="block">This is the name/location of where to
 create the file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the path of the zipFile</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestFile(java.io.File)">
<h3>setDestFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</span></div>
<div class="block">The file to create; required.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destFile</code> - The new destination File</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDestFile()">
<h3>getDestFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestFile</span>()</div>
<div class="block">The file to create.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the destination file</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.io.File)">
<h3>setBasedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span></div>
<div class="block">Directory from which to archive files; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the base directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompress(boolean)">
<h3>setCompress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompress</span><wbr><span class="parameters">(boolean&nbsp;c)</span></div>
<div class="block">Whether we want to compress the files or only store them;
 optional, default=true;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - if true, compress the files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCompress()">
<h3>isCompress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCompress</span>()</div>
<div class="block">Whether we want to compress the files or only store them;</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the files are to be compressed</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFilesonly(boolean)">
<h3>setFilesonly</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFilesonly</span><wbr><span class="parameters">(boolean&nbsp;f)</span></div>
<div class="block">If true, emulate Sun's jar utility by not adding parent directories;
 optional, defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>f</code> - if true, emulate sun's jar by not adding parent directories</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUpdate(boolean)">
<h3>setUpdate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUpdate</span><wbr><span class="parameters">(boolean&nbsp;c)</span></div>
<div class="block">If true, updates an existing file, otherwise overwrite
 any existing one; optional defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - if true, updates an existing zip file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isInUpdateMode()">
<h3>isInUpdateMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isInUpdateMode</span>()</div>
<div class="block">Are we updating an existing archive?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if updating an existing archive</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">Adds a set of files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the fileset to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addZipfileset(org.apache.tools.ant.types.ZipFileSet)">
<h3>addZipfileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addZipfileset</span><wbr><span class="parameters">(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;set)</span></div>
<div class="block">Adds a set of files that can be
 read from an archive and be given a prefix/fullpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the zipfileset to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.ResourceCollection)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</span></div>
<div class="block">Add a collection of resources to be archived.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - the resources to archive</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addZipGroupFileset(org.apache.tools.ant.types.FileSet)">
<h3>addZipGroupFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addZipGroupFileset</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">Adds a group of zip files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the group (a fileset) to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)">
<h3>setDuplicate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDuplicate</span><wbr><span class="parameters">(<a href="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a>&nbsp;df)</span></div>
<div class="block">Sets behavior for when a duplicate file is about to be added -
 one of <code>add</code>, <code>preserve</code> or <code>fail</code>.
 Possible values are: <code>add</code> (keep both
 of the files); <code>preserve</code> (keep the first version
 of the file found); <code>fail</code> halt a problem
 Default for zip tasks is <code>add</code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>df</code> - a <code>Duplicate</code> enumerated value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">
<h3>setWhenempty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWhenempty</span><wbr><span class="parameters">(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</span></div>
<div class="block">Sets behavior of the task when no files match.
 Possible values are: <code>fail</code> (throw an exception
 and halt the build); <code>skip</code> (do not create
 any archive, but issue a warning); <code>create</code>
 (make an archive with no entries).
 Default for zip tasks is <code>skip</code>;
 for jar tasks, <code>create</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>we</code> - a <code>WhenEmpty</code> enumerated value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Encoding to use for filenames, defaults to the platform's
 default encoding.

 <p>For a list of possible values see <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html">https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html</a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the encoding name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEncoding()">
<h3>getEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEncoding</span>()</div>
<div class="block">Encoding to use for filenames.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the encoding to use</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeepCompression(boolean)">
<h3>setKeepCompression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepCompression</span><wbr><span class="parameters">(boolean&nbsp;keep)</span></div>
<div class="block">Whether the original compression of entries coming from a ZIP
 archive should be kept (for example when updating an archive).
 Default is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keep</code> - if true, keep the original compression</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setComment(java.lang.String)">
<h3>setComment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setComment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;comment)</span></div>
<div class="block">Comment to use for archive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>comment</code> - The content of the comment.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getComment()">
<h3>getComment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getComment</span>()</div>
<div class="block">Comment of the archive</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Comment of the archive.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLevel(int)">
<h3>setLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLevel</span><wbr><span class="parameters">(int&nbsp;level)</span></div>
<div class="block">Set the compression level to use.  Default is
 ZipOutputStream.DEFAULT_COMPRESSION.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>level</code> - compression level.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLevel()">
<h3>getLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getLevel</span>()</div>
<div class="block">Get the compression level.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>compression level.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRoundUp(boolean)">
<h3>setRoundUp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRoundUp</span><wbr><span class="parameters">(boolean&nbsp;r)</span></div>
<div class="block">Whether the file modification times will be rounded up to the
 next even number of seconds.

 <p>Zip archives store file modification times with a
 granularity of two seconds, so the times will either be rounded
 up or down.  If you round down, the archive will always seem
 out-of-date when you rerun the task, so the default is to round
 up.  Rounding up may lead to a different type of problems like
 JSPs inside a web archive that seem to be slightly more recent
 than precompiled pages, rendering precompilation useless.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a <code>boolean</code> value</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreserve0Permissions(boolean)">
<h3>setPreserve0Permissions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreserve0Permissions</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Assume 0 Unix mode is intentional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPreserve0Permissions()">
<h3>getPreserve0Permissions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getPreserve0Permissions</span>()</div>
<div class="block">Assume 0 Unix mode is intentional.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUseLanguageEncodingFlag(boolean)">
<h3>setUseLanguageEncodingFlag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUseLanguageEncodingFlag</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to set the language encoding flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUseLanguageEnodingFlag()">
<h3>getUseLanguageEnodingFlag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getUseLanguageEnodingFlag</span>()</div>
<div class="block">Whether the language encoding flag will be used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCreateUnicodeExtraFields(org.apache.tools.ant.taskdefs.Zip.UnicodeExtraField)">
<h3>setCreateUnicodeExtraFields</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCreateUnicodeExtraFields</span><wbr><span class="parameters">(<a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a>&nbsp;b)</span></div>
<div class="block">Whether Unicode extra fields will be created.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCreateUnicodeExtraFields()">
<h3>getCreateUnicodeExtraFields</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a></span>&nbsp;<span class="element-name">getCreateUnicodeExtraFields</span>()</div>
<div class="block">Whether Unicode extra fields will be created.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFallBackToUTF8(boolean)">
<h3>setFallBackToUTF8</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFallBackToUTF8</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to fall back to UTF-8 if a name cannot be encoded using
 the specified encoding.

 <p>Defaults to false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFallBackToUTF8()">
<h3>getFallBackToUTF8</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getFallBackToUTF8</span>()</div>
<div class="block">Whether to fall back to UTF-8 if a name cannot be encoded using
 the specified encoding.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setZip64Mode(org.apache.tools.ant.taskdefs.Zip.Zip64ModeAttribute)">
<h3>setZip64Mode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setZip64Mode</span><wbr><span class="parameters">(<a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a>&nbsp;b)</span></div>
<div class="block">Whether Zip64 extensions should be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getZip64Mode()">
<h3>getZip64Mode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></span>&nbsp;<span class="element-name">getZip64Mode</span>()</div>
<div class="block">Whether Zip64 extensions will be used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModificationtime(java.lang.String)">
<h3>setModificationtime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModificationtime</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;time)</span></div>
<div class="block">Set all stored file modification times to <code>time</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - Milliseconds since 1970-01-01 00:00, or
        <code>YYYY-MM-DD{T/ }HH:MM[:SS[.SSS]][ ][&plusmn;ZZ[[:]ZZ]]</code>, or
        <code>MM/DD/YYYY HH:MM[:SS] {AM/PM}</code>, where {a/b} indicates
        that you must choose one of a or b, and [c] indicates that you
        may use or omit c. &plusmn;ZZZZ is the timezone offset, and may be
        literally "Z" to mean GMT.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModificationtime()">
<h3>getModificationtime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getModificationtime</span>()</div>
<div class="block">The file modification time previously provided to
 <a href="#setModificationtime(java.lang.String)"><code>setModificationtime(String)</code></a> or <code>null</code> if unset.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">validate and build</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasUpdatedFile()">
<h3>hasUpdatedFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasUpdatedFile</span>()</div>
<div class="block">Get the value of the updatedFile attribute.
 This should only be called after executeMain has been
 called.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if executeMain has written to the zip file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="executeMain()">
<h3>executeMain</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeMain</span>()
                 throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Build the zip file.
 This is called twice if doubleFilePass is true.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAddingNewFiles()">
<h3>isAddingNewFiles</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAddingNewFiles</span>()</div>
<div class="block">Indicates if the task is adding new files into the archive as opposed to
 copying back unchanged files from the backup copy</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if adding new files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addResources(org.apache.tools.ant.types.FileSet,org.apache.tools.ant.types.Resource[],org.apache.tools.zip.ZipOutputStream)">
<h3>addResources</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addResources</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fileset,
 <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add the given resources.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileset</code> - may give additional information like fullpath or
 permissions.</dd>
<dd><code>resources</code> - the resources to add</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addResources(org.apache.tools.ant.types.ResourceCollection,org.apache.tools.ant.types.Resource[],org.apache.tools.zip.ZipOutputStream)">
<h3>addResources</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addResources</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc,
 <a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add the given resources.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rc</code> - may give additional information like fullpath or
 permissions.</dd>
<dd><code>resources</code> - the resources to add</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">
<h3>initZipOutputStream</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initZipOutputStream</span><wbr><span class="parameters">(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">method for subclasses to override</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zOut</code> - the zip output stream</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on output error</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)">
<h3>finalizeZipOutputStream</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finalizeZipOutputStream</span><wbr><span class="parameters">(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">method for subclasses to override</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zOut</code> - the zip output stream</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on output error</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createEmptyZip(java.io.File)">
<h3>createEmptyZip</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">createEmptyZip</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</span>
                          throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create an empty zip file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - the zip file</dd>
<dt>Returns:</dt>
<dd>true for historic reasons</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[],java.io.File,boolean)">
<h3>getResourcesToAdd</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></span>&nbsp;<span class="element-name">getResourcesToAdd</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</span>
                                      throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p>

 <p>This method first delegates to getNonFileSetResourcesToAdd
 and then invokes the FileSet-arg version.  All this to keep
 backwards compatibility for subclasses that don't know how to
 deal with non-FileSet ResourceCollections.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rcs</code> - The resource collections to grab resources from</dd>
<dd><code>zipFile</code> - intended archive file (may or may not exist)</dd>
<dd><code>needsUpdate</code> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.</dd>
<dt>Returns:</dt>
<dd>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it likes</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourcesToAdd(org.apache.tools.ant.types.FileSet[],java.io.File,boolean)">
<h3>getResourcesToAdd</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></span>&nbsp;<span class="element-name">getResourcesToAdd</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>[]&nbsp;filesets,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</span>
                                      throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filesets</code> - The filesets to grab resources from</dd>
<dd><code>zipFile</code> - intended archive file (may or may not exist)</dd>
<dd><code>needsUpdate</code> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.</dd>
<dt>Returns:</dt>
<dd>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it likes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[],java.io.File,boolean)">
<h3>getNonFileSetResourcesToAdd</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></span>&nbsp;<span class="element-name">getNonFileSetResourcesToAdd</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</span>
                                                throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rcs</code> - The filesets to grab resources from</dd>
<dd><code>zipFile</code> - intended archive file (may or may not exist)</dd>
<dd><code>needsUpdate</code> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.</dd>
<dt>Returns:</dt>
<dd>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it likes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="grabResources(org.apache.tools.ant.types.FileSet[])">
<h3>grabResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]</span>&nbsp;<span class="element-name">grabResources</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>[]&nbsp;filesets)</span></div>
<div class="block">Fetch all included and not excluded resources from the sets.

 <p>Included directories will precede included files.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filesets</code> - an array of filesets</dd>
<dt>Returns:</dt>
<dd>the resources included</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection[])">
<h3>grabNonFileSetResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]</span>&nbsp;<span class="element-name">grabNonFileSetResources</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs)</span></div>
<div class="block">Fetch all included and not excluded resources from the collections.

 <p>Included directories will precede included files.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rcs</code> - an array of resource collections</dd>
<dt>Returns:</dt>
<dd>the resources included</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">
<h3>zipDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add a directory to the zip stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the directory to add to the archive</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField[])">
<h3>zipDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add a directory to the zip stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the directory to add to the archive</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dd><code>extra</code> - ZipExtraFields to add</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipDir(org.apache.tools.ant.types.Resource,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField[])">
<h3>zipDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipDir</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;dir,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add a directory to the zip stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the directory to add to the archive</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dd><code>extra</code> - ZipExtraFields to add</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentExtraFields()">
<h3>getCurrentExtraFields</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]</span>&nbsp;<span class="element-name">getCurrentExtraFields</span>()</div>
<div class="block">Provides the extra fields for the zip entry currently being
 added to the archive - if any.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>ZipExtraField[]</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentExtraFields(org.apache.tools.zip.ZipExtraField[])">
<h3>setCurrentExtraFields</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentExtraFields</span><wbr><span class="parameters">(<a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</span></div>
<div class="block">Sets the extra fields for the zip entry currently being
 added to the archive - if any.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extra</code> - ZipExtraField[]</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Adds a new entry to the archive, takes care of duplicates as well.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - the stream to read data for the entry from.  The
 caller of the method is responsible for closing the stream.</dd>
<dd><code>zOut</code> - the stream to write to.</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive.</dd>
<dd><code>lastModified</code> - last modification time for the entry.</dd>
<dd><code>fromArchive</code> - the original archive we are copying this
 entry from, will be null if we are not copying from an archive.</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int,org.apache.tools.zip.ZipExtraField[])">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode,
 <a href="../../zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a>[]&nbsp;extra)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Adds a new entry to the archive, takes care of duplicates as well.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - the stream to read data for the entry from.  The
 caller of the method is responsible for closing the stream.</dd>
<dd><code>zOut</code> - the stream to write to.</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive.</dd>
<dd><code>lastModified</code> - last modification time for the entry.</dd>
<dd><code>fromArchive</code> - the original archive we are copying this
 entry from, will be null if we are not copying from an archive.</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dd><code>extra</code> - ZipExtraFields to add</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Method that gets called when adding from <code>java.io.File</code> instances.

 <p>This implementation delegates to the six-arg version.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to add to the archive</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addParentDirs(java.io.File,java.lang.String,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">
<h3>addParentDirs</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addParentDirs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;entry,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 int&nbsp;dirMode)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Ensure all parent dirs of a given entry have been added.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the base directory to use (may be null)</dd>
<dd><code>entry</code> - the entry name to create directories from</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>prefix</code> - a prefix to place on the created entries</dd>
<dd><code>dirMode</code> - the directory mode</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanUp()">
<h3>cleanUp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanUp</span>()</div>
<div class="block">Do any clean up necessary to allow this instance to be used again.

 <p>When we get here, the Zip file has been closed and all we
 need to do is to reset some globals.</p>

 <p>This method will only reset globals that have been changed
 during execute(), it will not alter the attributes or nested
 child elements.  If you want to reset the instance so that you
 can later zip a completely different set of files, you must use
 the reset method.</p></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#reset()"><code>reset()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reset()">
<h3>reset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">reset</span>()</div>
<div class="block">Makes this instance reset all attributes to their default
 values and forget all children.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#cleanUp()"><code>cleanUp()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEmpty(org.apache.tools.ant.types.Resource[][])">
<h3>isEmpty</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEmpty</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[][]&nbsp;r)</span></div>
<div class="block">Check is the resource arrays are empty.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - the arrays to check</dd>
<dt>Returns:</dt>
<dd>true if all individual arrays are empty</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectFileResources(org.apache.tools.ant.types.Resource[])">
<h3>selectFileResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</span>&nbsp;<span class="element-name">selectFileResources</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig)</span></div>
<div class="block">Drops all non-file resources from the given array.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orig</code> - the resources to filter</dd>
<dt>Returns:</dt>
<dd>the filters resources</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectDirectoryResources(org.apache.tools.ant.types.Resource[])">
<h3>selectDirectoryResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</span>&nbsp;<span class="element-name">selectDirectoryResources</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig)</span></div>
<div class="block">Drops all non-directory resources from the given array.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orig</code> - the resources to filter</dd>
<dt>Returns:</dt>
<dd>the filters resources</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectResources(org.apache.tools.ant.types.Resource[],org.apache.tools.ant.types.resources.selectors.ResourceSelector)">
<h3>selectResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]</span>&nbsp;<span class="element-name">selectResources</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;orig,
 <a href="../types/resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>&nbsp;selector)</span></div>
<div class="block">Drops all resources from the given array that are not selected</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orig</code> - the resources to filter</dd>
<dd><code>selector</code> - ResourceSelector</dd>
<dt>Returns:</dt>
<dd>the filters resources</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logWhenWriting(java.lang.String,int)">
<h3>logWhenWriting</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logWhenWriting</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;level)</span></div>
<div class="block">Logs a message at the given output level, but only if this is
 the pass that will actually create the archive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msg</code> - String</dd>
<dd><code>level</code> - int</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
