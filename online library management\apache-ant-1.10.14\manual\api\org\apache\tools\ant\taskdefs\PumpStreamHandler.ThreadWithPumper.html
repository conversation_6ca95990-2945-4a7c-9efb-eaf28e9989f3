<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>PumpStreamHandler.ThreadWithPumper (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: PumpStreamHandler, class: ThreadWithPumper">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class PumpStreamHandler.ThreadWithPumper" class="title">Class PumpStreamHandler.ThreadWithPumper</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">java.lang.Thread</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.PumpStreamHandler.ThreadWithPumper</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">protected static class </span><span class="element-name type-name-label">PumpStreamHandler.ThreadWithPumper</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a></span></div>
<div class="block">Specialized subclass that allows access to the running StreamPumper.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-java.lang.Thread">Nested classes/interfaces inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a></h2>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.Builder.html" title="class or interface in java.lang" class="external-link">Thread.Builder</a><sup><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.Builder.html#preview-java.lang.Thread.Builder" title="class or interface in java.lang" class="external-link">PREVIEW</a></sup>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.State.html" title="class or interface in java.lang" class="external-link">Thread.State</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.UncaughtExceptionHandler.html" title="class or interface in java.lang" class="external-link">Thread.UncaughtExceptionHandler</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.lang.Thread">Fields inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#MAX_PRIORITY" title="class or interface in java.lang" class="external-link">MAX_PRIORITY</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#MIN_PRIORITY" title="class or interface in java.lang" class="external-link">MIN_PRIORITY</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#NORM_PRIORITY" title="class or interface in java.lang" class="external-link">NORM_PRIORITY</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.StreamPumper)" class="member-name-link">ThreadWithPumper</a><wbr>(<a href="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a>&nbsp;p)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPumper()" class="member-name-link">getPumper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Thread">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html" title="class or interface in java.lang" class="external-link">Thread</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#activeCount()" title="class or interface in java.lang" class="external-link">activeCount</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#checkAccess()" title="class or interface in java.lang" class="external-link">checkAccess</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#countStackFrames()" title="class or interface in java.lang" class="external-link">countStackFrames</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#currentThread()" title="class or interface in java.lang" class="external-link">currentThread</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#dumpStack()" title="class or interface in java.lang" class="external-link">dumpStack</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#enumerate(java.lang.Thread%5B%5D)" title="class or interface in java.lang" class="external-link">enumerate</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getAllStackTraces()" title="class or interface in java.lang" class="external-link">getAllStackTraces</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getContextClassLoader()" title="class or interface in java.lang" class="external-link">getContextClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getDefaultUncaughtExceptionHandler()" title="class or interface in java.lang" class="external-link">getDefaultUncaughtExceptionHandler</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getId()" title="class or interface in java.lang" class="external-link">getId</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getName()" title="class or interface in java.lang" class="external-link">getName</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getPriority()" title="class or interface in java.lang" class="external-link">getPriority</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getStackTrace()" title="class or interface in java.lang" class="external-link">getStackTrace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getState()" title="class or interface in java.lang" class="external-link">getState</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getThreadGroup()" title="class or interface in java.lang" class="external-link">getThreadGroup</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#getUncaughtExceptionHandler()" title="class or interface in java.lang" class="external-link">getUncaughtExceptionHandler</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#holdsLock(java.lang.Object)" title="class or interface in java.lang" class="external-link">holdsLock</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#interrupt()" title="class or interface in java.lang" class="external-link">interrupt</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#interrupted()" title="class or interface in java.lang" class="external-link">interrupted</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#isAlive()" title="class or interface in java.lang" class="external-link">isAlive</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#isDaemon()" title="class or interface in java.lang" class="external-link">isDaemon</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#isInterrupted()" title="class or interface in java.lang" class="external-link">isInterrupted</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#isVirtual()" title="class or interface in java.lang" class="external-link">isVirtual</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#join()" title="class or interface in java.lang" class="external-link">join</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#join(long)" title="class or interface in java.lang" class="external-link">join</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#join(long,int)" title="class or interface in java.lang" class="external-link">join</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#join(java.time.Duration)" title="class or interface in java.lang" class="external-link">join</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#ofPlatform()" title="class or interface in java.lang" class="external-link">ofPlatform</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#ofVirtual()" title="class or interface in java.lang" class="external-link">ofVirtual</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#onSpinWait()" title="class or interface in java.lang" class="external-link">onSpinWait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#resume()" title="class or interface in java.lang" class="external-link">resume</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#run()" title="class or interface in java.lang" class="external-link">run</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setContextClassLoader(java.lang.ClassLoader)" title="class or interface in java.lang" class="external-link">setContextClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setDaemon(boolean)" title="class or interface in java.lang" class="external-link">setDaemon</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setDefaultUncaughtExceptionHandler(java.lang.Thread.UncaughtExceptionHandler)" title="class or interface in java.lang" class="external-link">setDefaultUncaughtExceptionHandler</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setName(java.lang.String)" title="class or interface in java.lang" class="external-link">setName</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setPriority(int)" title="class or interface in java.lang" class="external-link">setPriority</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#setUncaughtExceptionHandler(java.lang.Thread.UncaughtExceptionHandler)" title="class or interface in java.lang" class="external-link">setUncaughtExceptionHandler</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#sleep(long)" title="class or interface in java.lang" class="external-link">sleep</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#sleep(long,int)" title="class or interface in java.lang" class="external-link">sleep</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#sleep(java.time.Duration)" title="class or interface in java.lang" class="external-link">sleep</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#start()" title="class or interface in java.lang" class="external-link">start</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#startVirtualThread(java.lang.Runnable)" title="class or interface in java.lang" class="external-link">startVirtualThread</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#stop()" title="class or interface in java.lang" class="external-link">stop</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#suspend()" title="class or interface in java.lang" class="external-link">suspend</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#threadId()" title="class or interface in java.lang" class="external-link">threadId</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Thread.html#yield()" title="class or interface in java.lang" class="external-link">yield</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.StreamPumper)">
<h3>ThreadWithPumper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ThreadWithPumper</span><wbr><span class="parameters">(<a href="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a>&nbsp;p)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getPumper()">
<h3>getPumper</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</a></span>&nbsp;<span class="element-name">getPumper</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
