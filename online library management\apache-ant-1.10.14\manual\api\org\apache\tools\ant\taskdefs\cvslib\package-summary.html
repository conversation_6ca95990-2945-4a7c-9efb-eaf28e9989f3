<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.cvslib (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.cvslib">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.cvslib" class="title">Package org.apache.tools.ant.taskdefs.cvslib</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.cvslib</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ChangeLogTask.html" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Examines the output of cvs log and group related changes together.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ChangeLogWriter.html" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogWriter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class used to generate an XML changelog.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CVSEntry.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CVSEntry</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">CVS Entry.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CvsTagDiff.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagDiff</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Examines the output of cvs rdiff between two tags.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CvsTagEntry.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagEntry</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds the information of a line of rdiff</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CvsUser.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsUser</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Represents a CVS user with a userID and a full name.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CvsVersion.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsVersion</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">this task allows to find out the client and the server version of a
 CVS installation

 example usage :
 &lt;cvsversion
 cvsRoot=&quot;:pserver:<EMAIL>:/home/<USER>
 passfile=&quot;c:/programme/cygwin/home/<USER>/.cvspass&quot;
 clientversionproperty=&quot;apacheclient&quot;
 serverversionproperty=&quot;apacheserver&quot;   /&gt;

 the task can be used also in the API by calling its execute method,
 then calling getServerVersion and/or getClientVersion</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
