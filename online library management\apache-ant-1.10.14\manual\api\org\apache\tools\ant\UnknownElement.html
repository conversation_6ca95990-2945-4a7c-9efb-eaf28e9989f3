<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>UnknownElement (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: UnknownElement">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class UnknownElement" class="title">Class UnknownElement</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.UnknownElement</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">UnknownElement</span>
<span class="extends-implements">extends <a href="Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Wrapper class that holds all the information necessary to create a task
 or data type that did not exist when Ant started, or one which
 has had its definition updated to use a different implementation class.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="Task.html#target">target</a>, <a href="Task.html#taskName">taskName</a>, <a href="Task.html#taskType">taskType</a>, <a href="Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="ProjectComponent.html#description">description</a>, <a href="ProjectComponent.html#location">location</a>, <a href="ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">UnknownElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates an UnknownElement for the given element name.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addChild(org.apache.tools.ant.UnknownElement)" class="member-name-link">addChild</a><wbr>(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;child)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a child element to this element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#applyPreSet(org.apache.tools.ant.UnknownElement)" class="member-name-link">applyPreSet</a><wbr>(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;u)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is used then the realobject of the UE is a PreSetDefinition.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(java.lang.Object)" class="member-name-link">configure</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;realObject)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configure the given object from this UnknownElement</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copy(org.apache.tools.ant.Project)" class="member-name-link">copy</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;newProject)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make a copy of the unknown element and set it in the new project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the real object if it's a task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getChildren()" class="member-name-link">getChildren</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getComponentName()" class="member-name-link">getComponentName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNamespace()" class="member-name-link">getNamespace</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the namespace of the XML element associated with this component.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotFoundException(java.lang.String,java.lang.String)" class="member-name-link">getNotFoundException</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;what,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a very verbose exception for when a task/data type cannot
 be found.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getQName()" class="member-name-link">getQName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the qname of the XML element associated with this component.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRealThing()" class="member-name-link">getRealThing</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the configured object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTag()" class="member-name-link">getTag</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the name of the XML element which generated this unknown
 element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTask()" class="member-name-link">getTask</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the task instance after it has been created and if it is a task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskName()" class="member-name-link">getTaskName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the name to use in logging messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWrapper()" class="member-name-link">getWrapper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the RuntimeConfigurable instance for this UnknownElement, containing
 the configuration information.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleChildren(java.lang.Object,org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">handleChildren</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;parentWrapper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates child elements, creates children of the children
 (recursively), and sets attributes of the child elements.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorFlush(java.lang.String)" class="member-name-link">handleErrorFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles error output sent to System.err by this task or its real task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorOutput(java.lang.String)" class="member-name-link">handleErrorOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles error output sent to System.err by this task or its real task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleFlush(java.lang.String)" class="member-name-link">handleFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles output sent to System.out by this task or its real task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleInput(byte%5B%5D,int,int)" class="member-name-link">handleInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delegate to realThing if present and if it as task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleOutput(java.lang.String)" class="member-name-link">handleOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles output sent to System.out by this task or its real task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeObject(org.apache.tools.ant.UnknownElement,org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">makeObject</a><wbr>(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;w)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a named task or data type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeTask(org.apache.tools.ant.UnknownElement,org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">makeTask</a><wbr>(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;w)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a named task and configures it up to the init() stage.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#maybeConfigure()" class="member-name-link">maybeConfigure</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates the real object instance and child elements, then configures
 the attributes and text of the real object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNamespace(java.lang.String)" class="member-name-link">setNamespace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namespace)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the namespace of the XML element associated with this component.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setQName(java.lang.String)" class="member-name-link">setQName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the namespace qname of the XML element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRealThing(java.lang.Object)" class="member-name-link">setRealThing</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;realThing)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the configured object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#similar(java.lang.Object)" class="member-name-link">similar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">like contents equals, but ignores project</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="Task.html#getOwningTarget()">getOwningTarget</a>, <a href="Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="Task.html#getTaskType()">getTaskType</a>, <a href="Task.html#init()">init</a>, <a href="Task.html#isInvalid()">isInvalid</a>, <a href="Task.html#log(java.lang.String)">log</a>, <a href="Task.html#log(java.lang.String,int)">log</a>, <a href="Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="Task.html#log(java.lang.Throwable,int)">log</a>, <a href="Task.html#perform()">perform</a>, <a href="Task.html#reconfigure()">reconfigure</a>, <a href="Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="ProjectComponent.html#clone()">clone</a>, <a href="ProjectComponent.html#getDescription()">getDescription</a>, <a href="ProjectComponent.html#getLocation()">getLocation</a>, <a href="ProjectComponent.html#getProject()">getProject</a>, <a href="ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>UnknownElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UnknownElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span></div>
<div class="block">Creates an UnknownElement for the given element name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>elementName</code> - The name of the unknown element.
                    Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getChildren()">
<h3>getChildren</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&gt;</span>&nbsp;<span class="element-name">getChildren</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the list of nested UnknownElements for this UnknownElement.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTag()">
<h3>getTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTag</span>()</div>
<div class="block">Returns the name of the XML element which generated this unknown
 element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the XML element which generated this unknown
         element.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNamespace()">
<h3>getNamespace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getNamespace</span>()</div>
<div class="block">Return the namespace of the XML element associated with this component.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Namespace URI used in the xmlns declaration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNamespace(java.lang.String)">
<h3>setNamespace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNamespace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namespace)</span></div>
<div class="block">Set the namespace of the XML element associated with this component.
 This method is typically called by the XML processor.
 If the namespace is "ant:current", the component helper
 is used to get the current antlib uri.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>namespace</code> - URI used in the xmlns declaration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getQName()">
<h3>getQName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getQName</span>()</div>
<div class="block">Return the qname of the XML element associated with this component.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>namespace Qname used in the element declaration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setQName(java.lang.String)">
<h3>setQName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setQName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname)</span></div>
<div class="block">Set the namespace qname of the XML element.
 This method is typically called by the XML processor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>qname</code> - the qualified name of the element</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWrapper()">
<h3>getWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">getWrapper</span>()</div>
<div class="block">Get the RuntimeConfigurable instance for this UnknownElement, containing
 the configuration information.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#getWrapper()">getWrapper</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Returns:</dt>
<dd>the configuration info.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="maybeConfigure()">
<h3>maybeConfigure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">maybeConfigure</span>()
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Creates the real object instance and child elements, then configures
 the attributes and text of the real object. This unknown element
 is then replaced with the real object in the containing target's list
 of children.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#maybeConfigure()">maybeConfigure</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the configuration fails</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configure(java.lang.Object)">
<h3>configure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;realObject)</span></div>
<div class="block">Configure the given object from this UnknownElement</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>realObject</code> - the real object this UnknownElement is representing.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleOutput(java.lang.String)">
<h3>handleOutput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles output sent to System.out by this task or its real task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#handleOutput(java.lang.String)">handleOutput</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - The output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleInput(byte[],int,int)">
<h3>handleInput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">handleInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Delegate to realThing if present and if it as task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buffer</code> - the buffer into which data is to be read.</dd>
<dd><code>offset</code> - the offset into the buffer at which data is stored.</dd>
<dd><code>length</code> - the amount of data to read.</dd>
<dt>Returns:</dt>
<dd>the number of bytes read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the data cannot be read.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="Task.html#handleInput(byte%5B%5D,int,int)"><code>Task.handleInput(byte[], int, int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleFlush(java.lang.String)">
<h3>handleFlush</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles output sent to System.out by this task or its real task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#handleFlush(java.lang.String)">handleFlush</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - The output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorOutput(java.lang.String)">
<h3>handleErrorOutput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles error output sent to System.err by this task or its real task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - The error output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorFlush(java.lang.String)">
<h3>handleErrorFlush</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles error output sent to System.err by this task or its real task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - The error output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()</div>
<div class="block">Executes the real object if it's a task. If it's not a task
 (e.g. a data type) then this method does nothing.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addChild(org.apache.tools.ant.UnknownElement)">
<h3>addChild</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addChild</span><wbr><span class="parameters">(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;child)</span></div>
<div class="block">Adds a child element to this element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>child</code> - The child element to add. Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleChildren(java.lang.Object,org.apache.tools.ant.RuntimeConfigurable)">
<h3>handleChildren</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleChildren</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;parentWrapper)</span>
                       throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Creates child elements, creates children of the children
 (recursively), and sets attributes of the child elements.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - The configured object for the parent.
               Must not be <code>null</code>.</dd>
<dd><code>parentWrapper</code> - The wrapper containing child wrappers
                      to be configured. Must not be <code>null</code>
                      if there are any children.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the children cannot be configured.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getComponentName()">
<h3>getComponentName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getComponentName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the component name - uses ProjectHelper#genComponentName()</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="applyPreSet(org.apache.tools.ant.UnknownElement)">
<h3>applyPreSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">applyPreSet</span><wbr><span class="parameters">(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;u)</span></div>
<div class="block">This is used then the realobject of the UE is a PreSetDefinition.
 This is also used when a presetdef is used on a presetdef
 The attributes, elements and text are applied to this
 UE.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>u</code> - an UnknownElement containing the attributes, elements and text</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeObject(org.apache.tools.ant.UnknownElement,org.apache.tools.ant.RuntimeConfigurable)">
<h3>makeObject</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">makeObject</span><wbr><span class="parameters">(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;w)</span></div>
<div class="block">Creates a named task or data type. If the real object is a task,
 it is configured up to the init() stage.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ue</code> - The unknown element to create the real object for.
           Must not be <code>null</code>.</dd>
<dd><code>w</code> - Ignored in this implementation.</dd>
<dt>Returns:</dt>
<dd>the task or data type represented by the given unknown element.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeTask(org.apache.tools.ant.UnknownElement,org.apache.tools.ant.RuntimeConfigurable)">
<h3>makeTask</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">makeTask</span><wbr><span class="parameters">(<a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue,
 <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;w)</span></div>
<div class="block">Creates a named task and configures it up to the init() stage.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ue</code> - The UnknownElement to create the real task for.
           Must not be <code>null</code>.</dd>
<dd><code>w</code> - Ignored.</dd>
<dt>Returns:</dt>
<dd>the task specified by the given unknown element, or
         <code>null</code> if the task name is not recognised.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNotFoundException(java.lang.String,java.lang.String)">
<h3>getNotFoundException</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">getNotFoundException</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;what,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns a very verbose exception for when a task/data type cannot
 be found.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>what</code> - The kind of thing being created. For example, when
             a task name could not be found, this would be
             <code>"task"</code>. Should not be <code>null</code>.</dd>
<dd><code>name</code> - The name of the element which could not be found.
             Should not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a detailed description of what might have caused the problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTaskName()">
<h3>getTaskName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTaskName</span>()</div>
<div class="block">Returns the name to use in logging messages.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Task.html#getTaskName()">getTaskName</a></code>&nbsp;in class&nbsp;<code><a href="Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Returns:</dt>
<dd>the name to use in logging messages.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTask()">
<h3>getTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">getTask</span>()</div>
<div class="block">Returns the task instance after it has been created and if it is a task.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a task instance or <code>null</code> if the real object is not
         a task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRealThing()">
<h3>getRealThing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getRealThing</span>()</div>
<div class="block">Return the configured object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the real thing whatever it is</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRealThing(java.lang.Object)">
<h3>setRealThing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRealThing</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;realThing)</span></div>
<div class="block">Set the configured object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>realThing</code> - the configured object</dd>
<dt>Since:</dt>
<dd>ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="similar(java.lang.Object)">
<h3>similar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">similar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
<div class="block">like contents equals, but ignores project</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>obj</code> - the object to check against</dd>
<dt>Returns:</dt>
<dd>true if this UnknownElement has the same contents the other</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="copy(org.apache.tools.ant.Project)">
<h3>copy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></span>&nbsp;<span class="element-name">copy</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;newProject)</span></div>
<div class="block">Make a copy of the unknown element and set it in the new project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newProject</code> - the project to create the UE in.</dd>
<dt>Returns:</dt>
<dd>the copied UE.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
