<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.listener (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.listener">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.listener" class="title">Package org.apache.tools.ant.listener</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.listener</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Uses ANSI Color Code Sequences to colorize messages
 sent to the console.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BigProjectLogger.html" title="class in org.apache.tools.ant.listener">BigProjectLogger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a special logger that is designed to make it easier to work
 with big projects, those that use imports and
 subant to build complex systems.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Jakarta Commons Logging listener.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Log4jListener.html" title="class in org.apache.tools.ant.listener">Log4jListener</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">Apache Log4j (1) is not developed any more.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Buffers log messages from DefaultLogger, and sends an e-mail with the
  results.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ProfileLogger.html" title="class in org.apache.tools.ant.listener">ProfileLogger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a special logger that is designed to profile builds.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SilentLogger.html" title="class in org.apache.tools.ant.listener">SilentLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A logger which logs nothing but build failure and what task might output</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Displays subproject names like <a href="BigProjectLogger.html" title="class in org.apache.tools.ant.listener"><code>BigProjectLogger</code></a>
 but is otherwise as quiet as <a href="../NoBannerLogger.html" title="class in org.apache.tools.ant"><code>NoBannerLogger</code></a>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Like a normal logger, except with timed outputs</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
