<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
<title>IDE Integration</title>
<base target="mainFrame"/>
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>IDE Integration</h3>
<p>
All the modern Java IDEs support Apache Ant almost out of the box.
</p>

<ul>
  <li>
    <a href="Integration/jext-plugin.html">AntWork Plugin for the Jext Java Text Editor
    (unbundled)</a>
  </li>
  <li>
    <a href="http://jdee.sourceforge.net/" target="_top">JDEE (Java Development Environment for
    Emacs)</a> has built-in text Ant integration: selection of target through text field, execution,
    hyperlink to compilation errors. Installation: built-in JDEE 2.2.8 or later. Configuration:
    through customize menu "Jde Build Function"
  </li>
  <li>
    <a href="https://www.jetbrains.com/idea/" target="_top">IDEA</a> has built-in GUI ANT
    integration: GUI selection of targets, execution, hyperlink to compilation errors
  </li>
  <li>
    <a href="https://netbeans.org/" target="_top">NetBeans</a> IDE uses Ant as the basis for its
    project system starting with the 4.0 release
  </li>
  <li>
    <a href="http://jedit.org/" target="_top">jEdit</a> is an open source Java IDE with some great
    plugins for Java dev, a good XML editor and the Antfarm plugin to execute targets in a build
    file
  </li>
  <li>
    <a href="https://eclipse.org/" target="_top">Eclipse</a> is IBM's counterpoint to NetBeans; an
    open source IDE with Java and Ant support
  </li>
  <li>
    <a href="http://www.placidsystems.com/virtualant/" target="_top">Virtual Ant</a> GUI allows you
    to work on a Virtual File System without dealing with the XML; plugs into Eclipse, Netbeans
    &amp; IntelliJ
  </li>
  <li>
    <a href="https://www.ibm.com/developerworks/offers/wsad2.html" target="_top">WebSphere Studio
    Application Developer</a>
  </li>
</ul>

</body>
</html>
