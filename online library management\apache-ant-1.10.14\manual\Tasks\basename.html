<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Basename Task</title>
</head>

<body>

<h2 id="echo">Basename</h2>

<h3>Description</h3>
<p>Task to determine the basename of a specified file, optionally minus a specified suffix.</p>
<p>When this task executes, it will set the specified property to the value of the last path element
of the specified file. If <var>file</var> is a directory, the basename will be the last directory
element. If <var>file</var> is a full-path, relative-path, or simple filename, the basename will be
the simple file name, without any directory elements.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The path to take the basename of.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>property</td>
    <td>The name of the property to set.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>suffix</td>
    <td>The suffix to remove from the resulting basename (specified either with or without
    the <q>.</q>).</td>
    <td>No</td>
  </tr>
</table>

<h3>Examples</h3>
<p>Set <code>jar.filename</code> to <samp>myjar.jar</samp>, if <code>lib.jarfile</code> is defined
as either a full-path filename (eg., <samp>/usr/local/lib/myjar.jar</samp>), a relative-path
filename (eg., <samp>lib/myjar.jar</samp>), or a simple filename (eg., <samp>myjar.jar</samp>).</p>
<pre>&lt;basename property=&quot;jar.filename&quot; file=&quot;${lib.jarfile}&quot;/&gt;</pre>

<p>Set <code>cmdname</code> to <samp>foo</samp>.</p>
<pre>
&lt;basename property=&quot;cmdname&quot; file=&quot;D:/usr/local/foo.exe&quot;
          suffix=&quot;.exe&quot;/&gt;</pre>

<p>Set <code>temp.dirname</code> to the last directory element of the path defined for
the <code>TEMP</code> environment variable.</p>
<pre>
&lt;property environment=&quot;env&quot;/&gt;
&lt;basename property=&quot;temp.dirname&quot; file=&quot;${env.TEMP}&quot;/&gt;
</pre>

</body>
</html>
