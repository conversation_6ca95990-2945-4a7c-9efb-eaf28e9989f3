<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional.net Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="tree: package: org.apache.tools.ant.taskdefs.optional.net">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.taskdefs.optional.net</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="../../../DirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant">DirectoryScanner</a> (implements org.apache.tools.ant.<a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a>, org.apache.tools.ant.types.<a href="../../../types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>, org.apache.tools.ant.types.selectors.<a href="../../../types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirrorImpl.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.<a href="../../../types/EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.Action.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.FTPSystemType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.Granularity.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.LanguageCode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTask.Action.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Action</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTask.FTPSystemType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.FTPSystemType</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTask.Granularity.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask.Granularity</a></li>
</ul>
</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" class="type-name-link external-link" title="class or interface in java.io">File</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPFileProxy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirrorImpl.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPFileProxy</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.FTPDirectoryScanner.AntFTPFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner.AntFTPFile</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.FTPDirectoryScanner.AntFTPRootFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner.AntFTPRootFile</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirrorImpl.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl</a> (implements org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.<a href="../../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.<a href="../../../Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.email.<a href="../../email/EmailTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="MimeMail.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">MimeMail</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTP.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="RExecTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="SetProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">SetProxy</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="TelnetTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="RExecTask.RExecSubTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecSubTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="RExecTask.RExecRead.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecRead</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="RExecTask.RExecWrite.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.RExecWrite</a></li>
</ul>
</li>
<li class="circle">org.apache.commons.net.SocketClient
<ul>
<li class="circle">org.apache.commons.net.bsd.RExecClient
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="RExecTask.AntRExecClient.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask.AntRExecClient</a></li>
</ul>
</li>
<li class="circle">org.apache.commons.net.telnet.TelnetClient
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="TelnetTask.AntTelnetClient.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.AntTelnetClient</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="TelnetTask.TelnetSubTask.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetSubTask</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="TelnetTask.TelnetRead.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetRead</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="TelnetTask.TelnetWrite.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.TelnetWrite</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.net.<a href="FTPTaskMirror.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirror</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
