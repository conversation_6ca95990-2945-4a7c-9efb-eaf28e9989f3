<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.filters (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.filters" class="title">Package org.apache.tools.ant.filters</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.filters</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="util/package-summary.html">org.apache.tools.ant.filters.util</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for core filter readers.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Parameterized base class for core filter readers.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Interface indicating that a reader may be chained to another one.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ClassConstants.html" title="class in org.apache.tools.ant.filters">ClassConstants</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Assembles the constants declared in a Java class in
 <code>key1=value1(line separator)key2=value2</code>
 format.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ConcatFilter.html" title="class in org.apache.tools.ant.filters">ConcatFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Concats a file before and/or after the file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="EscapeUnicode.html" title="class in org.apache.tools.ant.filters">EscapeUnicode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This method converts non-latin characters to unicode escapes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExpandProperties.html" title="class in org.apache.tools.ant.filters">ExpandProperties</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Expands Ant properties, if any, in the data.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FixCrLfFilter.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts text to local OS formatting conventions, as well as repair text
 damaged by misconfigured or misguided editors or file transfer programs.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FixCrLfFilter.AddAsisRemove.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.AddAsisRemove</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FixCrLfFilter.CrLf.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter.CrLf</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf" and "crlf".</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="HeadFilter.html" title="class in org.apache.tools.ant.filters">HeadFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Reads the first <code>n</code> lines of a stream.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LineContains.html" title="class in org.apache.tools.ant.filters">LineContains</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Filter which includes only those lines that contain the user-specified
 strings.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LineContains.Contains.html" title="class in org.apache.tools.ant.filters">LineContains.Contains</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds a contains element</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LineContainsRegExp.html" title="class in org.apache.tools.ant.filters">LineContainsRegExp</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Filter which includes only those lines that contain the user-specified
 regular expression matching strings.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Native2AsciiFilter.html" title="class in org.apache.tools.ant.filters">Native2AsciiFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A filter that performs translations from characters to their
 Unicode-escape sequences and vice-versa.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PrefixLines.html" title="class in org.apache.tools.ant.filters">PrefixLines</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Attaches a prefix to every line.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ReplaceTokens.html" title="class in org.apache.tools.ant.filters">ReplaceTokens</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Replaces tokens in the original input with user-supplied values.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ReplaceTokens.Token.html" title="class in org.apache.tools.ant.filters">ReplaceTokens.Token</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Holds a token</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SortFilter.html" title="class in org.apache.tools.ant.filters">SortFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">
 Sort a file before and/or after the file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="StringInputStream.html" title="class in org.apache.tools.ant.filters">StringInputStream</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Wraps a String as an InputStream.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="StripJavaComments.html" title="class in org.apache.tools.ant.filters">StripJavaComments</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a Java comment and string stripper reader that filters
 those lexical tokens out for purposes of simple Java parsing.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="StripLineBreaks.html" title="class in org.apache.tools.ant.filters">StripLineBreaks</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Filter to flatten the stream to a single line.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="StripLineComments.html" title="class in org.apache.tools.ant.filters">StripLineComments</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This filter strips line comments.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="StripLineComments.Comment.html" title="class in org.apache.tools.ant.filters">StripLineComments.Comment</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The class that holds a comment representation.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SuffixLines.html" title="class in org.apache.tools.ant.filters">SuffixLines</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Attaches a suffix to every line.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TabsToSpaces.html" title="class in org.apache.tools.ant.filters">TabsToSpaces</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts tabs to spaces.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TailFilter.html" title="class in org.apache.tools.ant.filters">TailFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Reads the last <code>n</code> lines of a stream.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This splits up input into tokens and passes
 the tokens to a sequence of filters.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.ChainableReaderFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract class that converts derived filter classes into
 ChainableReaderFilter's</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">filter to filter tokens matching regular expressions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Simple filter to filter lines contains strings</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Filter to delete characters</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.FileTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">class to read the complete input into a string</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">string filters implement this interface</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Filter remove empty tokens</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">filter to replace regex.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Simple replace string filter.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TokenFilter.StringTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">class to tokenize the input as areas separated
 by white space, or by a specified list of
 delim characters.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Filter to trim white space</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UniqFilter.html" title="class in org.apache.tools.ant.filters">UniqFilter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Like the Unix uniq(1) command, only returns tokens that are
 different from their ancestor token.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
