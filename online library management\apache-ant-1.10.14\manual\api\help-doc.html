<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>API Help (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="help">
<meta name="generator" content="javadoc/HelpWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="help-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li class="nav-bar-cell1-rev">Help</li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Help:</p>
<ul>
<li><a href="#help-navigation">Navigation</a></li>
<li><a href="#help-pages">Pages</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Help:&nbsp;</li>
<li><a href="#help-navigation">Navigation</a>&nbsp;|&nbsp;</li>
<li><a href="#help-pages">Pages</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<h1 class="title">JavaDoc Help</h1>
<ul class="help-toc">
<li><a href="#help-navigation">Navigation</a>: 
<ul class="help-subtoc">
<li><a href="#search">Search</a></li>
</ul>
</li>
<li><a href="#help-pages">Kinds of Pages</a>: 
<ul class="help-subtoc">
<li><a href="#overview">Overview</a></li>
<li><a href="#package">Package</a></li>
<li><a href="#class">Class or Interface</a></li>
<li><a href="#doc-file">Other Files</a></li>
<li><a href="#tree">Tree (Class Hierarchy)</a></li>
<li><a href="#deprecated">Deprecated API</a></li>
<li><a href="#constant-values">Constant Field Values</a></li>
<li><a href="#serialized-form">Serialized Form</a></li>
<li><a href="#all-packages">All Packages</a></li>
<li><a href="#all-classes">All Classes and Interfaces</a></li>
<li><a href="#index">Index</a></li>
</ul>
</li>
</ul>
<hr>
<div class="sub-title">
<h2 id="help-navigation">Navigation</h2>
Starting from the <a href="index.html">Overview</a> page, you can browse the documentation using the links in each page, and in the navigation bar at the top of each page. The <a href="index-all.html">Index</a> and Search box allow you to navigate to specific declarations and summary pages, including: <a href="allpackages-index.html">All Packages</a>, <a href="allclasses-index.html">All Classes and Interfaces</a>
<section class="help-section" id="search">
<h3>Search</h3>
<p>You can search for definitions of modules, packages, types, fields, methods, system properties and other terms defined in the API. These items can be searched using part or all of the name, optionally using "camelCase" abbreviations, or multiple search terms separated by whitespace. Some examples:</p>
<ul class="help-section-list">
<li><code>"j.l.obj"</code> matches "java.lang.Object"</li>
<li><code>"InpStr"</code> matches "java.io.InputStream"</li>
<li><code>"math exact long"</code> matches "java.lang.Math.absExact(long)"</li>
</ul>
<p>Refer to the <a href="https://docs.oracle.com/en/java/javase/20/docs/specs/javadoc/javadoc-search-spec.html">Javadoc Search Specification</a> for a full description of search features.</p>
</section>
</div>
<hr>
<div class="sub-title">
<h2 id="help-pages">Kinds of Pages</h2>
The following sections describe the different kinds of pages in this collection.
<section class="help-section" id="overview">
<h3>Overview</h3>
<p>The <a href="index.html">Overview</a> page is the front page of this API document and provides a list of all packages with a summary for each.  This page can also contain an overall description of the set of packages.</p>
</section>
<section class="help-section" id="package">
<h3>Package</h3>
<p>Each package has a page that contains a list of its classes and interfaces, with a summary for each. These pages may contain the following categories:</p>
<ul class="help-section-list">
<li>Interfaces</li>
<li>Classes</li>
<li>Enum Classes</li>
<li>Exception Classes</li>
<li>Annotation Interfaces</li>
</ul>
</section>
<section class="help-section" id="class">
<h3>Class or Interface</h3>
<p>Each class, interface, nested class and nested interface has its own separate page. Each of these pages has three sections consisting of a declaration and description, member summary tables, and detailed member descriptions. Entries in each of these sections are omitted if they are empty or not applicable.</p>
<ul class="help-section-list">
<li>Class Inheritance Diagram</li>
<li>Direct Subclasses</li>
<li>All Known Subinterfaces</li>
<li>All Known Implementing Classes</li>
<li>Class or Interface Declaration</li>
<li>Class or Interface Description</li>
</ul>
<br>
<ul class="help-section-list">
<li>Nested Class Summary</li>
<li>Enum Constant Summary</li>
<li>Field Summary</li>
<li>Property Summary</li>
<li>Constructor Summary</li>
<li>Method Summary</li>
<li>Required Element Summary</li>
<li>Optional Element Summary</li>
</ul>
<br>
<ul class="help-section-list">
<li>Enum Constant Details</li>
<li>Field Details</li>
<li>Property Details</li>
<li>Constructor Details</li>
<li>Method Details</li>
<li>Element Details</li>
</ul>
<p><span class="help-note">Note:</span> Annotation interfaces have required and optional elements, but not methods. Only enum classes have enum constants. The components of a record class are displayed as part of the declaration of the record class. Properties are a feature of JavaFX.</p>
<p>The summary entries are alphabetical, while the detailed descriptions are in the order they appear in the source code. This preserves the logical groupings established by the programmer.</p>
</section>
<section class="help-section" id="doc-file">
<h3>Other Files</h3>
<p>Packages and modules may contain pages with additional information related to the declarations nearby.</p>
</section>
<section class="help-section" id="tree">
<h3>Tree (Class Hierarchy)</h3>
<p>There is a <a href="overview-tree.html">Class Hierarchy</a> page for all packages, plus a hierarchy for each package. Each hierarchy page contains a list of classes and a list of interfaces. Classes are organized by inheritance structure starting with <code>java.lang.Object</code>. Interfaces do not inherit from <code>java.lang.Object</code>.</p>
<ul class="help-section-list">
<li>When viewing the Overview page, clicking on TREE displays the hierarchy for all packages.</li>
<li>When viewing a particular package, class or interface page, clicking on TREE displays the hierarchy for only that package.</li>
</ul>
</section>
<section class="help-section" id="deprecated">
<h3>Deprecated API</h3>
<p>The <a href="deprecated-list.html">Deprecated API</a> page lists all of the API that have been deprecated. A deprecated API is not recommended for use, generally due to shortcomings, and a replacement API is usually given. Deprecated APIs may be removed in future implementations.</p>
</section>
<section class="help-section" id="constant-values">
<h3>Constant Field Values</h3>
<p>The <a href="constant-values.html">Constant Field Values</a> page lists the static final fields and their values.</p>
</section>
<section class="help-section" id="serialized-form">
<h3>Serialized Form</h3>
<p>Each serializable or externalizable class has a description of its serialization fields and methods. This information is of interest to those who implement rather than use the API. While there is no link in the navigation bar, you can get to this information by going to any serialized class and clicking "Serialized Form" in the "See Also" section of the class description.</p>
</section>
<section class="help-section" id="all-packages">
<h3>All Packages</h3>
<p>The <a href="allpackages-index.html">All Packages</a> page contains an alphabetic index of all packages contained in the documentation.</p>
</section>
<section class="help-section" id="all-classes">
<h3>All Classes and Interfaces</h3>
<p>The <a href="allclasses-index.html">All Classes and Interfaces</a> page contains an alphabetic index of all classes and interfaces contained in the documentation, including annotation interfaces, enum classes, and record classes.</p>
</section>
<section class="help-section" id="index">
<h3>Index</h3>
<p>The <a href="index-all.html">Index</a> contains an alphabetic index of all classes, interfaces, constructors, methods, and fields in the documentation, as well as summary pages such as <a href="allpackages-index.html">All Packages</a>, <a href="allclasses-index.html">All Classes and Interfaces</a>.</p>
</section>
</div>
<hr>
<span class="help-footnote">This help file applies to API documentation generated by the standard doclet.</span></main>
</div>
</div>
</body>
</html>
