<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Uptodate Task</title>
</head>

<body>

<h2 id="uptodate">Uptodate</h2>
<h3>Description</h3>
<p>Sets a property if a target file or set of target files is more up-to-date than a source file or
set of source files. A single source file is specified using the <var>srcfile</var> attribute. A set
of source files is specified using the nested <code>&lt;srcfiles&gt;</code> elements. These
are <a href="../Types/fileset.html">FileSet</a>s, whereas multiple target files are specified using
a nested <a href="../Types/mapper.html"><code>&lt;mapper&gt;</code></a> element.</p>
<p>By default, the value of the property is set to <q>true</q> if the timestamp of the source
file(s) is not more recent than the timestamp of the corresponding target file(s). You can set the
value to something other than the default by specifying the <var>value</var> attribute.</p>
<p>If a <code>&lt;srcfiles&gt;</code> element is used, without also specifying
a <code>&lt;mapper&gt;</code> element, the default behavior is to use
a <a href="../Types/mapper.html#merge-mapper">merge mapper</a>, with the <var>to</var> attribute set
to the value of the <var>targetfile</var> attribute.</p>
<p>Normally, this task is used to set properties that are useful to avoid target execution depending
on the relative age of the specified files.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The name of the property to set.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The value to set the property to.</td>
    <td>No; defaults to <q>true</q>.</td>
  </tr>
  <tr>
    <td>srcfile</td>
    <td>The file to check against the target file(s).</td>
    <td>Yes, unless a nested
    <code>&lt;srcfiles&gt;</code> or <code>&lt;srcresources&gt;</code>
    element is present.</td>
  </tr>
  <tr>
    <td>targetfile</td>
    <td>The file for which we want to determine the status.</td>
    <td>Yes, unless a nested <code>&lt;mapper&gt;</code> element is present.</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4 id="srcfiles">srcfiles</h4>
<p>The nested <code>&lt;srcfiles&gt;</code> element is a <a href="../Types/fileset.html">fileset</a>
and allows you to specify a set of files to check against the target file(s).</p>

<p><strong>Note</strong>: You can specify either the <var>srcfile</var> attribute or
nested <code>&lt;srcfiles&gt;</code> elements, but not both.

<p>Note that the task will completely ignore any directories that seem to be matched by
the <var>srcfiles</var> fileset, it will only consider normal files.  If you need logic that applies
to directories as well, use a nested <code>srcresources</code> element and a <code>dirset</code>
(for example).</p>

<h4 id="srcresources">srcresources</h4>
<p><em>Since Apache Ant 1.7</em></p>
<p>The nested <code>&lt;srcresources&gt;</code> element is
a <a href="../Types/resources.html#union">union</a> and allows you to specify a collection of
resources to check against the target file(s).</p>

<h4 id="mapper">mapper</h4>
<p>The nested <code>&lt;mapper&gt;</code> element allows you to specify a set of target files to
check for being up-to-date with respect to a set of source files.</p>
<p>The mapper <var>to</var> attribute is relative to the target file, or to the <var>dir</var>
attribute of the nested <code>srcfiles</code> element.</p>
<p><em>Since Ant 1.6.3</em>, one can use a <code>filenamemapper</code> type in place of the mapper
element.</p>
<h3>Examples</h3>
<p>Set the property <code>xmlBuild.notRequired</code> to <q>true</q> if
the <samp>${deploy}/xmlClasses.jar</samp> file is more up-to-date than any of the DTD files in
the <samp>${src}/xml</samp> directory.</p>
<pre>
&lt;uptodate property=&quot;xmlBuild.notRequired&quot; targetfile=&quot;${deploy}\xmlClasses.jar&quot;&gt;
  &lt;srcfiles dir=&quot;${src}/xml&quot; includes=&quot;**/*.dtd&quot;/&gt;
&lt;/uptodate&gt;</pre>
<p>This can be written as:</p>
<pre>
&lt;uptodate property=&quot;xmlBuild.notRequired&quot;&gt;
  &lt;srcfiles dir= &quot;${src}/xml&quot; includes=&quot;**/*.dtd&quot;/&gt;
  &lt;mapper type=&quot;merge&quot; to=&quot;${deploy}\xmlClasses.jar&quot;/&gt;
&lt;/uptodate&gt;</pre>
<p>as well.</p>

<p>The <code>xmlBuild.notRequired</code> property can then be used in a <code>&lt;target&gt;</code>
tag's <var>unless</var> attribute to conditionally run that target. For example, running the
following target:</p>
<pre>
&lt;target name=&quot;xmlBuild&quot; depends=&quot;chkXmlBuild&quot; unless=&quot;xmlBuild.notRequired&quot;&gt;
  ...
&lt;/target&gt;</pre>
<p>will first run the <q>chkXmlBuild</q> target, which contains the <code>&lt;uptodate&gt;</code>
task that determines whether <code>xmlBuild.notRequired</code> gets set. The property named in
the <var>unless</var> attribute is then checked for being set/not set.  If it did get set (ie., the
jar file is up-to-date), then the <q>xmlBuild</q> target won't be run.</p>

<p>The following example shows a single source file being checked against a single target file:</p>
<pre>
&lt;uptodate property=&quot;isUpToDate&quot;
          srcfile=&quot;/usr/local/bin/testit&quot;
          targetfile=&quot;${build}/.flagfile&quot;/&gt;
</pre>
<p>sets the property <code>isUpToDate</code> to <q>true</q> if <samp>/usr/local/bin/testit</samp> is
not newer than <samp>${build}/.flagfile</samp>.</p>
<p>The following shows usage of a relative mapper.</p>
<pre>
&lt;uptodate property="checkUptodate.uptodate"&gt;
  &lt;srcfiles dir="src" includes="*"/&gt;
  &lt;mapper type="merge" to="../dest/output.done"/&gt;
&lt;/uptodate&gt;
&lt;echo message="checkUptodate result: ${checkUptodate.uptodate}"/&gt;</pre>
<p>The previous example can be a bit confusing, so it may be better to use absolute paths:</p>
<pre>
&lt;property name="dest.dir" location="dest"/&gt;
&lt;uptodate property="checkUptodate.uptodate"&gt;
  &lt;srcfiles dir="src" includes="*"/&gt;
  &lt;mapper type="merge" to="${dest.dir}/output.done"/&gt;
&lt;/uptodate&gt;</pre>

</body>
</html>
