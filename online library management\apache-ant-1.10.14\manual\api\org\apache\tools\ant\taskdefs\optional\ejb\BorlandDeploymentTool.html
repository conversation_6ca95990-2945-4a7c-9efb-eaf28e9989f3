<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>BorlandDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: BorlandDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class BorlandDeploymentTool" class="title">Class BorlandDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code>, <code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">BorlandDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a>
implements <a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></span></div>
<div class="block">BorlandDeploymentTool is dedicated to the Borland Application Server 4.5 and 4.5.1
 This task generates and compiles the stubs and skeletons for all ejb described into the
 Deployment Descriptor, builds the jar file including the support files and verify
 whether the produced jar is valid or not.
 The supported options are:
 <ul>
 <li>debug  (boolean)    : turn on the debug mode for generation of
                           stubs and skeletons (default:false)</li>
 <li>verify (boolean)    : turn on the verification at the end of the jar
                           production  (default:true) </li>
 <li>verifyargs (String) : add optional argument to verify command
                           (see vbj com.inprise.ejb.util.Verify)</li>
 <li>basdtd (String)     : location of the BAS DTD </li>
 <li>generateclient (boolean) : turn on the client jar file generation </li>
 <li>version (int)       : tell what is the Borland appserver version 4 or 5 </li>
 </ul>

<pre>

      &lt;ejbjar srcdir=&quot;${build.classes}&quot;
               basejarname=&quot;vsmp&quot;
               descriptordir=&quot;${rsc.dir}/hrmanager&quot;&gt;
        &lt;borland destdir=&quot;tstlib&quot;&gt;
          &lt;classpath refid=&quot;classpath&quot; /&gt;
        &lt;/borland&gt;
        &lt;include name=&quot;**\ejb-jar.xml&quot;/&gt;
        &lt;support dir=&quot;${build.classes}&quot;&gt;
          &lt;include name=&quot;demo\smp\*.class&quot;/&gt;
          &lt;include name=&quot;demo\helper\*.class&quot;/&gt;
         &lt;/support&gt;
     &lt;/ejbjar&gt;
</pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#BAS_DD" class="member-name-link">BAS_DD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#BES_DD" class="member-name-link">BES_DD</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_BAS_DTD_LOCATION" class="member-name-link">DEFAULT_BAS_DTD_LOCATION</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_BAS45_EJB11_DTD_LOCATION" class="member-name-link">DEFAULT_BAS45_EJB11_DTD_LOCATION</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JAVA2IIOP" class="member-name-link">JAVA2IIOP</a></code></div>
<div class="col-last even-row-color">
<div class="block">Java2iiop executable</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PUBLICID_BORLAND_EJB" class="member-name-link">PUBLICID_BORLAND_EJB</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Borland 1.1 ejb id</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#VERIFY" class="member-name-link">VERIFY</a></code></div>
<div class="col-last even-row-color">
<div class="block">Verify class</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</a>, <a href="GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</a>, <a href="GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</a>, <a href="GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</a>, <a href="GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</a>, <a href="GenericDeploymentTool.html#EJB_DD">EJB_DD</a>, <a href="GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</a>, <a href="GenericDeploymentTool.html#MANIFEST">MANIFEST</a>, <a href="GenericDeploymentTool.html#META_DIR">META_DIR</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">BorlandDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBorlandDescriptorHandler(java.io.File)" class="member-name-link">getBorlandDescriptorHandler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the borland descriptor handler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBASdtd(java.lang.String)" class="member-name-link">setBASdtd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter used to store the location of the borland DTD.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the debug mode for java2iiop (default false)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGenerateclient(boolean)" class="member-name-link">setGenerateclient</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">setter used to store whether the task will include the generate client task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJava2iiopParams(java.lang.String)" class="member-name-link">setJava2iiopParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If filled, the params are added to the java2iiop command.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProcessErrorStream(java.io.InputStream)" class="member-name-link">setProcessErrorStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the error stream of the process.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProcessInputStream(java.io.OutputStream)" class="member-name-link">setProcessInputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;param1)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Install a handler for the input stream of the subprocess.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProcessOutputStream(java.io.InputStream)" class="member-name-link">setProcessOutputStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output stream of the process.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuffix(java.lang.String)" class="member-name-link">setSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter used to store the suffix for the generated borland jar file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerify(boolean)" class="member-name-link">setVerify</a><wbr>(boolean&nbsp;verify)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the verify  mode for the produced jar (default true)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerifyArgs(java.lang.String)" class="member-name-link">setVerifyArgs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets some additional args to send to verify command</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVersion(int)" class="member-name-link">setVersion</a><wbr>(int&nbsp;version)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">setter used to store the borland appserver version [4 or 5]</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#start()" class="member-name-link">start</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start handling of the streams.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stop()" class="member-name-link">stop</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Stop handling of the streams - will not be restarted.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)" class="member-name-link">writeJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method used to encapsulate the writing of the JAR file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">addFileToJar</a>, <a href="GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</a>, <a href="GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</a>, <a href="GenericDeploymentTool.html#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">checkConfiguration</a>, <a href="GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a>, <a href="GenericDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</a>, <a href="GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</a>, <a href="GenericDeploymentTool.html#getConfig()">getConfig</a>, <a href="GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</a>, <a href="GenericDeploymentTool.html#getDestDir()">getDestDir</a>, <a href="GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</a>, <a href="GenericDeploymentTool.html#getLocation()">getLocation</a>, <a href="GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</a>, <a href="GenericDeploymentTool.html#getPublicId()">getPublicId</a>, <a href="GenericDeploymentTool.html#getTask()">getTask</a>, <a href="GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String,java.lang.String)">getVendorDDPrefix</a>, <a href="GenericDeploymentTool.html#log(java.lang.String,int)">log</a>, <a href="GenericDeploymentTool.html#needToRebuild(java.util.Hashtable,java.io.File)">needToRebuild</a>, <a href="GenericDeploymentTool.html#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">parseEjbFiles</a>, <a href="GenericDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a>, <a href="GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</a>, <a href="GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</a>, <a href="GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</a>, <a href="GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a>, <a href="GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</a>, <a href="GenericDeploymentTool.html#validateConfigured()">validateConfigured</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PUBLICID_BORLAND_EJB">
<h3>PUBLICID_BORLAND_EJB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_BORLAND_EJB</span></div>
<div class="block">Borland 1.1 ejb id</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.PUBLICID_BORLAND_EJB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_BAS45_EJB11_DTD_LOCATION">
<h3>DEFAULT_BAS45_EJB11_DTD_LOCATION</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_BAS45_EJB11_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.DEFAULT_BAS45_EJB11_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_BAS_DTD_LOCATION">
<h3>DEFAULT_BAS_DTD_LOCATION</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_BAS_DTD_LOCATION</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.DEFAULT_BAS_DTD_LOCATION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BAS_DD">
<h3>BAS_DD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">BAS_DD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.BAS_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BES_DD">
<h3>BES_DD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">BES_DD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.BES_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JAVA2IIOP">
<h3>JAVA2IIOP</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAVA2IIOP</span></div>
<div class="block">Java2iiop executable</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.JAVA2IIOP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERIFY">
<h3>VERIFY</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">VERIFY</span></div>
<div class="block">Verify class</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.VERIFY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>BorlandDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BorlandDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">set the debug mode for java2iiop (default false)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - the setting to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerify(boolean)">
<h3>setVerify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerify</span><wbr><span class="parameters">(boolean&nbsp;verify)</span></div>
<div class="block">set the verify  mode for the produced jar (default true)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verify</code> - the setting to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuffix(java.lang.String)">
<h3>setSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Setter used to store the suffix for the generated borland jar file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerifyArgs(java.lang.String)">
<h3>setVerifyArgs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerifyArgs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;args)</span></div>
<div class="block">sets some additional args to send to verify command</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - additional command line parameters</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBASdtd(java.lang.String)">
<h3>setBASdtd</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBASdtd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Setter used to store the location of the borland DTD. This can be a file on the system
 or a resource on the classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the DTD location.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGenerateclient(boolean)">
<h3>setGenerateclient</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGenerateclient</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">setter used to store whether the task will include the generate client task.
 (see : BorlandGenerateClient task)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - if true generate the client task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVersion(int)">
<h3>setVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVersion</span><wbr><span class="parameters">(int&nbsp;version)</span></div>
<div class="block">setter used to store the borland appserver version [4 or 5]</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>version</code> - app server version 4 or 5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJava2iiopParams(java.lang.String)">
<h3>setJava2iiopParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJava2iiopParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;params)</span></div>
<div class="block">If filled, the params are added to the java2iiop command.
 (ex: -no_warn_missing_define)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>params</code> - additional params for java2iiop</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBorlandDescriptorHandler(java.io.File)">
<h3>getBorlandDescriptorHandler</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></span>&nbsp;<span class="element-name">getBorlandDescriptorHandler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Get the borland descriptor handler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
<dt>Returns:</dt>
<dd>the descriptor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</span></div>
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable,java.lang.String)">addVendorFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - the map to add the files to.</dd>
<dd><code>ddPrefix</code> - the prefix to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">
<h3>writeJar</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method used to encapsulate the writing of the JAR file. Iterates over the
 filenames/java.io.Files in the Hashtable stored on the instance variable
 ejbFiles.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">writeJar</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name.</dd>
<dd><code>jarFile</code> - the jar file to write to.</dd>
<dd><code>files</code> - the files to write to the jar.</dd>
<dd><code>publicId</code> - the id to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="start()">
<h3>start</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">start</span>()
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Start handling of the streams..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html#start()">start</a></code>&nbsp;in interface&nbsp;<code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stop()">
<h3>stop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stop</span>()</div>
<div class="block">Stop handling of the streams - will not be restarted..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html#stop()">stop</a></code>&nbsp;in interface&nbsp;<code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProcessInputStream(java.io.OutputStream)">
<h3>setProcessInputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProcessInputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;param1)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Install a handler for the input stream of the subprocess..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html#setProcessInputStream(java.io.OutputStream)">setProcessInputStream</a></code>&nbsp;in interface&nbsp;<code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>param1</code> - output stream to write to the standard input stream of the
           subprocess</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProcessOutputStream(java.io.InputStream)">
<h3>setProcessOutputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProcessOutputStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Set the output stream of the process.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html#setProcessOutputStream(java.io.InputStream)">setProcessOutputStream</a></code>&nbsp;in interface&nbsp;<code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProcessErrorStream(java.io.InputStream)">
<h3>setProcessErrorStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProcessErrorStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Set the error stream of the process.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../ExecuteStreamHandler.html#setProcessErrorStream(java.io.InputStream)">setProcessErrorStream</a></code>&nbsp;in interface&nbsp;<code><a href="../../ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>is</code> - the input stream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
