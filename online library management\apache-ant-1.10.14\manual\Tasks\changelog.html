<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ChangeLog Task</title>
</head>

<body>

<h2 id="changelog">CvsChangeLog</h2>
<h3>Description</h3>
<p>Generates an XML-formatted report file of the change logs recorded in
a <a href="https://www.nongnu.org/cvs/" target="_top">CVS</a> repository.</p>
<p><strong>Important</strong>: This task needs <kbd>cvs</kbd> on the path. If it isn't, you will get
an error (such as <code>error=2</code> on Windows). If <code>&lt;cvs&gt;</code> doesn't work, try to
execute <kbd>cvs.exe</kbd> from the command line in the target directory in which you are working.
Also note that this task assumes that the <kbd>cvs</kbd> executable is compatible with the Unix
version, this is not completely true for certain other CVS clients&mdash;like CVSNT for
example&mdash;and some operation may fail when using such an incompatible client.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td colspan="3" class="left">Attributes from parent <code>&lt;cvs&gt;</code> task which are
    meaningful here<br/><em>Since Apache Ant 1.6.1</em></td>
  </tr>
  <tr>
    <td>cvsRoot</td>
    <td>the <code>CVSROOT</code> variable.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>cvsRsh</td>
    <td>the <code>CVS_RSH</code> variable.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>package</td>
    <td>the package/module to check out.  <strong>Note</strong>: multiple attributes can be split
      using spaces.  Use a nested <code>&lt;module&gt;</code> element if you want to specify a
      module with spaces in its name.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>port</td>
    <td>Port used by CVS to communicate with the server.</td>
    <td>No; defaults to <q>2401</q></td>
  </tr>
  <tr>
    <td>passfile</td>
    <td>Password file to read passwords from.</td>
    <td>No; defaults to <q>~/.cvspass</q></td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if the command exits with a return code other than <q>0</q></td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>tag</td>
    <td>query the changelog for a specific branch.</td>
    <td>No</td>
  </tr>
  <tr>
    <td colspan="3" class="left">Specific attributes</td>
  </tr>
  <tr>
    <td>dir</td>
    <td>The directory from which to run the <kbd>cvs log</kbd> command.</td>
    <td>No; defaults to <q>${basedir}</q></td>
  </tr>
  <tr>
    <td>destfile</td>
    <td>The file in which to write the change log report.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>usersfile</td>
    <td>Property file that contains name-value pairs mapping user IDs and names that should be used
      in the report in place of the user ID.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>daysinpast</td>
    <td>Sets the number of days into the past for which the change log information should be
      retrieved.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>start</td>
    <td>The earliest date from which change logs are to be included in the report.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>end</td>
    <td>The latest date to which change logs are to be included in the report.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>remote</td>
    <td>If set to true, works against the repository (using <kbd>cvs rlog</kbd>) without a working
      copy.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>startTag</td>
    <td>The start of a tag range. If <var>endTag</var> is also specified, they must both be on the
      same branch. If <var>endTag</var> is not specified, the end of the range will be the latest on
      the same branch on which <var>startTag</var> lives. <em>Since Ant 1.8.0</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>endTag</td>
    <td>The end of a tag range. If <var>startTag</var> is also specified, they must both be on the
      same branch. If <var>startTag</var> is not specified, the start of the range will be the top
      of the branch on which <var>endTag</var> lives. <em>Since Ant 1.8.0</em></td>
    <td>No</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4 id="user">user</h4>
<p>The nested <code>&lt;user&gt;</code> element allows you to specify a mapping between a user ID as
it appears on the CVS server and a name to include in the formatted report.  Anytime the specified
user ID has made a change in the repository, the <code>&lt;author&gt;</code> tag in the report file
will include the name specified in <var>displayname</var> rather than the user ID.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>displayname</td>
    <td>The name to be used in the CVS change log report.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>userid</td>
    <td>The user ID of the person as it exists on the CVS server.
    </td>
    <td>Yes</td>
  </tr>
</table>

<h4>module</h4>

<p>Specifies a package/module to work on, unlike the package attribute modules specified using this
attribute can contain spaces in their name.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The module's/package's name.</td>
    <td>Yes</td>
  </tr>
</table>

<h3>Examples</h3>
<p>Generate a change log report for all the changes that have been made under
the <samp>dve/network</samp> directory. Write these changes into the
file <samp>changelog.xml</samp>.</p>
<pre>
&lt;cvschangelog dir=&quot;dve/network&quot;
              destfile=&quot;changelog.xml&quot;/&gt;</pre>

<p>Generate a change log report for any changes that were made under the <samp>dve/network</samp>
directory in the past 10 days. Write these changes into the file <samp>changelog.xml</samp>.</p>
<pre>
&lt;cvschangelog dir=&quot;dve/network&quot;
              destfile=&quot;changelog.xml&quot;
              daysinpast=&quot;10&quot;/&gt;</pre>

<p>Generate a change log report for any changes that were made between February 20, 2002 and March
20, 2002 under the <samp>dve/network</samp> directory. Write these changes into the
file <samp>changelog.xml</samp>.</p>
<pre>
&lt;cvschangelog dir=&quot;dve/network&quot;
              destfile=&quot;changelog.xml&quot;
              start=&quot;20 Feb 2002&quot;
              end=&quot;20 Mar 2002&quot;/&gt;</pre>

<p>Generate a change log report for any changes that were made after February 20, 2002 under
the <samp>dve/network</samp> directory. Write these changes into the
file <samp>changelog.xml</samp>.</p>
<pre>
&lt;cvschangelog dir=&quot;dve/network&quot;
              destfile=&quot;changelog.xml&quot;
              start=&quot;20 Feb 2002&quot;/&gt;</pre>

<p>Generate a change log report for all the changes that were made under
the <code>dve/network</code> directory, substituting the name <samp>Peter Donald</samp> in
the <code>&lt;author&gt;</code> tags anytime a change made by the user ID <samp>donaldp</samp> is
encountered.  Write these changes into the file <samp>changelog.xml</samp>.</p>
<pre>
&lt;cvschangelog dir=&quot;dve/network&quot;
              destfile=&quot;changelog.xml&quot;&gt;
    &lt;user displayname=&quot;Peter Donald&quot; userid=&quot;donaldp&quot;/&gt;
&lt;/cvschangelog&gt;</pre>

<p>Generate a change log report on the <code>ANT_16_BRANCH</code>.</p>
<pre>
&lt;cvschangelog dir=&quot;c:/dev/asf/ant.head&quot; passfile=&quot;c:/home/<USER>/.cvspass&quot;
              destfile=&quot;changelogant.xml&quot; tag=&quot;ANT_16_BRANCH&quot;/&gt;</pre>
<h4>Generate Report</h4>
<p>Ant includes a basic XSLT stylesheet that you can use to generate a HTML report based on the XML
output. The following example illustrates how to generate a HTML report from the XML report.</p>

<pre>
&lt;style in="changelog.xml"
      out="changelog.html"
      style="${ant.home}/etc/changelog.xsl"&gt;
  &lt;param name="title" expression="Ant ChangeLog"/&gt;
  &lt;param name="module" expression="ant"/&gt;
  &lt;param name="cvsweb" expression="https://cvs.apache.org/viewcvs/"/&gt;
&lt;/style&gt;</pre>

<h4>Sample Output</h4>
<pre>
&lt;changelog&gt;
  &lt;entry&gt;
    &lt;date&gt;2002-03-06&lt;/date&gt;
    &lt;time&gt;12:00&lt;/time&gt;
    &lt;author&gt;Peter Donald&lt;/author&gt;
    &lt;file&gt;
      &lt;name&gt;org/apache/myrmidon/build/AntlibDescriptorTask.java&lt;/name&gt;
      &lt;revision&gt;1.3&lt;/revision&gt;
      &lt;prevrevision&gt;1.2&lt;/prevrevision&gt;
    &lt;/file&gt;
    &lt;msg&gt;&lt;![CDATA[Use URLs directly rather than go via a File.

This allows templates to be stored inside jar]]&gt;&lt;/msg&gt;
  &lt;/entry&gt;
&lt;/changelog&gt;</pre>

</body>
</html>
