<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JonasDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: JonasDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class JonasDeploymentTool" class="title">Class JonasDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JonasDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></span></div>
<div class="block">The deployment tool to add the jonas specific deployment descriptors to the
 ejb JAR file. JONAS only requires one additional file jonas-ejb-jar.xml.</div>
<dl class="notes">
<dt>Version:</dt>
<dd>1.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="EjbJar.html#createJonas()"><code>EjbJar.createJonas()</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DAVID_ORB" class="member-name-link">DAVID_ORB</a></code></div>
<div class="col-last even-row-color">
<div class="block">DAVID ORB.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#EJB_JAR_1_1_DTD" class="member-name-link">EJB_JAR_1_1_DTD</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Name of the standard deployment descriptor DTD (these files are stored in
 the ${JONAS_ROOT}/xml directory).</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#EJB_JAR_1_1_PUBLIC_ID" class="member-name-link">EJB_JAR_1_1_PUBLIC_ID</a></code></div>
<div class="col-last even-row-color">
<div class="block">Public Id of the standard deployment descriptor DTD.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#EJB_JAR_2_0_DTD" class="member-name-link">EJB_JAR_2_0_DTD</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#EJB_JAR_2_0_PUBLIC_ID" class="member-name-link">EJB_JAR_2_0_PUBLIC_ID</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#GENIC_CLASS" class="member-name-link">GENIC_CLASS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">GenIC class name (JOnAS 2.5)</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JEREMIE_ORB" class="member-name-link">JEREMIE_ORB</a></code></div>
<div class="col-last even-row-color">
<div class="block">JEREMIE ORB.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JONAS_DD" class="member-name-link">JONAS_DD</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Default JOnAS deployment descriptor name.</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JONAS_EJB_JAR_2_4_DTD" class="member-name-link">JONAS_EJB_JAR_2_4_DTD</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the JOnAS-specific deployment descriptor DTD (these files are
 stored in the ${JONAS_ROOT}/xml directory).</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JONAS_EJB_JAR_2_4_PUBLIC_ID" class="member-name-link">JONAS_EJB_JAR_2_4_PUBLIC_ID</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Public Id of the JOnAS-specific deployment descriptor DTD.</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#JONAS_EJB_JAR_2_5_DTD" class="member-name-link">JONAS_EJB_JAR_2_5_DTD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JONAS_EJB_JAR_2_5_PUBLIC_ID" class="member-name-link">JONAS_EJB_JAR_2_5_PUBLIC_ID</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#OLD_GENIC_CLASS_1" class="member-name-link">OLD_GENIC_CLASS_1</a></code></div>
<div class="col-last even-row-color">
<div class="block">Old GenIC class name (JOnAS 2.4.x).</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#OLD_GENIC_CLASS_2" class="member-name-link">OLD_GENIC_CLASS_2</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Old GenIC class name.</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#RMI_ORB" class="member-name-link">RMI_ORB</a></code></div>
<div class="col-last even-row-color">
<div class="block">RMI ORB.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</a>, <a href="GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</a>, <a href="GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</a>, <a href="GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</a>, <a href="GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</a>, <a href="GenericDeploymentTool.html#EJB_DD">EJB_DD</a>, <a href="GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</a>, <a href="GenericDeploymentTool.html#MANIFEST">MANIFEST</a>, <a href="GenericDeploymentTool.html#META_DIR">META_DIR</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JonasDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">checkConfiguration</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Verify the configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJarBaseName(java.lang.String)" class="member-name-link">getJarBaseName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Using the EJB descriptor file name passed from the <code>ejbjar</code>
 task, this method returns the "basename" which will be used to name the
 completed JAR file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVendorOutputJarFile(java.lang.String)" class="member-name-link">getVendorOutputJarFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">processDescriptor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aDescriptorName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)" class="member-name-link">registerKnownDTDs</a><wbr>(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register the locations of all known DTDs.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAdditionalargs(java.lang.String)" class="member-name-link">setAdditionalargs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the additional arguments.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJarsuffix(java.lang.String)" class="member-name-link">setJarsuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the jar suffix.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavac(java.lang.String)" class="member-name-link">setJavac</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the java compiler to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavacopts(java.lang.String)" class="member-name-link">setJavacopts</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the options to pass to the java compiler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJonasroot(java.io.File)" class="member-name-link">setJonasroot</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the JOnAS root directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgenerated(boolean)" class="member-name-link">setKeepgenerated</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>keepgenerated</code> flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgeneric(boolean)" class="member-name-link">setKeepgeneric</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>keepgeneric</code> flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNocompil(boolean)" class="member-name-link">setNocompil</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>nocompil</code> flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNogenic(boolean)" class="member-name-link">setNogenic</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>nogenic</code> flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNovalidation(boolean)" class="member-name-link">setNovalidation</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>novalidation</code> flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOrb(java.lang.String)" class="member-name-link">setOrb</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>orb</code> to construct classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRmicopts(java.lang.String)" class="member-name-link">setRmicopts</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the options to pass to the rmi compiler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSecpropag(boolean)" class="member-name-link">setSecpropag</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>secpropag</code> flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(boolean)" class="member-name-link">setVerbose</a><wbr>(boolean&nbsp;aBoolean)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <code>verbose</code> flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)" class="member-name-link">writeJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarfile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method used to encapsulate the writing of the JAR file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">addFileToJar</a>, <a href="GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</a>, <a href="GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</a>, <a href="GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a>, <a href="GenericDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</a>, <a href="GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</a>, <a href="GenericDeploymentTool.html#getConfig()">getConfig</a>, <a href="GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</a>, <a href="GenericDeploymentTool.html#getDestDir()">getDestDir</a>, <a href="GenericDeploymentTool.html#getLocation()">getLocation</a>, <a href="GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</a>, <a href="GenericDeploymentTool.html#getPublicId()">getPublicId</a>, <a href="GenericDeploymentTool.html#getTask()">getTask</a>, <a href="GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String,java.lang.String)">getVendorDDPrefix</a>, <a href="GenericDeploymentTool.html#log(java.lang.String,int)">log</a>, <a href="GenericDeploymentTool.html#needToRebuild(java.util.Hashtable,java.io.File)">needToRebuild</a>, <a href="GenericDeploymentTool.html#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">parseEjbFiles</a>, <a href="GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</a>, <a href="GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</a>, <a href="GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a>, <a href="GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</a>, <a href="GenericDeploymentTool.html#validateConfigured()">validateConfigured</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="EJB_JAR_1_1_PUBLIC_ID">
<h3>EJB_JAR_1_1_PUBLIC_ID</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EJB_JAR_1_1_PUBLIC_ID</span></div>
<div class="block">Public Id of the standard deployment descriptor DTD.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_1_1_PUBLIC_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EJB_JAR_2_0_PUBLIC_ID">
<h3>EJB_JAR_2_0_PUBLIC_ID</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EJB_JAR_2_0_PUBLIC_ID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_2_0_PUBLIC_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JONAS_EJB_JAR_2_4_PUBLIC_ID">
<h3>JONAS_EJB_JAR_2_4_PUBLIC_ID</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JONAS_EJB_JAR_2_4_PUBLIC_ID</span></div>
<div class="block">Public Id of the JOnAS-specific deployment descriptor DTD.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_4_PUBLIC_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JONAS_EJB_JAR_2_5_PUBLIC_ID">
<h3>JONAS_EJB_JAR_2_5_PUBLIC_ID</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JONAS_EJB_JAR_2_5_PUBLIC_ID</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_5_PUBLIC_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RMI_ORB">
<h3>RMI_ORB</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">RMI_ORB</span></div>
<div class="block">RMI ORB.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.RMI_ORB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JEREMIE_ORB">
<h3>JEREMIE_ORB</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JEREMIE_ORB</span></div>
<div class="block">JEREMIE ORB.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JEREMIE_ORB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DAVID_ORB">
<h3>DAVID_ORB</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DAVID_ORB</span></div>
<div class="block">DAVID ORB.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.DAVID_ORB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EJB_JAR_1_1_DTD">
<h3>EJB_JAR_1_1_DTD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EJB_JAR_1_1_DTD</span></div>
<div class="block">Name of the standard deployment descriptor DTD (these files are stored in
 the ${JONAS_ROOT}/xml directory).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_1_1_DTD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EJB_JAR_2_0_DTD">
<h3>EJB_JAR_2_0_DTD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EJB_JAR_2_0_DTD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_2_0_DTD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JONAS_EJB_JAR_2_4_DTD">
<h3>JONAS_EJB_JAR_2_4_DTD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JONAS_EJB_JAR_2_4_DTD</span></div>
<div class="block">Name of the JOnAS-specific deployment descriptor DTD (these files are
 stored in the ${JONAS_ROOT}/xml directory).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_4_DTD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JONAS_EJB_JAR_2_5_DTD">
<h3>JONAS_EJB_JAR_2_5_DTD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JONAS_EJB_JAR_2_5_DTD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_5_DTD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JONAS_DD">
<h3>JONAS_DD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JONAS_DD</span></div>
<div class="block">Default JOnAS deployment descriptor name.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GENIC_CLASS">
<h3>GENIC_CLASS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">GENIC_CLASS</span></div>
<div class="block">GenIC class name (JOnAS 2.5)</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.GENIC_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="OLD_GENIC_CLASS_1">
<h3>OLD_GENIC_CLASS_1</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">OLD_GENIC_CLASS_1</span></div>
<div class="block">Old GenIC class name (JOnAS 2.4.x).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.OLD_GENIC_CLASS_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="OLD_GENIC_CLASS_2">
<h3>OLD_GENIC_CLASS_2</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">OLD_GENIC_CLASS_2</span></div>
<div class="block">Old GenIC class name.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.OLD_GENIC_CLASS_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JonasDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JonasDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setKeepgenerated(boolean)">
<h3>setKeepgenerated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgenerated</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>keepgenerated</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAdditionalargs(java.lang.String)">
<h3>setAdditionalargs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAdditionalargs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Sets the additional arguments.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - additional args.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNocompil(boolean)">
<h3>setNocompil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNocompil</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>nocompil</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNovalidation(boolean)">
<h3>setNovalidation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNovalidation</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>novalidation</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavac(java.lang.String)">
<h3>setJavac</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavac</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Sets the java compiler to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - the java compiler.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavacopts(java.lang.String)">
<h3>setJavacopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavacopts</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Set the options to pass to the java compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - the options.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRmicopts(java.lang.String)">
<h3>setRmicopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRmicopts</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Set the options to pass to the rmi compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - the options.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSecpropag(boolean)">
<h3>setSecpropag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSecpropag</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>secpropag</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerbose(boolean)">
<h3>setVerbose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>verbose</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJonasroot(java.io.File)">
<h3>setJonasroot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJonasroot</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aFile)</span></div>
<div class="block">Set the JOnAS root directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aFile</code> - the JOnAS root directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeepgeneric(boolean)">
<h3>setKeepgeneric</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgeneric</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>keepgeneric</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJarsuffix(java.lang.String)">
<h3>setJarsuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJarsuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Sets the jar suffix.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - the string to use as the suffix.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOrb(java.lang.String)">
<h3>setOrb</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOrb</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aString)</span></div>
<div class="block">Sets the <code>orb</code> to construct classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aString</code> - 'RMI', 'JEREMIE', or 'DAVID'.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNogenic(boolean)">
<h3>setNogenic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNogenic</span><wbr><span class="parameters">(boolean&nbsp;aBoolean)</span></div>
<div class="block">Sets the <code>nogenic</code> flag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aBoolean</code> - <code>true</code> if the flag must be set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>processDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processDescriptor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aDescriptorName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span></div>
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files...</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>aDescriptorName</code> - the name of the deployment descriptor</dd>
<dd><code>saxParser</code> - a SAX parser which can be used to parse the deployment descriptor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">
<h3>writeJar</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarfile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method used to encapsulate the writing of the JAR file. Iterates over the
 filenames/java.io.Files in the Hashtable stored on the instance variable
 ejbFiles..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">writeJar</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name to use.</dd>
<dd><code>jarfile</code> - the jar file to write to.</dd>
<dd><code>ejbFiles</code> - the files to write to the jar.</dd>
<dd><code>publicId</code> - the id to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</span></div>
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable,java.lang.String)">addVendorFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - a hashtable entryname -&gt; file.</dd>
<dd><code>ddPrefix</code> - a prefix to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVendorOutputJarFile(java.lang.String)">
<h3>getVendorOutputJarFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getVendorOutputJarFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName)</span></div>
<div class="block">.</div>
</section>
</li>
<li>
<section class="detail" id="getJarBaseName(java.lang.String)">
<h3>getJarBaseName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getJarBaseName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</span></div>
<div class="block">Using the EJB descriptor file name passed from the <code>ejbjar</code>
 task, this method returns the "basename" which will be used to name the
 completed JAR file..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dt>Returns:</dt>
<dd>The "basename" which will be used to name the
                           completed JAR file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">
<h3>registerKnownDTDs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerKnownDTDs</span><wbr><span class="parameters">(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</span></div>
<div class="block">Register the locations of all known DTDs.

 vendor-specific subclasses should override this method to define
 the vendor-specific locations of the EJB DTDs.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>handler</code> - no used in this class.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>checkConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkConfiguration</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span>
                           throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Verify the configuration.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">checkConfiguration</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - the name of the descriptor file.</dd>
<dd><code>saxParser</code> - not used.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
