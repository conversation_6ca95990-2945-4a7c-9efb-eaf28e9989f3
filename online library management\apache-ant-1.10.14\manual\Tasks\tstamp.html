<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>TStamp Task</title>
</head>

<body>

<h2 id="tstamp">Tstamp</h2>

<h3>Description</h3>
<p>Sets the <code>DSTAMP</code>, <code>TSTAMP</code>, and <code>TODAY</code> properties in the
current project. By default, the <code>DSTAMP</code> property is in the
format <q>yyyyMMdd</q>, <code>TSTAMP</code> is in the format <q>hhmm</q>, and <code>TODAY</code> is
in the format <q>MMMM dd yyyy</q>. Use the nested <code>&lt;format&gt;</code> element to specify a
different format.</p>

<p>These properties can be used in the build file, for instance, to create timestamped filenames, or
used to replace placeholder tags inside documents to indicate, for example, the release date. The
best place for this task is probably in an initialization target.</p>

<p><em>Since Ant 1.10.2</em> the magic property <code>ant.tstamp.now</code> can be used to specify a
fixed date value in order to create reproducible builds. Its value must be a number and is
interpreted as seconds since the epoch (midnight 1970-01-01). With <code>ant.tstamp.now.iso</code>
you could also specify that value in ISO-8601 format (<code>1972-04-17T08:07:00Z</code>). If you
specify a value in an invalid format an INFO message will be logged and the value will be
ignored.</p>

<p>
  <em>Since Ant 1.10.8</em> the <code>SOURCE_DATE_EPOCH</code> environment variable value (if set)
  will be honoured for <a href="https://reproducible-builds.org/specs/source-date-epoch/#idm55">reproducible builds</a>.
  Ant will log a DEBUG message if an invalid value (value that cannot be parsed to an integer), is specified
  for that environment variable and will instead use the "current" date.
</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>prefix</td>
    <td>Prefix used for all properties set.</td>
    <td>No; default is no prefix</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<p>The task supports a <code>&lt;format&gt;</code> nested element that allows a property to be set
to the current date and time in a given format.  The date/time patterns are as defined in the
Java <a href="https://docs.oracle.com/javase/8/docs/api/java/text/SimpleDateFormat.html"
target="_top">SimpleDateFormat</a> class.  The format element also allows offsets to be applied to
the time to generate different time values.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The property to receive the date/time string in the given pattern.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>pattern</td>
    <td>The date/time pattern to be used. The values are as defined by the Java SimpleDateFormat
      class.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>timezone</td>
    <td>The timezone to use for displaying time. The values are as defined by the
      Java <a href="https://docs.oracle.com/javase/8/docs/api/java/util/TimeZone.html"
      target="_top">TimeZone</a> class.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>offset</td>
    <td>The numeric offset to the current time</td>
    <td>No</td>
  </tr>
  <tr>
    <td>unit</td>
    <td>The unit of the offset to be applied to the current time.  Valid values are
      <ul>
        <li><q>millisecond</q></li>
        <li><q>second</q></li>
        <li><q>minute</q></li>
        <li><q>hour</q></li>
        <li><q>day</q></li>
        <li><q>week</q></li>
        <li><q>month</q></li>
        <li><q>year</q></li>
      </ul>
    </td>
    <td>No</td>
  </tr>
  <tr>
    <td>locale</td>
    <td>The locale used to create date/time string. The general form is <q>language[, country[,
      variant]]</q> (either <q>variant</q> or both <q>variant</q> and <q>country</q> may be
      omitted). For more information please refer to documentation for
      the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html"
      target="_top">Locale</a> class.</td>
    <td>No</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Set the standard <code>DSTAMP</code>, <code>TSTAMP</code>, and <code>TODAY</code> properties
according to the default formats.</p>
<pre>&lt;tstamp/&gt;</pre>

<p>Set the standard properties as well as the property <code>TODAY_UK</code> with the date/time
pattern <q>d-MMMM-yyyy</q> using English locale (eg. 21-May-2001).</p>
<pre>
&lt;tstamp&gt;
    &lt;format property=&quot;TODAY_GB&quot; pattern=&quot;d-MMMM-yyyy&quot; locale=&quot;en,GB&quot;/&gt;
&lt;/tstamp&gt;</pre>

<p>Create a timestamp, in the property <code>touch.time</code>, 5 hours before the current time. The
format in this example is suitable for use with the <code>&lt;touch&gt;</code> task. The standard
properties are set also.</p>
<pre>
&lt;tstamp&gt;
    &lt;format property=&quot;touch.time&quot; pattern=&quot;MM/dd/yyyy hh:mm aa&quot;
            offset=&quot;-5&quot; unit=&quot;hour&quot;/&gt;
&lt;/tstamp&gt;</pre>

<p>Set three properties with the standard formats, prefixed
with <q>start.</q>: <code>start.DSTAMP</code>, <code>start.TSTAMP</code>,
and <code>start.TODAY</code>.</p>
<pre>&lt;tstamp prefix="start"/&gt;</pre>

</body>
</html>
