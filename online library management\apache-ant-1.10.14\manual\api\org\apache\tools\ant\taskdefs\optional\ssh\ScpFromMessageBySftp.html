<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ScpFromMessageBySftp (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ssh, class: ScpFromMessageBySftp">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ssh</a></div>
<h1 title="Class ScpFromMessageBySftp" class="title">Class ScpFromMessageBySftp</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="AbstractSshMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">org.apache.tools.ant.taskdefs.optional.ssh.AbstractSshMessage</a>
<div class="inheritance"><a href="ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">org.apache.tools.ant.taskdefs.optional.ssh.ScpFromMessage</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ssh.ScpFromMessageBySftp</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ScpFromMessageBySftp</span>
<span class="extends-implements">extends <a href="ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a></span></div>
<div class="block">A helper object representing an scp download.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(boolean,com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean)" class="member-name-link">ScpFromMessageBySftp</a><wbr>(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for ScpFromMessageBySftp.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(boolean,com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean,boolean)" class="member-name-link">ScpFromMessageBySftp</a><wbr>(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive,
 boolean&nbsp;preserveLastModified)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for ScpFromMessageBySftp.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean)" class="member-name-link">ScpFromMessageBySftp</a><wbr>(com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for ScpFromMessageBySftp.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Carry out the transfer.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ssh.ScpFromMessage">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ssh.<a href="ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a></h3>
<code><a href="ScpFromMessage.html#getPreserveLastModified()">getPreserveLastModified</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ssh.AbstractSshMessage">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ssh.<a href="AbstractSshMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">AbstractSshMessage</a></h3>
<code><a href="AbstractSshMessage.html#getCompressed()">getCompressed</a>, <a href="AbstractSshMessage.html#getProgressMonitor()">getProgressMonitor</a>, <a href="AbstractSshMessage.html#getVerbose()">getVerbose</a>, <a href="AbstractSshMessage.html#log(java.lang.String)">log</a>, <a href="AbstractSshMessage.html#logStats(long,long,long)">logStats</a>, <a href="AbstractSshMessage.html#openExecChannel(java.lang.String)">openExecChannel</a>, <a href="AbstractSshMessage.html#openSftpChannel()">openSftpChannel</a>, <a href="AbstractSshMessage.html#sendAck(java.io.OutputStream)">sendAck</a>, <a href="AbstractSshMessage.html#setLogListener(org.apache.tools.ant.taskdefs.optional.ssh.LogListener)">setLogListener</a>, <a href="AbstractSshMessage.html#trackProgress(long,long,int)">trackProgress</a>, <a href="AbstractSshMessage.html#waitForAck(java.io.InputStream)">waitForAck</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(boolean,com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean)">
<h3>ScpFromMessageBySftp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ScpFromMessageBySftp</span><wbr><span class="parameters">(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive)</span></div>
<div class="block">Constructor for ScpFromMessageBySftp.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - if true log extra information</dd>
<dd><code>session</code> - the Scp session to use</dd>
<dd><code>aRemoteFile</code> - the remote file name</dd>
<dd><code>aLocalFile</code> - the local file</dd>
<dd><code>recursive</code> - if true use recursion</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean)">
<h3>ScpFromMessageBySftp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ScpFromMessageBySftp</span><wbr><span class="parameters">(com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive)</span></div>
<div class="block">Constructor for ScpFromMessageBySftp.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>session</code> - the Scp session to use</dd>
<dd><code>aRemoteFile</code> - the remote file name</dd>
<dd><code>aLocalFile</code> - the local file</dd>
<dd><code>recursive</code> - if true use recursion</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(boolean,com.jcraft.jsch.Session,java.lang.String,java.io.File,boolean,boolean)">
<h3>ScpFromMessageBySftp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ScpFromMessageBySftp</span><wbr><span class="parameters">(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;aRemoteFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;aLocalFile,
 boolean&nbsp;recursive,
 boolean&nbsp;preserveLastModified)</span></div>
<div class="block">Constructor for ScpFromMessageBySftp.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - if true log extra information</dd>
<dd><code>session</code> - the Scp session to use</dd>
<dd><code>aRemoteFile</code> - the remote file name</dd>
<dd><code>aLocalFile</code> - the local file</dd>
<dd><code>recursive</code> - if true use recursion</dd>
<dd><code>preserveLastModified</code> - whether to preserve file
 modification times</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
com.jcraft.jsch.JSchException</span></div>
<div class="block">Carry out the transfer.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ScpFromMessage.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on i/o errors</dd>
<dd><code>com.jcraft.jsch.JSchException</code> - on errors detected by scp</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
