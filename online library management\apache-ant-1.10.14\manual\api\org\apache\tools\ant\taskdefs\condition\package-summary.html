<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.condition (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.condition">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.condition" class="title">Package org.apache.tools.ant.taskdefs.condition</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.condition</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="And.html" title="class in org.apache.tools.ant.taskdefs.condition">And</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">&lt;and&gt; condition container.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AntVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An Ant version condition.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Interface for conditions to use inside the &lt;condition&gt; task.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Baseclass for the &lt;condition&gt; task as well as several
 conditions - ensures that the types of conditions inside the task
 and the "container" conditions are in sync.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Contains.html" title="class in org.apache.tools.ant.taskdefs.condition">Contains</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Is one string part of another string?</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Equals.html" title="class in org.apache.tools.ant.taskdefs.condition">Equals</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Simple comparison condition.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FilesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">FilesMatch</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compares two files for equality based on size and
 content.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="HasFreeSpace.html" title="class in org.apache.tools.ant.taskdefs.condition">HasFreeSpace</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">&lt;hasfreespace&gt;</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="HasMethod.html" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">test for a method</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition to wait for a HTTP request to succeed.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IsFailure.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFailure</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Condition to test a return-code for failure.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition that tests whether a given string evals to false</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IsFileSelected.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFileSelected</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a condition that checks to see if a file passes an embedded selector.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IsLastModified.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition that makes assertions about the last modified date of a
 resource.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IsLastModified.CompareMode.html" title="class in org.apache.tools.ant.taskdefs.condition">IsLastModified.CompareMode</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">describes comparison modes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Test for a host being reachable using ICMP "ping" packets &amp; echo operations.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Condition that tests whether a given reference has been defined.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition that tests whether a given property has been set.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IsSigned.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSigned</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Checks whether a jarfile is signed: if the name of the
 signature is passed, the file is checked for presence of that
 particular signature; otherwise the file is checked for the
 existence of any signature.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition that tests whether a given string evals to true</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JavaVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">JavaVersion</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An Java version condition.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Matches.html" title="class in org.apache.tools.ant.taskdefs.condition">Matches</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Simple regular expression condition.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Not.html" title="class in org.apache.tools.ant.taskdefs.condition">Not</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">&lt;not&gt; condition.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Or.html" title="class in org.apache.tools.ant.taskdefs.condition">Or</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">&lt;or&gt; condition container.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Condition that tests the OS type.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Test for the XML parser supporting a particular feature</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ResourceContains.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceContains</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">&lt;resourcecontains&gt;
 Is a string contained in a resource (file currently)?</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ResourceExists.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourceExists</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition that checks whether a given resource exists.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ResourcesMatch.html" title="class in org.apache.tools.ant.taskdefs.condition">ResourcesMatch</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compares resources for equality based on size and content.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Condition to wait for a TCP/IP socket to have a listener.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TypeFound.html" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">looks for a task or other Ant type that exists.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Xor.html" title="class in org.apache.tools.ant.taskdefs.condition">Xor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The <code>Xor</code> condition type to exclusive or operations.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
