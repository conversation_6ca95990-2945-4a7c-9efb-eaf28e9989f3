<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>TarResource (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources, class: TarResource">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resources</a></div>
<h1 title="Class TarResource" class="title">Class TarResource</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../Resource.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.Resource</a>
<div class="inheritance"><a href="ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">org.apache.tools.ant.types.resources.ArchiveResource</a>
<div class="inheritance">org.apache.tools.ant.types.resources.TarResource</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarResource</span>
<span class="extends-implements">extends <a href="ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></span></div>
<div class="block">A Resource representation of an entry in a tar archive.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.Resource">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#MAGIC">MAGIC</a>, <a href="../Resource.html#UNKNOWN_DATETIME">UNKNOWN_DATETIME</a>, <a href="../Resource.html#UNKNOWN_SIZE">UNKNOWN_SIZE</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TarResource</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.File,org.apache.tools.tar.TarEntry)" class="member-name-link">TarResource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;a,
 <a href="../../../tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct a TarResource representing the specified
 entry in the specified archive.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.Resource,org.apache.tools.tar.TarEntry)" class="member-name-link">TarResource</a><wbr>(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;a,
 <a href="../../../tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;e)</code></div>
<div class="col-last even-row-color">
<div class="block">Construct a TarResource representing the specified
 entry in the specified archive.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fetchEntry()" class="member-name-link">fetchEntry</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">fetches information from the named entry inside the archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getGid()" class="member-name-link">getGid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getGroup()" class="member-name-link">getGroup</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputStream()" class="member-name-link">getInputStream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return an InputStream for reading the contents of this Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLinkFlag()" class="member-name-link">getLinkFlag</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLinkName()" class="member-name-link">getLinkName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLongGid()" class="member-name-link">getLongGid</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLongUid()" class="member-name-link">getLongUid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputStream()" class="member-name-link">getOutputStream</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an OutputStream for the Resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="TarResource.html" title="class in org.apache.tools.ant.types.resources">TarResource</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRef()" class="member-name-link">getRef</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform the check for circular references and return the
 referenced Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getUid()" class="member-name-link">getUid</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUserName()" class="member-name-link">getUserName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.resources.ArchiveResource">Methods inherited from class&nbsp;org.apache.tools.ant.types.resources.<a href="ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></h3>
<code><a href="ArchiveResource.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</a>, <a href="ArchiveResource.html#checkEntry()">checkEntry</a>, <a href="ArchiveResource.html#compareTo(org.apache.tools.ant.types.Resource)">compareTo</a>, <a href="ArchiveResource.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="ArchiveResource.html#equals(java.lang.Object)">equals</a>, <a href="ArchiveResource.html#getArchive()">getArchive</a>, <a href="ArchiveResource.html#getLastModified()">getLastModified</a>, <a href="ArchiveResource.html#getMode()">getMode</a>, <a href="ArchiveResource.html#getSize()">getSize</a>, <a href="ArchiveResource.html#hashCode()">hashCode</a>, <a href="ArchiveResource.html#isDirectory()">isDirectory</a>, <a href="ArchiveResource.html#isExists()">isExists</a>, <a href="ArchiveResource.html#setArchive(java.io.File)">setArchive</a>, <a href="ArchiveResource.html#setMode(int)">setMode</a>, <a href="ArchiveResource.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="ArchiveResource.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.Resource">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#as(java.lang.Class)">as</a>, <a href="../Resource.html#asOptional(java.lang.Class)">asOptional</a>, <a href="../Resource.html#clone()">clone</a>, <a href="../Resource.html#getMagicNumber(byte%5B%5D)">getMagicNumber</a>, <a href="../Resource.html#getName()">getName</a>, <a href="../Resource.html#isFilesystemOnly()">isFilesystemOnly</a>, <a href="../Resource.html#iterator()">iterator</a>, <a href="../Resource.html#setDirectory(boolean)">setDirectory</a>, <a href="../Resource.html#setExists(boolean)">setExists</a>, <a href="../Resource.html#setLastModified(long)">setLastModified</a>, <a href="../Resource.html#setName(java.lang.String)">setName</a>, <a href="../Resource.html#setSize(long)">setSize</a>, <a href="../Resource.html#size()">size</a>, <a href="../Resource.html#toLongString()">toLongString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TarResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarResource</span>()</div>
<div class="block">Default constructor.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.File,org.apache.tools.tar.TarEntry)">
<h3>TarResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarResource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;a,
 <a href="../../../tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;e)</span></div>
<div class="block">Construct a TarResource representing the specified
 entry in the specified archive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - the archive as File.</dd>
<dd><code>e</code> - the TarEntry.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.Resource,org.apache.tools.tar.TarEntry)">
<h3>TarResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarResource</span><wbr><span class="parameters">(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;a,
 <a href="../../../tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a>&nbsp;e)</span></div>
<div class="block">Construct a TarResource representing the specified
 entry in the specified archive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - the archive as Resource.</dd>
<dd><code>e</code> - the TarEntry.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getInputStream()">
<h3>getInputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">getInputStream</span>()
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Return an InputStream for reading the contents of this Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getInputStream()">getInputStream</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>an InputStream object.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the tar file cannot be opened,
         or the entry cannot be read.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputStream()">
<h3>getOutputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></span>&nbsp;<span class="element-name">getOutputStream</span>()
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get an OutputStream for the Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getOutputStream()">getOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>an OutputStream to which content can be written.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if unable to provide the content of this
         Resource as a stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/UnsupportedOperationException.html" title="class or interface in java.lang" class="external-link">UnsupportedOperationException</a></code> - if OutputStreams are not
         supported for this Resource type.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUserName()">
<h3>getUserName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUserName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the user name for the tar entry</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getGroup()">
<h3>getGroup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getGroup</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the group name for the tar entry</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLongUid()">
<h3>getLongUid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongUid</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the uid for the tar entry</dd>
<dt>Since:</dt>
<dd>1.10.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUid()">
<h3>getUid</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getUid</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the uid for the tar entry</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLongGid()">
<h3>getLongGid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLongGid</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the gid for the tar entry</dd>
<dt>Since:</dt>
<dd>1.10.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getGid()">
<h3>getGid</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getGid</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the uid for the tar entry</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLinkName()">
<h3>getLinkName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLinkName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the link "name" (=path) of this entry; an empty string if this is no link</dd>
<dt>Since:</dt>
<dd>1.10.12</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLinkFlag()">
<h3>getLinkFlag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">getLinkFlag</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the link "flag" (=type) of this entry</dd>
<dt>Since:</dt>
<dd>1.10.12</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fetchEntry()">
<h3>fetchEntry</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fetchEntry</span>()</div>
<div class="block">fetches information from the named entry inside the archive.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ArchiveResource.html#fetchEntry()">fetchEntry</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRef()">
<h3>getRef</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="TarResource.html" title="class in org.apache.tools.ant.types.resources">TarResource</a></span>&nbsp;<span class="element-name">getRef</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../Resource.html#getRef()">Resource</a></code></span></div>
<div class="block">Perform the check for circular references and return the
 referenced Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ArchiveResource.html#getRef()">getRef</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveResource.html" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a></code></dd>
<dt>Returns:</dt>
<dd><code>Resource</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
