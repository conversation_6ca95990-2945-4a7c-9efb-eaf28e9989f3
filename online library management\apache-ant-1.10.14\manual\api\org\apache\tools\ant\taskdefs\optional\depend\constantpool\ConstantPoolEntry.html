<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ConstantPoolEntry (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.depend.constantpool, class: ConstantPoolEntry">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.depend.constantpool</a></div>
<h1 title="Class ConstantPoolEntry" class="title">Class ConstantPoolEntry</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ClassCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ClassCPInfo</a></code>, <code><a href="ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantCPInfo</a></code>, <code><a href="FieldRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">FieldRefCPInfo</a></code>, <code><a href="InterfaceMethodRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">InterfaceMethodRefCPInfo</a></code>, <code><a href="MethodHandleCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodHandleCPInfo</a></code>, <code><a href="MethodRefCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">MethodRefCPInfo</a></code>, <code><a href="NameAndTypeCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">NameAndTypeCPInfo</a></code>, <code><a href="Utf8CPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">Utf8CPInfo</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ConstantPoolEntry</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">An entry in the constant pool. This class contains a representation of the
 constant pool entries. It is an abstract base class for all the different
 forms of constant pool entry.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><code>ConstantPool</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_CLASS" class="member-name-link">CONSTANT_CLASS</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Class entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_DOUBLE" class="member-name-link">CONSTANT_DOUBLE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for Double entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_FIELDREF" class="member-name-link">CONSTANT_FIELDREF</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Field Reference entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_FLOAT" class="member-name-link">CONSTANT_FLOAT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for Float entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_INTEGER" class="member-name-link">CONSTANT_INTEGER</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Integer entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_INTERFACEMETHODREF" class="member-name-link">CONSTANT_INTERFACEMETHODREF</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for Interface Method Reference entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_INVOKEDYNAMIC" class="member-name-link">CONSTANT_INVOKEDYNAMIC</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for InvokeDynamic entries</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_LONG" class="member-name-link">CONSTANT_LONG</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for Long entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_METHODHANDLE" class="member-name-link">CONSTANT_METHODHANDLE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Method Handle entries</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_METHODREF" class="member-name-link">CONSTANT_METHODREF</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for Method Reference entries.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_METHODTYPE" class="member-name-link">CONSTANT_METHODTYPE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Method Type entries</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_MODULEINFO" class="member-name-link">CONSTANT_MODULEINFO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for CONSTANT_Module_info entry</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_NAMEANDTYPE" class="member-name-link">CONSTANT_NAMEANDTYPE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for Name and Type entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_PACKAGEINFO" class="member-name-link">CONSTANT_PACKAGEINFO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for CONSTANT_Package_info entry (within a module)</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CONSTANT_STRING" class="member-name-link">CONSTANT_STRING</a></code></div>
<div class="col-last even-row-color">
<div class="block">Tag value for String entries.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CONSTANT_UTF8" class="member-name-link">CONSTANT_UTF8</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Tag value for UTF8 entries.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int)" class="member-name-link">ConstantPoolEntry</a><wbr>(int&nbsp;tagValue,
 int&nbsp;entries)</code></div>
<div class="col-last even-row-color">
<div class="block">Initialise the constant pool entry.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNumEntries()" class="member-name-link">getNumEntries</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the number of Constant Pool Entry slots within the constant pool
 occupied by this entry.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTag()" class="member-name-link">getTag</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Entry's type tag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isResolved()" class="member-name-link">isResolved</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether this entry has been resolved.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>abstract void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#read(java.io.DataInputStream)" class="member-name-link">read</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">read a constant pool entry from a class stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#readEntry(java.io.DataInputStream)" class="member-name-link">readEntry</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Read a constant pool entry from a stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)" class="member-name-link">resolve</a><wbr>(<a href="ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a>&nbsp;constantPool)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resolve this constant pool entry with respect to its dependents in
 the constant pool.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="CONSTANT_UTF8">
<h3>CONSTANT_UTF8</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_UTF8</span></div>
<div class="block">Tag value for UTF8 entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_UTF8">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_INTEGER">
<h3>CONSTANT_INTEGER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_INTEGER</span></div>
<div class="block">Tag value for Integer entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_INTEGER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_FLOAT">
<h3>CONSTANT_FLOAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_FLOAT</span></div>
<div class="block">Tag value for Float entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_FLOAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_LONG">
<h3>CONSTANT_LONG</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_LONG</span></div>
<div class="block">Tag value for Long entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_LONG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_DOUBLE">
<h3>CONSTANT_DOUBLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_DOUBLE</span></div>
<div class="block">Tag value for Double entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_DOUBLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_CLASS">
<h3>CONSTANT_CLASS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_CLASS</span></div>
<div class="block">Tag value for Class entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_STRING">
<h3>CONSTANT_STRING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_STRING</span></div>
<div class="block">Tag value for String entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_STRING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_FIELDREF">
<h3>CONSTANT_FIELDREF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_FIELDREF</span></div>
<div class="block">Tag value for Field Reference entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_FIELDREF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_METHODREF">
<h3>CONSTANT_METHODREF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_METHODREF</span></div>
<div class="block">Tag value for Method Reference entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_METHODREF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_INTERFACEMETHODREF">
<h3>CONSTANT_INTERFACEMETHODREF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_INTERFACEMETHODREF</span></div>
<div class="block">Tag value for Interface Method Reference entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_INTERFACEMETHODREF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_NAMEANDTYPE">
<h3>CONSTANT_NAMEANDTYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_NAMEANDTYPE</span></div>
<div class="block">Tag value for Name and Type entries.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_NAMEANDTYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_METHODHANDLE">
<h3>CONSTANT_METHODHANDLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_METHODHANDLE</span></div>
<div class="block">Tag value for Method Handle entries</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_METHODHANDLE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_METHODTYPE">
<h3>CONSTANT_METHODTYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_METHODTYPE</span></div>
<div class="block">Tag value for Method Type entries</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_METHODTYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_INVOKEDYNAMIC">
<h3>CONSTANT_INVOKEDYNAMIC</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_INVOKEDYNAMIC</span></div>
<div class="block">Tag value for InvokeDynamic entries</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_INVOKEDYNAMIC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_MODULEINFO">
<h3>CONSTANT_MODULEINFO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_MODULEINFO</span></div>
<div class="block">Tag value for CONSTANT_Module_info entry</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_MODULEINFO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CONSTANT_PACKAGEINFO">
<h3>CONSTANT_PACKAGEINFO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CONSTANT_PACKAGEINFO</span></div>
<div class="block">Tag value for CONSTANT_Package_info entry (within a module)</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_PACKAGEINFO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(int,int)">
<h3>ConstantPoolEntry</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ConstantPoolEntry</span><wbr><span class="parameters">(int&nbsp;tagValue,
 int&nbsp;entries)</span></div>
<div class="block">Initialise the constant pool entry.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tagValue</code> - the tag value which identifies which type of constant
      pool entry this is.</dd>
<dd><code>entries</code> - the number of constant pool entry slots this entry
      occupies.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="readEntry(java.io.DataInputStream)">
<h3>readEntry</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</a></span>&nbsp;<span class="element-name">readEntry</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</span>
                                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Read a constant pool entry from a stream. This is a factory method
 which reads a constant pool entry form a stream and returns the
 appropriate subclass for the entry.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cpStream</code> - the stream from which the constant pool entry is to
      be read.</dd>
<dt>Returns:</dt>
<dd>the appropriate ConstantPoolEntry subclass representing the
      constant pool entry from the stream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the constant pool entry cannot be read
      from the stream</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isResolved()">
<h3>isResolved</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isResolved</span>()</div>
<div class="block">Indicates whether this entry has been resolved. In general a constant
 pool entry can reference another constant pool entry by its index
 value. Resolution involves replacing this index value with the
 constant pool entry at that index.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this entry has been resolved.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">
<h3>resolve</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resolve</span><wbr><span class="parameters">(<a href="ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</a>&nbsp;constantPool)</span></div>
<div class="block">Resolve this constant pool entry with respect to its dependents in
 the constant pool.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>constantPool</code> - the constant pool of which this entry is a member
      and against which this entry is to be resolved.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="read(java.io.DataInputStream)">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public abstract</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">read</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/DataInputStream.html" title="class or interface in java.io" class="external-link">DataInputStream</a>&nbsp;cpStream)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">read a constant pool entry from a class stream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cpStream</code> - the DataInputStream which contains the constant pool
      entry to be read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading the entry from
      the stream.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTag()">
<h3>getTag</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getTag</span>()</div>
<div class="block">Get the Entry's type tag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The Tag value of this entry</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNumEntries()">
<h3>getNumEntries</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getNumEntries</span>()</div>
<div class="block">Get the number of Constant Pool Entry slots within the constant pool
 occupied by this entry.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of slots used.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
