<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.optional" class="title">Package org.apache.tools.ant.taskdefs.optional</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.optional</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ANTLR.html" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Invokes the ANTLR Translator generator on a grammar file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Cab.html" title="class in org.apache.tools.ant.taskdefs.optional">Cab</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Create a CAB archive.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="EchoProperties.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Displays all the current properties in the build.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A enumerated type for the format attribute.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Javah.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Generates JNI header files using javah.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts files from native encodings to ASCII.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="NetRexxC.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compiles NetRexx source files.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated class corresponding to the trace attribute.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated class corresponding to the verbose attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PropertyFile.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Modifies settings in a property file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PropertyFile.Entry.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Instance of this class represents nested elements of
 a task propertyfile.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PropertyFile.Entry.Operation.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Operation</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "+", "-", "="</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="PropertyFile.Entry.Type.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Type</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Enumerated attribute with the values "int", "date" and "string".</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="PropertyFile.Unit.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Unit</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Borrowed from Tstamp</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.
<div class="deprecation-comment">since 1.5.x.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ReplaceRegExp.html" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Performs regular expression string replacements in a text
 file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Rpm.html" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Invokes the rpm tool to build a Linux installation file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SchemaValidate.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Validate XML Schema documents.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SchemaValidate.SchemaLocation.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">representation of a schema location.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Script.html" title="class in org.apache.tools.ant.taskdefs.optional">Script</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Executes a script.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TraXLiaison.html" title="class in org.apache.tools.ant.taskdefs.optional">TraXLiaison</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Concrete liaison for XSLT processor implementing TraX.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Xalan2TraceSupport.html" title="class in org.apache.tools.ant.taskdefs.optional">Xalan2TraceSupport</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Sets up trace support for a given transformer.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Checks XML files are valid (or only well formed).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The class to create to set a feature of the parser.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A Parser property.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="XSLTTraceSupport.html" title="interface in org.apache.tools.ant.taskdefs.optional">XSLTTraceSupport</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Sets up trace support for a given transformer.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
