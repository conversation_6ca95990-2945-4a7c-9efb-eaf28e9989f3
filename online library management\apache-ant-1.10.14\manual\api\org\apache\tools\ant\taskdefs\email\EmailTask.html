<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>EmailTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.email, class: EmailTask">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.email</a></div>
<h1 title="Class EmailTask" class="title">Class EmailTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.email.EmailTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../optional/net/MimeMail.html" title="class in org.apache.tools.ant.taskdefs.optional.net">MimeMail</a></code>, <code><a href="../SendEmail.html" title="class in org.apache.tools.ant.taskdefs">SendEmail</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">EmailTask</span>
<span class="extends-implements">extends <a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">A task to send SMTP email. This is a refactoring of the SendMail and
 MimeMail tasks such that both are within a single task.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="EmailTask.Encoding.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.email">EmailTask.Encoding</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerates the encoding constants.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#AUTO" class="member-name-link">AUTO</a></code></div>
<div class="col-last even-row-color">
<div class="block">Constant to show that the best available mailer should be used.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#MIME" class="member-name-link">MIME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Constant to allow the Mime mailer to be requested</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PLAIN" class="member-name-link">PLAIN</a></code></div>
<div class="col-last even-row-color">
<div class="block">Constant to allow the plaintext mailer to be requested</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#UU" class="member-name-link">UU</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Constant to allow the UU mailer to be requested</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">EmailTask</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBcc(org.apache.tools.ant.taskdefs.email.EmailAddress)" class="member-name-link">addBcc</a><wbr>(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a "bcc" address element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCc(org.apache.tools.ant.taskdefs.email.EmailAddress)" class="member-name-link">addCc</a><wbr>(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a "cc" address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a set of files (nested fileset attribute).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFrom(org.apache.tools.ant.taskdefs.email.EmailAddress)" class="member-name-link">addFrom</a><wbr>(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a from address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addMessage(org.apache.tools.ant.taskdefs.email.Message)" class="member-name-link">addMessage</a><wbr>(<a href="Message.html" title="class in org.apache.tools.ant.taskdefs.email">Message</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a message element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReplyTo(org.apache.tools.ant.taskdefs.email.EmailAddress)" class="member-name-link">addReplyTo</a><wbr>(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a replyto address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTo(org.apache.tools.ant.taskdefs.email.EmailAddress)" class="member-name-link">addTo</a><wbr>(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a to address element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createAttachments()" class="member-name-link">createAttachments</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a Path as container for attachments.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Header.html" title="class in org.apache.tools.ant.taskdefs.email">Header</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createHeader()" class="member-name-link">createHeader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested header element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send an email.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCharset()" class="member-name-link">getCharset</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the character set of mail message.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeFileNames()" class="member-name-link">getIncludeFileNames</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get whether file names should be included.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBccList(java.lang.String)" class="member-name-link">setBccList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand to set the "bcc" address element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCcList(java.lang.String)" class="member-name-link">setCcList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand to set the "cc" address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCharset(java.lang.String)" class="member-name-link">setCharset</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;charset)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the character set of mail message.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnableStartTLS(boolean)" class="member-name-link">setEnableStartTLS</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to allow authentication to switch to a TLS
 connection via STARTTLS.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(org.apache.tools.ant.taskdefs.email.EmailTask.Encoding)" class="member-name-link">setEncoding</a><wbr>(<a href="EmailTask.Encoding.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask.Encoding</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the preferred encoding method.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnError(boolean)" class="member-name-link">setFailOnError</a><wbr>(boolean&nbsp;failOnError)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether BuildExceptions should be passed back to the core.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFiles(java.lang.String)" class="member-name-link">setFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filenames)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the list of files to be attached.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFrom(java.lang.String)" class="member-name-link">setFrom</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;address)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand to set the from address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnoreInvalidRecipients(boolean)" class="member-name-link">setIgnoreInvalidRecipients</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether invalid recipients should be ignored (but a warning
 will be logged) instead of making the task fail.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludefilenames(boolean)" class="member-name-link">setIncludefilenames</a><wbr>(boolean&nbsp;includeFileNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to include filenames.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMailhost(java.lang.String)" class="member-name-link">setMailhost</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;host)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMailport(int)" class="member-name-link">setMailport</a><wbr>(int&nbsp;port)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the mail server port.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessage(java.lang.String)" class="member-name-link">setMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand method to set the message.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessageFile(java.io.File)" class="member-name-link">setMessageFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand method to set the message from a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessageFileInputEncoding(java.lang.String)" class="member-name-link">setMessageFileInputEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the encoding to expect when reading the message from a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessageMimeType(java.lang.String)" class="member-name-link">setMessageMimeType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand method to set type of the text message, text/plain by default
 but text/html or text/xml is quite feasible.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPassword(java.lang.String)" class="member-name-link">setPassword</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the password for SMTP auth; this requires JavaMail.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReplyTo(java.lang.String)" class="member-name-link">setReplyTo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;address)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand to set the replyto address element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSSL(boolean)" class="member-name-link">setSSL</a><wbr>(boolean&nbsp;ssl)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to send data over SSL.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSubject(java.lang.String)" class="member-name-link">setSubject</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;subject)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the subject line of the email.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setToList(java.lang.String)" class="member-name-link">setToList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shorthand to set the "to" address element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUser(java.lang.String)" class="member-name-link">setUser</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;user)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the user for SMTP auth; this requires JavaMail.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#init()">init</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="AUTO">
<h3>AUTO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">AUTO</span></div>
<div class="block">Constant to show that the best available mailer should be used.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.email.EmailTask.AUTO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MIME">
<h3>MIME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">MIME</span></div>
<div class="block">Constant to allow the Mime mailer to be requested</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.email.EmailTask.MIME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="UU">
<h3>UU</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">UU</span></div>
<div class="block">Constant to allow the UU mailer to be requested</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.email.EmailTask.UU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PLAIN">
<h3>PLAIN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PLAIN</span></div>
<div class="block">Constant to allow the plaintext mailer to be requested</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.email.EmailTask.PLAIN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>EmailTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">EmailTask</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setUser(java.lang.String)">
<h3>setUser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUser</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;user)</span></div>
<div class="block">Set the user for SMTP auth; this requires JavaMail.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>user</code> - the String username.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPassword(java.lang.String)">
<h3>setPassword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPassword</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</span></div>
<div class="block">Set the password for SMTP auth; this requires JavaMail.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>password</code> - the String password.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSSL(boolean)">
<h3>setSSL</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSSL</span><wbr><span class="parameters">(boolean&nbsp;ssl)</span></div>
<div class="block">Set whether to send data over SSL.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ssl</code> - boolean; if true SSL will be used.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEnableStartTLS(boolean)">
<h3>setEnableStartTLS</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEnableStartTLS</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set whether to allow authentication to switch to a TLS
 connection via STARTTLS.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean; if true STARTTLS will be supported.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEncoding(org.apache.tools.ant.taskdefs.email.EmailTask.Encoding)">
<h3>setEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="EmailTask.Encoding.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask.Encoding</a>&nbsp;encoding)</span></div>
<div class="block">Set the preferred encoding method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - The encoding (one of AUTO, MIME, UU, PLAIN).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMailport(int)">
<h3>setMailport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMailport</span><wbr><span class="parameters">(int&nbsp;port)</span></div>
<div class="block">Set the mail server port.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>port</code> - The port to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMailhost(java.lang.String)">
<h3>setMailhost</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMailhost</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;host)</span></div>
<div class="block">Set the host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>host</code> - The host to connect to.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSubject(java.lang.String)">
<h3>setSubject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSubject</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;subject)</span></div>
<div class="block">Set the subject line of the email.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>subject</code> - Subject of this email.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMessage(java.lang.String)">
<h3>setMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">Shorthand method to set the message.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - Message body of this email.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMessageFile(java.io.File)">
<h3>setMessageFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessageFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Shorthand method to set the message from a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - The file from which to take the message.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMessageMimeType(java.lang.String)">
<h3>setMessageMimeType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessageMimeType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type)</span></div>
<div class="block">Shorthand method to set type of the text message, text/plain by default
 but text/html or text/xml is quite feasible.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - The new MessageMimeType value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addMessage(org.apache.tools.ant.taskdefs.email.Message)">
<h3>addMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addMessage</span><wbr><span class="parameters">(<a href="Message.html" title="class in org.apache.tools.ant.taskdefs.email">Message</a>&nbsp;message)</span>
                throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Add a message element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The message object.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if a message has already been added.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFrom(org.apache.tools.ant.taskdefs.email.EmailAddress)">
<h3>addFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFrom</span><wbr><span class="parameters">(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</span></div>
<div class="block">Add a from address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The address to send from.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFrom(java.lang.String)">
<h3>setFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFrom</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;address)</span></div>
<div class="block">Shorthand to set the from address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The address to send mail from.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReplyTo(org.apache.tools.ant.taskdefs.email.EmailAddress)">
<h3>addReplyTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReplyTo</span><wbr><span class="parameters">(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</span></div>
<div class="block">Add a replyto address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The address to reply to.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setReplyTo(java.lang.String)">
<h3>setReplyTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReplyTo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;address)</span></div>
<div class="block">Shorthand to set the replyto address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The address to which replies should be directed.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTo(org.apache.tools.ant.taskdefs.email.EmailAddress)">
<h3>addTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTo</span><wbr><span class="parameters">(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</span></div>
<div class="block">Add a to address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - An email address.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setToList(java.lang.String)">
<h3>setToList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setToList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</span></div>
<div class="block">Shorthand to set the "to" address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - Comma-separated list of addresses.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addCc(org.apache.tools.ant.taskdefs.email.EmailAddress)">
<h3>addCc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addCc</span><wbr><span class="parameters">(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</span></div>
<div class="block">Add a "cc" address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The email address.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCcList(java.lang.String)">
<h3>setCcList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCcList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</span></div>
<div class="block">Shorthand to set the "cc" address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - Comma separated list of addresses.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addBcc(org.apache.tools.ant.taskdefs.email.EmailAddress)">
<h3>addBcc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBcc</span><wbr><span class="parameters">(<a href="EmailAddress.html" title="class in org.apache.tools.ant.taskdefs.email">EmailAddress</a>&nbsp;address)</span></div>
<div class="block">Add a "bcc" address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>address</code> - The email address.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBccList(java.lang.String)">
<h3>setBccList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBccList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;list)</span></div>
<div class="block">Shorthand to set the "bcc" address element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - comma separated list of addresses.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnError(boolean)">
<h3>setFailOnError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnError</span><wbr><span class="parameters">(boolean&nbsp;failOnError)</span></div>
<div class="block">Set whether BuildExceptions should be passed back to the core.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failOnError</code> - The new FailOnError value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFiles(java.lang.String)">
<h3>setFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filenames)</span></div>
<div class="block">Set the list of files to be attached.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filenames</code> - Comma-separated list of files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</span></div>
<div class="block">Add a set of files (nested fileset attribute).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fs</code> - The fileset.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAttachments()">
<h3>createAttachments</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createAttachments</span>()</div>
<div class="block">Creates a Path as container for attachments.  Supports any
 filesystem resource-collections that way.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the path to be configured.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createHeader()">
<h3>createHeader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Header.html" title="class in org.apache.tools.ant.taskdefs.email">Header</a></span>&nbsp;<span class="element-name">createHeader</span>()</div>
<div class="block">Create a nested header element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a Header instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludefilenames(boolean)">
<h3>setIncludefilenames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludefilenames</span><wbr><span class="parameters">(boolean&nbsp;includeFileNames)</span></div>
<div class="block">Set whether to include filenames.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includeFileNames</code> - Whether to include filenames in the text of the
      message.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeFileNames()">
<h3>getIncludeFileNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeFileNames</span>()</div>
<div class="block">Get whether file names should be included.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Identifies whether file names should be included.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnoreInvalidRecipients(boolean)">
<h3>setIgnoreInvalidRecipients</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnoreInvalidRecipients</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether invalid recipients should be ignored (but a warning
 will be logged) instead of making the task fail.

 <p>Even with this property set to true the task will still fail
 if the mail couldn't be sent to any recipient at all.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()</div>
<div class="block">Send an email.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCharset(java.lang.String)">
<h3>setCharset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCharset</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;charset)</span></div>
<div class="block">Sets the character set of mail message.
 Will be ignored if mimeType contains ....; Charset=... substring or
 encoding is not <code>mime</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>charset</code> - the character encoding to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCharset()">
<h3>getCharset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCharset</span>()</div>
<div class="block">Returns the character set of mail message.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Charset of mail message.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMessageFileInputEncoding(java.lang.String)">
<h3>setMessageFileInputEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessageFileInputEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Sets the encoding to expect when reading the message from a file.
 <p>Will be ignored if the message has been specified inline.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the name of the charset used</dd>
<dt>Since:</dt>
<dd>Ant 1.9.4</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
