
OPENJDK ASSEMBLY EXCEPTION

The OpenJDK source code made available by Oracle America, Inc. (Oracle) at
openjdk.org ("OpenJDK Code") is distributed under the terms of the GNU
General Public License <https://www.gnu.org/copyleft/gpl.html> version 2
only ("GPL2"), with the following clarification and special exception.

    Linking this OpenJDK Code statically or dynamically with other code
    is making a combined work based on this library.  Thus, the terms
    and conditions of GPL2 cover the whole combination.

    As a special exception, Oracle gives you permission to link this
    OpenJDK Code with certain code licensed by Oracle as indicated at
    https://openjdk.org/legal/exception-modules-2007-05-08.html
    ("Designated Exception Modules") to produce an executable,
    regardless of the license terms of the Designated Exception Modules,
    and to copy and distribute the resulting executable under GPL2,
    provided that the Designated Exception Modules continue to be
    governed by the licenses under which they were offered by Oracle.

As such, it allows licensees and sublicensees of Oracle's GPL2 OpenJDK Code
to build an executable that includes those portions of necessary code that
Oracle could not provide under GPL2 (or that Oracle has provided under GPL2
with the Classpath exception).  If you modify or add to the OpenJDK code,
that new GPL2 code may still be combined with Designated Exception Modules
if the new code is made subject to this exception by its copyright holder.
