<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Translate Task</title>
</head>

<body>

<h2 id="translate">Translate</h2>
<h3>Description</h3>
<p>Identifies keys in files delimited by special tokens and translates them with values read from
resource bundles.</p>
<p>A resource bundle contains locale-specific key-value pairs.  A resource bundle is a hierarchical
set of property files.  A bundle name makes up its base family name.  Each file that makes up this
bundle has this name plus its locale.  For example, if the resource bundle name
is <samp>MyResources</samp>, the file that contains German text will take the
name <samp>MyResources_de</samp>.  In addition to language, country and variant are also used to
form the files in the bundle.</p>
<p>The resource bundle lookup searches for resource files with various suffixes on the basis of (1)
the desired locale and (2) the default locale (basebundlename), in the following order from
lower-level (more specific) to parent-level (less specific):</p>
<pre>
basebundlename + &quot;_&quot; + language1 + &quot;_&quot; + country1 + &quot;_&quot; + variant1
basebundlename + &quot;_&quot; + language1 + &quot;_&quot; + country1
basebundlename + &quot;_&quot; + language1
basebundlename
basebundlename + &quot;_&quot; + language2 + &quot;_&quot; + country2 + &quot;_&quot; + variant2
basebundlename + &quot;_&quot; + language2 + &quot;_&quot; + country2
basebundlename + &quot;_&quot; + language2
</pre>
<p>The file names generated thus are appended with the string <samp>.properties</samp> to make up
the file names that are to be used.</p>
<p>File encoding is supported.  The encoding scheme of the source files, destination files and the
bundle files can be specified.</p>
<p>Destination files can be explicitly overwritten using the <var>forceoverwrite</var> attribute.
If <var>forceoverwrite</var> is <q>false</q>, the destination file is overwritten only if either the
source file or any of the files that make up the bundle have been modified after the destination
file was last modified.</p>
<p><em>Since Apache Ant 1.6</em> line endings of source files are preserved in the translated
files.</p>
<p><a href="../Types/fileset.html">FileSet</a>s are used to select files to translate.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>todir</td>
    <td>Destination directory where destination files are to be created.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>starttoken</td>
    <td>The starting token to identify keys.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>endtoken</td>
    <td>The ending token to identify keys.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>bundle</td>
    <td>Family name of resource bundle.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>bundlelanguage</td>
    <td>Locale specific language of resource bundle.</td>
    <td>No; defaults to default locale's language</td>
  </tr>
  <tr>
    <td>bundlecountry</td>
    <td>Locale specific country of resource bundle.</td>
    <td>No; defaults to default locale's country</td>
  </tr>
  <tr>
    <td>bundlevariant</td>
    <td>Locale specific variant of resource bundle.</td>
    <td>No; defaults to <var>bundlelanguage</var>_<var>bundlecountry</var></td>
  </tr>
  <tr>
    <td>srcencoding</td>
    <td>Source file encoding scheme.</td>
    <td>No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>destencoding</td>
    <td>Destination file encoding scheme.</td>
    <td>No; defaults to <var>srcencoding</var></td>
  </tr>
  <tr>
    <td>bundleencoding</td>
    <td>Resource Bundle file encoding scheme.</td>
    <td>No; defaults to <var>srcencoding</var></td>
  </tr>
  <tr>
    <td>forceoverwrite</td>
    <td>Overwrite existing files even if the destination files are newer.</td>
    <td>No; defaults to <q>no</q></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>fileset</h4>
<p><a href="../Types/fileset.html">FileSets</a> are used to select files that contain keys for which
value translated files are to be generated.</p>

<h3>Examples</h3>
<p>Translate source file encoded in English into its Japanese equivalent using a resource bundle
encoded in Japanese.</p>
<pre>
&lt;translate toDir=&quot;$(dest.dir}/ja&quot;
           starttoken=&quot;#&quot;
           endtoken=&quot;#&quot;
           bundle=&quot;resource/BaseResource&quot;
           bundlelanguage=&quot;ja&quot;
           forceoverwrite=&quot;yes&quot;
           srcencoding=&quot;ISO8859_1&quot;
           destencoding=&quot;SJIS&quot;
           bundleencoding=&quot;SJIS&quot;&gt;
    &lt;fileset dir=&quot;${src.dir}&quot;&gt;
        &lt;include name=&quot;**/*.jsp&quot;/&gt;
    &lt;/fileset&gt;
&lt;/translate&gt;</pre>

</body>
</html>
