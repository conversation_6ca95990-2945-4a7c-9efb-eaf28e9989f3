<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>MultiRootFileSet Type</title>
</head>

<body>

<h2 id="multirootfileset">MultiRootFileSet</h2>

<p><em>Since Ant 1.9.4</em></p>

<p>A MultiRootFileSet is a group of files or directories. These files or directories can be
found in a directory forrest starting with a set of base directories and are matched by patterns
taken from a number of <a href="patternset.html">PatternSets</a>
and <a href="selectors.html">Selectors</a>.</p>

<p>MultiRootFileSet acts as a union of <a href="fileset.html">FileSets</a>
and <a href="dirset.html">DirSets</a> that share the same patterns and selectors.</p>

<p>MultiRootFileSet supports all attributes and nested elements of FileSet and DirSet except for
the <var>dir</var> attribute.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>basedirs</td>
    <td>Comma separated list of directories that build the roots of the MultiRootFileSet.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>cache</td>
    <td>Whether to cache results; disabling may seriously impact performance</td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>type</td>
    <td>The type of file system entities which will be included in this set.  Acceptable values
    are:
    <ul>
      <li><q>file</q>&mdash;regular files</li>
      <li><q>dir</q>&mdash;directories</li>
      <li><q>both</q>&mdash;regular files and directories</li>
    </ul>
    </td>
    <td>No; defaults to <q>file</q></td>
  </tr>
  <tr>
    <td>includes</td>
    <td>A comma- or space-separated list of patterns of directories that must be included.</td>
    <td>No; defaults to all directories</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>Name of a file; each line of this file is taken to be an include pattern.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>A comma- or space-separated list of patterns of directories that must be excluded.</td>
    <td>No; defaults to none</td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>Name of a file; each line of this file is taken to be an exclude pattern.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>casesensitive</td>
    <td>Specifies whether case-sensitivity should be applied (<q>true</q>, <q>yes</q>, <q>on</q>
     or <q>false</q>, <q>no</q>, <q>off</q>).</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>followsymlinks</td>
    <td>Shall symbolic links be followed? See <a href="fileset.html#symlink">fileset's
      documentation</a>.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>erroronmissingdir</td>
    <td>Specify what happens if one of the base directories does not exist.  If <q>true</q> a
      build error will happen, if <q>false</q>, the subtree will be ignored/empty.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>

<p>PatternSets can be specified as nested <code>&lt;patternset&gt;</code> elements. In addition,
MultiRootFileSet holds an implicit PatternSet and supports the
nested <code>&lt;include&gt;</code>, <code>&lt;includesfile&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;excludesfile&gt;</code> elements of <code>&lt;patternset&gt;</code> directly, as
well as <code>&lt;patternset&gt;</code>'s attributes.</p>

<p>Selectors are available as nested elements within the MultiRootFileSet.  If any of the
selectors within the MultiRootFileSet do not select the file or directory, it is not considered
part of the MultiRootFileSet. This makes a MultiRootFileSet equivalent to
an <code>&lt;and&gt;</code> selector container.</p>

<p>In addition basedirs for the MultiRootFileSet can be specified as nested <code>basedir</code>
elements that have a single <var>file</var> attribute.</p>

<h3>Examples</h3>

<pre>
&lt;multirootfileset basedirs=&quot;${build.dir},${other.project.dir}&quot;&gt;
  &lt;include name=&quot;apps/**/classes&quot;/&gt;
  &lt;exclude name=&quot;apps/**/*Test*&quot;/&gt;
&lt;/multirootfileset&gt;
</pre>
<p>Groups all files inside <samp>classes</samp> found under the <samp>apps</samp> subdirectory
of <samp>${build.dir}</samp> or <samp>${other.project.dir}</samp>, except those that have the
text <samp>Test</samp> in their name.</p>

<pre>
&lt;multirootfileset&gt;
  &lt;basedir file=&quot;${build.dir}&quot;/&gt;
  &lt;basedir file=&quot;${other.project.dir}&quot;
  &lt;include name=&quot;apps/**/classes&quot;/&gt;
  &lt;exclude name=&quot;apps/**/*Test*&quot;/&gt;
&lt;/multirootfileset&gt;
</pre>
<p>Is equivalent to the first example but used nested <code>basedir</code> elements.  The nested
elements and the <var>basedirs</var> attribute can be used at the same time and the will be
merged.</p>

</body>
</html>
