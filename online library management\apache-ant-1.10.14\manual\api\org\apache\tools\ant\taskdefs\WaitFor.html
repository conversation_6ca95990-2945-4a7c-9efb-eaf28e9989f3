<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>WaitFor (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: WaitFor">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class WaitFor" class="title">Class WaitFor</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">org.apache.tools.ant.taskdefs.condition.ConditionBase</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.WaitFor</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="optional/testing/BlockFor.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">BlockFor</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">WaitFor</span>
<span class="extends-implements">extends <a href="condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></span></div>
<div class="block">Wait for an external event to occur.

 Wait for an external process to start or to complete some
 task. This is useful with the <code>parallel</code> task to
 synchronize the execution of tests with server startup.

 The following attributes can be specified on a waitfor task:
 <ul>
 <li>maxwait - maximum length of time to wait before giving up</li>
 <li>maxwaitunit - The unit to be used to interpret maxwait attribute</li>
 <li>checkevery - amount of time to sleep between each check</li>
 <li>checkeveryunit - The unit to be used to interpret checkevery attribute</li>
 <li>timeoutproperty - name of a property to set if maxwait has been exceeded.</li>
 </ul>

 The maxwaitunit and checkeveryunit are allowed to have the following values:
 millisecond, second, minute, hour, day and week. The default is millisecond.

 For programmatic use/subclassing, there are two methods that may be overridden,
 <code>processSuccess</code> and <code>processTimeout</code></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="WaitFor.Unit.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a></code></div>
<div class="col-last even-row-color">
<div class="block">The enumeration of units:
 millisecond, second, minute, hour, day, week</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_CHECK_MILLIS" class="member-name-link">DEFAULT_CHECK_MILLIS</a></code></div>
<div class="col-last even-row-color">
<div class="block">default check time</div>
</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_MAX_WAIT_MILLIS" class="member-name-link">DEFAULT_MAX_WAIT_MILLIS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">default wait time</div>
</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#ONE_DAY" class="member-name-link">ONE_DAY</a></code></div>
<div class="col-last even-row-color">
<div class="block">a day in milliseconds</div>
</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#ONE_HOUR" class="member-name-link">ONE_HOUR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">an hour in milliseconds</div>
</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#ONE_MILLISECOND" class="member-name-link">ONE_MILLISECOND</a></code></div>
<div class="col-last even-row-color">
<div class="block">a millisecond</div>
</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#ONE_MINUTE" class="member-name-link">ONE_MINUTE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">a minute in milliseconds</div>
</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#ONE_SECOND" class="member-name-link">ONE_SECOND</a></code></div>
<div class="col-last even-row-color">
<div class="block">a second in milliseconds</div>
</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#ONE_WEEK" class="member-name-link">ONE_WEEK</a></code></div>
<div class="col-last odd-row-color">
<div class="block">a week in milliseconds</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">WaitFor</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor, names this task "waitfor".</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">WaitFor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor that takes the name of the task in the task name.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#calculateCheckEveryMillis()" class="member-name-link">calculateCheckEveryMillis</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the check wait time, in milliseconds.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#calculateMaxWaitMillis()" class="member-name-link">calculateMaxWaitMillis</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the maximum wait time, in milliseconds.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check repeatedly for the specified conditions until they become
 true or the timeout expires.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processSuccess()" class="member-name-link">processSuccess</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Actions to be taken on a successful waitfor.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processTimeout()" class="member-name-link">processTimeout</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Actions to be taken on an unsuccessful wait.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCheckEvery(long)" class="member-name-link">setCheckEvery</a><wbr>(long&nbsp;time)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the time between each check</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCheckEveryUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)" class="member-name-link">setCheckEveryUnit</a><wbr>(<a href="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the check every time unit</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxWait(long)" class="member-name-link">setMaxWait</a><wbr>(long&nbsp;time)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the maximum length of time to wait.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxWaitUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)" class="member-name-link">setMaxWaitUnit</a><wbr>(<a href="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the max wait time unit</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeoutProperty(java.lang.String)" class="member-name-link">setTimeoutProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name the property to set after a timeout.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.condition.ConditionBase">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.condition.<a href="condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></h3>
<code><a href="condition/ConditionBase.html#add(org.apache.tools.ant.taskdefs.condition.Condition)">add</a>, <a href="condition/ConditionBase.html#addAnd(org.apache.tools.ant.taskdefs.condition.And)">addAnd</a>, <a href="condition/ConditionBase.html#addAvailable(org.apache.tools.ant.taskdefs.Available)">addAvailable</a>, <a href="condition/ConditionBase.html#addChecksum(org.apache.tools.ant.taskdefs.Checksum)">addChecksum</a>, <a href="condition/ConditionBase.html#addContains(org.apache.tools.ant.taskdefs.condition.Contains)">addContains</a>, <a href="condition/ConditionBase.html#addEquals(org.apache.tools.ant.taskdefs.condition.Equals)">addEquals</a>, <a href="condition/ConditionBase.html#addFilesMatch(org.apache.tools.ant.taskdefs.condition.FilesMatch)">addFilesMatch</a>, <a href="condition/ConditionBase.html#addHttp(org.apache.tools.ant.taskdefs.condition.Http)">addHttp</a>, <a href="condition/ConditionBase.html#addIsFalse(org.apache.tools.ant.taskdefs.condition.IsFalse)">addIsFalse</a>, <a href="condition/ConditionBase.html#addIsFileSelected(org.apache.tools.ant.taskdefs.condition.IsFileSelected)">addIsFileSelected</a>, <a href="condition/ConditionBase.html#addIsReference(org.apache.tools.ant.taskdefs.condition.IsReference)">addIsReference</a>, <a href="condition/ConditionBase.html#addIsSet(org.apache.tools.ant.taskdefs.condition.IsSet)">addIsSet</a>, <a href="condition/ConditionBase.html#addIsTrue(org.apache.tools.ant.taskdefs.condition.IsTrue)">addIsTrue</a>, <a href="condition/ConditionBase.html#addNot(org.apache.tools.ant.taskdefs.condition.Not)">addNot</a>, <a href="condition/ConditionBase.html#addOr(org.apache.tools.ant.taskdefs.condition.Or)">addOr</a>, <a href="condition/ConditionBase.html#addOs(org.apache.tools.ant.taskdefs.condition.Os)">addOs</a>, <a href="condition/ConditionBase.html#addSocket(org.apache.tools.ant.taskdefs.condition.Socket)">addSocket</a>, <a href="condition/ConditionBase.html#addUptodate(org.apache.tools.ant.taskdefs.UpToDate)">addUptodate</a>, <a href="condition/ConditionBase.html#countConditions()">countConditions</a>, <a href="condition/ConditionBase.html#getConditions()">getConditions</a>, <a href="condition/ConditionBase.html#getTaskName()">getTaskName</a>, <a href="condition/ConditionBase.html#setTaskName(java.lang.String)">setTaskName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ONE_MILLISECOND">
<h3>ONE_MILLISECOND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_MILLISECOND</span></div>
<div class="block">a millisecond</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_MILLISECOND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_SECOND">
<h3>ONE_SECOND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_SECOND</span></div>
<div class="block">a second in milliseconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_SECOND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_MINUTE">
<h3>ONE_MINUTE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_MINUTE</span></div>
<div class="block">a minute in milliseconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_MINUTE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_HOUR">
<h3>ONE_HOUR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_HOUR</span></div>
<div class="block">an hour in milliseconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_HOUR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_DAY">
<h3>ONE_DAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_DAY</span></div>
<div class="block">a day in milliseconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_DAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ONE_WEEK">
<h3>ONE_WEEK</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">ONE_WEEK</span></div>
<div class="block">a week in milliseconds</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.ONE_WEEK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_MAX_WAIT_MILLIS">
<h3>DEFAULT_MAX_WAIT_MILLIS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">DEFAULT_MAX_WAIT_MILLIS</span></div>
<div class="block">default wait time</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.DEFAULT_MAX_WAIT_MILLIS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_CHECK_MILLIS">
<h3>DEFAULT_CHECK_MILLIS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">DEFAULT_CHECK_MILLIS</span></div>
<div class="block">default check time</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.WaitFor.DEFAULT_CHECK_MILLIS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>WaitFor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WaitFor</span>()</div>
<div class="block">Constructor, names this task "waitfor".</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>WaitFor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WaitFor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;taskName)</span></div>
<div class="block">Constructor that takes the name of the task in the task name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>taskName</code> - the name of the task.</dd>
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMaxWait(long)">
<h3>setMaxWait</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxWait</span><wbr><span class="parameters">(long&nbsp;time)</span></div>
<div class="block">Set the maximum length of time to wait.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - a <code>long</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMaxWaitUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">
<h3>setMaxWaitUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxWaitUnit</span><wbr><span class="parameters">(<a href="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</span></div>
<div class="block">Set the max wait time unit</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unit</code> - an enumerated <code>Unit</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCheckEvery(long)">
<h3>setCheckEvery</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCheckEvery</span><wbr><span class="parameters">(long&nbsp;time)</span></div>
<div class="block">Set the time between each check</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>time</code> - a <code>long</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCheckEveryUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">
<h3>setCheckEveryUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCheckEveryUnit</span><wbr><span class="parameters">(<a href="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</span></div>
<div class="block">Set the check every time unit</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unit</code> - an enumerated <code>Unit</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeoutProperty(java.lang.String)">
<h3>setTimeoutProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeoutProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;p)</span></div>
<div class="block">Name the property to set after a timeout.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the property name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check repeatedly for the specified conditions until they become
 true or the timeout expires.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calculateCheckEveryMillis()">
<h3>calculateCheckEveryMillis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">calculateCheckEveryMillis</span>()</div>
<div class="block">Get the check wait time, in milliseconds.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>how long to wait between checks</dd>
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="calculateMaxWaitMillis()">
<h3>calculateMaxWaitMillis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">calculateMaxWaitMillis</span>()</div>
<div class="block">Get the maximum wait time, in milliseconds.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>how long to wait before timing out</dd>
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processSuccess()">
<h3>processSuccess</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processSuccess</span>()</div>
<div class="block">Actions to be taken on a successful waitfor.
 This is an override point. The base implementation does nothing.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processTimeout()">
<h3>processTimeout</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processTimeout</span>()</div>
<div class="block">Actions to be taken on an unsuccessful wait.
 This is an override point. It is where the timeout processing takes place.
 The base implementation sets the timeoutproperty if there was a timeout
 and the property was defined.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
