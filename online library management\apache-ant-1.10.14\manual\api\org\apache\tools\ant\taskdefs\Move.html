<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Move (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Move">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Move" class="title">Class Move</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copy</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Move</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Move</span>
<span class="extends-implements">extends <a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></span></div>
<div class="block">Moves a file or directory to a new file or directory.
 By default, the
 destination file is overwritten if it already exists.
 When <i>overwrite</i> is
 turned off, then files are only moved if the source file is
 newer than the destination file, or when the destination file does
 not exist.

 <p>Source files and directories are only deleted when the file or
 directory has been copied to the destination successfully.  Filtering
 also works.</p>

 <p>This implementation is based on Arnout Kuiper's initial design
 document, the following mailing list discussions, and the
 copyfile/copydir tasks.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.Copy">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></h3>
<code><a href="Copy.html#completeDirMap">completeDirMap</a>, <a href="Copy.html#destDir">destDir</a>, <a href="Copy.html#destFile">destFile</a>, <a href="Copy.html#dirCopyMap">dirCopyMap</a>, <a href="Copy.html#failonerror">failonerror</a>, <a href="Copy.html#file">file</a>, <a href="Copy.html#fileCopyMap">fileCopyMap</a>, <a href="Copy.html#filesets">filesets</a>, <a href="Copy.html#fileUtils">fileUtils</a>, <a href="Copy.html#filtering">filtering</a>, <a href="Copy.html#flatten">flatten</a>, <a href="Copy.html#forceOverwrite">forceOverwrite</a>, <a href="Copy.html#includeEmpty">includeEmpty</a>, <a href="Copy.html#mapperElement">mapperElement</a>, <a href="Copy.html#preserveLastModified">preserveLastModified</a>, <a href="Copy.html#rcs">rcs</a>, <a href="Copy.html#verbosity">verbosity</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Move</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor of object.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteDir(java.io.File)" class="member-name-link">deleteDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Go and delete the directory tree.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteDir(java.io.File,boolean)" class="member-name-link">deleteDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d,
 boolean&nbsp;deleteFiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Go and delete the directory tree.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#doFileOperations()" class="member-name-link">doFileOperations</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override copy's doFileOperations to move the files instead of copying them.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#okToDelete(java.io.File)" class="member-name-link">okToDelete</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Its only ok to delete a directory tree if there are no files in it.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#renameFile(java.io.File,java.io.File,boolean,boolean)" class="member-name-link">renameFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attempts to rename a file from a source to a destination.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPerformGcOnFailedDelete(boolean)" class="member-name-link">setPerformGcOnFailedDelete</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to perform a garbage collection before retrying a failed delete.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateAttributes()" class="member-name-link">validateAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ensure we have a consistent and legal set of attributes, and set
 any internal flags necessary based on different combinations
 of attributes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.Copy">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></h3>
<code><a href="Copy.html#add(org.apache.tools.ant.types.ResourceCollection)">add</a>, <a href="Copy.html#add(org.apache.tools.ant.util.FileNameMapper)">add</a>, <a href="Copy.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="Copy.html#buildMap(java.io.File,java.io.File,java.lang.String%5B%5D,org.apache.tools.ant.util.FileNameMapper,java.util.Hashtable)">buildMap</a>, <a href="Copy.html#buildMap(org.apache.tools.ant.types.Resource%5B%5D,java.io.File,org.apache.tools.ant.util.FileNameMapper)">buildMap</a>, <a href="Copy.html#createFilterChain()">createFilterChain</a>, <a href="Copy.html#createFilterSet()">createFilterSet</a>, <a href="Copy.html#createMapper()">createMapper</a>, <a href="Copy.html#doResourceOperations(java.util.Map)">doResourceOperations</a>, <a href="Copy.html#execute()">execute</a>, <a href="Copy.html#getEncoding()">getEncoding</a>, <a href="Copy.html#getFileUtils()">getFileUtils</a>, <a href="Copy.html#getFilterChains()">getFilterChains</a>, <a href="Copy.html#getFilterSets()">getFilterSets</a>, <a href="Copy.html#getForce()">getForce</a>, <a href="Copy.html#getOutputEncoding()">getOutputEncoding</a>, <a href="Copy.html#getPreserveLastModified()">getPreserveLastModified</a>, <a href="Copy.html#isEnableMultipleMapping()">isEnableMultipleMapping</a>, <a href="Copy.html#scan(java.io.File,java.io.File,java.lang.String%5B%5D,java.lang.String%5B%5D)">scan</a>, <a href="Copy.html#scan(org.apache.tools.ant.types.Resource%5B%5D,java.io.File)">scan</a>, <a href="Copy.html#setEnableMultipleMappings(boolean)">setEnableMultipleMappings</a>, <a href="Copy.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="Copy.html#setFailOnError(boolean)">setFailOnError</a>, <a href="Copy.html#setFile(java.io.File)">setFile</a>, <a href="Copy.html#setFiltering(boolean)">setFiltering</a>, <a href="Copy.html#setFlatten(boolean)">setFlatten</a>, <a href="Copy.html#setForce(boolean)">setForce</a>, <a href="Copy.html#setGranularity(long)">setGranularity</a>, <a href="Copy.html#setIncludeEmptyDirs(boolean)">setIncludeEmptyDirs</a>, <a href="Copy.html#setOutputEncoding(java.lang.String)">setOutputEncoding</a>, <a href="Copy.html#setOverwrite(boolean)">setOverwrite</a>, <a href="Copy.html#setPreserveLastModified(boolean)">setPreserveLastModified</a>, <a href="Copy.html#setPreserveLastModified(java.lang.String)">setPreserveLastModified</a>, <a href="Copy.html#setQuiet(boolean)">setQuiet</a>, <a href="Copy.html#setTodir(java.io.File)">setTodir</a>, <a href="Copy.html#setTofile(java.io.File)">setTofile</a>, <a href="Copy.html#setVerbose(boolean)">setVerbose</a>, <a href="Copy.html#supportsNonFileResources()">supportsNonFileResources</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Move</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Move</span>()</div>
<div class="block">Constructor of object.
 This sets the forceOverwrite attribute of the Copy parent class
 to true.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setPerformGcOnFailedDelete(boolean)">
<h3>setPerformGcOnFailedDelete</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPerformGcOnFailedDelete</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to perform a garbage collection before retrying a failed delete.

 <p>This may be required on Windows (where it is set to true by
 default) but also on other operating systems, for example when
 deleting directories from an NFS share.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validateAttributes()">
<h3>validateAttributes</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateAttributes</span>()
                           throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Ensure we have a consistent and legal set of attributes, and set
 any internal flags necessary based on different combinations
 of attributes..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Copy.html#validateAttributes()">validateAttributes</a></code>&nbsp;in class&nbsp;<code><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doFileOperations()">
<h3>doFileOperations</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">doFileOperations</span>()</div>
<div class="block">Override copy's doFileOperations to move the files instead of copying them.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Copy.html#doFileOperations()">doFileOperations</a></code>&nbsp;in class&nbsp;<code><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="okToDelete(java.io.File)">
<h3>okToDelete</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">okToDelete</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</span></div>
<div class="block">Its only ok to delete a directory tree if there are no files in it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the directory to check</dd>
<dt>Returns:</dt>
<dd>true if a deletion can go ahead</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteDir(java.io.File)">
<h3>deleteDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</span></div>
<div class="block">Go and delete the directory tree.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the directory to delete</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteDir(java.io.File,boolean)">
<h3>deleteDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d,
 boolean&nbsp;deleteFiles)</span></div>
<div class="block">Go and delete the directory tree.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the directory to delete</dd>
<dd><code>deleteFiles</code> - whether to delete files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="renameFile(java.io.File,java.io.File,boolean,boolean)">
<h3>renameFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">renameFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile,
 boolean&nbsp;filtering,
 boolean&nbsp;overwrite)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Attempts to rename a file from a source to a destination.
 If overwrite is set to true, this method overwrites existing file
 even if the destination file is newer.  Otherwise, the source file is
 renamed only if the destination file is older than it.
 Method then checks if token filtering is used.  If it is, this method
 returns false assuming it is the responsibility to the copyFile method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFile</code> - the file to rename</dd>
<dd><code>destFile</code> - the destination file</dd>
<dd><code>filtering</code> - if true, filtering is in operation, file will
                   be copied/deleted instead of renamed</dd>
<dd><code>overwrite</code> - if true force overwrite even if destination file
                   is newer than source file</dd>
<dt>Returns:</dt>
<dd>true if the file was renamed</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if an error occurs</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
