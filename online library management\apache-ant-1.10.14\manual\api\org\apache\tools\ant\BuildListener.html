<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>BuildListener (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, interface: BuildListener">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Interface BuildListener" class="title">Interface BuildListener</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code>, <code><a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></code>, <code><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></code>, <code><a href="loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">AntClassLoader2</a></code>, <code><a href="loader/AntClassLoader5.html" title="class in org.apache.tools.ant.loader">AntClassLoader5</a></code>, <code><a href="taskdefs/optional/sound/AntSoundPlayer.html" title="class in org.apache.tools.ant.taskdefs.optional.sound">AntSoundPlayer</a></code>, <code><a href="listener/BigProjectLogger.html" title="class in org.apache.tools.ant.listener">BigProjectLogger</a></code>, <code><a href="listener/CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</a></code>, <code><a href="DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code>, <code><a href="taskdefs/optional/junit/FailureRecorder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FailureRecorder</a></code>, <code><a href="listener/Log4jListener.html" title="class in org.apache.tools.ant.listener">Log4jListener</a></code>, <code><a href="listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</a></code>, <code><a href="types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a></code>, <code><a href="NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></code>, <code><a href="listener/ProfileLogger.html" title="class in org.apache.tools.ant.listener">ProfileLogger</a></code>, <code><a href="taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</a></code>, <code><a href="taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</a></code>, <code><a href="listener/SilentLogger.html" title="class in org.apache.tools.ant.listener">SilentLogger</a></code>, <code><a href="listener/SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a></code>, <code><a href="util/SplitClassLoader.html" title="class in org.apache.tools.ant.util">SplitClassLoader</a></code>, <code><a href="listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></code>, <code><a href="XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">BuildListener</span><span class="extends-implements">
extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></span></div>
<div class="block">Instances of classes that implement this interface can register
 to be notified when things happened during a build.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="BuildEvent.html" title="class in org.apache.tools.ant"><code>BuildEvent</code></a></li>
<li><a href="Project.html#addBuildListener(org.apache.tools.ant.BuildListener)"><code>Project.addBuildListener(BuildListener)</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that the last target has finished.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#buildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that a build has started.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals a message logging event.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#targetFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that a target has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that a target is starting.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#taskFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that a task has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Signals that a task is starting.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="buildStarted(org.apache.tools.ant.BuildEvent)">
<h3>buildStarted</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">buildStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a build has started. This event
 is fired before any targets have started.

 <p>This event is fired before the project instance is fully
 configured.  In particular no properties have been set and the
 project may not know its name or default target, yet.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that the last target has finished. This event
 will still be fired if an error occurred during the build.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a target is starting.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetFinished(org.apache.tools.ant.BuildEvent)">
<h3>targetFinished</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">targetFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a target has finished. This event will
 still be fired if an error occurred during the build.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a task is starting.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskFinished(org.apache.tools.ant.BuildEvent)">
<h3>taskFinished</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">taskFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals that a task has finished. This event will still
 be fired if an error occurred during the build.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Signals a message logging event.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
<li><a href="BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
