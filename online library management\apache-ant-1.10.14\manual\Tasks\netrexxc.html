<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>NetRexxC Task</title>
</head>

<body>

<h2 id="netrexxc">NetRexxC</h2>
<h3>Description</h3>
<p>Compiles a <a href="https://www.ibm.com/software/awdtools/netrexx/library.html"
target="_top">NetRexx</a> source tree within the running (Apache Ant) JVM.</p>
<p>The source and destination directory will be recursively scanned for NetRexx source files to
compile.  Only NetRexx files that have no corresponding class file or where the class file is older
than the java file will be compiled.</p>
<p>Files in the source tree are copied to the destination directory, allowing support files to be
located properly in the classpath. The source files are copied because the NetRexx compiler cannot
produce class files in a specific directory via parameters</p>
<p>The directory structure of the source tree should follow the package hierarchy.</p>
<p>It is possible to refine the set of files that are being compiled/copied.  This can be done with
the <var>includes</var>, <var>includesfile</var>, <var>excludes</var>, <var>excludesfile</var>
and <var>defaultexcludes</var> attributes. With the <var>includes</var> or <var>includesfile</var>
attribute you specify the files you want to have included by using patterns. The <var>exclude</var>
or <var>excludesfile</var> attribute is used to specify the files you want to have excluded.  This
is also done with patterns.  And finally with the <var>defaultexcludes</var> attribute, you can
specify whether you want to use default exclusions or not. See the section
on <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on how the
inclusion/exclusion of files works, and how to write patterns.</p>
<p>This task forms an implicit <a href="../Types/fileset.html">FileSet</a> and supports most
attributes of <code>&lt;fileset&gt;</code> (<var>dir</var> becomes <var>srcdir</var>) as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;patternset&gt;</code> elements.</p>
<p>All attributes except <var>classpath</var>, <var>srcdir</var> and <var>destDir</var> are also
available as properties in the form <code>ant.netrexxc.<i>attributename</i></code>,
eg.<br/><code class="code">&lt;property name="ant.netrexxc.verbose"
value="noverbose"/&gt;</code><br/> or from the command line as<br/><kbd>ant
-Dant.netrexxc.verbose=noverbose ...</kbd></p>
<p><strong>Note</strong>: This task depends on external libraries not included in the Apache Ant
distribution. See <a href="../install.html#librarydependencies">Library Dependencies</a> for more
information.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>binary</td>
    <td>Whether literals are treated as the Java binary type rather than the NetRexx types</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>The classpath to use during compilation</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comments</td>
    <td>Whether comments are passed through to the generated Java source file</td>
    <td>No</td>
  </tr>
  <tr>
    <td>compact</td>
    <td>Whether error messages come out in compact or verbose format.</td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>compile</td>
    <td>Whether the NetRexx compiler should compile the generated Java code</td>
    <td>No</td>
  </tr>
  <tr>
    <td>console</td>
    <td>Whether or not messages should be displayed on the 'console'. Note that this task will rely
      on the default value for filtering compile messages.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>crossref</td>
    <td>Whether variable cross references are generated</td>
    <td>No</td>
  </tr>
  <tr>
    <td>decimal</td>
    <td>Whether decimal arithmetic should be used for the NetRexx code. Setting this to <q>off</q>
      will report decimal arithmetic as an error, for performance critical applications.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>Whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td>No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>destDir</td>
    <td>the destination directory into which the NetRexx source files should be copied and then
      compiled</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>diag</td>
    <td>Whether diagnostic information about the compile is generated</td>
    <td>No</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>Comma- or space-separated list of patterns of files that must be excluded.</td>
    <td>No; defaults to default excludes or none if <var>defaultexcludes</var> is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>Name of a file. Each line of this file is taken to be an exclude pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>explicit</td>
    <td>Whether variables must be declared explicitly before use</td>
    <td>No</td>
  </tr>
  <tr>
    <td>format</td>
    <td>Whether the generated Java code is formatted nicely or left to match NetRexx line numbers
      for call stack debugging</td>
    <td>No</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>Comma- or space-separated list of patterns of files that must be included.</td>
    <td>No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>Name of a file. Each line of this file is taken to be an include pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>java</td>
    <td>Whether the generated Java code is produced</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keep</td>
    <td>Sets whether the generated java source file should be kept after compilation. The generated
      files will have an extension
      of <samp>.java.keep</samp>, <strong>not</strong> <samp>.java</samp>. Use <q>removeKeepExtension</q>
      to change that.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>logo</td>
    <td>Whether the compiler text logo is displayed when compiling</td>
    <td>No</td>
  </tr>
  <tr>
    <td>removeKeepExtension</td>
    <td>Tells whether the trailing <samp>.keep</samp> in <var>compile</var>=<q>false</q> mode should
      be removed so that the resulting Java source file really ends on <samp>.java</samp>. This
      facilitates the use of the <kbd>javadoc</kbd> tool later on.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Whether the generated <samp>.java</samp> files should be replaced when compiling</td>
    <td>No</td>
  </tr>
  <tr>
    <td>savelog</td>
    <td>Whether the compiler messages will be written to <samp>NetRexxC.log</samp> as well as to the
      console</td>
    <td>No</td>
  </tr>
  <tr>
    <td>sourcedir</td>
    <td>Tells the NetRexx compiler to store the class files in the same directory as the source
      files. The alternative is the working directory</td>
    <td>No</td>
  </tr>
  <tr>
    <td>srcDir</td>
    <td>Sets the directory to find the source NetRexx files in</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>strictargs</td>
    <td>Tells the NetRexx compiler that method calls always need parentheses, even if no arguments
      are needed, e.g. <code>aStringVar.getBytes</code> vs. <code>aStringVar.getBytes()</code></td>
    <td>No</td>
  </tr>
  <tr>
    <td>strictassign</td>
    <td>Tells the NetRexx compile that assignments must match exactly on type</td>
    <td>No</td>
  </tr>
  <tr>
    <td>strictcase</td>
    <td>Specifies whether the NetRexx compiler should be case sensitive or not</td>
    <td>No</td>
  </tr>
  <tr>
    <td>strictimport</td>
    <td>Whether classes need to be imported explicitly using an <code>import</code> statement</td>
    <td>No; by default the NetRexx compiler will import certain packages automatically</td>
  </tr>
  <tr>
    <td>strictprops</td>
    <td>Whether local properties need to be qualified explicitly using <code>this</code></td>
    <td>No</td>
  </tr>
  <tr>
    <td>strictsignal</td>
    <td>Whether the compiler should force catching of exceptions by explicitly named types</td>
    <td>No</td>
  </tr>
  <tr>
    <td>symbols</td>
    <td>Whether debug symbols should be generated into the class file</td>
    <td>No</td>
  </tr>
  <tr>
    <td>time</td>
    <td>Asks the NetRexx compiler to print compilation times to the console</td>
    <td>No</td>
  </tr>
  <tr>
    <td>trace</td>
    <td>Turns on or off tracing and directs the resultant trace output</td>
    <td>No</td>
  </tr>
  <tr>
    <td>utf8</td>
    <td>Tells the NetRexx compiler that the source is in UTF8</td>
    <td>No</td>
  </tr>
  <tr>
    <td>verbose</td>
    <td>Tells whether lots of warnings and error messages should be generated</td>
    <td>No</td>
  </tr>
  <tr>
    <td>suppressMethodArgumentNotUsed</td>
    <td>Tells whether we should filter out the <q>Method argument not used</q> messages
      in <var>strictargs</var> mode.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>suppressPrivatePropertyNotUsed</td>
    <td>Tells whether we should filter out the <q>Private Property defined, but not used</q>
      messages in <var>strictargs</var> mode.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>suppressVariableNotUsed</td>
    <td>Tells whether we should filter out the <q>Variable set but not used</q> messages
      in <var>strictargs</var> mode.  Please be careful with this one, as you can hide errors behind
      it!</td>
    <td>No</td>
  </tr>
  <tr>
    <td>suppressExceptionNotSignalled</td>
    <td>Tells whether we should filter out the <q>Exception is declared, but not signalled within
      the method</q> messages in <var>strictsignal</var> mode.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>suppressDeprecation</td>
    <td>Tells whether we should filter out any deprecation messages of the compiler output</td>
    <td>No</td>
  </tr>
</table>
<h3>Examples</h3>

<pre>
&lt;netrexxc srcDir=&quot;/source/project&quot;
          includes=&quot;vnr/util/*&quot;
          destDir=&quot;/source/project/build&quot;
          classpath=&quot;/source/project2/proj.jar&quot;
          comments=&quot;true&quot;
          crossref=&quot;false&quot; replace=&quot;true&quot;
          keep=&quot;true&quot;/&gt;</pre>

</body>
</html>
