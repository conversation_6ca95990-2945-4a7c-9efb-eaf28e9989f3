<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>XMLResultAggregator (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: XMLResultAggregator">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class XMLResultAggregator" class="title">Class XMLResultAggregator</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">XMLResultAggregator</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>
implements <a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></span></div>
<div class="block">Aggregates all &lt;junit&gt; XML formatter testsuite data under
 a specific directory and transforms the results via XSLT.
 It is not particularly clean but
 should be helpful while I am thinking about another technique.

 <p>The main problem is due to the fact that a JVM can be forked for a testcase
 thus making it impossible to aggregate all testcases since the listener is
 (obviously) in the forked JVM. A solution could be to write a
 TestListener that will receive events from the TestRunner via sockets. This
 is IMHO the simplest way to do it to avoid this file hacking thing.</p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_DIR" class="member-name-link">DEFAULT_DIR</a></code></div>
<div class="col-last even-row-color">
<div class="block">The default directory: <code>&#046;</code>.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_FILENAME" class="member-name-link">DEFAULT_FILENAME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the default file name: <code>TESTS-TestSuites.xml</code></div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#filesets" class="member-name-link">filesets</a></code></div>
<div class="col-last even-row-color">
<div class="block">the list of all filesets, that should contains the xml to aggregate</div>
</div>
<div class="col-first odd-row-color"><code>protected int</code></div>
<div class="col-second odd-row-color"><code><a href="#generatedId" class="member-name-link">generatedId</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the current generated id</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#toDir" class="member-name-link">toDir</a></code></div>
<div class="col-last even-row-color">
<div class="block">the directory to write the file to</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#toFile" class="member-name-link">toFile</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the name of the result file</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#transformers" class="member-name-link">transformers</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junit.XMLConstants">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.junit.<a href="XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</a></h3>
<code><a href="XMLConstants.html#ATTR_CLASSNAME">ATTR_CLASSNAME</a>, <a href="XMLConstants.html#ATTR_ERRORS">ATTR_ERRORS</a>, <a href="XMLConstants.html#ATTR_FAILURES">ATTR_FAILURES</a>, <a href="XMLConstants.html#ATTR_ID">ATTR_ID</a>, <a href="XMLConstants.html#ATTR_MESSAGE">ATTR_MESSAGE</a>, <a href="XMLConstants.html#ATTR_NAME">ATTR_NAME</a>, <a href="XMLConstants.html#ATTR_PACKAGE">ATTR_PACKAGE</a>, <a href="XMLConstants.html#ATTR_SKIPPED">ATTR_SKIPPED</a>, <a href="XMLConstants.html#ATTR_TESTS">ATTR_TESTS</a>, <a href="XMLConstants.html#ATTR_TIME">ATTR_TIME</a>, <a href="XMLConstants.html#ATTR_TYPE">ATTR_TYPE</a>, <a href="XMLConstants.html#ATTR_VALUE">ATTR_VALUE</a>, <a href="XMLConstants.html#ERROR">ERROR</a>, <a href="XMLConstants.html#FAILURE">FAILURE</a>, <a href="XMLConstants.html#HOSTNAME">HOSTNAME</a>, <a href="XMLConstants.html#PROPERTIES">PROPERTIES</a>, <a href="XMLConstants.html#PROPERTY">PROPERTY</a>, <a href="XMLConstants.html#SYSTEM_ERR">SYSTEM_ERR</a>, <a href="XMLConstants.html#SYSTEM_OUT">SYSTEM_OUT</a>, <a href="XMLConstants.html#TESTCASE">TESTCASE</a>, <a href="XMLConstants.html#TESTSUITE">TESTSUITE</a>, <a href="XMLConstants.html#TESTSUITES">TESTSUITES</a>, <a href="XMLConstants.html#TIMESTAMP">TIMESTAMP</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">XMLResultAggregator</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileSet(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileSet</a><wbr>(<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new fileset containing the XML results to aggregate</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTestSuite(org.w3c.dom.Element,org.w3c.dom.Element)" class="member-name-link">addTestSuite</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a>&nbsp;testsuite)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new testsuite node to the document.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDocument()" class="member-name-link">createDocument</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a DOM tree.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createReport()" class="member-name-link">createReport</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate a report based on the document created by the merge.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Aggregate all testsuites into a single document and write it to the
 specified directory and file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestinationFile()" class="member-name-link">getDestinationFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the full destination file where to write the result.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFiles()" class="member-name-link">getFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get all <code>.xml</code> files in the fileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTodir(java.io.File)" class="member-name-link">setTodir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory where the results should be written.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTofile(java.lang.String)" class="member-name-link">setTofile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the aggregated results file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeDOMTree(org.w3c.dom.Document,java.io.File)" class="member-name-link">writeDOMTree</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Document.html" title="class or interface in org.w3c.dom" class="external-link">Document</a>&nbsp;doc,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write the DOM tree to a file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="filesets">
<h3>filesets</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&gt;</span>&nbsp;<span class="element-name">filesets</span></div>
<div class="block">the list of all filesets, that should contains the xml to aggregate</div>
</section>
</li>
<li>
<section class="detail" id="toFile">
<h3>toFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toFile</span></div>
<div class="block">the name of the result file</div>
</section>
</li>
<li>
<section class="detail" id="toDir">
<h3>toDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">toDir</span></div>
<div class="block">the directory to write the file to</div>
</section>
</li>
<li>
<section class="detail" id="transformers">
<h3>transformers</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a>&gt;</span>&nbsp;<span class="element-name">transformers</span></div>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_DIR">
<h3>DEFAULT_DIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_DIR</span></div>
<div class="block">The default directory: <code>&#046;</code>. It is resolved from the project directory</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator.DEFAULT_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_FILENAME">
<h3>DEFAULT_FILENAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_FILENAME</span></div>
<div class="block">the default file name: <code>TESTS-TestSuites.xml</code></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator.DEFAULT_FILENAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generatedId">
<h3>generatedId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">generatedId</span></div>
<div class="block">the current generated id</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>XMLResultAggregator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">XMLResultAggregator</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createReport()">
<h3>createReport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</a></span>&nbsp;<span class="element-name">createReport</span>()</div>
<div class="block">Generate a report based on the document created by the merge.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the report</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTofile(java.lang.String)">
<h3>setTofile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTofile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set the name of the aggregated results file. It must be relative
 from the <code>todir</code> attribute. If not set it will use <a href="#DEFAULT_FILENAME"><code>DEFAULT_FILENAME</code></a></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the name of the file.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setTodir(java.io.File)"><code>setTodir(File)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTodir(java.io.File)">
<h3>setTodir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTodir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;value)</span></div>
<div class="block">Set the destination directory where the results should be written. If not
 set if will use <a href="#DEFAULT_DIR"><code>DEFAULT_DIR</code></a>. When given a relative directory
 it will resolve it from the project directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the directory where to write the results, absolute or
 relative.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileSet(org.apache.tools.ant.types.FileSet)">
<h3>addFileSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileSet</span><wbr><span class="parameters">(<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</span></div>
<div class="block">Add a new fileset containing the XML results to aggregate</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fs</code> - the new fileset of xml results.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Aggregate all testsuites into a single document and write it to the
 specified directory and file.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown if there is a serious error while writing
          the document.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDestinationFile()">
<h3>getDestinationFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestinationFile</span>()</div>
<div class="block">Get the full destination file where to write the result. It is made of
 the <code>todir</code> and <code>tofile</code> attributes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the destination file where should be written the result file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFiles()">
<h3>getFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</span>&nbsp;<span class="element-name">getFiles</span>()</div>
<div class="block">Get all <code>.xml</code> files in the fileset.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>all files in the fileset that end with a '.xml'.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeDOMTree(org.w3c.dom.Document,java.io.File)">
<h3>writeDOMTree</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeDOMTree</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Document.html" title="class or interface in org.w3c.dom" class="external-link">Document</a>&nbsp;doc,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write the DOM tree to a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>doc</code> - the XML document to dump to disk.</dd>
<dd><code>file</code> - the filename to write the document to. Should obviously be a .xml file.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - thrown if there is an error while writing the content.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createDocument()">
<h3>createDocument</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a></span>&nbsp;<span class="element-name">createDocument</span>()</div>
<div class="block">Create a DOM tree.
 Has 'testsuites' as firstchild and aggregates all
 testsuite results that exists in the base directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the root element of DOM tree that aggregates all testsuites.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTestSuite(org.w3c.dom.Element,org.w3c.dom.Element)">
<h3>addTestSuite</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTestSuite</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a>&nbsp;root,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Element.html" title="class or interface in org.w3c.dom" class="external-link">Element</a>&nbsp;testsuite)</span></div>
<div class="block"><p>Add a new testsuite node to the document.
 The main difference is that it
 split the previous fully qualified name into a package and a name.</p>
 <p>For example: <code>org.apache.Whatever</code> will be split into
 <code>org.apache</code> and <code>Whatever</code>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>root</code> - the root element to which the <code>testsuite</code> node should
        be appended.</dd>
<dd><code>testsuite</code> - the element to append to the given root. It will slightly
        modify the original node to change the name attribute and add
        a package one.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
