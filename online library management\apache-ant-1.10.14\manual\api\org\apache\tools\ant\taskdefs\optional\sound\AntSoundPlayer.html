<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>AntSoundPlayer (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.sound, class: AntSoundPlayer">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.sound</a></div>
<h1 title="Class AntSoundPlayer" class="title">Class AntSoundPlayer</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.sound.AntSoundPlayer</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineListener.html" title="class or interface in javax.sound.sampled" class="external-link">LineListener</a></code>, <code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AntSoundPlayer</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineListener.html" title="class or interface in javax.sound.sampled" class="external-link">LineListener</a>, <a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></span></div>
<div class="block">This class is designed to be used by any AntTask that requires audio output.

 It implements the BuildListener interface to listen for BuildEvents
 and could be easily extended to provide audio output upon any
 specific build events occurring.

 I have only tested this with .WAV and .AIFF sound file formats. Both seem to work fine.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">AntSoundPlayer</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBuildFailedSound(java.io.File,int,java.lang.Long)" class="member-name-link">addBuildFailedSound</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fileFail,
 int&nbsp;loopsFail,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;durationFail)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBuildSuccessfulSound(java.io.File,int,java.lang.Long)" class="member-name-link">addBuildSuccessfulSound</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 int&nbsp;loops,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;duration)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired after the last target has finished.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired before any targets are started.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired whenever a message is logged.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired when a target has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired when a target is started.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskFinished</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired when a task has finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fired when a task is started.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#update(javax.sound.sampled.LineEvent)" class="member-name-link">update</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineEvent.html" title="class or interface in javax.sound.sampled" class="external-link">LineEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is implemented to listen for any line events and closes the
 clip if required.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>AntSoundPlayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntSoundPlayer</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addBuildSuccessfulSound(java.io.File,int,java.lang.Long)">
<h3>addBuildSuccessfulSound</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBuildSuccessfulSound</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 int&nbsp;loops,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;duration)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the location of the audio file to be played when the
        build is successful</dd>
<dd><code>loops</code> - the number of times the file should be played when
        the build is successful</dd>
<dd><code>duration</code> - the number of milliseconds the file should be
        played when the build is successful</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addBuildFailedSound(java.io.File,int,java.lang.Long)">
<h3>addBuildFailedSound</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBuildFailedSound</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fileFail,
 int&nbsp;loopsFail,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;durationFail)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileFail</code> - the location of the audio file to be played
        when the build fails</dd>
<dd><code>loopsFail</code> - the number of times the file should be played
        when the build is fails</dd>
<dd><code>durationFail</code> - the number of milliseconds the file should be
        played when the build fails</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="update(javax.sound.sampled.LineEvent)">
<h3>update</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">update</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineEvent.html" title="class or interface in javax.sound.sampled" class="external-link">LineEvent</a>&nbsp;event)</span></div>
<div class="block">This is implemented to listen for any line events and closes the
 clip if required.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineListener.html#update(javax.sound.sampled.LineEvent)" title="class or interface in javax.sound.sampled" class="external-link">update</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/javax/sound/sampled/LineListener.html" title="class or interface in javax.sound.sampled" class="external-link">LineListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the line event to follow</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildStarted(org.apache.tools.ant.BuildEvent)">
<h3>buildStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired before any targets are started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - ignored</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired after the last target has finished. This event
  will still be thrown if an error occurred during the build.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the build finished event.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired when a target is started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetFinished(org.apache.tools.ant.BuildEvent)">
<h3>targetFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired when a target has finished. This event will
  still be thrown if an error occurred during the build.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired when a task is started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskFinished(org.apache.tools.ant.BuildEvent)">
<h3>taskFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskFinished</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired when a task has finished. This event will still
  be throw if an error occurred during the build.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="../../../BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Fired whenever a message is logged.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in interface&nbsp;<code><a href="../../../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the build event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="../../../BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
