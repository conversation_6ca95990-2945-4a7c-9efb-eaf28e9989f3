<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>WLRmic (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.rmic, class: WLRmic">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.rmic</a></div>
<h1 title="Class WLRmic" class="title">Class WLRmic</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.rmic.WLRmic</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">WLRmic</span>
<span class="extends-implements">extends <a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></span></div>
<div class="block">The implementation of the rmic for WebLogic</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMPILER_NAME" class="member-name-link">COMPILER_NAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">the name of this adapter for users to select</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NO_WLRMIC_ON_CLASSPATH" class="member-name-link">ERROR_NO_WLRMIC_ON_CLASSPATH</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The error string to use if not able to find the weblogic rmic</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_WLRMIC_FAILED" class="member-name-link">ERROR_WLRMIC_FAILED</a></code></div>
<div class="col-last even-row-color">
<div class="block">The error string to use if not able to start the weblogic rmic</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#UNSUPPORTED_STUB_OPTION" class="member-name-link">UNSUPPORTED_STUB_OPTION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">unsupported error message</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#WL_RMI_SKEL_SUFFIX" class="member-name-link">WL_RMI_SKEL_SUFFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">The skeleton suffix</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WL_RMI_STUB_SUFFIX" class="member-name-link">WL_RMI_STUB_SUFFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The stub suffix</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#WLRMIC_CLASSNAME" class="member-name-link">WLRMIC_CLASSNAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">The classname of the weblogic rmic</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.rmic.<a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></h3>
<code><a href="DefaultRmicAdapter.html#RMI_SKEL_SUFFIX">RMI_SKEL_SUFFIX</a>, <a href="DefaultRmicAdapter.html#RMI_STUB_SUFFIX">RMI_STUB_SUFFIX</a>, <a href="DefaultRmicAdapter.html#RMI_TIE_SUFFIX">RMI_TIE_SUFFIX</a>, <a href="DefaultRmicAdapter.html#STUB_1_1">STUB_1_1</a>, <a href="DefaultRmicAdapter.html#STUB_1_2">STUB_1_2</a>, <a href="DefaultRmicAdapter.html#STUB_COMPAT">STUB_COMPAT</a>, <a href="DefaultRmicAdapter.html#STUB_OPTION_1_1">STUB_OPTION_1_1</a>, <a href="DefaultRmicAdapter.html#STUB_OPTION_1_2">STUB_OPTION_1_2</a>, <a href="DefaultRmicAdapter.html#STUB_OPTION_COMPAT">STUB_OPTION_COMPAT</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">WLRmic</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addStubVersionOptions()" class="member-name-link">addStubVersionOptions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point; no stub version is returned.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#areIiopAndIdlSupported()" class="member-name-link">areIiopAndIdlSupported</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the iiop and idl switches are supported.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Carry out the rmic compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSkelClassSuffix()" class="member-name-link">getSkelClassSuffix</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the suffix for the rmic skeleton classes</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStubClassSuffix()" class="member-name-link">getStubClassSuffix</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the suffix for the rmic stub classes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#preprocessCompilerArgs(java.lang.String%5B%5D)" class="member-name-link">preprocessCompilerArgs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Strip out all -J args from the command list.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.rmic.<a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></h3>
<code><a href="DefaultRmicAdapter.html#filterJvmCompilerArgs(java.lang.String%5B%5D)">filterJvmCompilerArgs</a>, <a href="DefaultRmicAdapter.html#getClasspath()">getClasspath</a>, <a href="DefaultRmicAdapter.html#getCompileClasspath()">getCompileClasspath</a>, <a href="DefaultRmicAdapter.html#getMapper()">getMapper</a>, <a href="DefaultRmicAdapter.html#getRmic()">getRmic</a>, <a href="DefaultRmicAdapter.html#getTieClassSuffix()">getTieClassSuffix</a>, <a href="DefaultRmicAdapter.html#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">logAndAddFilesToCompile</a>, <a href="DefaultRmicAdapter.html#setRmic(org.apache.tools.ant.taskdefs.Rmic)">setRmic</a>, <a href="DefaultRmicAdapter.html#setupRmicCommand()">setupRmicCommand</a>, <a href="DefaultRmicAdapter.html#setupRmicCommand(java.lang.String%5B%5D)">setupRmicCommand</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="WLRMIC_CLASSNAME">
<h3>WLRMIC_CLASSNAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WLRMIC_CLASSNAME</span></div>
<div class="block">The classname of the weblogic rmic</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WLRMIC_CLASSNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="COMPILER_NAME">
<h3>COMPILER_NAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMPILER_NAME</span></div>
<div class="block">the name of this adapter for users to select</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.COMPILER_NAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_WLRMIC_ON_CLASSPATH">
<h3>ERROR_NO_WLRMIC_ON_CLASSPATH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_WLRMIC_ON_CLASSPATH</span></div>
<div class="block">The error string to use if not able to find the weblogic rmic</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_NO_WLRMIC_ON_CLASSPATH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_WLRMIC_FAILED">
<h3>ERROR_WLRMIC_FAILED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_WLRMIC_FAILED</span></div>
<div class="block">The error string to use if not able to start the weblogic rmic</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_WLRMIC_FAILED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="WL_RMI_STUB_SUFFIX">
<h3>WL_RMI_STUB_SUFFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WL_RMI_STUB_SUFFIX</span></div>
<div class="block">The stub suffix</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_STUB_SUFFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="WL_RMI_SKEL_SUFFIX">
<h3>WL_RMI_SKEL_SUFFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WL_RMI_SKEL_SUFFIX</span></div>
<div class="block">The skeleton suffix</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_SKEL_SUFFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="UNSUPPORTED_STUB_OPTION">
<h3>UNSUPPORTED_STUB_OPTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">UNSUPPORTED_STUB_OPTION</span></div>
<div class="block">unsupported error message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.UNSUPPORTED_STUB_OPTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>WLRmic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WLRmic</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="areIiopAndIdlSupported()">
<h3>areIiopAndIdlSupported</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">areIiopAndIdlSupported</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="DefaultRmicAdapter.html#areIiopAndIdlSupported()">DefaultRmicAdapter</a></code></span></div>
<div class="block">Whether the iiop and idl switches are supported.

 <p>This implementation returns false if running on Java 11
 onwards and true otherwise.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DefaultRmicAdapter.html#areIiopAndIdlSupported()">areIiopAndIdlSupported</a></code>&nbsp;in class&nbsp;<code><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>true if the iiop and idl switches are supported</dd>
<dt>Since:</dt>
<dd>Ant 1.10.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">execute</span>()
                throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Carry out the rmic compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the compilation succeeded</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStubClassSuffix()">
<h3>getStubClassSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStubClassSuffix</span>()</div>
<div class="block">Get the suffix for the rmic stub classes</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DefaultRmicAdapter.html#getStubClassSuffix()">getStubClassSuffix</a></code>&nbsp;in class&nbsp;<code><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>the stub suffix</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSkelClassSuffix()">
<h3>getSkelClassSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSkelClassSuffix</span>()</div>
<div class="block">Get the suffix for the rmic skeleton classes</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DefaultRmicAdapter.html#getSkelClassSuffix()">getSkelClassSuffix</a></code>&nbsp;in class&nbsp;<code><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>the skeleton suffix</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="preprocessCompilerArgs(java.lang.String[])">
<h3>preprocessCompilerArgs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">preprocessCompilerArgs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</span></div>
<div class="block">Strip out all -J args from the command list.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DefaultRmicAdapter.html#preprocessCompilerArgs(java.lang.String%5B%5D)">preprocessCompilerArgs</a></code>&nbsp;in class&nbsp;<code><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>compilerArgs</code> - the original compiler arguments</dd>
<dt>Returns:</dt>
<dd>the filtered set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addStubVersionOptions()">
<h3>addStubVersionOptions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">addStubVersionOptions</span>()</div>
<div class="block">This is an override point; no stub version is returned. If any
 stub option is set, a warning is printed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DefaultRmicAdapter.html#addStubVersionOptions()">addStubVersionOptions</a></code>&nbsp;in class&nbsp;<code><a href="DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>null, for no stub version</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
