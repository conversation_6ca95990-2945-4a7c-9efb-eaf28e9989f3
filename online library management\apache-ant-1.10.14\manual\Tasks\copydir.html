<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Copydir Task</title>
</head>

<body>

<h2 id="copydir">Copydir</h2>
<h3><em><u>Deprecated</u></em></h3>
<p><em>This task has been <u>deprecated</u>.  Use the <code>Copy</code> task instead.</em></p>
<h3>Description</h3>
<p>Copies a directory tree from the source to the destination.</p>
<p>It is possible to refine the set of files that are being copied. This can be done with
the <var>includes</var>, <var>includesfile</var>, <var>excludes</var>, <var>excludesfile</var>
and <var>defaultexcludes</var> attributes. With the <var>includes</var> or <var>includesfile</var>
attribute you specify the files you want to have included by using patterns. The <var>exclude</var>
or <var>excludesfile</var> attribute is used to specify the files you want to have excluded. This is
also done with patterns. And finally with the <var>defaultexcludes</var> attribute, you can specify
whether you want to use default exclusions or not. See the section
on <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on how the
inclusion/exclusion of files works, and how to write patterns.</p>
<p>This task forms an implicit <a href="../Types/fileset.html">FileSet</a> and supports most
attributes of <code>&lt;fileset&gt;</code> (<var>dir</var> becomes <var>src</var>) as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>
and <code>&lt;patternset&gt;</code> elements.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>src</td>
    <td>the directory to copy.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>dest</td>
    <td>the directory to copy to.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>comma- or space-separated list of patterns of files that must be included.</td>
    <td>No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>name of a file. Each line of this file is taken to be an include pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>comma- or space-separated list of patterns of files that must be excluded.</td>
    <td>No; defaults to default excludes or none if <var>defaultexcludes</var> is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>name of a file. Each line of this file is taken to be an exclude pattern</td>
    <td>No</td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>indicates whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td>No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>filtering</td>
    <td>indicates whether token filtering should take place during the copy</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>flatten</td>
    <td>ignore directory structure of source directory, copy all files into a single directory,
      specified by the <code>dest</code> attribute.</td>
    <td>No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>forceoverwrite</td>
    <td>overwrite existing files even if the destination files are newer.</td>
    <td>No; default is <q>false</q></td>
  </tr>
</table>
<h3>Examples</h3>
<p>Copy the directory <samp>${src}/resources</samp> to <samp>${dist}</samp>.</p>
<pre>
&lt;copydir src=&quot;${src}/resources&quot;
         dest=&quot;${dist}&quot;/&gt;</pre>

<p>Copy the directory <samp>${src}/resources</samp> to <samp>${dist}</samp>
recursively. All <samp>.java</samp> files are copied, except for files with the
name <samp>Test.java</samp>.</p>
<pre>
&lt;copydir src=&quot;${src}/resources&quot;
         dest=&quot;${dist}&quot;
         includes=&quot;**/*.java&quot;
         excludes=&quot;**/Test.java&quot;/&gt;</pre>

<p>Copy the directory <samp>${src}/resources</samp> to <samp>${dist}</samp>
recursively. All <samp>.java</samp> files are copied, except for the files under
the <samp>mypackage/test</samp> directory.</p>
<pre>
&lt;copydir src=&quot;${src}/resources&quot;
         dest=&quot;${dist}&quot;
         includes=&quot;**/*.java&quot;
         excludes=&quot;mypackage/test/**&quot;/&gt;</pre>

</body>
</html>
