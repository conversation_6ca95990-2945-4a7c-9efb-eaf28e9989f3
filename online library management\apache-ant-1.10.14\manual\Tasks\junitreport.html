<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JUnitReport Task</title>
</head>

<body>

<h2 id="junitreport">JUnitReport</h2>
<h3>Description</h3>
<p>Merge the individual XML files generated by the <code>JUnit</code> task and eventually apply a
stylesheet on the resulting merged document to provide a browsable report of the testcases
results.</p>

<p><strong>Note</strong>: This task depends on external libraries not included in the Apache Ant
distribution. See <a href="../install.html#librarydependencies">Library Dependencies</a> for more
information.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>tofile</td>
    <td>The name of the XML file that will aggregate all individual XML testsuites previously
      generated by the <code>JUnit</code> task.</td>
    <td>No; defaults to <samp>TESTS-TestSuites.xml</samp></td>
  </tr>
  <tr>
    <td>todir</td>
    <td>The directory where the file resulting from the individual XML testsuite aggregation should
      be written.</td>
    <td>No; defaults to current directory</td>
  </tr>
</table>
<h3 id="nested">Parameters specified as nested elements</h3>
<h4>fileset</h4>
<p><code>junitreport</code> collects individual xml files generated by the <code>JUnit</code> task
using the nested <a href="../Types/fileset.html"><code>&lt;FileSet&gt;</code></a> element.</p>
<h4>report</h4>
<p>Generate a browsable report based on the document created by the merge.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>format</td>
    <td>The format of the generated report. Must be either <q>noframes</q> or <q>frames</q>.</td>
    <td>No; defaults to <q>frames</q></td>
  </tr>
  <tr>
    <td>styledir</td>
    <td>The directory where the stylesheets are defined. They must be conforming to the following
      conventions:
      <ul>
        <li><q>frames</q> format: the stylesheet must be named <samp>junit-frames.xsl</samp>,
          or <samp>junit-frames-saxon.xsl</samp> if you are using Saxon 9+.</li>
        <li><q>noframes</q> format: the stylesheet must be named <samp>junit-noframes.xsl</samp>,
          or <samp>junit-noframes-saxon.xsl</samp> if you are using Saxon 9+.</li>
      </ul>
    </td>
    <td>No; defaults to embedded stylesheets</td>
  </tr>
  <tr>
    <td>todir</td>
    <td>The directory where the files resulting from the transformation should be written to.</td>
    <td>No; defaults to current directory</td>
  </tr>
</table>
<p>Ant assumes the following concerning the <q>frames</q> and <q>noframes</q> formats:</p>
<ul>
  <li>The <q>frames</q> format uses a stylesheet which is generating output <em>only</em> by
    redirecting.</li>
  <li>The <q>noframes</q> format does not use redirecting and generates one file
    called <samp>junit-noframes.html</samp>.</li>
</ul>
<p>Custom versions of <samp>junit-frames.xsl</samp> or <samp>junit-noframes.xsl</samp> must adhere
to the above conventions.</p>

<h3>Nested element of the report tag</h3>
<h4>param</h4>
<p><em>Since Ant 1.7</em>, the <var>report</var> tag supports nested <code>param</code> tags.  These
tags can pass XSL parameters to the stylesheet.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>Name of the XSL parameter</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>expression</td>
    <td>Text value to be placed into the <code>param</code>.<br/>Was originally intended to be an
      XSL expression.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>if</td>
    <td>The <code>param</code> will only be passed <a href="../properties.html#if+unless">if this
      property is set</a>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>unless</td>
    <td>The <code>param</code> will not be passed <a href="../properties.html#if+unless">if this
      property is set</a>.</td>
    <td>No</td>
  </tr>
</table>

<p>The built-in stylesheets support the following parameters:</p>
<table>
  <tr>
    <th scope="col">XSL Parameter</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>TITLE</td>
    <td>Title used in <code>&lt;title&gt;</code> and <code>&lt;h1&gt;</code> tags</td>
    <td>No; defaults to <q>Unit Test Results</q></td>
  </tr>
</table>

<h4>classpath</h4>
<p><em>Since Ant 1.9.5</em>. Like for the <a href="../Tasks/style.html#classpath">XSLT task</a>, a
nested <code>&lt;classpath&gt;</code> will be used to load the processor.</p>

<h4>factory</h4>
<p><em>Since Ant 1.9.5</em>. Like for the <a href="../Tasks/style.html#factory">XSLT task</a>, a
nested <code>&lt;factory&gt;</code> can be used to specify factory settings.</p>

<h3>Examples</h3>

<p>Generate a <samp>TESTS-TestSuites.xml</samp> file in the directory <samp>reports</samp> and
generate the default framed report in the directory <samp>report/html</samp>.</p>

<pre>
&lt;junitreport todir=&quot;./reports&quot;&gt;
    &lt;fileset dir=&quot;./reports&quot;&gt;
        &lt;include name=&quot;TEST-*.xml&quot;/&gt;
    &lt;/fileset&gt;
    &lt;report format=&quot;frames&quot; todir=&quot;./report/html&quot;/&gt;
&lt;/junitreport&gt;</pre>

<p>This example requires a file called <samp>junitreport/junit-frames.xsl</samp>. The XSL
parameters <q>key1</q> and <q>key2</q> will be passed to the XSL transformation.</p>

<pre>
&lt;junitreport todir="${outputdir}"&gt;
    &lt;fileset dir="${jrdir}"&gt;
        &lt;include name="TEST-*.xml"/&gt;
    &lt;/fileset&gt;
    &lt;report todir="${outputdir}/html"
            styledir="junitreport"
            format="frames"&gt;
        &lt;param name="key1" expression="value1"/&gt;
        &lt;param name="key2" expression="value2"/&gt;
    &lt;/report&gt;
&lt;/junitreport&gt;</pre>

</body>
</html>
