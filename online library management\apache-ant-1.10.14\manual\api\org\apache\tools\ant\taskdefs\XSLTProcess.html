<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>XSLTProcess (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: XSLTProcess">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class XSLTProcess" class="title">Class XSLTProcess</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.XSLTProcess</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">XSLTProcess</span>
<span class="extends-implements">extends <a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a>
implements <a href="XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></span></div>
<div class="block">Processes a set of XML documents via XSLT. This is
 useful for building views of XML based documentation.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="XSLTProcess.Factory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></code></div>
<div class="col-last even-row-color">
<div class="block">The factory element to configure a transformer factory</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="XSLTProcess.OutputProperty.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Specify how the result tree should be output as specified
 in the <a href="https://www.w3.org/TR/xslt#output">
 specification</a>.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="XSLTProcess.Param.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></code></div>
<div class="col-last even-row-color">
<div class="block">The Param inner class used to store XSL parameters</div>
</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="XSLTProcess.ParamType.html" class="type-name-link" title="enum class in org.apache.tools.ant.taskdefs">XSLTProcess.ParamType</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enum for types of the parameter expression.</div>
</div>
<div class="col-first even-row-color"><code>final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="XSLTProcess.TraceConfiguration.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></code></div>
<div class="col-last even-row-color">
<div class="block">Configuration for Xalan2 traces.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROCESSOR_TRAX" class="member-name-link">PROCESSOR_TRAX</a></code></div>
<div class="col-last even-row-color">
<div class="block">The default processor is trax</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">XSLTProcess</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">add</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a collection of resources to style in addition to the
 given file or the implicit fileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.util.FileNameMapper)" class="member-name-link">add</a><wbr>(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a nested filenamemapper.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredStyle(org.apache.tools.ant.types.resources.Resources)" class="member-name-link">addConfiguredStyle</a><wbr>(<a href="../types/resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</a>&nbsp;rc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested &lt;style&gt; element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)" class="member-name-link">addConfiguredXMLCatalog</a><wbr>(<a href="../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a>&nbsp;xmlCatalog)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the catalog to our internal catalog</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addMapper(org.apache.tools.ant.types.Mapper)" class="member-name-link">addMapper</a><wbr>(<a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;mapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Defines the mapper to map source to destination files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSysproperty(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addSysproperty</a><wbr>(<a href="../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A system property to set during transformation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSyspropertyset(org.apache.tools.ant.types.PropertySet)" class="member-name-link">addSyspropertyset</a><wbr>(<a href="../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;sysp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A set of system properties to set during transformation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#configureLiaison(java.io.File)" class="member-name-link">configureLiaison</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;stylesheet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.7</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configureLiaison(org.apache.tools.ant.types.Resource)" class="member-name-link">configureLiaison</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;stylesheet)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads the stylesheet and set xsl:param parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the optional classpath to the XSL processor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createFactory()" class="member-name-link">createFactory</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the factory element to configure a trax liaison.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createOutputProperty()" class="member-name-link">createOutputProperty</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of an output property to be configured.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createParam()" class="member-name-link">createParam</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of an XSL parameter for configuration by Ant.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.TraceConfiguration.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createTrace()" class="member-name-link">createTrace</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables Xalan2 traces and uses the given configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFactory()" class="member-name-link">getFactory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the factory instance configured for this processor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLiaison()" class="member-name-link">getLiaison</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Liaison implementation to use in processing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputProperties()" class="member-name-link">getOutputProperties</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an enumeration on the outputproperties.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSuppressWarnings()" class="member-name-link">getSuppressWarnings</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to suppress warning messages of the processor.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="XSLTProcess.TraceConfiguration.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTraceConfiguration()" class="member-name-link">getTraceConfiguration</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configuration for Xalan2 traces.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getXMLCatalog()" class="member-name-link">getXMLCatalog</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the XML catalog containing entity definitions</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleError(java.lang.String)" class="member-name-link">handleError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Throws an exception with the given message if failOnError is
 true, otherwise logs the message using the WARN level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleError(java.lang.Throwable)" class="member-name-link">handleError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;ex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Throws an exception with the given nested exception if
 failOnError is true, otherwise logs the message using the WARN
 level.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleTransformationError(java.lang.Exception)" class="member-name-link">handleTransformationError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a>&nbsp;ex)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Throws an exception with the given nested exception if
 failOnError and failOnTransformationError are true, otherwise
 logs the message using the WARN level.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize internal instance of XMLCatalog.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasedir(java.io.File)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base directory;
 optional, default is the project's basedir.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the optional classpath to the XSL processor</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the reference to an optional classpath to the XSL processor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory into which the XSL result
 files should be copied to;
 required, unless <code>in</code> and <code>out</code> are
 specified.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtension(java.lang.String)" class="member-name-link">setExtension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the desired file extension to be used for the target;
 optional, default is html.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnError(boolean)" class="member-name-link">setFailOnError</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether any errors should make the build fail.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnNoResources(boolean)" class="member-name-link">setFailOnNoResources</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the build should fail if the nested resource collection is empty.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnTransformationError(boolean)" class="member-name-link">setFailOnTransformationError</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether transformation errors should make the build fail.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFileDirParameter(java.lang.String)" class="member-name-link">setFileDirParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileDirParameter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass the directory name of the current processed file as a xsl parameter
 to the transformation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFileNameParameter(java.lang.String)" class="member-name-link">setFileNameParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileNameParameter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pass the filename of the current processed file as a xsl parameter
 to the transformation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForce(boolean)" class="member-name-link">setForce</a><wbr>(boolean&nbsp;force)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to check dependencies, or always generate;
 optional, default is false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIn(java.io.File)" class="member-name-link">setIn</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">specifies a single XML document to be styled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOut(java.io.File)" class="member-name-link">setOut</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the output name for the styled result from the
 <code>in</code> attribute; required if <code>in</code> is set</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProcessor(java.lang.String)" class="member-name-link">setProcessor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;processor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the XSL processor to use; optional, default trax.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReloadStylesheet(boolean)" class="member-name-link">setReloadStylesheet</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Controls whether the stylesheet is reloaded for every transform.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setScanIncludedDirectories(boolean)" class="member-name-link">setScanIncludedDirectories</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to style all files in the included directories as well;
 optional, default is true.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStyle(java.lang.String)" class="member-name-link">setStyle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xslFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name of the stylesheet to use - given either relative
 to the project's basedir or as an absolute path; required.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuppressWarnings(boolean)" class="member-name-link">setSuppressWarnings</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to suppress warning messages of the processor.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUseImplicitFileset(boolean)" class="member-name-link">setUseImplicitFileset</a><wbr>(boolean&nbsp;useimplicitfileset)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to use the implicit fileset.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setXslResource(org.apache.tools.ant.types.Resource)" class="member-name-link">setXslResource</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;xslResource)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">API method to set the XSL Resource.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLogger">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a></h3>
<code><a href="XSLTLogger.html#log(java.lang.String)">log</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PROCESSOR_TRAX">
<h3>PROCESSOR_TRAX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROCESSOR_TRAX</span></div>
<div class="block">The default processor is trax</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.XSLTProcess.PROCESSOR_TRAX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>XSLTProcess</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">XSLTProcess</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setScanIncludedDirectories(boolean)">
<h3>setScanIncludedDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setScanIncludedDirectories</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to style all files in the included directories as well;
 optional, default is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - true if files in included directories are processed.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setReloadStylesheet(boolean)">
<h3>setReloadStylesheet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReloadStylesheet</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Controls whether the stylesheet is reloaded for every transform.

 <p>Setting this to true may get around a bug in certain
 Xalan-J versions, default is false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - a <code>boolean</code> value</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addMapper(org.apache.tools.ant.types.Mapper)">
<h3>addMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addMapper</span><wbr><span class="parameters">(<a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;mapper)</span></div>
<div class="block">Defines the mapper to map source to destination files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mapper</code> - the mapper to use</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if more than one mapper is defined</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.ResourceCollection)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</span></div>
<div class="block">Adds a collection of resources to style in addition to the
 given file or the implicit fileset.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rc</code> - the collection of resources to style</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredStyle(org.apache.tools.ant.types.resources.Resources)">
<h3>addConfiguredStyle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredStyle</span><wbr><span class="parameters">(<a href="../types/resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</a>&nbsp;rc)</span></div>
<div class="block">Add a nested &lt;style&gt; element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rc</code> - the configured Resources object represented as &lt;style&gt;.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setXslResource(org.apache.tools.ant.types.Resource)">
<h3>setXslResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setXslResource</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;xslResource)</span></div>
<div class="block">API method to set the XSL Resource.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xslResource</code> - Resource to set as the stylesheet.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.util.FileNameMapper)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</span>
         throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds a nested filenamemapper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileNameMapper</code> - the mapper to add</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if more than one mapper is defined</dd>
<dt>Since:</dt>
<dd>Ant 1.7.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an execution problem.</dd>
<dt>To do:</dt>
<dd>validate that if either in or out is defined, then both are</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForce(boolean)">
<h3>setForce</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForce</span><wbr><span class="parameters">(boolean&nbsp;force)</span></div>
<div class="block">Set whether to check dependencies, or always generate;
 optional, default is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>force</code> - true if always generate.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.io.File)">
<h3>setBasedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span></div>
<div class="block">Set the base directory;
 optional, default is the project's basedir.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the base directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir)</span></div>
<div class="block">Set the destination directory into which the XSL result
 files should be copied to;
 required, unless <code>in</code> and <code>out</code> are
 specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the name of the destination directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExtension(java.lang.String)">
<h3>setExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set the desired file extension to be used for the target;
 optional, default is html.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the extension to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStyle(java.lang.String)">
<h3>setStyle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStyle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;xslFile)</span></div>
<div class="block">Name of the stylesheet to use - given either relative
 to the project's basedir or as an absolute path; required.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xslFile</code> - the stylesheet to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the optional classpath to the XSL processor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to use when loading the XSL processor</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Set the optional classpath to the XSL processor</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a path instance to be configured by the Ant core.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Set the reference to an optional classpath to the XSL processor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - the id of the Ant path instance to act as the classpath
          for loading the XSL processor</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setProcessor(java.lang.String)">
<h3>setProcessor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProcessor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;processor)</span></div>
<div class="block">Set the name of the XSL processor to use; optional, default trax.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>processor</code> - the name of the XSL processor</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUseImplicitFileset(boolean)">
<h3>setUseImplicitFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUseImplicitFileset</span><wbr><span class="parameters">(boolean&nbsp;useimplicitfileset)</span></div>
<div class="block">Whether to use the implicit fileset.

 <p>Set this to false if you want explicit control with nested
 resource collections.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>useimplicitfileset</code> - set to true if you want to use implicit fileset</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">
<h3>addConfiguredXMLCatalog</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredXMLCatalog</span><wbr><span class="parameters">(<a href="../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a>&nbsp;xmlCatalog)</span></div>
<div class="block">Add the catalog to our internal catalog</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>xmlCatalog</code> - the XMLCatalog instance to use to look up DTDs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFileNameParameter(java.lang.String)">
<h3>setFileNameParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileNameParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileNameParameter)</span></div>
<div class="block">Pass the filename of the current processed file as a xsl parameter
 to the transformation. This value sets the name of that xsl parameter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileNameParameter</code> - name of the xsl parameter retrieving the
                          current file name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFileDirParameter(java.lang.String)">
<h3>setFileDirParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFileDirParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileDirParameter)</span></div>
<div class="block">Pass the directory name of the current processed file as a xsl parameter
 to the transformation. This value sets the name of that xsl parameter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileDirParameter</code> - name of the xsl parameter retrieving the
                         current file directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuppressWarnings(boolean)">
<h3>setSuppressWarnings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuppressWarnings</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to suppress warning messages of the processor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSuppressWarnings()">
<h3>getSuppressWarnings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getSuppressWarnings</span>()</div>
<div class="block">Whether to suppress warning messages of the processor.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnTransformationError(boolean)">
<h3>setFailOnTransformationError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnTransformationError</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether transformation errors should make the build fail.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnError(boolean)">
<h3>setFailOnError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnError</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether any errors should make the build fail.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnNoResources(boolean)">
<h3>setFailOnNoResources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnNoResources</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether the build should fail if the nested resource collection is empty.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSysproperty(org.apache.tools.ant.types.Environment.Variable)">
<h3>addSysproperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSysproperty</span><wbr><span class="parameters">(<a href="../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;sysp)</span></div>
<div class="block">A system property to set during transformation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - Environment.Variable</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSyspropertyset(org.apache.tools.ant.types.PropertySet)">
<h3>addSyspropertyset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSyspropertyset</span><wbr><span class="parameters">(<a href="../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;sysp)</span></div>
<div class="block">A set of system properties to set during transformation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sysp</code> - PropertySet</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTrace()">
<h3>createTrace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.TraceConfiguration.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></span>&nbsp;<span class="element-name">createTrace</span>()</div>
<div class="block">Enables Xalan2 traces and uses the given configuration.

 <p>Note that this element doesn't have any effect with a
 processor other than trax or if the Transformer is not Xalan2's
 transformer implementation.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>TraceConfiguration</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTraceConfiguration()">
<h3>getTraceConfiguration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.TraceConfiguration.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.TraceConfiguration</a></span>&nbsp;<span class="element-name">getTraceConfiguration</span>()</div>
<div class="block">Configuration for Xalan2 traces.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>TraceConfiguration</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOut(java.io.File)">
<h3>setOut</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOut</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outFile)</span></div>
<div class="block">Specifies the output name for the styled result from the
 <code>in</code> attribute; required if <code>in</code> is set</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outFile</code> - the output File instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIn(java.io.File)">
<h3>setIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIn</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inFile)</span></div>
<div class="block">specifies a single XML document to be styled. Should be used
 with the <code>out</code> attribute; required if <code>out</code> is set</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inFile</code> - the input file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFactory()">
<h3>getFactory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></span>&nbsp;<span class="element-name">getFactory</span>()</div>
<div class="block">Get the factory instance configured for this processor</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the factory instance in use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getXMLCatalog()">
<h3>getXMLCatalog</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</a></span>&nbsp;<span class="element-name">getXMLCatalog</span>()</div>
<div class="block">Get the XML catalog containing entity definitions</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the XML catalog for the task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputProperties()">
<h3>getOutputProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a>&gt;</span>&nbsp;<span class="element-name">getOutputProperties</span>()</div>
<div class="block">Get an enumeration on the outputproperties.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the outputproperties</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLiaison()">
<h3>getLiaison</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></span>&nbsp;<span class="element-name">getLiaison</span>()</div>
<div class="block">Get the Liaison implementation to use in processing.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an instance of the XSLTLiaison interface.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createParam()">
<h3>createParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</a></span>&nbsp;<span class="element-name">createParam</span>()</div>
<div class="block">Create an instance of an XSL parameter for configuration by Ant.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an instance of the Param class to be configured.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createOutputProperty()">
<h3>createOutputProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</a></span>&nbsp;<span class="element-name">createOutputProperty</span>()</div>
<div class="block">Create an instance of an output property to be configured.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the newly created output property.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()
          throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Initialize internal instance of XMLCatalog.
 Initialize XPath for parameter evaluation.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#init()">init</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configureLiaison(java.io.File)">
<h3>configureLiaison</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureLiaison</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;stylesheet)</span>
                         throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.7</div>
</div>
<div class="block">Loads the stylesheet and set xsl:param parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stylesheet</code> - the file from which to load the stylesheet.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the stylesheet cannot be loaded.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configureLiaison(org.apache.tools.ant.types.Resource)">
<h3>configureLiaison</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureLiaison</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;stylesheet)</span>
                         throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Loads the stylesheet and set xsl:param parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stylesheet</code> - the resource from which to load the stylesheet.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the stylesheet cannot be loaded.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createFactory()">
<h3>createFactory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</a></span>&nbsp;<span class="element-name">createFactory</span>()
                                  throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the factory element to configure a trax liaison.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the newly created factory element.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the element is created more than one time.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleError(java.lang.String)">
<h3>handleError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</span></div>
<div class="block">Throws an exception with the given message if failOnError is
 true, otherwise logs the message using the WARN level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msg</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleError(java.lang.Throwable)">
<h3>handleError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;ex)</span></div>
<div class="block">Throws an exception with the given nested exception if
 failOnError is true, otherwise logs the message using the WARN
 level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ex</code> - Throwable</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleTransformationError(java.lang.Exception)">
<h3>handleTransformationError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleTransformationError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a>&nbsp;ex)</span></div>
<div class="block">Throws an exception with the given nested exception if
 failOnError and failOnTransformationError are true, otherwise
 logs the message using the WARN level.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ex</code> - Exception</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
