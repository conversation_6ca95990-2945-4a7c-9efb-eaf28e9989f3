<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Writing a Simple Buildfile</title>
</head>

<body>
<h1>Using Apache Ant</h1>
<h2 id="buildfile">Writing a Simple Buildfile</h2>
<p>Apache Ant's buildfiles are written in XML. Each buildfile contains one project and at least one (default)
target. Targets contain task elements.  Each task element of the buildfile can have an <var>id</var> attribute and can
later be referred to by the value supplied to this. The value has to be unique. (For additional information, see
the <a href="#tasks">Tasks</a> section below.)</p>

<h3 id="projects">Projects</h3>
<p>A <em>project</em> has three attributes:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>the name of the project.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>default</td>
    <td>the default target to use when no target is supplied.</td>
    <td>No; however, <em>since Ant 1.6.0</em>, every project includes an implicit target that contains any and all
      top-level tasks and/or types. This target will always be executed as part of the project's initialization, even
      when Ant is run with the <a href="running.html#options"><kbd>-projecthelp</kbd></a> option.
    </td>
  </tr>
  <tr>
    <td>basedir</td>
    <td>the base directory from which all path calculations are done. A relative path is resolved relative to the
      directory containing the buildfile.
    </td>
    <td>No; defaults to the parent directory of the buildfile, unless overridden by the project's <var>basedir</var> or
      the <code>basedir</code> property</td>
  </tr>
</table>
<p>Optionally, a description for the project can be provided as a top-level <code>&lt;description&gt;</code> element
(see the <a href="Types/description.html">description</a> type).</p>

<p>Each project defines one or more <em>targets</em>.  A target is a set of <em>tasks</em> you want to be executed. When
starting Ant, you can select which target(s) you want to have executed. When no target is given, the
project's <var>default</var> is used.</p>

<h3 id="targets">Targets</h3>
<p>A target can depend on other targets. You might have a target for compiling, for example, and a target for creating a
distributable. You can only build a distributable when you have compiled first, so the <q>distribute</q>
target <em>depends on</em> the <q>compile</q> target. Ant resolves these dependencies.</p>
<p>It should be noted, however, that Ant's <var>depends</var> attribute only specifies the <em>order</em> in which
targets should be executed&mdash;it does not affect whether the target that specifies the dependency(s) gets executed if
the dependent target(s) did not (need to) run.</p>

<p>More information can be found in the dedicated <a href="targets.html">manual page</a>.</p>

<h3 id="tasks">Tasks</h3>
<p>A task is a piece of code that can be executed.</p>
<p>A task can have multiple attributes (or arguments, if you prefer). The value of an attribute might contain references
to a property. These references will be resolved before the task is executed.</p>
<p>Tasks have a common structure:</p>

<pre>&lt;<i>name</i> <var>attribute1</var>=&quot;<i>value1</i>&quot; <var>attribute2</var>=&quot;<i>value2</i>&quot; ... /&gt;</pre>

<p>where <code><i>name</i></code> is the name of the task, <var>attributeN</var> is the attribute name,
and <code><i>valueN</i></code> is the value for this attribute.</p>
<p>There is a set of <a href="tasklist.html" target="navFrame">built-in tasks</a>, but it is also very easy
to <a href="develop.html#writingowntask">write your own</a>.</p>
<p>All tasks can have a <var>name</var> attribute. The value of this attribute will be used in the logging messages
generated by Ant.</p>
<p>Tasks can be assigned an <var>id</var> attribute:</p>

<pre>&lt;<i>taskname</i> <var>id</var>="<i>taskID</i>" ... /&gt;</pre>

<p>where <code><i>taskname</i></code> is the name of the task, and <code><i>taskID</i></code> is a unique identifier for
this task.  You can refer to the corresponding task object in scripts or other tasks via this name.  For example, in
scripts you could do:</p>

<pre>
&lt;script ... &gt;
  task1.setFoo("bar");
&lt;/script&gt;</pre>
<p>to set the <code>foo</code> attribute of this particular task instance.  In another task (written in Java), you can
access the instance via <code class="code">project.getReference("task1")</code>.</p>
<p>Note 1: If <q>task1</q> has not been run yet, then it has not been configured (ie., no attributes have been set), and
if it is going to be configured later, anything you've done to the instance may be overwritten.</p>
<p>Note 2: Future versions of Ant will most likely <em>not</em> be backward-compatible with this behaviour, since there
will likely be no task instances at all, only proxies.</p>

<h3 id="properties">Properties</h3>

<p>Properties are an important way to customize a build process or
  to just provide shortcuts for strings that are used repeatedly
  inside a buildfile.</p>

<p>In its most simple form properties are defined in the buildfile
  (for example by the <a href="Tasks/property.html">property</a>
  task) or might be set outside Ant.  A property has a name and a
  value; the name is case-sensitive. Properties may be used in the
  value of task attributes or in the nested text of tasks that support
  them.  This is done by placing the property name between
  <q>${</q> and <q>}</q> in the
  attribute value. For example, if there is a <code>builddir</code>
  property with the value <q>build</q>, then this could be used
  in an attribute like this: <samp>${builddir}/classes</samp>.  This
  is resolved at run-time as <samp>build/classes</samp>.</p>

<p><em>Since Ant 1.8.0</em>, property expansion has become much more powerful
  than simple key value pairs, more details can be
  found <a href="properties.html">in the concepts section</a> of this
  manual.</p>

<h3 id="example">Example Buildfile</h3>
<pre>
&lt;project name=&quot;MyProject&quot; default=&quot;dist&quot; basedir=&quot;.&quot;&gt;
  &lt;description&gt;
    simple example build file
  &lt;/description&gt;
  &lt;!-- set global properties for this build --&gt;
  &lt;property name=&quot;src&quot; location=&quot;src&quot;/&gt;
  &lt;property name=&quot;build&quot; location=&quot;build&quot;/&gt;
  &lt;property name=&quot;dist&quot; location=&quot;dist&quot;/&gt;

  &lt;target name=&quot;init&quot;&gt;
    &lt;!-- Create the time stamp --&gt;
    &lt;tstamp/&gt;
    &lt;!-- Create the build directory structure used by compile --&gt;
    &lt;mkdir dir=&quot;${build}&quot;/&gt;
  &lt;/target&gt;

  &lt;target name=&quot;compile&quot; depends=&quot;init&quot;
        description=&quot;compile the source&quot;&gt;
    &lt;!-- Compile the Java code from ${src} into ${build} --&gt;
    &lt;javac srcdir=&quot;${src}&quot; destdir=&quot;${build}&quot;/&gt;
  &lt;/target&gt;

  &lt;target name=&quot;dist&quot; depends=&quot;compile&quot;
        description=&quot;generate the distribution&quot;&gt;
    &lt;!-- Create the distribution directory --&gt;
    &lt;mkdir dir=&quot;${dist}/lib&quot;/&gt;

    &lt;!-- Put everything in ${build} into the MyProject-${DSTAMP}.jar file --&gt;
    &lt;jar jarfile=&quot;${dist}/lib/MyProject-${DSTAMP}.jar&quot; basedir=&quot;${build}&quot;/&gt;
  &lt;/target&gt;

  &lt;target name=&quot;clean&quot;
        description=&quot;clean up&quot;&gt;
    &lt;!-- Delete the ${build} and ${dist} directory trees --&gt;
    &lt;delete dir=&quot;${build}&quot;/&gt;
    &lt;delete dir=&quot;${dist}&quot;/&gt;
  &lt;/target&gt;
&lt;/project&gt;</pre>

<p>Notice that we are declaring properties outside any target. <em>Since Ant 1.6</em>, all tasks can be declared outside
targets (earlier version only allowed <code>&lt;property&gt;</code>, <code>&lt;typedef&gt;</code>
and <code>&lt;taskdef&gt;</code>).  When you do this they are evaluated before any targets are executed.  Some tasks
will generate build failures if they are used outside of targets as they may cause infinite loops otherwise
(<code>&lt;antcall&gt;</code> for example).</p>

<p>We have given some targets descriptions; this causes the <kbd>-projecthelp</kbd> invocation option to list them as
public targets with the descriptions; the other target is internal and not listed.</p>
<p>Finally, for this target to work the source in the <samp>src</samp> subdirectory should be stored in a directory tree
which matches the package names. Check the <code>&lt;javac&gt;</code> task for details.</p>

<h3 id="filters">Token Filters</h3>
<p>A project can have a set of tokens that might be automatically expanded if found when a file is copied, when the
filtering-copy behavior is selected in the tasks that support this. These might be set in the buildfile by
the <a href="Tasks/filter.html">filter</a> task.</p>
<p>Since this can potentially be a very harmful behavior, the tokens in the files <strong>must</strong> be of the
form <code>@<var>token</var>@</code>, where <var>token</var> is the token name that is set in
the <code>&lt;filter&gt;</code> task. This token syntax matches the syntax of other build systems that perform such
filtering and remains sufficiently orthogonal to most programming and scripting languages, as well as with documentation
systems.</p>
<p><strong>Note</strong>: If a token with the format <code>@<var>token</var>@</code> is found in a file, but no filter
is associated with that token, no changes take place; therefore, no escaping method is available&mdash;but as long as
you choose appropriate names for your tokens, this should not cause problems.</p>
<p><strong>Warning</strong>: If you copy binary files with filtering turned on, you can corrupt the files. This feature
should be used with text files <em>only</em>.</p>

<h3 id="path">Path-like Structures</h3>
<p>You can specify <code>PATH</code>- and <code>CLASSPATH</code>-type references using both <q>:</q> and <q>;</q> as
separator characters. Ant will convert the separator to the correct character of the current operating system.</p>
<p>Wherever path-like values need to be specified, a nested element can be used. This takes the general form of:</p>
<pre>
&lt;classpath&gt;
  &lt;pathelement path=&quot;${classpath}&quot;/&gt;
  &lt;pathelement location=&quot;lib/helper.jar&quot;/&gt;
&lt;/classpath&gt;</pre>
<p>The <var>location</var> attribute specifies a single file or directory relative to the project's base directory (or
an absolute filename), while the <var>path</var> attribute accepts colon- or semicolon-separated lists of
locations. The <var>path</var> attribute is intended to be used with predefined paths&mdash;in any other case, multiple
elements with <var>location</var> attributes should be preferred.</p>
<p><em>Since Ant 1.8.2</em> the <var>location</var> attribute can also contain a wildcard in its last path component
(i.e. it can end in a <q>*</q>) in order to support wildcard <code>CLASSPATH</code>s introduced with Java 6.  Ant will
not expand or evaluate the wildcards and the resulting path may not work as anything else but
a <code>CLASSPATH</code>&mdash;or even as a <code>CLASSPATH</code> for JVM prior to Java 6.</p>
<p>As a shortcut, the <code>&lt;classpath&gt;</code> tag
supports <var>path</var> and
<var>location</var> attributes of its own, so:</p>
<pre>
&lt;classpath&gt;
  &lt;pathelement path=&quot;${classpath}&quot;/&gt;
&lt;/classpath&gt;
</pre>
<p>can be abbreviated to:</p>
<pre>&lt;classpath path=&quot;${classpath}&quot;/&gt;</pre>
<p>In addition, one or more <a href="Types/resources.html#collection">resource collections</a> can be specified as
nested elements (these must consist of <a href="Types/resources.html#file">file</a>-type resources only).  Additionally,
it should be noted that although resource collections are processed in the order encountered, certain resource
collection types such as <a href="Types/fileset.html">fileset</a>, <a href="Types/dirset.html">dirset</a>
and <a href="Types/resources.html#files">files</a> are undefined in terms of order.</p>
<pre>
&lt;classpath&gt;
  &lt;pathelement path=&quot;${classpath}&quot;/&gt;
  &lt;fileset dir=&quot;lib&quot;&gt;
    &lt;include name=&quot;**/*.jar&quot;/&gt;
  &lt;/fileset&gt;
  &lt;pathelement location=&quot;classes&quot;/&gt;
  &lt;dirset dir=&quot;${build.dir}&quot;&gt;
    &lt;include name=&quot;apps/**/classes&quot;/&gt;
    &lt;exclude name=&quot;apps/**/*Test*&quot;/&gt;
  &lt;/dirset&gt;
  &lt;filelist refid=&quot;third-party_jars&quot;/&gt;
&lt;/classpath&gt;</pre>
<p>This builds a path that holds the value of <samp>${classpath}</samp>, followed by all jar files in
the <samp>lib</samp> directory, the <samp>classes</samp> directory, all directories named <samp>classes</samp> under
the <samp>apps</samp> subdirectory of <samp>${build.dir}</samp>, except those that have the text <code>Test</code> in
their name, and the files specified in the referenced FileList.</p>
<p>If you want to use the same path-like structure for several tasks, you can define them with
a <code>&lt;path&gt;</code> element at the same level as <code>&lt;target&gt;</code>s, and reference them via their
<var>id</var> attribute&mdash;see <a href="#references">References</a> for an
example.</p>

<p>By default a path-like structure will re-evaluate all nested resource collections whenever it is used, which may lead
to unnecessary re-scanning of the filesystem.  <em>Since Ant 1.8.0</em>, path has an optional <var>cache</var>
attribute, if it is set to <q>true</q>, the path instance will only scan its nested resource collections once and assume
it doesn't change during the build anymore (the default for <var>cache</var> still is <q>false</q>).  Even if you are
using the path only in a single task it may improve overall performance to set <var>cache</var> to <q>true</q> if you
are using complex nested constructs.</p>

<p>A path-like structure can include a reference to another path-like structure (a path being itself a resource
collection) via nested <code>&lt;path&gt;</code> elements:</p>
<pre>
&lt;path id=&quot;base.path&quot;&gt;
  &lt;pathelement path=&quot;${classpath}&quot;/&gt;
  &lt;fileset dir=&quot;lib&quot;&gt;
    &lt;include name=&quot;**/*.jar&quot;/&gt;
  &lt;/fileset&gt;
  &lt;pathelement location=&quot;classes&quot;/&gt;
&lt;/path&gt;

&lt;path id=&quot;tests.path&quot; cache=&quot;true&quot;&gt;
  &lt;path refid=&quot;base.path&quot;/&gt;
  &lt;pathelement location=&quot;testclasses&quot;/&gt;
&lt;/path&gt;</pre>
<p>The shortcuts previously mentioned for <code>&lt;classpath&gt;</code> are also valid
for <code>&lt;path&gt;</code>. For example:</p>
<pre>
&lt;path id=&quot;base.path&quot;&gt;
  &lt;pathelement path=&quot;${classpath}&quot;/&gt;
&lt;/path&gt;</pre>
<p>can be written as:</p>
<pre>&lt;path id=&quot;base.path&quot; path=&quot;${classpath}&quot;/&gt;</pre>
<h4 id="pathshortcut">Path Shortcut</h4>
<p><em>Since Ant 1.6</em>, there is a shortcut for converting paths to OS specific strings in properties. One can use
the expression <samp>${toString:<em>pathreference</em>}</samp> to convert a path element reference to a string that can
be used for a path argument. For example:</p>
<pre>
&lt;path id="lib.path.ref"&gt;
  &lt;fileset dir="lib" includes="*.jar"/&gt;
&lt;/path&gt;
&lt;javac srcdir="src" destdir="classes"&gt;
  &lt;compilerarg arg="-Xbootclasspath/p:${toString:lib.path.ref}"/&gt;
&lt;/javac&gt;</pre>

<h3 id="arg">Command-line Arguments</h3>
<p>Several tasks take arguments that will be passed to another process on the command line. To make it easier to specify
arguments that contain space characters, nested <code>arg</code> elements can be used.</p>
<table class="attr">
<tr>
  <th scope="col">Attribute</th>
  <th scope="col">Description</th>
  <th scope="col">Required</th>
</tr>
  <tr>
    <td>value</td>
    <td>a single command-line argument; can contain space characters.</td>
    <td rowspan="5">Exactly one of these.</td>
  </tr>
  <tr>
    <td>file</td>
    <td class="left">The name of a file as a single command-line argument; will be replaced with the absolute filename
      of the file.</td>
  </tr>
  <tr>
    <td>path</td>
    <td class="left">A string that will be treated as a path-like string as a single command-line argument; you can
      use <q>;</q> or <q>:</q> as path separators and Ant will convert it to the platform's local conventions.</td>
  </tr>
  <tr>
    <td>pathref</td>
    <td class="left"><a href="#references">Reference</a> to a path defined elsewhere.  Ant will convert it to the
      platform's local conventions.</td>
  </tr>
  <tr>
    <td>line</td>
    <td class="left">a space-delimited list of command-line arguments.</td>
  </tr>
  <tr>
    <td>prefix</td>
    <td>A fixed string to be placed in front of the argument. In the case of a line broken into parts, it will be placed
      in front of every part. <em>Since Ant 1.8.</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>suffix</td>
    <td>A fixed string to be placed immediately after the argument. In the case of a line broken into parts, it will be
      placed after every part. <em>Since Ant 1.8.</em></td>
    <td>No</td>
  </tr>
</table>

<p>It is highly recommended to avoid the <var>line</var> version when possible.  Ant will try to split the command line
in a way similar to what a (Unix) shell would do, but may create something that is very different from what you expect
under some circumstances.</p>

<h4>Examples</h4>
<pre>&lt;arg value=&quot;-l -a&quot;/&gt;</pre>
<p>is a single command-line argument containing a space character, <em>not</em> separate options <q>-l</q>
and <q>-a</q>.</p>
<pre>&lt;arg line=&quot;-l -a&quot;/&gt;</pre>
<p>This is a command line with two separate options, <q>-l</q> and <q>-a</q>.</p>
<pre>&lt;arg path=&quot;/dir;/dir2:\dir3&quot;/&gt;</pre>
<p>is a single command-line argument with the value <code>\dir;\dir2;\dir3</code> on DOS-based systems
and <code>/dir:/dir2:/dir3</code> on Unix(-like) systems.</p>

<h3 id="references">References</h3>

<p>Any project element can be assigned an identifier using its <var>id</var> attribute. In most cases the element can
subsequently be referenced by specifying the <var>refid</var> attribute on an element of the same type.  This can be
useful if you are going to replicate the same snippet of XML over and over again&mdash;using
a <code>&lt;classpath&gt;</code> structure more than once, for example.</p>
<p>The following example:</p>
<pre>
&lt;project ... &gt;
  &lt;target ... &gt;
    &lt;rmic ...&gt;
      &lt;classpath&gt;
        &lt;pathelement location=&quot;lib/&quot;/&gt;
        &lt;pathelement path=&quot;${java.class.path}/&quot;/&gt;
        &lt;pathelement path=&quot;${additional.path}&quot;/&gt;
      &lt;/classpath&gt;
    &lt;/rmic&gt;
  &lt;/target&gt;

  &lt;target ... &gt;
    &lt;javac ...&gt;
      &lt;classpath&gt;
        &lt;pathelement location=&quot;lib/&quot;/&gt;
        &lt;pathelement path=&quot;${java.class.path}/&quot;/&gt;
        &lt;pathelement path=&quot;${additional.path}&quot;/&gt;
      &lt;/classpath&gt;
    &lt;/javac&gt;
  &lt;/target&gt;
&lt;/project&gt;</pre>
<p>could be rewritten as:</p>
<pre>
&lt;project ... &gt;
  &lt;path id=&quot;project.class.path&quot;&gt;
    &lt;pathelement location=&quot;lib/&quot;/&gt;
    &lt;pathelement path=&quot;${java.class.path}/&quot;/&gt;
    &lt;pathelement path=&quot;${additional.path}&quot;/&gt;
  &lt;/path&gt;

  &lt;target ... &gt;
    &lt;rmic ...&gt;
      &lt;classpath refid=&quot;project.class.path&quot;/&gt;
    &lt;/rmic&gt;
  &lt;/target&gt;

  &lt;target ... &gt;
    &lt;javac ...&gt;
      &lt;classpath refid=&quot;project.class.path&quot;/&gt;
    &lt;/javac&gt;
  &lt;/target&gt;
&lt;/project&gt;</pre>
<p>All tasks that use nested elements
for <a href="Types/patternset.html">PatternSet</a>s, <a href="Types/fileset.html">FileSet</a>s, <a href="Types/zipfileset.html">ZipFileSet</a>s
or <a href="#path">path-like structures</a> accept references to these structures as shown in the
examples. Using <var>refid</var> on a task will ordinarily have the same effect (referencing a task already declared),
but the user should be aware that the interpretation of this attribute is dependent on the implementation of the element
upon which it is specified. Some tasks (the <a href="Tasks/property.html">property</a> task is a handy example)
deliberately assign a different meaning to <var>refid</var>.</p>

<h3 id="external-tasks">Use of external tasks</h3>
<p>Ant supports a plugin mechanism for using third party tasks. For using them you have to do two steps:</p>
<ol>
  <li>place their implementation somewhere where Ant can find them.</li>
  <li>declare them.</li>
</ol>
<p>Don't add anything to the <code>CLASSPATH</code> environment variable&mdash;this is often the reason for very obscure
errors. Use Ant's own <a href="install.html#optionalTasks">mechanisms</a> for adding libraries:</p>
<ul>
  <li>via command line argument <kbd>-lib</kbd></li>
  <li>adding to <code>${user.home}/.ant/lib</code></li>
  <li>adding to <code>${ant.home}/lib</code></li>
</ul>
<p>For the declaration there are several ways:</p>
<ul>
  <li>declare a single task per using instruction using
    <code>&lt;<a href="Tasks/taskdef.html">taskdef</a> name=&quot;taskname&quot;
      classname=&quot;ImplementationClass&quot;/&gt;</code><br/>
    <code class="code">&lt;taskdef name=&quot;for&quot; classname=&quot;net.sf.antcontrib.logic.For&quot;/&gt; &lt;for
      ... /&gt;</code>
  </li>
  <li>declare a bundle of tasks using a <samp>properties</samp> file holding these taskname&ndash;ImplementationClass
    pairs and <code>&lt;taskdef&gt;</code><br/>
    <code class="code">&lt;taskdef resource=&quot;net/sf/antcontrib/antcontrib.properties&quot;/&gt; &lt;for
    ... /&gt;</code>
  </li>
  <li>declare a bundle of tasks using an <a href="Types/antlib.html">xml file</a> holding these
    taskname-ImplementationClass-pairs and <code>&lt;taskdef&gt;</code><br/>
    <code class="code">&lt;taskdef resource=&quot;net/sf/antcontrib/antlib.xml&quot;/&gt; &lt;for ... /&gt;</code>
  </li>
  <li>declare a bundle of tasks using an xml file named <samp>antlib.xml</samp>, XML namespace
    and <a href="Types/antlib.html#antlibnamespace"><code>antlib:</code> protocol handler</a><br/>
    <code class="code">&lt;project xmlns:ac=&quot;antlib:net.sf.antcontrib&quot;/&gt; &lt;ac:for ... /&gt;</code>
  </li>
</ul>

If you need a special function, you should
<ol>
  <li>have a look at this manual, because Ant provides lot of tasks</li>
  <li>have a look at the external task page <a href="https://ant.apache.org/external.html" target="_top">online</a></li>
  <li>have a look at the external task <a href="https://cwiki.apache.org/confluence/display/ANT/AntExternalTaskdefs" target="_top">wiki
    page</a></li>
  <li>ask on the <a href="https://ant.apache.org/mail.html#User%20List" target="_top">Ant user</a> list</li>
  <li><a href="tutorial-writing-tasks.html">implement</a> (and share) your own</li>
</ol>

</body>
</html>
