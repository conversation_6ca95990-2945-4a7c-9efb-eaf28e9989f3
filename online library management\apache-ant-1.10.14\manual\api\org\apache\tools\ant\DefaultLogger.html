<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DefaultLogger (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: DefaultLogger">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class DefaultLogger" class="title">Class DefaultLogger</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.DefaultLogger</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></code>, <code><a href="listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</a></code>, <code><a href="NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></code>, <code><a href="listener/ProfileLogger.html" title="class in org.apache.tools.ant.listener">ProfileLogger</a></code>, <code><a href="listener/SilentLogger.html" title="class in org.apache.tools.ant.listener">SilentLogger</a></code>, <code><a href="listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DefaultLogger</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></span></div>
<div class="block">Writes build events to a PrintStream. Currently, it
 only writes which targets are being executed, and
 any messages that get logged.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#emacsMode" class="member-name-link">emacsMode</a></code></div>
<div class="col-last even-row-color">
<div class="block">Whether or not to use emacs-style output</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a></code></div>
<div class="col-second odd-row-color"><code><a href="#err" class="member-name-link">err</a></code></div>
<div class="col-last odd-row-color">
<div class="block">PrintStream to write error messages to</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LEFT_COLUMN_SIZE" class="member-name-link">LEFT_COLUMN_SIZE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Size of left-hand column for right-justified task name.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#lSep" class="member-name-link">lSep</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color"><code>protected int</code></div>
<div class="col-second even-row-color"><code><a href="#msgOutputLevel" class="member-name-link">msgOutputLevel</a></code></div>
<div class="col-last even-row-color">
<div class="block">Lowest level of message to write out</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a></code></div>
<div class="col-second odd-row-color"><code><a href="#out" class="member-name-link">out</a></code></div>
<div class="col-last odd-row-color">
<div class="block">PrintStream to write non-error messages to</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DefaultLogger</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Sole constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Responds to a build being started by just remembering the current time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#extractProjectName(org.apache.tools.ant.BuildEvent)" class="member-name-link">extractProjectName</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the project name or null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatTime(long)" class="member-name-link">formatTime</a><wbr>(long&nbsp;millis)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Convenience method to format a specified length of time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildFailedMessage()" class="member-name-link">getBuildFailedMessage</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point: the message that indicates whether a build failed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBuildSuccessfulMessage()" class="member-name-link">getBuildSuccessfulMessage</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point: the message that indicates that a build succeeded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMessageOutputLevel()" class="member-name-link">getMessageOutputLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimestamp()" class="member-name-link">getTimestamp</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the current time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation which allows subclasses to receive the
 same output that is generated here.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message, if the priority is suitable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#printMessage(java.lang.String,java.io.PrintStream,int)" class="member-name-link">printMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;stream,
 int&nbsp;priority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Prints a message to a PrintStream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEmacsMode(boolean)" class="member-name-link">setEmacsMode</a><wbr>(boolean&nbsp;emacsMode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets this logger to produce emacs (and other editor) friendly output.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorPrintStream(java.io.PrintStream)" class="member-name-link">setErrorPrintStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;err)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the output stream to which this logger is to send error messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMessageOutputLevel(int)" class="member-name-link">setMessageOutputLevel</a><wbr>(int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the highest level of message this logger should respond to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputPrintStream(java.io.PrintStream)" class="member-name-link">setOutputPrintStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the output stream to which this logger is to send its output.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">No-op implementation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message to say that the target has started if this
 logger allows information-level messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">No-op implementation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">No-op implementation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="LEFT_COLUMN_SIZE">
<h3>LEFT_COLUMN_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LEFT_COLUMN_SIZE</span></div>
<div class="block">Size of left-hand column for right-justified task name.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#messageLogged(org.apache.tools.ant.BuildEvent)"><code>messageLogged(BuildEvent)</code></a></li>
<li><a href="../../../../constant-values.html#org.apache.tools.ant.DefaultLogger.LEFT_COLUMN_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="out">
<h3>out</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a></span>&nbsp;<span class="element-name">out</span></div>
<div class="block">PrintStream to write non-error messages to</div>
</section>
</li>
<li>
<section class="detail" id="err">
<h3>err</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a></span>&nbsp;<span class="element-name">err</span></div>
<div class="block">PrintStream to write error messages to</div>
</section>
</li>
<li>
<section class="detail" id="msgOutputLevel">
<h3>msgOutputLevel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">msgOutputLevel</span></div>
<div class="block">Lowest level of message to write out</div>
</section>
</li>
<li>
<section class="detail" id="lSep">
<h3>lSep</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">lSep</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Line separator</div>
</section>
</li>
<li>
<section class="detail" id="emacsMode">
<h3>emacsMode</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">emacsMode</span></div>
<div class="block">Whether or not to use emacs-style output</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DefaultLogger</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DefaultLogger</span>()</div>
<div class="block">Sole constructor.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMessageOutputLevel(int)">
<h3>setMessageOutputLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMessageOutputLevel</span><wbr><span class="parameters">(int&nbsp;level)</span></div>
<div class="block">Sets the highest level of message this logger should respond to.

 Only messages with a message level lower than or equal to the
 given level should be written to the log.
 <p>
 Constants for the message levels are in the
 <a href="Project.html" title="class in org.apache.tools.ant"><code>Project</code></a> class. The order of the levels, from least
 to most verbose, is <code>MSG_ERR</code>, <code>MSG_WARN</code>,
 <code>MSG_INFO</code>, <code>MSG_VERBOSE</code>,
 <code>MSG_DEBUG</code>.
 <p>
 The default message level for DefaultLogger is Project.MSG_ERR.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</a></code>&nbsp;in interface&nbsp;<code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>level</code> - the logging level for the logger.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMessageOutputLevel()">
<h3>getMessageOutputLevel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMessageOutputLevel</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildLogger.html#getMessageOutputLevel()">getMessageOutputLevel</a></code>&nbsp;in interface&nbsp;<code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
<dt>Returns:</dt>
<dd>Returns the <a href="BuildLogger.html#setMessageOutputLevel(int)"><code>currently set</code></a> message output level.
 The <code>default</code> implementation of this method returns <code>MSG_INFO</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputPrintStream(java.io.PrintStream)">
<h3>setOutputPrintStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputPrintStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;output)</span></div>
<div class="block">Sets the output stream to which this logger is to send its output.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</a></code>&nbsp;in interface&nbsp;<code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - The output stream for the logger.
               Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorPrintStream(java.io.PrintStream)">
<h3>setErrorPrintStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorPrintStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;err)</span></div>
<div class="block">Sets the output stream to which this logger is to send error messages.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</a></code>&nbsp;in interface&nbsp;<code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>err</code> - The error stream for the logger.
            Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEmacsMode(boolean)">
<h3>setEmacsMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEmacsMode</span><wbr><span class="parameters">(boolean&nbsp;emacsMode)</span></div>
<div class="block">Sets this logger to produce emacs (and other editor) friendly output.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildLogger.html#setEmacsMode(boolean)">setEmacsMode</a></code>&nbsp;in interface&nbsp;<code><a href="BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</a></code></dd>
<dt>Parameters:</dt>
<dd><code>emacsMode</code> - <code>true</code> if output is to be unadorned so that
                  emacs and other editors can parse files names, etc.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildStarted(org.apache.tools.ant.BuildEvent)">
<h3>buildStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Responds to a build being started by just remembering the current time.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - Ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildFailedMessage()">
<h3>getBuildFailedMessage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBuildFailedMessage</span>()</div>
<div class="block">This is an override point: the message that indicates whether a build failed.
 Subclasses can change/enhance the message.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The classic "BUILD FAILED"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBuildSuccessfulMessage()">
<h3>getBuildSuccessfulMessage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBuildSuccessfulMessage</span>()</div>
<div class="block">This is an override point: the message that indicates that a build succeeded.
 Subclasses can change/enhance the message.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The classic "BUILD SUCCESSFUL"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Logs a message to say that the target has started if this
 logger allows information-level messages.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - An event with any relevant extra information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetFinished(org.apache.tools.ant.BuildEvent)">
<h3>targetFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">No-op implementation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - Ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">No-op implementation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - Ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskFinished(org.apache.tools.ant.BuildEvent)">
<h3>taskFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">No-op implementation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - Ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Logs a message, if the priority is suitable.
 In non-emacs mode, task level messages are prefixed by the
 task name which is right-justified.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - A BuildEvent containing message information.
              Must not be <code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
<li><a href="BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="formatTime(long)">
<h3>formatTime</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">formatTime</span><wbr><span class="parameters">(long&nbsp;millis)</span></div>
<div class="block">Convenience method to format a specified length of time.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>millis</code> - Length of time to format, in milliseconds.</dd>
<dt>Returns:</dt>
<dd>the time as a formatted string.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="util/DateUtils.html#formatElapsedTime(long)"><code>DateUtils.formatElapsedTime(long)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="printMessage(java.lang.String,java.io.PrintStream,int)">
<h3>printMessage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">printMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;stream,
 int&nbsp;priority)</span></div>
<div class="block">Prints a message to a PrintStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The message to print.
                 Should not be <code>null</code>.</dd>
<dd><code>stream</code> - A PrintStream to print the message to.
                 Must not be <code>null</code>.</dd>
<dd><code>priority</code> - The priority of the message.
                 (Ignored in this implementation.)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">Empty implementation which allows subclasses to receive the
 same output that is generated here.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - Message being logged. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimestamp()">
<h3>getTimestamp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTimestamp</span>()</div>
<div class="block">Get the current time.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the current time as a formatted string.</dd>
<dt>Since:</dt>
<dd>Ant1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extractProjectName(org.apache.tools.ant.BuildEvent)">
<h3>extractProjectName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">extractProjectName</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Get the project name or null</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - the event</dd>
<dt>Returns:</dt>
<dd>the project that raised this event</dd>
<dt>Since:</dt>
<dd>Ant1.7.1</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
