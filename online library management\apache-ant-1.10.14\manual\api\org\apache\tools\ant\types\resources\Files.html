<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Files (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources, class: Files">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resources</a></div>
<h1 title="Class Files" class="title">Class Files</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.AbstractSelectorContainer</a>
<div class="inheritance">org.apache.tools.ant.types.resources.Files</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code>, <code><a href="../selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Files</span>
<span class="extends-implements">extends <a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a>
implements <a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></span></div>
<div class="block">ResourceCollection implementation; like AbstractFileSet with absolute paths.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Files</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Construct a new <code>Files</code> collection.</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.resources.Files)" class="member-name-link">Files</a><wbr>(<a href="Files.html" title="class in org.apache.tools.ant.types.resources">Files</a>&nbsp;f)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct a new <code>Files</code> collection, shallowly cloned
 from the specified <code>Files</code>.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendExcludes(java.lang.String%5B%5D)" class="member-name-link">appendExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Append <code>excludes</code> to the current list of include
 patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendIncludes(java.lang.String%5B%5D)" class="member-name-link">appendIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Append <code>includes</code> to the current list of include
 patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">appendSelector</a><wbr>(<a href="../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new selector into this container.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clone()" class="member-name-link">clone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a deep clone of this instance, except for the nested selectors
 (the list of selectors is a shallow clone of this instance's list).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExclude()" class="member-name-link">createExclude</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a name entry to the exclude list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExcludesFile()" class="member-name-link">createExcludesFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a name entry to the excludes files list.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createInclude()" class="member-name-link">createInclude</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a name entry to the include list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createIncludesFile()" class="member-name-link">createIncludesFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a name entry to the include files list.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createPatternSet()" class="member-name-link">createPatternSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested patternset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultexcludes()" class="member-name-link">getDefaultexcludes</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get whether default exclusions should be used or not.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Files.html" title="class in org.apache.tools.ant.types.resources">Files</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRef()" class="member-name-link">getRef</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform the check for circular references and return the
 referenced Files collection.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasPatterns()" class="member-name-link">hasPatterns</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find out whether this Files collection has patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCaseSensitive()" class="member-name-link">isCaseSensitive</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find out if this Files collection is case-sensitive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFilesystemOnly()" class="member-name-link">isFilesystemOnly</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Always returns true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFollowSymlinks()" class="member-name-link">isFollowSymlinks</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find out whether symbolic links should be followed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#iterator()" class="member-name-link">iterator</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fulfill the ResourceCollection contract.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mergeExcludes(org.apache.tools.ant.Project)" class="member-name-link">mergeExcludes</a><wbr>(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the merged exclude patterns for this Files collection.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mergeIncludes(org.apache.tools.ant.Project)" class="member-name-link">mergeIncludes</a><wbr>(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the merged include patterns for this Files collection.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mergePatterns(org.apache.tools.ant.Project)" class="member-name-link">mergePatterns</a><wbr>(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the merged patterns for this Files collection.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCaseSensitive(boolean)" class="member-name-link">setCaseSensitive</a><wbr>(boolean&nbsp;caseSensitive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set case-sensitivity of the Files collection.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultexcludes(boolean)" class="member-name-link">setDefaultexcludes</a><wbr>(boolean&nbsp;useDefaultExcludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether default exclusions should be used or not.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludes(java.lang.String)" class="member-name-link">setExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Append <code>excludes</code> to the current list of exclude
 patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludesfile(java.io.File)" class="member-name-link">setExcludesfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;excl)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the <code>File</code> containing the excludes patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFollowSymlinks(boolean)" class="member-name-link">setFollowSymlinks</a><wbr>(boolean&nbsp;followSymlinks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not symbolic links should be followed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludes(java.lang.String)" class="member-name-link">setIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Append <code>includes</code> to the current list of include
 patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludesfile(java.io.File)" class="member-name-link">setIncludesfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;incl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the <code>File</code> containing the includes patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefid(org.apache.tools.ant.types.Reference)" class="member-name-link">setRefid</a><wbr>(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make this instance in effect a reference to another instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#size()" class="member-name-link">size</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fulfill the ResourceCollection contract.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Format this Files collection as a String.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.AbstractSelectorContainer">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></h3>
<code><a href="../selectors/AbstractSelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../selectors/AbstractSelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../selectors/AbstractSelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../selectors/AbstractSelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../selectors/AbstractSelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../selectors/AbstractSelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../selectors/AbstractSelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../selectors/AbstractSelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../selectors/AbstractSelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../selectors/AbstractSelectorContainer.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="../selectors/AbstractSelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../selectors/AbstractSelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../selectors/AbstractSelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../selectors/AbstractSelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../selectors/AbstractSelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../selectors/AbstractSelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../selectors/AbstractSelectorContainer.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="../selectors/AbstractSelectorContainer.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="../selectors/AbstractSelectorContainer.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="../selectors/AbstractSelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../selectors/AbstractSelectorContainer.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="../selectors/AbstractSelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../selectors/AbstractSelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../selectors/AbstractSelectorContainer.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="../selectors/AbstractSelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../selectors/AbstractSelectorContainer.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="../selectors/AbstractSelectorContainer.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../selectors/AbstractSelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../selectors/AbstractSelectorContainer.html#hasSelectors()">hasSelectors</a>, <a href="../selectors/AbstractSelectorContainer.html#selectorCount()">selectorCount</a>, <a href="../selectors/AbstractSelectorContainer.html#selectorElements()">selectorElements</a>, <a href="../selectors/AbstractSelectorContainer.html#validate()">validate</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Files</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Files</span>()</div>
<div class="block">Construct a new <code>Files</code> collection.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.resources.Files)">
<h3>Files</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">Files</span><wbr><span class="parameters">(<a href="Files.html" title="class in org.apache.tools.ant.types.resources">Files</a>&nbsp;f)</span></div>
<div class="block">Construct a new <code>Files</code> collection, shallowly cloned
 from the specified <code>Files</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>f</code> - the <code>Files</code> to use as a template.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setRefid(org.apache.tools.ant.types.Reference)">
<h3>setRefid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefid</span><wbr><span class="parameters">(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span>
              throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Make this instance in effect a reference to another instance.

 <p>You must not set another attribute or nest elements inside
 this element if you make it a reference.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a></code>&nbsp;in class&nbsp;<code><a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - the <code>Reference</code> to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createPatternSet()">
<h3>createPatternSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></span>&nbsp;<span class="element-name">createPatternSet</span>()</div>
<div class="block">Create a nested patternset.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>PatternSet</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createInclude()">
<h3>createInclude</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createInclude</span>()</div>
<div class="block">Add a name entry to the include list.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>PatternSet.NameEntry</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createIncludesFile()">
<h3>createIncludesFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createIncludesFile</span>()</div>
<div class="block">Add a name entry to the include files list.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>PatternSet.PatternFileNameEntry</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExclude()">
<h3>createExclude</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createExclude</span>()</div>
<div class="block">Add a name entry to the exclude list.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>PatternSet.NameEntry</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExcludesFile()">
<h3>createExcludesFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createExcludesFile</span>()</div>
<div class="block">Add a name entry to the excludes files list.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>PatternSet.PatternFileNameEntry</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludes(java.lang.String)">
<h3>setIncludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</span></div>
<div class="block">Append <code>includes</code> to the current list of include
 patterns.

 <p>Patterns may be separated by a comma or a space.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includes</code> - the <code>String</code> containing the include patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="appendIncludes(java.lang.String[])">
<h3>appendIncludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">appendIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</span></div>
<div class="block">Append <code>includes</code> to the current list of include
 patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includes</code> - array containing the include patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludes(java.lang.String)">
<h3>setExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</span></div>
<div class="block">Append <code>excludes</code> to the current list of exclude
 patterns.

 <p>Patterns may be separated by a comma or a space.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - the <code>String</code> containing the exclude patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="appendExcludes(java.lang.String[])">
<h3>appendExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">appendExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</span></div>
<div class="block">Append <code>excludes</code> to the current list of include
 patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - array containing the exclude patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludesfile(java.io.File)">
<h3>setIncludesfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludesfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;incl)</span>
                     throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the <code>File</code> containing the includes patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>incl</code> - <code>File</code> instance.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludesfile(java.io.File)">
<h3>setExcludesfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludesfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;excl)</span>
                     throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Set the <code>File</code> containing the excludes patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excl</code> - <code>File</code> instance.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDefaultexcludes(boolean)">
<h3>setDefaultexcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultexcludes</span><wbr><span class="parameters">(boolean&nbsp;useDefaultExcludes)</span></div>
<div class="block">Set whether default exclusions should be used or not.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>useDefaultExcludes</code> - <code>boolean</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultexcludes()">
<h3>getDefaultexcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDefaultexcludes</span>()</div>
<div class="block">Get whether default exclusions should be used or not.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the defaultexclusions value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCaseSensitive(boolean)">
<h3>setCaseSensitive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCaseSensitive</span><wbr><span class="parameters">(boolean&nbsp;caseSensitive)</span></div>
<div class="block">Set case-sensitivity of the Files collection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>caseSensitive</code> - <code>boolean</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCaseSensitive()">
<h3>isCaseSensitive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCaseSensitive</span>()</div>
<div class="block">Find out if this Files collection is case-sensitive.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>boolean</code> indicating whether the Files
 collection is case-sensitive.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFollowSymlinks(boolean)">
<h3>setFollowSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFollowSymlinks</span><wbr><span class="parameters">(boolean&nbsp;followSymlinks)</span></div>
<div class="block">Set whether or not symbolic links should be followed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>followSymlinks</code> - whether or not symbolic links should be followed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFollowSymlinks()">
<h3>isFollowSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFollowSymlinks</span>()</div>
<div class="block">Find out whether symbolic links should be followed.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>boolean</code> indicating whether symbolic links
         should be followed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="iterator()">
<h3>iterator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</span>&nbsp;<span class="element-name">iterator</span>()</div>
<div class="block">Fulfill the ResourceCollection contract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#iterator()" title="class or interface in java.lang" class="external-link">iterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></dd>
<dt>Returns:</dt>
<dd>an Iterator of Resources.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="size()">
<h3>size</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">size</span>()</div>
<div class="block">Fulfill the ResourceCollection contract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../ResourceCollection.html#size()">size</a></code>&nbsp;in interface&nbsp;<code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
<dt>Returns:</dt>
<dd>number of elements as int.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasPatterns()">
<h3>hasPatterns</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasPatterns</span>()</div>
<div class="block">Find out whether this Files collection has patterns.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether any patterns are in this container.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>appendSelector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">appendSelector</span><wbr><span class="parameters">(<a href="../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</span></div>
<div class="block">Add a new selector into this container.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../selectors/SelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a></code>&nbsp;in interface&nbsp;<code><a href="../selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../selectors/AbstractSelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a></code>&nbsp;in class&nbsp;<code><a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the new <code>FileSelector</code> to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Format this Files collection as a String.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../selectors/AbstractSelectorContainer.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>a descriptive <code>String</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clone()">
<h3>clone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">clone</span>()</div>
<div class="block">Create a deep clone of this instance, except for the nested selectors
 (the list of selectors is a shallow clone of this instance's list).</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../selectors/AbstractSelectorContainer.html#clone()">clone</a></code>&nbsp;in class&nbsp;<code><a href="../selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>a cloned Object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="mergeIncludes(org.apache.tools.ant.Project)">
<h3>mergeIncludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">mergeIncludes</span><wbr><span class="parameters">(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the merged include patterns for this Files collection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - Project instance.</dd>
<dt>Returns:</dt>
<dd>the include patterns of the default pattern set and all
 nested patternsets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="mergeExcludes(org.apache.tools.ant.Project)">
<h3>mergeExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">mergeExcludes</span><wbr><span class="parameters">(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the merged exclude patterns for this Files collection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - Project instance.</dd>
<dt>Returns:</dt>
<dd>the exclude patterns of the default pattern set and all
 nested patternsets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="mergePatterns(org.apache.tools.ant.Project)">
<h3>mergePatterns</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></span>&nbsp;<span class="element-name">mergePatterns</span><wbr><span class="parameters">(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Get the merged patterns for this Files collection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - Project instance.</dd>
<dt>Returns:</dt>
<dd>the default patternset merged with the additional sets
 in a new PatternSet instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFilesystemOnly()">
<h3>isFilesystemOnly</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFilesystemOnly</span>()</div>
<div class="block">Always returns true.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</a></code>&nbsp;in interface&nbsp;<code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
<dt>Returns:</dt>
<dd>true indicating that all elements of a Files collection
              will be FileResources.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRef()">
<h3>getRef</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Files.html" title="class in org.apache.tools.ant.types.resources">Files</a></span>&nbsp;<span class="element-name">getRef</span>()</div>
<div class="block">Perform the check for circular references and return the
 referenced Files collection.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>FileCollection</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
