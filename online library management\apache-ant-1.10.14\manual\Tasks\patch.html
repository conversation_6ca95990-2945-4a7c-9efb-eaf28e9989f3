<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Patch Task</title>
</head>

<body>

<h2 id="patch">Patch</h2>
<h3>Description</h3>
<p>Applies a diff file to originals. Requires <kbd>patch</kbd> to be on the execution path.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>patchfile</td>
    <td>the file that includes the diff output</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>originalfile</td>
    <td>the file to patch</td>
    <td>No; tries to guess it from the diff file</td>
  </tr>
  <tr>
    <td>destfile</td>
    <td>the file to send the output to instead of patching the file(s) in place.  <em>since Apache
      Ant 1.6</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>backups</td>
    <td>Keep backups of the unpatched files</td>
    <td>No</td>
  </tr>
  <tr>
    <td>quiet</td>
    <td>Work silently unless an error occurs</td>
    <td>No</td>
  </tr>
  <tr>
    <td>reverse</td>
    <td>Assume patch was created with old and new files swapped.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ignorewhitespace</td>
    <td>Ignore whitespace differences.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>strip</td>
    <td>Strip the smallest prefix containing <q>num</q> leading slashes from filenames.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>dir</td>
    <td>The directory in which to run the <code>patch</code> command.</td>
    <td>No; default is the project's <var>basedir</var></td>
  </tr>
  <tr>
    <td>failonerror</td>
    <td>Stop the build process if the command exits with a return code signaling failure. <em>since
      Ant 1.8.0</em></td>
    <td>No; defaults to <q>false</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Apply the diff included in <samp>module.1.0-1.1.patch</samp> to the files in base directory
guessing the filename(s) from the diff output.</p>
<pre>&lt;patch patchfile=&quot;module.1.0-1.1.patch&quot;/&gt;</pre>

<p>Here, one leading directory part will be removed:</p>
<pre>&lt;patch patchfile=&quot;module.1.0-1.1.patch&quot; strip=&quot;1&quot;/&gt;</pre>
<p>i.e. if the diff output looked like</p>
<pre>
--- a/mod1.0/A  Mon Jun  5 17:28:41 2000
+++ a/mod1.1/A  Mon Jun  5 17:28:49 2000</pre>
<p>the leading <samp>a/</samp> will be stripped.</p>

</body>
</html>
