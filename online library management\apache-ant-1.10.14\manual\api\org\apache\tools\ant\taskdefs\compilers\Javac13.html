<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Javac13 (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.compilers, class: Javac13">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.compilers</a></div>
<h1 title="Class Javac13" class="title">Class Javac13</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.compilers.Javac13</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></code>, <code><a href="CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Javac13</span>
<span class="extends-implements">extends <a href="DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</a></span></div>
<div class="block">The implementation of the javac compiler for JDK 1.3
 This is primarily a cut-and-paste from the original javac task before it
 was refactored.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.compilers.<a href="DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</a></h3>
<code><a href="DefaultCompilerAdapter.html#attributes">attributes</a>, <a href="DefaultCompilerAdapter.html#bootclasspath">bootclasspath</a>, <a href="DefaultCompilerAdapter.html#compileClasspath">compileClasspath</a>, <a href="DefaultCompilerAdapter.html#compileList">compileList</a>, <a href="DefaultCompilerAdapter.html#compileSourcepath">compileSourcepath</a>, <a href="DefaultCompilerAdapter.html#debug">debug</a>, <a href="DefaultCompilerAdapter.html#depend">depend</a>, <a href="DefaultCompilerAdapter.html#deprecation">deprecation</a>, <a href="DefaultCompilerAdapter.html#destDir">destDir</a>, <a href="DefaultCompilerAdapter.html#encoding">encoding</a>, <a href="DefaultCompilerAdapter.html#extdirs">extdirs</a>, <a href="DefaultCompilerAdapter.html#includeAntRuntime">includeAntRuntime</a>, <a href="DefaultCompilerAdapter.html#includeJavaRuntime">includeJavaRuntime</a>, <a href="DefaultCompilerAdapter.html#location">location</a>, <a href="DefaultCompilerAdapter.html#lSep">lSep</a>, <a href="DefaultCompilerAdapter.html#memoryInitialSize">memoryInitialSize</a>, <a href="DefaultCompilerAdapter.html#memoryMaximumSize">memoryMaximumSize</a>, <a href="DefaultCompilerAdapter.html#modulepath">modulepath</a>, <a href="DefaultCompilerAdapter.html#moduleSourcepath">moduleSourcepath</a>, <a href="DefaultCompilerAdapter.html#optimize">optimize</a>, <a href="DefaultCompilerAdapter.html#project">project</a>, <a href="DefaultCompilerAdapter.html#release">release</a>, <a href="DefaultCompilerAdapter.html#src">src</a>, <a href="DefaultCompilerAdapter.html#target">target</a>, <a href="DefaultCompilerAdapter.html#upgrademodulepath">upgrademodulepath</a>, <a href="DefaultCompilerAdapter.html#verbose">verbose</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Javac13</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the compilation.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.compilers.<a href="DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</a></h3>
<code><a href="DefaultCompilerAdapter.html#addCurrentCompilerArgs(org.apache.tools.ant.types.Commandline)">addCurrentCompilerArgs</a>, <a href="DefaultCompilerAdapter.html#addExtdirsToClasspath(org.apache.tools.ant.types.Path)">addExtdirsToClasspath</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_1Plus()">assumeJava1_1Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_2Plus()">assumeJava1_2Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_3Plus()">assumeJava1_3Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_4Plus()">assumeJava1_4Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_5Plus()">assumeJava1_5Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_6Plus()">assumeJava1_6Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_7Plus()">assumeJava1_7Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava1_8Plus()">assumeJava1_8Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava10Plus()">assumeJava10Plus</a>, <a href="DefaultCompilerAdapter.html#assumeJava11()">assumeJava11</a>, <a href="DefaultCompilerAdapter.html#assumeJava12()">assumeJava12</a>, <a href="DefaultCompilerAdapter.html#assumeJava13()">assumeJava13</a>, <a href="DefaultCompilerAdapter.html#assumeJava14()">assumeJava14</a>, <a href="DefaultCompilerAdapter.html#assumeJava15()">assumeJava15</a>, <a href="DefaultCompilerAdapter.html#assumeJava16()">assumeJava16</a>, <a href="DefaultCompilerAdapter.html#assumeJava17()">assumeJava17</a>, <a href="DefaultCompilerAdapter.html#assumeJava18()">assumeJava18</a>, <a href="DefaultCompilerAdapter.html#assumeJava19()">assumeJava19</a>, <a href="DefaultCompilerAdapter.html#assumeJava9()">assumeJava9</a>, <a href="DefaultCompilerAdapter.html#assumeJava9Plus()">assumeJava9Plus</a>, <a href="DefaultCompilerAdapter.html#executeExternalCompile(java.lang.String%5B%5D,int)">executeExternalCompile</a>, <a href="DefaultCompilerAdapter.html#executeExternalCompile(java.lang.String%5B%5D,int,boolean)">executeExternalCompile</a>, <a href="DefaultCompilerAdapter.html#getBootClassPath()">getBootClassPath</a>, <a href="DefaultCompilerAdapter.html#getCompileClasspath()">getCompileClasspath</a>, <a href="DefaultCompilerAdapter.html#getJavac()">getJavac</a>, <a href="DefaultCompilerAdapter.html#getModulepath()">getModulepath</a>, <a href="DefaultCompilerAdapter.html#getModulesourcepath()">getModulesourcepath</a>, <a href="DefaultCompilerAdapter.html#getNoDebugArgument()">getNoDebugArgument</a>, <a href="DefaultCompilerAdapter.html#getProject()">getProject</a>, <a href="DefaultCompilerAdapter.html#getSupportedFileExtensions()">getSupportedFileExtensions</a>, <a href="DefaultCompilerAdapter.html#getUpgrademodulepath()">getUpgrademodulepath</a>, <a href="DefaultCompilerAdapter.html#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">logAndAddFilesToCompile</a>, <a href="DefaultCompilerAdapter.html#setJavac(org.apache.tools.ant.taskdefs.Javac)">setJavac</a>, <a href="DefaultCompilerAdapter.html#setupJavacCommand()">setupJavacCommand</a>, <a href="DefaultCompilerAdapter.html#setupJavacCommand(boolean)">setupJavacCommand</a>, <a href="DefaultCompilerAdapter.html#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">setupJavacCommandlineSwitches</a>, <a href="DefaultCompilerAdapter.html#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline,boolean)">setupJavacCommandlineSwitches</a>, <a href="DefaultCompilerAdapter.html#setupModernJavacCommand()">setupModernJavacCommand</a>, <a href="DefaultCompilerAdapter.html#setupModernJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">setupModernJavacCommandlineSwitches</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Javac13</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Javac13</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">execute</span>()
                throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the compiler ran with a zero exit result (ok)</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the compilation has problems.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
