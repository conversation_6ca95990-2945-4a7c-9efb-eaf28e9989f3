<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>TarConstants (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.tar, interface: TarConstants">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.tar</a></div>
<h1 title="Interface TarConstants" class="title">Interface TarConstants</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="TarArchiveSparseEntry.html" title="class in org.apache.tools.tar">TarArchiveSparseEntry</a></code>, <code><a href="TarEntry.html" title="class in org.apache.tools.tar">TarEntry</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">TarConstants</span></div>
<div class="block">This interface contains all the definitions used in the package.

 For tar formats (FORMAT_OLDGNU, FORMAT_POSIX, etc.) see GNU tar
 <I>tar.h</I> type <I>enum archive_format</I></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ATIMELEN_GNU" class="member-name-link">ATIMELEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the access time field in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#CHKSUMLEN" class="member-name-link">CHKSUMLEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the checksum field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CTIMELEN_GNU" class="member-name-link">CTIMELEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the created time field in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEVLEN" class="member-name-link">DEVLEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of each of the device fields (major and minor) in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#FORMAT_OLDGNU" class="member-name-link">FORMAT_OLDGNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">GNU format as per before tar 1.12.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#FORMAT_POSIX" class="member-name-link">FORMAT_POSIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Pure Posix format.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#GIDLEN" class="member-name-link">GIDLEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the group id field in a header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#GNAMELEN" class="member-name-link">GNAMELEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the group name field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#GNU_LONGLINK" class="member-name-link">GNU_LONGLINK</a></code></div>
<div class="col-last even-row-color">
<div class="block">The name of the GNU tar entry which contains a long name.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#GNU_TMAGIC" class="member-name-link">GNU_TMAGIC</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The magic tag representing a GNU tar archive.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#ISEXTENDEDLEN_GNU" class="member-name-link">ISEXTENDEDLEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the is extension field in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#ISEXTENDEDLEN_GNU_SPARSE" class="member-name-link">ISEXTENDEDLEN_GNU_SPARSE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the is extension field in a sparse header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_BLK" class="member-name-link">LF_BLK</a></code></div>
<div class="col-last even-row-color">
<div class="block">Block device file type.</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_CHR" class="member-name-link">LF_CHR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Character device file type.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_CONTIG" class="member-name-link">LF_CONTIG</a></code></div>
<div class="col-last even-row-color">
<div class="block">Contiguous file type.</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_DIR" class="member-name-link">LF_DIR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Directory file type.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_FIFO" class="member-name-link">LF_FIFO</a></code></div>
<div class="col-last even-row-color">
<div class="block">FIFO (pipe) file type.</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_GNUTYPE_LONGLINK" class="member-name-link">LF_GNUTYPE_LONGLINK</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Identifies the *next* file on the tape as having a long linkname.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_GNUTYPE_LONGNAME" class="member-name-link">LF_GNUTYPE_LONGNAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">Identifies the *next* file on the tape as having a long name.</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_GNUTYPE_SPARSE" class="member-name-link">LF_GNUTYPE_SPARSE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Sparse file type.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_LINK" class="member-name-link">LF_LINK</a></code></div>
<div class="col-last even-row-color">
<div class="block">Link file type.</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_NORMAL" class="member-name-link">LF_NORMAL</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Normal file type.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_OLDNORM" class="member-name-link">LF_OLDNORM</a></code></div>
<div class="col-last even-row-color">
<div class="block">LF_ constants represent the "link flag" of an entry, or more commonly,
 the "entry type".</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_PAX_EXTENDED_HEADER_LC" class="member-name-link">LF_PAX_EXTENDED_HEADER_LC</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Identifies the entry as a Pax extended header.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_PAX_EXTENDED_HEADER_UC" class="member-name-link">LF_PAX_EXTENDED_HEADER_UC</a></code></div>
<div class="col-last even-row-color">
<div class="block">Identifies the entry as a Pax extended header (SunOS tar -E).</div>
</div>
<div class="col-first odd-row-color"><code>static final byte</code></div>
<div class="col-second odd-row-color"><code><a href="#LF_PAX_GLOBAL_EXTENDED_HEADER" class="member-name-link">LF_PAX_GLOBAL_EXTENDED_HEADER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Identifies the entry as a Pax global extended header.</div>
</div>
<div class="col-first even-row-color"><code>static final byte</code></div>
<div class="col-second even-row-color"><code><a href="#LF_SYMLINK" class="member-name-link">LF_SYMLINK</a></code></div>
<div class="col-last even-row-color">
<div class="block">Symbolic link file type.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LONGNAMESLEN_GNU" class="member-name-link">LONGNAMESLEN_GNU</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the long names field in an old GNU header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MAGIC_OFFSET" class="member-name-link">MAGIC_OFFSET</a></code></div>
<div class="col-last even-row-color">
<div class="block">Offset of start of magic field within header record</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#MAGIC_POSIX" class="member-name-link">MAGIC_POSIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The magic tag representing a POSIX tar archive.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MAGICLEN" class="member-name-link">MAGICLEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the magic field in a header buffer including the version.</div>
</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#MAXID" class="member-name-link">MAXID</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The maximum value of gid/uid in a tar archive which can
 be expressed in octal char notation (that's 7 sevens, octal).</div>
</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#MAXSIZE" class="member-name-link">MAXSIZE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The maximum size of a file in a tar archive
 which can be expressed in octal char notation (that's 11 sevens, octal).</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MODELEN" class="member-name-link">MODELEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the mode field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MODTIMELEN" class="member-name-link">MODTIMELEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the modification time field in a header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NAMELEN" class="member-name-link">NAMELEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the name field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#OFFSETLEN_GNU" class="member-name-link">OFFSETLEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the multivolume start offset field in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PAD2LEN_GNU" class="member-name-link">PAD2LEN_GNU</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the padding field in an old GNU header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#PREFIXLEN" class="member-name-link">PREFIXLEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">Length of the prefix field.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#PURE_MAGICLEN" class="member-name-link">PURE_MAGICLEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the magic field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#REALSIZELEN_GNU" class="member-name-link">REALSIZELEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the real size field in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SIZELEN" class="member-name-link">SIZELEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the size field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SPARSELEN_GNU" class="member-name-link">SPARSELEN_GNU</a></code></div>
<div class="col-last even-row-color">
<div class="block">The sum of the length of all sparse headers in an old GNU header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SPARSELEN_GNU_SPARSE" class="member-name-link">SPARSELEN_GNU_SPARSE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The sum of the length of all sparse headers in a sparse header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TMAGIC" class="member-name-link">TMAGIC</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#UIDLEN" class="member-name-link">UIDLEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The length of the user id field in a header buffer.</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#UNAMELEN" class="member-name-link">UNAMELEN</a></code></div>
<div class="col-last even-row-color">
<div class="block">The length of the user name field in a header buffer.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#VERSION_GNU_SPACE" class="member-name-link">VERSION_GNU_SPACE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#VERSION_GNU_ZERO" class="member-name-link">VERSION_GNU_ZERO</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VERSION_OFFSET" class="member-name-link">VERSION_OFFSET</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Offset of start of magic field within header record</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#VERSION_POSIX" class="member-name-link">VERSION_POSIX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#VERSIONLEN" class="member-name-link">VERSIONLEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Previously this was regarded as part of "magic" field, but it
 is separate.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="FORMAT_OLDGNU">
<h3>FORMAT_OLDGNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FORMAT_OLDGNU</span></div>
<div class="block">GNU format as per before tar 1.12.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.FORMAT_OLDGNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="FORMAT_POSIX">
<h3>FORMAT_POSIX</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">FORMAT_POSIX</span></div>
<div class="block">Pure Posix format.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.FORMAT_POSIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NAMELEN">
<h3>NAMELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NAMELEN</span></div>
<div class="block">The length of the name field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.NAMELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MODELEN">
<h3>MODELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODELEN</span></div>
<div class="block">The length of the mode field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MODELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="UIDLEN">
<h3>UIDLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">UIDLEN</span></div>
<div class="block">The length of the user id field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.UIDLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GIDLEN">
<h3>GIDLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GIDLEN</span></div>
<div class="block">The length of the group id field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GIDLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAXID">
<h3>MAXID</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">MAXID</span></div>
<div class="block">The maximum value of gid/uid in a tar archive which can
 be expressed in octal char notation (that's 7 sevens, octal).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAXID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CHKSUMLEN">
<h3>CHKSUMLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CHKSUMLEN</span></div>
<div class="block">The length of the checksum field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.CHKSUMLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SIZELEN">
<h3>SIZELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SIZELEN</span></div>
<div class="block">The length of the size field in a header buffer.
 Includes the trailing space or NUL.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.SIZELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAXSIZE">
<h3>MAXSIZE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">MAXSIZE</span></div>
<div class="block">The maximum size of a file in a tar archive
 which can be expressed in octal char notation (that's 11 sevens, octal).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAXSIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAGIC_OFFSET">
<h3>MAGIC_OFFSET</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAGIC_OFFSET</span></div>
<div class="block">Offset of start of magic field within header record</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAGIC_OFFSET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAGICLEN">
<h3>MAGICLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAGICLEN</span></div>
<div class="block">The length of the magic field in a header buffer including the version.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAGICLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PURE_MAGICLEN">
<h3>PURE_MAGICLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PURE_MAGICLEN</span></div>
<div class="block">The length of the magic field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.PURE_MAGICLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERSION_OFFSET">
<h3>VERSION_OFFSET</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VERSION_OFFSET</span></div>
<div class="block">Offset of start of magic field within header record</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.VERSION_OFFSET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERSIONLEN">
<h3>VERSIONLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">VERSIONLEN</span></div>
<div class="block">Previously this was regarded as part of "magic" field, but it
 is separate.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.VERSIONLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MODTIMELEN">
<h3>MODTIMELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MODTIMELEN</span></div>
<div class="block">The length of the modification time field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MODTIMELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="UNAMELEN">
<h3>UNAMELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">UNAMELEN</span></div>
<div class="block">The length of the user name field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.UNAMELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GNAMELEN">
<h3>GNAMELEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GNAMELEN</span></div>
<div class="block">The length of the group name field in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNAMELEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEVLEN">
<h3>DEVLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEVLEN</span></div>
<div class="block">The length of each of the device fields (major and minor) in a header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.DEVLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PREFIXLEN">
<h3>PREFIXLEN</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PREFIXLEN</span></div>
<div class="block">Length of the prefix field.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.PREFIXLEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATIMELEN_GNU">
<h3>ATIMELEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ATIMELEN_GNU</span></div>
<div class="block">The length of the access time field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.ATIMELEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CTIMELEN_GNU">
<h3>CTIMELEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CTIMELEN_GNU</span></div>
<div class="block">The length of the created time field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.CTIMELEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="OFFSETLEN_GNU">
<h3>OFFSETLEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">OFFSETLEN_GNU</span></div>
<div class="block">The length of the multivolume start offset field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.OFFSETLEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LONGNAMESLEN_GNU">
<h3>LONGNAMESLEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LONGNAMESLEN_GNU</span></div>
<div class="block">The length of the long names field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LONGNAMESLEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PAD2LEN_GNU">
<h3>PAD2LEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">PAD2LEN_GNU</span></div>
<div class="block">The length of the padding field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.PAD2LEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SPARSELEN_GNU">
<h3>SPARSELEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SPARSELEN_GNU</span></div>
<div class="block">The sum of the length of all sparse headers in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.SPARSELEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ISEXTENDEDLEN_GNU">
<h3>ISEXTENDEDLEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ISEXTENDEDLEN_GNU</span></div>
<div class="block">The length of the is extension field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.ISEXTENDEDLEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REALSIZELEN_GNU">
<h3>REALSIZELEN_GNU</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">REALSIZELEN_GNU</span></div>
<div class="block">The length of the real size field in an old GNU header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.REALSIZELEN_GNU">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SPARSELEN_GNU_SPARSE">
<h3>SPARSELEN_GNU_SPARSE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SPARSELEN_GNU_SPARSE</span></div>
<div class="block">The sum of the length of all sparse headers in a sparse header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.SPARSELEN_GNU_SPARSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ISEXTENDEDLEN_GNU_SPARSE">
<h3>ISEXTENDEDLEN_GNU_SPARSE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">ISEXTENDEDLEN_GNU_SPARSE</span></div>
<div class="block">The length of the is extension field in a sparse header buffer.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.ISEXTENDEDLEN_GNU_SPARSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_OLDNORM">
<h3>LF_OLDNORM</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_OLDNORM</span></div>
<div class="block">LF_ constants represent the "link flag" of an entry, or more commonly,
 the "entry type". This is the "old way" of indicating a normal file.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_OLDNORM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_NORMAL">
<h3>LF_NORMAL</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_NORMAL</span></div>
<div class="block">Normal file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_NORMAL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_LINK">
<h3>LF_LINK</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_LINK</span></div>
<div class="block">Link file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_LINK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_SYMLINK">
<h3>LF_SYMLINK</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_SYMLINK</span></div>
<div class="block">Symbolic link file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_SYMLINK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_CHR">
<h3>LF_CHR</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_CHR</span></div>
<div class="block">Character device file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_CHR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_BLK">
<h3>LF_BLK</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_BLK</span></div>
<div class="block">Block device file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_BLK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_DIR">
<h3>LF_DIR</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_DIR</span></div>
<div class="block">Directory file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_FIFO">
<h3>LF_FIFO</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_FIFO</span></div>
<div class="block">FIFO (pipe) file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_FIFO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_CONTIG">
<h3>LF_CONTIG</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_CONTIG</span></div>
<div class="block">Contiguous file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_CONTIG">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_GNUTYPE_LONGLINK">
<h3>LF_GNUTYPE_LONGLINK</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_GNUTYPE_LONGLINK</span></div>
<div class="block">Identifies the *next* file on the tape as having a long linkname.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_GNUTYPE_LONGLINK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_GNUTYPE_LONGNAME">
<h3>LF_GNUTYPE_LONGNAME</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_GNUTYPE_LONGNAME</span></div>
<div class="block">Identifies the *next* file on the tape as having a long name.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_GNUTYPE_LONGNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_GNUTYPE_SPARSE">
<h3>LF_GNUTYPE_SPARSE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_GNUTYPE_SPARSE</span></div>
<div class="block">Sparse file type.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_GNUTYPE_SPARSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_PAX_EXTENDED_HEADER_LC">
<h3>LF_PAX_EXTENDED_HEADER_LC</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_PAX_EXTENDED_HEADER_LC</span></div>
<div class="block">Identifies the entry as a Pax extended header.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_PAX_EXTENDED_HEADER_LC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_PAX_EXTENDED_HEADER_UC">
<h3>LF_PAX_EXTENDED_HEADER_UC</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_PAX_EXTENDED_HEADER_UC</span></div>
<div class="block">Identifies the entry as a Pax extended header (SunOS tar -E).</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_PAX_EXTENDED_HEADER_UC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LF_PAX_GLOBAL_EXTENDED_HEADER">
<h3>LF_PAX_GLOBAL_EXTENDED_HEADER</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type">byte</span>&nbsp;<span class="element-name">LF_PAX_GLOBAL_EXTENDED_HEADER</span></div>
<div class="block">Identifies the entry as a Pax global extended header.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_PAX_GLOBAL_EXTENDED_HEADER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TMAGIC">
<h3>TMAGIC</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TMAGIC</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.TMAGIC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAGIC_POSIX">
<h3>MAGIC_POSIX</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">MAGIC_POSIX</span></div>
<div class="block">The magic tag representing a POSIX tar archive.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAGIC_POSIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERSION_POSIX">
<h3>VERSION_POSIX</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">VERSION_POSIX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.VERSION_POSIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GNU_TMAGIC">
<h3>GNU_TMAGIC</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">GNU_TMAGIC</span></div>
<div class="block">The magic tag representing a GNU tar archive.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNU_TMAGIC">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERSION_GNU_SPACE">
<h3>VERSION_GNU_SPACE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">VERSION_GNU_SPACE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.VERSION_GNU_SPACE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="VERSION_GNU_ZERO">
<h3>VERSION_GNU_ZERO</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">VERSION_GNU_ZERO</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.VERSION_GNU_ZERO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GNU_LONGLINK">
<h3>GNU_LONGLINK</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">GNU_LONGLINK</span></div>
<div class="block">The name of the GNU tar entry which contains a long name.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNU_LONGLINK">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
