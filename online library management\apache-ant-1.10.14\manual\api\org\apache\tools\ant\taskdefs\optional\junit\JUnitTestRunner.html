<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JUnitTestRunner (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: JUnitTestRunner">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class JUnitTestRunner" class="title">Class JUnitTestRunner</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.JUnitTestRunner</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>junit.framework.TestListener</code>, <code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JUnitTestRunner</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements junit.framework.TestListener, <a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></span></div>
<div class="block">Simple Testrunner for JUnit that runs all tests of a testsuite.

 <p>This TestRunner expects a name of a TestCase class as its
 argument. If this class provides a static suite() method it will be
 called and the resulting Test will be run. So, the signature should be
 <pre>
     public static junit.framework.Test suite()
 </pre>

 <p>If no such method exists, all public methods starting with
 "test" and taking no argument will be run.</p>

 <p>Summary output is generated at the end.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitTestRunnerMirror">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></h3>
<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#ERRORS">ERRORS</a>, <a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#FAILURES">FAILURES</a>, <a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#IGNORED_FILE_NAME">IGNORED_FILE_NAME</a>, <a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#SUCCESS">SUCCESS</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,boolean)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,boolean,java.lang.ClassLoader)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor to use when the user has specified a classpath.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,java.lang.ClassLoader)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor to use when the user has specified a classpath.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,java.lang.ClassLoader)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor to use when the user has specified a classpath.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String%5B%5D,boolean,boolean,boolean,boolean,boolean)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String%5B%5D,boolean,boolean,boolean,boolean,boolean,java.lang.ClassLoader)" class="member-name-link">JUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor to use when the user has specified a classpath.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addError(junit.framework.Test,java.lang.Throwable)" class="member-name-link">addError</a><wbr>(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFailure(junit.framework.Test,java.lang.Throwable)" class="member-name-link">addFailure</a><wbr>(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener for JUnit &lt;= 3.4.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFailure(junit.framework.Test,junit.framework.AssertionFailedError)" class="member-name-link">addFailure</a><wbr>(junit.framework.Test&nbsp;test,
 junit.framework.AssertionFailedError&nbsp;t)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener for JUnit &gt; 3.4.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.JUnitResultFormatter)" class="member-name-link">addFormatter</a><wbr>(<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>&nbsp;f)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a formatter.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror)" class="member-name-link">addFormatter</a><wbr>(<a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;f)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a formatter to the test.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endTest(junit.framework.Test)" class="member-name-link">endTest</a><wbr>(junit.framework.Test&nbsp;test)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#filterStack(java.lang.String)" class="member-name-link">filterStack</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stack)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Filters stack frames from internal JUnit and Ant classes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getFilteredTrace(java.lang.Throwable)" class="member-name-link">getFilteredTrace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a filtered stack trace.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRetCode()" class="member-name-link">getRetCode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns what System.exit() would return in the standalone version.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorFlush(java.lang.String)" class="member-name-link">handleErrorFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle output sent to System.err.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorOutput(java.lang.String)" class="member-name-link">handleErrorOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle output sent to System.err.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleFlush(java.lang.String)" class="member-name-link">handleFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle output sent to System.out.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleInput(byte%5B%5D,int,int)" class="member-name-link">handleInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle input.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleOutput(java.lang.String)" class="member-name-link">handleOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle a string destined for standard output.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#main(java.lang.String%5B%5D)" class="member-name-link">main</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Entry point for standalone (forked) mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#run()" class="member-name-link">run</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPermissions(org.apache.tools.ant.types.Permissions)" class="member-name-link">setPermissions</a><wbr>(<a href="../../../types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a>&nbsp;permissions)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Permissions for the test run.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startTest(junit.framework.Test)" class="member-name-link">startTest</a><wbr>(junit.framework.Test&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure)</span></div>
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput)</span></div>
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>showOutput</code> - whether to send output to System.out/.err as well as formatters.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,boolean)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents)</span></div>
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>showOutput</code> - whether to send output to System.out/.err as well as formatters.</dd>
<dd><code>logTestListenerEvents</code> - whether to print TestListener events.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String[],boolean,boolean,boolean,boolean,boolean)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents)</span></div>
<div class="block">Constructor for fork=true or when the user hasn't specified a
 classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>methods</code> - names of methods of the test to be executed.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>showOutput</code> - whether to send output to System.out/.err as well as formatters.</dd>
<dd><code>logTestListenerEvents</code> - whether to print TestListener events.</dd>
<dt>Since:</dt>
<dd>1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,java.lang.ClassLoader)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Constructor to use when the user has specified a classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>loader</code> - the classloader to use running the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,java.lang.ClassLoader)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Constructor to use when the user has specified a classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>showOutput</code> - whether to send output to System.out/.err as well as formatters.</dd>
<dd><code>loader</code> - the classloader to use running the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,boolean,boolean,boolean,boolean,boolean,java.lang.ClassLoader)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Constructor to use when the user has specified a classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>haltOnError</code> - whether to stop the run if an error is found.</dd>
<dd><code>filtertrace</code> - whether to filter junit.*.* stack frames out of exceptions</dd>
<dd><code>haltOnFailure</code> - whether to stop the run if failure is found.</dd>
<dd><code>showOutput</code> - whether to send output to System.out/.err as well as formatters.</dd>
<dd><code>logTestListenerEvents</code> - whether to print TestListener events.</dd>
<dd><code>loader</code> - the classloader to use running the test.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String[],boolean,boolean,boolean,boolean,boolean,java.lang.ClassLoader)">
<h3>JUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filtertrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;loader)</span></div>
<div class="block">Constructor to use when the user has specified a classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - JUnitTest</dd>
<dd><code>methods</code> - String[]</dd>
<dd><code>haltOnError</code> - boolean</dd>
<dd><code>filtertrace</code> - boolean</dd>
<dd><code>haltOnFailure</code> - boolean</dd>
<dd><code>showOutput</code> - boolean</dd>
<dd><code>logTestListenerEvents</code> - boolean</dd>
<dd><code>loader</code> - ClassLoader</dd>
<dt>Since:</dt>
<dd>1.8.2</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="run()">
<h3>run</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">run</span>()</div>
<div class="block">Run the test.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#run()">run</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRetCode()">
<h3>getRetCode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getRetCode</span>()</div>
<div class="block">Returns what System.exit() would return in the standalone version.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#getRetCode()">getRetCode</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Returns:</dt>
<dd>2 if errors occurred, 1 if tests failed else 0.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startTest(junit.framework.Test)">
<h3>startTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startTest</span><wbr><span class="parameters">(junit.framework.Test&nbsp;t)</span></div>
<div class="block">Interface TestListener.

 <p>A new Test is started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>startTest</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>t</code> - the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endTest(junit.framework.Test)">
<h3>endTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endTest</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test)</span></div>
<div class="block">Interface TestListener.

 <p>A Test is finished.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>endTest</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFailure(junit.framework.Test,java.lang.Throwable)">
<h3>addFailure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFailure</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</span></div>
<div class="block">Interface TestListener for JUnit &lt;= 3.4.

 <p>A Test failed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
<dd><code>t</code> - the exception thrown by the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFailure(junit.framework.Test,junit.framework.AssertionFailedError)">
<h3>addFailure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFailure</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 junit.framework.AssertionFailedError&nbsp;t)</span></div>
<div class="block">Interface TestListener for JUnit &gt; 3.4.

 <p>A Test failed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addFailure</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
<dd><code>t</code> - the assertion thrown by the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addError(junit.framework.Test,java.lang.Throwable)">
<h3>addError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addError</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</span></div>
<div class="block">Interface TestListener.

 <p>An error occurred while running the test.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addError</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
<dd><code>t</code> - the error thrown by the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPermissions(org.apache.tools.ant.types.Permissions)">
<h3>setPermissions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPermissions</span><wbr><span class="parameters">(<a href="../../../types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</a>&nbsp;permissions)</span></div>
<div class="block">Permissions for the test run.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#setPermissions(org.apache.tools.ant.types.Permissions)">setPermissions</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>permissions</code> - the permissions to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleOutput(java.lang.String)">
<h3>handleOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handle a string destined for standard output.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#handleOutput(java.lang.String)">handleOutput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - the string to output</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleInput(byte[],int,int)">
<h3>handleInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">handleInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Handle input.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#handleInput(byte%5B%5D,int,int)">handleInput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buffer</code> - not used.</dd>
<dd><code>offset</code> - not used.</dd>
<dd><code>length</code> - not used.</dd>
<dt>Returns:</dt>
<dd>-1 always.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - never.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../../../Task.html#handleInput(byte%5B%5D,int,int)"><code>Task.handleInput(byte[], int, int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorOutput(java.lang.String)">
<h3>handleErrorOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handle output sent to System.err..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - output for System.err</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleFlush(java.lang.String)">
<h3>handleFlush</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handle output sent to System.out..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#handleFlush(java.lang.String)">handleFlush</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - output for System.out.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorFlush(java.lang.String)">
<h3>handleErrorFlush</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handle output sent to System.err..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>output</code> - coming from System.err</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFormatter(org.apache.tools.ant.taskdefs.optional.junit.JUnitResultFormatter)">
<h3>addFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFormatter</span><wbr><span class="parameters">(<a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>&nbsp;f)</span></div>
<div class="block">Add a formatter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>f</code> - the formatter to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFormatter(org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror)">
<h3>addFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFormatter</span><wbr><span class="parameters">(<a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;f)</span></div>
<div class="block">Add a formatter to the test..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror)">addFormatter</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>f</code> - the formatter to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="main(java.lang.String[])">
<h3>main</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">main</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Entry point for standalone (forked) mode.
 <p>
 Parameters: testcaseclassname plus parameters in the format
 key=value, none of which is required.
 </p>
 <table border="1">
 <caption>Test runner attributes</caption>
 <tr>
 <th>key</th><th>description</th><th>default value</th>
 </tr>
 <tr>
 <td>haltOnError</td><td>halt test on errors?</td><td>false</td>
 </tr>
 <tr>
 <td>haltOnFailure</td><td>halt test on failures?</td><td>false</td>
 </tr>
 <tr>
 <td>formatter</td><td>A JUnitResultFormatter given as
 classname,filename. If filename is omitted, System.out is
 assumed.</td><td>none</td>
 </tr>
 <tr>
 <td>showoutput</td><td>send output to System.err/.out as
 well as to the formatters?</td><td>false</td>
 </tr>
 <tr>
 <td>logtestlistenerevents</td><td>log TestListener events to
 System.out.</td><td>false</td>
 </tr>
 <tr>
 <td>methods</td><td>Comma-separated list of names of individual
 test methods to execute.</td><td>null</td>
 </tr>
 </table></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - the command line arguments.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFilteredTrace(java.lang.Throwable)">
<h3>getFilteredTrace</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFilteredTrace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</span></div>
<div class="block">Returns a filtered stack trace.
 This is ripped out of junit.runner.BaseTestRunner.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t</code> - the exception to filter.</dd>
<dt>Returns:</dt>
<dd>the filtered stack trace.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="filterStack(java.lang.String)">
<h3>filterStack</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">filterStack</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stack)</span></div>
<div class="block">Filters stack frames from internal JUnit and Ant classes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stack</code> - the stack trace to filter.</dd>
<dt>Returns:</dt>
<dd>the filtered stack.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
