<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>InputHandler</title>
</head>

<body>
<h1>Using Apache Ant&trade; Tasks Outside of Ant</h1>

<h2>Rationale</h2>

<p>Apache Ant provides a rich set of tasks for buildfile creators and
administrators.  But what about programmers?  Can the functionality
provided by Ant tasks be used in Java programs?</p>

<p>Yes, and its quite easy.  Before getting into the details, however,
we should mention the pros and cons of this approach:

<h3>Pros</h3>

<table>
<tr>
<td><strong>Robust</strong></td>
<td>
Ant tasks are very robust.  They have been banged on by many people.
Ant tasks have been used in many different contexts, and have
therefore been instrumented to take care of a great many boundary
conditions and potentially obscure errors.
</td>
</tr>
<tr>
<td><strong>Cross Platform</strong></td>
<td>
Ant tasks are cross platform.  They have been tested on all of the
volume platforms, and several rather unusual ones (Netware and OS/390, to
name a few).
</td>
</tr>
<tr>
<td><strong>Community Support</strong></td>
<td>
Using Ant tasks means you have less of your own code to support.  Ant
code is supported by the entire Apache Ant community.
</td>
</tr>
</table>

<h3>Cons</h3>

<table>
<tr>
<td><strong>Dependency on Ant Libraries</strong></td>
<td>
Obviously, if you use an Ant task in your code, you will have to add
<q>ant.jar</q> to your path.  Of course, you could use a code optimizer to
remove the unnecessary classes, but you will still probably require a
chunk of the Ant core.
</td>
</tr>
<tr>
<td><strong>Loss of Flexibility</strong></td>
<td>
At some point, if you find yourself having to modify the Ant code, it
probably makes more sense to "roll your own."  Of course, you can
still steal some code snippets and good ideas.  This is the beauty of
open source!
</td>
</tr>
</table>

<h2>Example</h2>

<p>Let's say you want to unzip a zip file programmatically from java
into a certain directory.  Of course you could write your own routine
to do this, but why not use the Ant task that has already been written?</p>

<p>In my example, I wanted to be able to unzip a file from within an
XSLT Transformation.  XSLT Transformers can be extended by plugging in
static methods in java.  I therefore need a function something like
this:</p>

<pre>
/**
 * Unzip a zip file into a given directory.
 *
 * @param zipFilepath A pathname representing a local zip file
 * @param destinationDir where to unzip the archive to
 */
 static public void unzip(String zipFilepath, String destinationDir)
</pre>

<p>
The Ant task to perform this function
is <code>org.apache.tools.ant.taskdefs.Expand</code>.  All we have to do
is create a dummy Ant <code>Project</code> and <code>Target</code>,
set the <code>Task</code> parameters that would normally be set in a
buildfile, and call <code>execute()</code>.</p>

<p>First, let's make sure we have the proper includes:</p>

<pre>
import org.apache.tools.ant.Project;
import org.apache.tools.ant.Target;
import org.apache.tools.ant.taskdefs.Expand;
import java.io.File;</pre>

<p>The function call is actually quite simple:</p>

<pre>
static public void unzip(String zipFilepath, String destinationDir) {

    final class Expander extends Expand {
        public Expander() {
            project = new Project();
            project.init();
            taskType = "unzip";
            taskName = "unzip";
            target = new Target();
        }
    }
    Expander expander = new Expander();
    expander.setSrc(new File(zipfile));
    expander.setDest(new File(destdir));
    expander.execute();
}</pre>

<p>In actual practice, you will probably want to add your own error
handling code and you may not want to use a local inner class.
However, the point of the example is to show how an Ant task can be
called programmatically in relatively few lines of code.</p>

<p>The question you are probably asking yourself at this point
is: <em>How would I know which classes and methods have to be called in
order to set up a dummy Project and Target?</em> The answer is: you
don't.  Ultimately, you have to be willing to get your feet wet and
read the source code.  The above example is merely designed to whet
your appetite and get you started.  Go for it!</p>

</body>
</html>
