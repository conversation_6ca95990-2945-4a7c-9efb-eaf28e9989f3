<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>FileList Type</title>
</head>

<body>

<h2 id="filelist">FileList</h2>

<p>FileLists are explicitly named lists of files.  Whereas FileSets act as filters, returning
only those files that exist in the file system and match specified patterns, FileLists are
useful for specifying files that may or may not exist.  Multiple files are specified as a list
of files, relative to the specified directory, with no support for wildcard expansion (filenames
with wildcards will be included in the list unchanged).  FileLists can appear inside tasks that
support this feature or as stand-alone types.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>dir</td>
    <td>The base directory of this FileList.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>files</td>
    <td>The list of file names. This is a list of file name separated by whitespace, or by
    commas.</td>
    <td>Yes, unless there is a nested file element</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>filelist</code>
      a <a href="../using.html#references">reference</a> to
      a <code>filelist</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<h4>Nested element: file</h4>
<p><em>Since Apache Ant 1.6.2</em></p>
<p>This represents a file name. The nested element allows filenames containing white space and
commas.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>name</td>
    <td>The name of the file.</td>
    <td>Yes</td>
  </tr>
</table>
<h4>Examples</h4>
<pre>
&lt;filelist
    id=&quot;docfiles&quot;
    dir=&quot;${doc.src}&quot;
    files=&quot;foo.xml,bar.xml&quot;/&gt;</pre>

<p>The files <samp>${doc.src}/foo.xml</samp> and <samp>${doc.src}/bar.xml</samp>.  Note that
these files may not (yet) actually exist.</p>

<pre>
&lt;filelist
    id=&quot;docfiles&quot;
    dir=&quot;${doc.src}&quot;
    files=&quot;foo.xml
           bar.xml&quot;/&gt;</pre>

<p>Same files as the example above.</p>

<pre>&lt;filelist refid=&quot;docfiles&quot;/&gt;</pre>

<p>Same files as the example above.</p>

<pre>
&lt;filelist
    id=&quot;docfiles&quot;
    dir=&quot;${doc.src}&quot;&gt;
    &lt;file name="foo.xml"/&gt;
    &lt;file name="bar.xml"/&gt;
&lt;/filelist&gt;</pre>

<p>Same files as the example above.</p>

</body>
</html>
