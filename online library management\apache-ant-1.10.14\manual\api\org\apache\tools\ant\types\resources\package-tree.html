<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.types.resources Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="tree: package: org.apache.tools.ant.types.resources">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.types.resources</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AbstractClasspathResource.ClassLoaderWithFlag.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource.ClassLoaderWithFlag</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="FileResourceIterator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">FileResourceIterator</a> (implements java.util.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a>&lt;E&gt;)</li>
<li class="circle">org.apache.tools.ant.<a href="../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../DataType.html" class="type-name-link" title="class in org.apache.tools.ant.types">DataType</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../AbstractFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">AbstractFileSet</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="../selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../FileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types">FileSet</a> (implements org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="BCFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BCFileSet</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="MultiRootFileSet.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MultiRootFileSet</a> (implements org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AbstractResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractResourceCollectionWrapper</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="BaseResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionWrapper</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="SizeLimitCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">SizeLimitCollection</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AllButFirst.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AllButFirst</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AllButLast.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AllButLast</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="First.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">First</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Last.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Last</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Sort.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Sort</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Tokens.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Tokens</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="LazyResourceCollectionWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">LazyResourceCollectionWrapper</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="../selectors/AbstractSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="../selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Files.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Files</a> (implements org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Archives.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Archives</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="BaseResourceCollectionContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BaseResourceCollectionContainer</a> (implements org.apache.tools.ant.types.resources.<a href="AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a>, java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Difference.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Difference</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Intersect.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Intersect</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Union.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Union</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="MappedResourceCollection.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MappedResourceCollection</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.<a href="../Resource.html" class="type-name-link" title="class in org.apache.tools.ant.types">Resource</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AbstractClasspathResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">AbstractClasspathResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="JavaConstantResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">JavaConstantResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="JavaResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">JavaResource</a> (implements org.apache.tools.ant.types.resources.<a href="URLProvider.html" title="interface in org.apache.tools.ant.types.resources">URLProvider</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ArchiveResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ArchiveResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="TarResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">TarResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ZipResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ZipResource</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="FileResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">FileResource</a> (implements org.apache.tools.ant.types.resources.<a href="Appendable.html" title="interface in org.apache.tools.ant.types.resources">Appendable</a>, org.apache.tools.ant.types.resources.<a href="FileProvider.html" title="interface in org.apache.tools.ant.types.resources">FileProvider</a>, org.apache.tools.ant.types.<a href="../ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>, org.apache.tools.ant.types.resources.<a href="Touchable.html" title="interface in org.apache.tools.ant.types.resources">Touchable</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="LogOutputResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">LogOutputResource</a> (implements org.apache.tools.ant.types.resources.<a href="Appendable.html" title="interface in org.apache.tools.ant.types.resources">Appendable</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="PropertyResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">PropertyResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ResourceDecorator.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ContentTransformingResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ContentTransformingResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="CompressedResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">CompressedResource</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="BZip2Resource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">BZip2Resource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="GZipResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">GZipResource</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="MappedResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">MappedResource</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="StringResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">StringResource</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="URLResource.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">URLResource</a> (implements org.apache.tools.ant.types.resources.<a href="URLProvider.html" title="interface in org.apache.tools.ant.types.resources">URLProvider</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ResourceList.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ResourceList</a> (implements org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Resources.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Resources</a> (implements org.apache.tools.ant.types.resources.<a href="AppendableResourceCollection.html" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a>)</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="selectors/ResourceSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources.selectors">ResourceSelectorContainer</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Restrict.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">Restrict</a> (implements org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" class="type-name-link external-link" title="class or interface in java.io">IOException</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="ImmutableResourceException.html" class="type-name-link" title="class in org.apache.tools.ant.types.resources">ImmutableResourceException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Appendable.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">Appendable</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="FileProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">FileProvider</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" class="type-name-link external-link" title="class or interface in java.lang">Iterable</a>&lt;T&gt;
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../ResourceCollection.html" class="type-name-link" title="interface in org.apache.tools.ant.types">ResourceCollection</a>
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="AppendableResourceCollection.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">AppendableResourceCollection</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="Touchable.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">Touchable</a></li>
<li class="circle">org.apache.tools.ant.types.resources.<a href="URLProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources">URLProvider</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.resources.<a href="MultiRootFileSet.SetType.html" class="type-name-link" title="enum class in org.apache.tools.ant.types.resources">MultiRootFileSet.SetType</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
