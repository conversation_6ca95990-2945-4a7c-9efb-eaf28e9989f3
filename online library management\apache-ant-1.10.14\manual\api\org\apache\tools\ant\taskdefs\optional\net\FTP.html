<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>FTP (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.net, class: FTP">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.net</a></div>
<h1 title="Class FTP" class="title">Class FTP</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.net.FTP</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FTP</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Basic FTP client. Performs the following actions:
 <ul>
   <li><strong>send</strong> - send files to a remote server. This is the
   default action.</li>
   <li><strong>get</strong> - retrieve files from a remote server.</li>
   <li><strong>del</strong> - delete files from a remote server.</li>
   <li><strong>list</strong> - create a file listing.</li>
   <li><strong>chmod</strong> - change unix file permissions.</li>
   <li><strong>rmdir</strong> - remove directories, if empty, from a
   remote server.</li>
 </ul>
 <strong>Note:</strong> Some FTP servers - notably the Solaris server - seem
 to hold data ports open after a "retr" operation, allowing them to timeout
 instead of shutting them down cleanly. This happens in active or passive
 mode, and the ports will remain open even after ending the FTP session. FTP
 "send" operations seem to close ports immediately. This behavior may cause
 problems on some systems when downloading large sets of files.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FTP.Action.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a></code></div>
<div class="col-last even-row-color">
<div class="block">an action to perform, one of
 "send", "put", "recv", "get", "del", "delete", "list", "mkdir", "chmod",
 "rmdir"</div>
</div>
<div class="col-first odd-row-color"><code>protected class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FTP.FTPDirectoryScanner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</a></code></div>
<div class="col-last odd-row-color">
<div class="block">internal class allowing to read the contents of a remote file system
 using the FTP protocol
 used in particular for ftp get operations
 differences with DirectoryScanner
 "" (the root of the fileset) is never included in the included directories
 followSymlinks defaults to false</div>
</div>
<div class="col-first even-row-color"><code>protected static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FTP.FTPFileProxy.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPFileProxy</a></code></div>
<div class="col-last even-row-color">
<div class="block">internal class providing a File-like interface to some of the information
 available from the FTP server</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FTP.FTPSystemType.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a></code></div>
<div class="col-last odd-row-color">
<div class="block">one of the valid system type keys recognized by the systemTypeKey
 attribute.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FTP.Granularity.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a></code></div>
<div class="col-last even-row-color">
<div class="block">represents one of the valid timestamp adjustment values
 recognized by the <code>timestampGranularity</code> attribute.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FTP.LanguageCode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enumerated class for languages.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color"><code><a href="#ACTION_STRS" class="member-name-link">ACTION_STRS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#ACTION_TARGET_STRS" class="member-name-link">ACTION_TARGET_STRS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#CHMOD" class="member-name-link">CHMOD</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#COMPLETED_ACTION_STRS" class="member-name-link">COMPLETED_ACTION_STRS</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_FTP_PORT" class="member-name-link">DEFAULT_FTP_PORT</a></code></div>
<div class="col-last even-row-color">
<div class="block">Default port for FTP</div>
</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DEL_FILES" class="member-name-link">DEL_FILES</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#GET_FILES" class="member-name-link">GET_FILES</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#LIST_FILES" class="member-name-link">LIST_FILES</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MK_DIR" class="member-name-link">MK_DIR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#RM_DIR" class="member-name-link">RM_DIR</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SEND_FILES" class="member-name-link">SEND_FILES</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#SITE_CMD" class="member-name-link">SITE_CMD</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">FTP</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(javax.net.ssl.HostnameVerifier)" class="member-name-link">add</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/javax/net/ssl/HostnameVerifier.html" title="class or interface in javax.net.ssl" class="external-link">HostnameVerifier</a>&nbsp;hostnameVerifier)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A set of files to upload or download</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkAttributes()" class="member-name-link">checkAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks to see that all required parameters are set.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createParents(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">createParents</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates all parent directories specified in a complete relative
 pathname.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delFile(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">delFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete a file from the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#doSiteCommand(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">doSiteCommand</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theCMD)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends a site command to the ftp server</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Runs the task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeRetryable(org.apache.tools.ant.util.RetryHandler,org.apache.tools.ant.util.Retryable,java.lang.String)" class="member-name-link">executeRetryable</a><wbr>(<a href="../../../util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</a>&nbsp;h,
 <a href="../../../util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</a>&nbsp;r,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executable a retryable object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultDateFormatConfig()" class="member-name-link">getDefaultDateFormatConfig</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)" class="member-name-link">getFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieve a single file from the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRecentDateFormatConfig()" class="member-name-link">getRecentDateFormatConfig</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getServerLanguageCodeConfig()" class="member-name-link">getServerLanguageCodeConfig</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getServerTimeZoneConfig()" class="member-name-link">getServerTimeZoneConfig</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShortMonthNamesConfig()" class="member-name-link">getShortMonthNamesConfig</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSystemTypeKey()" class="member-name-link">getSystemTypeKey</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isUpToDate(org.apache.commons.net.ftp.FTPClient,java.io.File,java.lang.String)" class="member-name-link">isUpToDate</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;localFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;remoteFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks to see if the remote file is current as compared with the local
 file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listFile(org.apache.commons.net.ftp.FTPClient,java.io.BufferedWriter,java.lang.String)" class="member-name-link">listFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/BufferedWriter.html" title="class or interface in java.io" class="external-link">BufferedWriter</a>&nbsp;bw,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">List information about a single file from the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeRemoteDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">makeRemoteDir</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the specified directory on the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveFile(java.lang.String)" class="member-name-link">resolveFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Correct a file path to correspond to the remote host requirements.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#rmDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)" class="member-name-link">rmDir</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete a directory, if empty, from the remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sendFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)" class="member-name-link">sendFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends a single file to the remote host.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAccount(java.lang.String)" class="member-name-link">setAccount</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pAccount)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the login account to use on the specified server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setAction(java.lang.String)" class="member-name-link">setAction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;action)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAction(org.apache.tools.ant.taskdefs.optional.net.FTP.Action)" class="member-name-link">setAction</a><wbr>(<a href="FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a>&nbsp;action)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the FTP action to be taken.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBinary(boolean)" class="member-name-link">setBinary</a><wbr>(boolean&nbsp;binary)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, uses binary mode, otherwise text mode (default is binary).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setChmod(java.lang.String)" class="member-name-link">setChmod</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the file permission mode (Unix only) for files sent to the
 server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDataTimeout(int)" class="member-name-link">setDataTimeout</a><wbr>(int&nbsp;dataTimeout)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the timeout on the data connection in milliseconds.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultDateFormatConfig(java.lang.String)" class="member-name-link">setDefaultDateFormatConfig</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultDateFormat)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the defaultDateFormatConfig attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDepends(boolean)" class="member-name-link">setDepends</a><wbr>(boolean&nbsp;depends)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set to true to transmit only files that are new or changed from their
 remote counterparts.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEnableRemoteVerification(boolean)" class="member-name-link">setEnableRemoteVerification</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to verify that data and control connections are
 connected to the same remote host.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnoreNoncriticalErrors(boolean)" class="member-name-link">setIgnoreNoncriticalErrors</a><wbr>(boolean&nbsp;ignoreNoncriticalErrors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the flag to skip errors on directory creation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInitialSiteCommand(java.lang.String)" class="member-name-link">setInitialSiteCommand</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;initialCommand)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the initialSiteCommand attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setListing(java.io.File)" class="member-name-link">setListing</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;listing)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The output file for the "list" action.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewer(boolean)" class="member-name-link">setNewer</a><wbr>(boolean&nbsp;newer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A synonym for <code>depends</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPassive(boolean)" class="member-name-link">setPassive</a><wbr>(boolean&nbsp;passive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies whether to use passive mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPassword(java.lang.String)" class="member-name-link">setPassword</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the login password for the given user id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPort(int)" class="member-name-link">setPort</a><wbr>(int&nbsp;port)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the FTP port used by the remote server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreserveLastModified(boolean)" class="member-name-link">setPreserveLastModified</a><wbr>(boolean&nbsp;preserveLastModified)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set to true to preserve modification times for "gotten" files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRecentDateFormatConfig(java.lang.String)" class="member-name-link">setRecentDateFormatConfig</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;recentDateFormat)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the recentDateFormatConfig attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRemotedir(java.lang.String)" class="member-name-link">setRemotedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the remote directory where files will be placed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRetriesAllowed(java.lang.String)" class="member-name-link">setRetriesAllowed</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;retriesAllowed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Defines how many times to retry executing FTP command before giving up.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSeparator(java.lang.String)" class="member-name-link">setSeparator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the remote file separator character.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setServer(java.lang.String)" class="member-name-link">setServer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;server)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the FTP server to send files to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setServerLanguageCodeConfig(org.apache.tools.ant.taskdefs.optional.net.FTP.LanguageCode)" class="member-name-link">setServerLanguageCodeConfig</a><wbr>(<a href="FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a>&nbsp;serverLanguageCode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the serverLanguageCode attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setServerTimeZoneConfig(java.lang.String)" class="member-name-link">setServerTimeZoneConfig</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;serverTimeZoneId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the serverTimeZoneConfig attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShortMonthNamesConfig(java.lang.String)" class="member-name-link">setShortMonthNamesConfig</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shortMonthNames)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the shortMonthNamesConfig attribute</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSiteCommand(java.lang.String)" class="member-name-link">setSiteCommand</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;siteCommand)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the siteCommand attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSkipFailedTransfers(boolean)" class="member-name-link">setSkipFailedTransfers</a><wbr>(boolean&nbsp;skipFailedTransfers)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, enables unsuccessful file put, delete and get
 operations to be skipped with a warning and the remainder
 of the files still transferred.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSystemTypeKey(org.apache.tools.ant.taskdefs.optional.net.FTP.FTPSystemType)" class="member-name-link">setSystemTypeKey</a><wbr>(<a href="FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a>&nbsp;systemKey)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the systemTypeKey attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeDiffAuto(boolean)" class="member-name-link">setTimeDiffAuto</a><wbr>(boolean&nbsp;timeDiffAuto)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">&quot;true&quot; to find out automatically the time difference
 between local and remote machine.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeDiffMillis(long)" class="member-name-link">setTimeDiffMillis</a><wbr>(long&nbsp;timeDiffMillis)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">number of milliseconds to add to the time on the remote machine
 to get the time on the local machine.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimestampGranularity(org.apache.tools.ant.taskdefs.optional.net.FTP.Granularity)" class="member-name-link">setTimestampGranularity</a><wbr>(<a href="FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a>&nbsp;timestampGranularity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the timestampGranularity attribute</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUmask(java.lang.String)" class="member-name-link">setUmask</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theUmask)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the default mask for file creation on a unix server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUseFtps(boolean)" class="member-name-link">setUseFtps</a><wbr>(boolean&nbsp;useFtps)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to use ftps instead of ftp.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUserid(java.lang.String)" class="member-name-link">setUserid</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userid)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the login user id to use on the specified server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(boolean)" class="member-name-link">setVerbose</a><wbr>(boolean&nbsp;verbose)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set to true to receive notification about each file as it is
 transferred.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWakeUpTransferInterval(int)" class="member-name-link">setWakeUpTransferInterval</a><wbr>(int&nbsp;wakeUpTransferInterval)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the time interval when we should automatically
 call a command triggering a transfer
 The parameter is in seconds</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#transferFiles(org.apache.commons.net.ftp.FTPClient)" class="member-name-link">transferFiles</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sends all files specified by the configured filesets to the remote
 server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#transferFiles(org.apache.commons.net.ftp.FTPClient,org.apache.tools.ant.types.FileSet)" class="member-name-link">transferFiles</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">For each file in the fileset, do the appropriate action: send, get,
 delete, or list.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="SEND_FILES">
<h3>SEND_FILES</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SEND_FILES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.SEND_FILES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="GET_FILES">
<h3>GET_FILES</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">GET_FILES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.GET_FILES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEL_FILES">
<h3>DEL_FILES</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEL_FILES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.DEL_FILES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LIST_FILES">
<h3>LIST_FILES</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LIST_FILES</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.LIST_FILES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MK_DIR">
<h3>MK_DIR</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MK_DIR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.MK_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="CHMOD">
<h3>CHMOD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">CHMOD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.CHMOD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RM_DIR">
<h3>RM_DIR</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">RM_DIR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.RM_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SITE_CMD">
<h3>SITE_CMD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SITE_CMD</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.SITE_CMD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_FTP_PORT">
<h3>DEFAULT_FTP_PORT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_FTP_PORT</span></div>
<div class="block">Default port for FTP</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.DEFAULT_FTP_PORT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ACTION_STRS">
<h3>ACTION_STRS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">ACTION_STRS</span></div>
</section>
</li>
<li>
<section class="detail" id="COMPLETED_ACTION_STRS">
<h3>COMPLETED_ACTION_STRS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">COMPLETED_ACTION_STRS</span></div>
</section>
</li>
<li>
<section class="detail" id="ACTION_TARGET_STRS">
<h3>ACTION_TARGET_STRS</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">ACTION_TARGET_STRS</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>FTP</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FTP</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setRemotedir(java.lang.String)">
<h3>setRemotedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRemotedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</span></div>
<div class="block">Sets the remote directory where files will be placed. This may be a
 relative or absolute path, and must be in the path syntax expected by
 the remote server. No correction of path syntax will be performed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - the remote directory name.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setServer(java.lang.String)">
<h3>setServer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setServer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;server)</span></div>
<div class="block">Sets the FTP server to send files to.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>server</code> - the remote server name.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPort(int)">
<h3>setPort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPort</span><wbr><span class="parameters">(int&nbsp;port)</span></div>
<div class="block">Sets the FTP port used by the remote server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>port</code> - the port on which the remote server is listening.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUserid(java.lang.String)">
<h3>setUserid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUserid</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userid)</span></div>
<div class="block">Sets the login user id to use on the specified server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userid</code> - remote system userid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUseFtps(boolean)">
<h3>setUseFtps</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUseFtps</span><wbr><span class="parameters">(boolean&nbsp;useFtps)</span></div>
<div class="block">Whether to use ftps instead of ftp.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.10.13</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(javax.net.ssl.HostnameVerifier)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/javax/net/ssl/HostnameVerifier.html" title="class or interface in javax.net.ssl" class="external-link">HostnameVerifier</a>&nbsp;hostnameVerifier)</span></div>
</section>
</li>
<li>
<section class="detail" id="setPassword(java.lang.String)">
<h3>setPassword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPassword</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</span></div>
<div class="block">Sets the login password for the given user id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>password</code> - the password on the remote system.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAccount(java.lang.String)">
<h3>setAccount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAccount</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pAccount)</span></div>
<div class="block">Sets the login account to use on the specified server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pAccount</code> - the account name on remote system</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBinary(boolean)">
<h3>setBinary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBinary</span><wbr><span class="parameters">(boolean&nbsp;binary)</span></div>
<div class="block">If true, uses binary mode, otherwise text mode (default is binary).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>binary</code> - if true use binary mode in transfers.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPassive(boolean)">
<h3>setPassive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPassive</span><wbr><span class="parameters">(boolean&nbsp;passive)</span></div>
<div class="block">Specifies whether to use passive mode. Set to true if you are behind a
 firewall and cannot connect without it. Passive mode is disabled by
 default.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>passive</code> - true is passive mode should be used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerbose(boolean)">
<h3>setVerbose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(boolean&nbsp;verbose)</span></div>
<div class="block">Set to true to receive notification about each file as it is
 transferred.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - true if verbose notifications are required.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNewer(boolean)">
<h3>setNewer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewer</span><wbr><span class="parameters">(boolean&nbsp;newer)</span></div>
<div class="block">A synonym for <code>depends</code>. Set to true to transmit only new
 or changed files.

 See the related attributes timediffmillis and timediffauto.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newer</code> - if true only transfer newer files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeDiffMillis(long)">
<h3>setTimeDiffMillis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeDiffMillis</span><wbr><span class="parameters">(long&nbsp;timeDiffMillis)</span></div>
<div class="block">number of milliseconds to add to the time on the remote machine
 to get the time on the local machine.

 use in conjunction with <code>newer</code></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timeDiffMillis</code> - number of milliseconds</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeDiffAuto(boolean)">
<h3>setTimeDiffAuto</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeDiffAuto</span><wbr><span class="parameters">(boolean&nbsp;timeDiffAuto)</span></div>
<div class="block">&quot;true&quot; to find out automatically the time difference
 between local and remote machine.

 This requires right to create
 and delete a temporary file in the remote directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timeDiffAuto</code> - true = find automatically the time diff</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreserveLastModified(boolean)">
<h3>setPreserveLastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreserveLastModified</span><wbr><span class="parameters">(boolean&nbsp;preserveLastModified)</span></div>
<div class="block">Set to true to preserve modification times for "gotten" files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>preserveLastModified</code> - if true preserver modification times.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDepends(boolean)">
<h3>setDepends</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDepends</span><wbr><span class="parameters">(boolean&nbsp;depends)</span></div>
<div class="block">Set to true to transmit only files that are new or changed from their
 remote counterparts. The default is to transmit all files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>depends</code> - if true only transfer newer files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSeparator(java.lang.String)">
<h3>setSeparator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSeparator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator)</span></div>
<div class="block">Sets the remote file separator character. This normally defaults to the
 Unix standard forward slash, but can be manually overridden using this
 call if the remote server requires some other separator. Only the first
 character of the string is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>separator</code> - the file separator on the remote system.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setChmod(java.lang.String)">
<h3>setChmod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setChmod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theMode)</span></div>
<div class="block">Sets the file permission mode (Unix only) for files sent to the
 server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>theMode</code> - unix style file mode for the files sent to the remote
        system.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUmask(java.lang.String)">
<h3>setUmask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUmask</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theUmask)</span></div>
<div class="block">Sets the default mask for file creation on a unix server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>theUmask</code> - unix style umask for files created on the remote server.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">A set of files to upload or download</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the set of files to be added to the list of files to be
        transferred.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAction(java.lang.String)">
<h3>setAction</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAction</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;action)</span>
               throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</div>
</div>
<div class="block">Sets the FTP action to be taken. Currently accepts "put", "get", "del",
 "mkdir", "chmod", "list", and "site".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>action</code> - the FTP action to be performed.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the action is not a valid action.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAction(org.apache.tools.ant.taskdefs.optional.net.FTP.Action)">
<h3>setAction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAction</span><wbr><span class="parameters">(<a href="FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</a>&nbsp;action)</span>
               throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sets the FTP action to be taken. Currently accepts "put", "get", "del",
 "mkdir", "chmod", "list", and "site".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>action</code> - the FTP action to be performed.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the action is not a valid action.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setListing(java.io.File)">
<h3>setListing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setListing</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;listing)</span></div>
<div class="block">The output file for the "list" action. This attribute is ignored for
 any other actions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>listing</code> - file in which to store the listing.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSkipFailedTransfers(boolean)">
<h3>setSkipFailedTransfers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSkipFailedTransfers</span><wbr><span class="parameters">(boolean&nbsp;skipFailedTransfers)</span></div>
<div class="block">If true, enables unsuccessful file put, delete and get
 operations to be skipped with a warning and the remainder
 of the files still transferred.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>skipFailedTransfers</code> - true if failures in transfers are ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnoreNoncriticalErrors(boolean)">
<h3>setIgnoreNoncriticalErrors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnoreNoncriticalErrors</span><wbr><span class="parameters">(boolean&nbsp;ignoreNoncriticalErrors)</span></div>
<div class="block">set the flag to skip errors on directory creation.
 (and maybe later other server specific errors)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ignoreNoncriticalErrors</code> - true if non-critical errors should not
        cause a failure.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSystemTypeKey(org.apache.tools.ant.taskdefs.optional.net.FTP.FTPSystemType)">
<h3>setSystemTypeKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSystemTypeKey</span><wbr><span class="parameters">(<a href="FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</a>&nbsp;systemKey)</span></div>
<div class="block">Sets the systemTypeKey attribute.
 Method for setting <code>FTPClientConfig</code> remote system key.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>systemKey</code> - the key to be set - BUT if blank
 the default value of null (which signifies "autodetect") will be kept.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDefaultDateFormatConfig(java.lang.String)">
<h3>setDefaultDateFormatConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultDateFormatConfig</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultDateFormat)</span></div>
<div class="block">Sets the defaultDateFormatConfig attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>defaultDateFormat</code> - configuration to be set, unless it is
 null or empty string, in which case ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRecentDateFormatConfig(java.lang.String)">
<h3>setRecentDateFormatConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRecentDateFormatConfig</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;recentDateFormat)</span></div>
<div class="block">Sets the recentDateFormatConfig attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>recentDateFormat</code> - configuration to be set, unless it is
 null or empty string, in which case ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setServerLanguageCodeConfig(org.apache.tools.ant.taskdefs.optional.net.FTP.LanguageCode)">
<h3>setServerLanguageCodeConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setServerLanguageCodeConfig</span><wbr><span class="parameters">(<a href="FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</a>&nbsp;serverLanguageCode)</span></div>
<div class="block">Sets the serverLanguageCode attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>serverLanguageCode</code> - configuration to be set, unless it is
 null or empty string, in which case ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setServerTimeZoneConfig(java.lang.String)">
<h3>setServerTimeZoneConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setServerTimeZoneConfig</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;serverTimeZoneId)</span></div>
<div class="block">Sets the serverTimeZoneConfig attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>serverTimeZoneId</code> - configuration to be set, unless it is
 null or empty string, in which case ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShortMonthNamesConfig(java.lang.String)">
<h3>setShortMonthNamesConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShortMonthNamesConfig</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shortMonthNames)</span></div>
<div class="block">Sets the shortMonthNamesConfig attribute</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shortMonthNames</code> - configuration to be set, unless it is
 null or empty string, in which case ignored.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>FTPClientConfig</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRetriesAllowed(java.lang.String)">
<h3>setRetriesAllowed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRetriesAllowed</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;retriesAllowed)</span></div>
<div class="block">Defines how many times to retry executing FTP command before giving up.
 Default is 0 - try once and if failure then give up.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>retriesAllowed</code> - number of retries to allow.  -1 means
 keep trying forever. "forever" may also be specified as a
 synonym for -1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSystemTypeKey()">
<h3>getSystemTypeKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSystemTypeKey</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the systemTypeKey.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultDateFormatConfig()">
<h3>getDefaultDateFormatConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultDateFormatConfig</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the defaultDateFormatConfig.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRecentDateFormatConfig()">
<h3>getRecentDateFormatConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRecentDateFormatConfig</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the recentDateFormatConfig.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getServerLanguageCodeConfig()">
<h3>getServerLanguageCodeConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getServerLanguageCodeConfig</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the serverLanguageCodeConfig.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getServerTimeZoneConfig()">
<h3>getServerTimeZoneConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getServerTimeZoneConfig</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the serverTimeZoneConfig.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getShortMonthNamesConfig()">
<h3>getShortMonthNamesConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getShortMonthNamesConfig</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the shortMonthNamesConfig.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimestampGranularity(org.apache.tools.ant.taskdefs.optional.net.FTP.Granularity)">
<h3>setTimestampGranularity</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimestampGranularity</span><wbr><span class="parameters">(<a href="FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</a>&nbsp;timestampGranularity)</span></div>
<div class="block">Sets the timestampGranularity attribute</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timestampGranularity</code> - The timestampGranularity to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSiteCommand(java.lang.String)">
<h3>setSiteCommand</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSiteCommand</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;siteCommand)</span></div>
<div class="block">Sets the siteCommand attribute.  This attribute
 names the command that will be executed if the action
 is "site".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>siteCommand</code> - The siteCommand to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInitialSiteCommand(java.lang.String)">
<h3>setInitialSiteCommand</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInitialSiteCommand</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;initialCommand)</span></div>
<div class="block">Sets the initialSiteCommand attribute.  This attribute
 names a site command that will be executed immediately
 after connection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>initialCommand</code> - The initialSiteCommand to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEnableRemoteVerification(boolean)">
<h3>setEnableRemoteVerification</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEnableRemoteVerification</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to verify that data and control connections are
 connected to the same remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDataTimeout(int)">
<h3>setDataTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDataTimeout</span><wbr><span class="parameters">(int&nbsp;dataTimeout)</span></div>
<div class="block">Sets the timeout on the data connection in milliseconds.
 Any negative value is discarded and leaves the default
 A value of 0 means an infinite timeout</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dataTimeout</code> - int</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWakeUpTransferInterval(int)">
<h3>setWakeUpTransferInterval</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWakeUpTransferInterval</span><wbr><span class="parameters">(int&nbsp;wakeUpTransferInterval)</span></div>
<div class="block">Sets the time interval when we should automatically
 call a command triggering a transfer
 The parameter is in seconds</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>wakeUpTransferInterval</code> - int</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkAttributes()">
<h3>checkAttributes</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkAttributes</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Checks to see that all required parameters are set.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the configuration is not valid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="executeRetryable(org.apache.tools.ant.util.RetryHandler,org.apache.tools.ant.util.Retryable,java.lang.String)">
<h3>executeRetryable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">executeRetryable</span><wbr><span class="parameters">(<a href="../../../util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</a>&nbsp;h,
 <a href="../../../util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</a>&nbsp;r,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descr)</span>
                         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Executable a retryable object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>h</code> - the retry handler.</dd>
<dd><code>r</code> - the object that should be retried until it succeeds
          or the number of retries is reached.</dd>
<dd><code>descr</code> - a description of the command that is being run.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="transferFiles(org.apache.commons.net.ftp.FTPClient,org.apache.tools.ant.types.FileSet)">
<h3>transferFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">transferFiles</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;fs)</span>
                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">For each file in the fileset, do the appropriate action: send, get,
 delete, or list.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTPClient instance used to perform FTP actions</dd>
<dd><code>fs</code> - the fileset on which the actions are performed.</dd>
<dt>Returns:</dt>
<dd>the number of files to be transferred.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading a file</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem in the configuration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="transferFiles(org.apache.commons.net.ftp.FTPClient)">
<h3>transferFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">transferFiles</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends all files specified by the configured filesets to the remote
 server.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTPClient instance used to perform FTP actions</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem reading a file</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem in the configuration.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveFile(java.lang.String)">
<h3>resolveFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">resolveFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file)</span></div>
<div class="block">Correct a file path to correspond to the remote host requirements. This
 implementation currently assumes that the remote end can handle
 Unix-style paths with forward-slash separators. This can be overridden
 with the <code>separator</code> task parameter. No attempt is made to
 determine what syntax is appropriate for the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the remote file name to be resolved</dd>
<dt>Returns:</dt>
<dd>the filename as it will appear on the server.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createParents(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>createParents</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createParents</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Creates all parent directories specified in a complete relative
 pathname. Attempts to create existing directories will not cause
 errors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the FTP client instance to use to execute FTP actions on
        the remote server.</dd>
<dd><code>filename</code> - the name of the file whose parents should be created.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - under non documented circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it is impossible to cd to a remote directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isUpToDate(org.apache.commons.net.ftp.FTPClient,java.io.File,java.lang.String)">
<h3>isUpToDate</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isUpToDate</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;localFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;remoteFile)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Checks to see if the remote file is current as compared with the local
 file. Returns true if the target file is up to date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftpclient</dd>
<dd><code>localFile</code> - local file</dd>
<dd><code>remoteFile</code> - remote file</dd>
<dt>Returns:</dt>
<dd>true if the target file is up to date</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the date of the remote files cannot be found and the action is
 GET_FILES</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="doSiteCommand(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>doSiteCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">doSiteCommand</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;theCMD)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends a site command to the ftp server</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>theCMD</code> - command to execute</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sendFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)">
<h3>sendFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sends a single file to the remote host. <code>filename</code> may
 contain a relative path specification. When this is the case, <code>sendFile</code>
 will attempt to create any necessary parent directories before sending
 the file. The file will then be sent using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>dir</code> - base directory of the file to be sent (local)</dd>
<dd><code>filename</code> - relative path of the file to be send
        locally relative to dir
        remotely relative to the remotedir attribute</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="delFile(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>delFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Delete a file from the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>filename</code> - file to delete</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is set to false
 and the deletion could not be done</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="rmDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>rmDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">rmDir</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dirname)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Delete a directory, if empty, from the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>dirname</code> - directory to delete</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is set to false
 and the deletion could not be done</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFile(org.apache.commons.net.ftp.FTPClient,java.lang.String,java.lang.String)">
<h3>getFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">getFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Retrieve a single file from the remote host. <code>filename</code> may
 contain a relative path specification. <p>

 The file will then be retrieved using the entire relative path spec -
 no attempt is made to change directories. It is anticipated that this
 may eventually cause problems with some FTP servers, but it simplifies
 the coding.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - the ftp client</dd>
<dd><code>dir</code> - local base directory to which the file should go back</dd>
<dd><code>filename</code> - relative path of the file based upon the ftp remote directory
        and/or the local base directory (dir)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if skipFailedTransfers is false
 and the file cannot be retrieved.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="listFile(org.apache.commons.net.ftp.FTPClient,java.io.BufferedWriter,java.lang.String)">
<h3>listFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">listFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/BufferedWriter.html" title="class or interface in java.io" class="external-link">BufferedWriter</a>&nbsp;bw,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">List information about a single file from the remote host. <code>filename</code>
 may contain a relative path specification. <p>

 The file listing will then be retrieved using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - ftp client</dd>
<dd><code>bw</code> - buffered writer</dd>
<dd><code>filename</code> - the directory one wants to list</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in unknown circumstances</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeRemoteDir(org.apache.commons.net.ftp.FTPClient,java.lang.String)">
<h3>makeRemoteDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">makeRemoteDir</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the specified directory on the remote host.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ftp</code> - The FTP client connection</dd>
<dd><code>dir</code> - The directory to create (format must be correct for host
      type)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in unknown circumstances</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if ignoreNoncriticalErrors has not been set to true
         and a directory could not be created, for instance because it was
         already existing. Precisely, the codes 521, 550 and 553 will trigger
         a BuildException</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Runs the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the task fails or is not configured
         correctly.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;level)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
