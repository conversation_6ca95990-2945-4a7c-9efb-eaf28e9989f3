<?xml version="1.0" encoding="UTF-8"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!--
  This POM has been created manually by the Ant Development Team.
  Please contact us if you are not satisfied with the data contained in this POM.
  URL : https://ant.apache.org
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.ant</groupId>
  <artifactId>ant-parent</artifactId>
  <version>1.10.14</version>
  <packaging>pom</packaging>
  <description>master POM</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <name>Apache Ant</name>
  <url>https://ant.apache.org/</url>
  <inceptionYear>2000</inceptionYear>
  <organization>
    <name>The Apache Software Foundation</name>
    <url>https://www.apache.org/</url>
  </organization>
  <distributionManagement>
    <!-- Null out inherited apache distribution repo by default -->
    <repository>
      <id>dummy</id>
      <name>Dummy to avoid accidental deploys</name>
      <url>https://nowhere.net/</url>
    </repository>
  </distributionManagement>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/ant.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/ant.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf/ant.git</url>
  </scm>
  <ciManagement>
    <system>jenkins</system>
    <url>https://builds.apache.org/job/Ant-Build-from-POMs/</url>
  </ciManagement>
  <mailingLists>
    <mailingList>
      <name>Ant Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/ant-dev</archive>
    </mailingList>
    <mailingList>
      <name>Ant Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/ant-user</archive>
    </mailingList>
  </mailingLists>
  <issueManagement>
    <system>bugzilla</system>
    <url>https://issues.apache.org/bugzilla/</url>
  </issueManagement>
  <modules>
    <module>ant</module>
    <module>ant-antlr</module>
    <module>ant-apache-bcel</module>
    <module>ant-apache-bsf</module>
    <module>ant-apache-log4j</module>
    <module>ant-apache-oro</module>
    <module>ant-apache-regexp</module>
    <module>ant-apache-resolver</module>
    <module>ant-apache-xalan2</module>
    <module>ant-commons-logging</module>
    <module>ant-commons-net</module>
    <module>ant-imageio</module>
    <module>ant-jai</module>
    <module>ant-javamail</module>
    <module>ant-jakartamail</module>
    <module>ant-jdepend</module>
    <module>ant-jmf</module>
    <module>ant-jsch</module>
    <module>ant-junit</module>
    <module>ant-junit4</module>
    <module>ant-junitlauncher</module>
    <module>ant-launcher</module>
    <module>ant-netrexx</module>
    <module>ant-swing</module>
    <module>ant-testutil</module>
    <module>ant-xz</module>
  </modules>
  <dependencies>
     <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <sourceDirectory>../../../src/main</sourceDirectory>
    <testSourceDirectory>../../../src/testcases</testSourceDirectory>
    <outputDirectory>../../../target/classes</outputDirectory>
    <testOutputDirectory>../../../target/testcases</testOutputDirectory>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.0</version>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>2.22.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.8</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <profiles>
    <profile>
      <id>javac-8</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <dependencies>
        <dependency>
          <groupId>com.sun</groupId>
          <artifactId>tools</artifactId>
          <version>1.8.0</version>
          <scope>system</scope>
          <systemPath>${java.home}/../lib/tools.jar</systemPath>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
</project>
