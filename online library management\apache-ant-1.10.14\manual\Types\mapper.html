<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Mapper Type</title>
</head>

<body>

<h2 id="mapper">Mapping File Names</h2>
<p>Some tasks take source files and create target files. Depending on the task, it may be quite
obvious which name a target file will have (using <a href="../Tasks/javac.html">javac</a>, you know
there will be <samp>.class</samp> files for your <samp>.java</samp> files)&mdash;in other cases you
may want to specify the target files, either to help Apache Ant or to get an extra bit of
functionality.</p>
<p>While source files are usually specified as <a href="fileset.html">fileset</a>s, you don't
specify target files directly&mdash;instead, you tell Ant how to find the target file(s) for one
source file. An instance of <code class="code">org.apache.tools.ant.util.FileNameMapper</code> is
responsible for this. It constructs target file names based on rules that can be parameterized
with <var>from</var> and <var>to</var> attributes&mdash;the exact meaning of which is
implementation-dependent.</p>
<p>These instances are defined in <code>&lt;mapper&gt;</code> elements with the following
attributes:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>type</td>
    <td>specifies one of the built-in implementations.</td>
    <td rowspan="2">Exactly one of these</td>
  </tr>
  <tr>
    <td>classname</td>
    <td class="left">specifies the implementation by class name.</td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>the classpath to use when looking up
      <code>classname</code>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>the classpath to use, given as <a href="../using.html#references">reference</a> to a
      path defined elsewhere.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>from</td>
    <td>the <code>from</code> attribute for the given implementation.</td>
    <td>Depends on implementation</td>
  </tr>
  <tr>
    <td>to</td>
    <td>the <code>to</code> attribute for the given implementation.</td>
    <td>Depends on implementation</td>
  </tr>
  <tr>
    <td>refid</td>
    <td>Makes this <code>mapper</code>
      a <a href="../using.html#references">reference</a> to
      a <code>mapper</code> defined elsewhere. If specified no other
      attributes or nested elements are allowed.</td>
    <td>No</td>
  </tr>
</table>
<p>Note that Ant will not automatically convert <q>/</q> or <q>\</q> characters in the <var>to</var>
and <var>from</var> attributes to the correct directory separator of your current platform.  If you
need to specify this separator, use <code>${file.separator}</code> instead.  For the regexp
mapper, <code>${file.separator}</code> will not work, as on Windows it is the <q>\</q> character,
and this is an escape character for regular expressions, one should use
the <code>handledirsep</code> attribute instead.
</p>
<h3>Parameters specified as nested elements</h3>
<p>The classpath can be specified via a nested <code>&lt;classpath&gt;</code>, as well&mdash;that
is, a <a href="../using.html#path">path</a>-like structure.</p>
<p><em>Since Ant 1.7.0</em>, nested File Mappers can be supplied via
either <code>&lt;mapper&gt;</code> elements
or <a href="../Tasks/typedef.html"><code>&lt;typedef&gt;</code></a>'d implementations
of <code class="code">org.apache.tools.ant.util.FileNameMapper</code>.  If nested File Mappers are
specified by either means, the mapper will be implicitly configured as
a <a href="#composite-mapper">composite mapper</a>.</p>
<h3>The built-in mapper types</h3>
<p>All built-in mappers are case-sensitive.</p>
<p><em>Since Ant 1.7.0</em>, each of the built-in mapper implementation types is directly accessible
using a specific tagname. This makes it possible for filename mappers to support attributes in
addition to the generally available <var>to</var> and <var>from</var>.<br/>
The <code>&lt;mapper <var>type</var>|<var>classname</var>=&quot;...&quot;&gt;</code> usage form
remains valid for reasons of backward compatibility.</p>

    <!--                                        -->
    <!--             Identity Mapper            -->
    <!--                                        -->

<h4 id="identity-mapper">identity</h4>
<p>The target file name is identical to the source file name. Both <var>to</var> and <var>from</var>
will be ignored.</p>
<h5>Examples</h5>
<pre>
&lt;mapper type=&quot;identity&quot;/&gt;
&lt;identitymapper/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>A.java</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>foo/bar/B.java</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td><code>C.properties</code></td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>Classes/dir/dir2/A.properties</code></td>
  </tr>
</table>

    <!--                                        -->
    <!--             Flatten Mapper             -->
    <!--                                        -->

<h4 id="flatten-mapper">flatten</h4>
<p>The target file name is identical to the source file name, with all leading directory information
stripped off. Both <var>to</var> and <var>from</var> will be ignored.</p>
<h5>Examples</h5>
<pre>
&lt;mapper type=&quot;flatten&quot;/&gt;
&lt;flattenmapper/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>A.java</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>B.java</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td><code>C.properties</code></td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>A.properties</code></td>
  </tr>
</table>

    <!--                                        -->
    <!--             Merge Mapper               -->
    <!--                                        -->

<h4 id="merge-mapper">merge</h4>
<p>The target file name will always be the same, as defined by <var>to</var>&mdash;<var>from</var>
will be ignored.</p>
<h5>Examples</h5>
<pre>
&lt;mapper type=&quot;merge&quot; to=&quot;archive.tar&quot;/&gt;
&lt;mergemapper to=&quot;archive.tar&quot;/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>archive.tar</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>archive.tar</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td><code>archive.tar</code></td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>archive.tar</code></td>
  </tr>
</table>

    <!--                                        -->
    <!--              Glob Mapper               -->
    <!--                                        -->

<h4 id="glob-mapper">glob</h4>
<p>Both <var>to</var> and <var>from</var> are required and define patterns that may contain at most
one <q>*</q>. For each source file that matches the <var>from</var> pattern, a target file name will
be constructed from the <var>to</var> pattern by substituting the <q>*</q> in the <var>to</var>
pattern with the text that matches the <q>*</q> in the <var>from</var> pattern. Source file names
that don't match the <var>from</var> pattern will be ignored.</p>
<h5>Examples</h5>
<pre>
&lt;mapper type=&quot;glob&quot; from=&quot;*.java&quot; to=&quot;*.java.bak&quot;/&gt;
&lt;globmapper from=&quot;*.java&quot; to=&quot;*.java.bak&quot;/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>A.java.bak</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>foo/bar/B.java.bak</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td>ignored</td>
  </tr>
</table>
<pre>
&lt;mapper type=&quot;glob&quot; from=&quot;C*ies&quot; to=&quot;Q*y&quot;/&gt;
&lt;globmapper from=&quot;C*ies&quot; to=&quot;Q*y&quot;/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td><code>Q.property</code></td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>Qlasses/dir/dir2/A.property</code></td>
  </tr>
</table>
<p>The <code>globmapper</code> mapper can take the following extra attributes.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>casesensitive</td>
    <td>
      This attribute can be <q>true</q> or <q>false</q>.  If this is <q>false</q>, the mapper will
      ignore case when matching the glob pattern.  <em>Since Ant 1.6.3</em>
    </td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>handledirsep</td>
    <td>
      This attribute can be <q>true</q> or <q>false</q>.  If this is specified, the mapper will
      ignore the difference between the normal directory separator characters&mdash;<q>\</q>
      and <q>/</q>.  This attribute is useful for cross-platform build files.  <em>Since Ant
      1.6.3</em>
    <td>No; default is <q>false</q></td>
  </tr>
</table>
<p>An example:</p>
<pre>
&lt;pathconvert property="x" targetos="unix"&gt;
  &lt;path path="Aj.Java"/&gt;
  &lt;mapper&gt;
  &lt;chainedmapper&gt;
    &lt;flattenmapper/&gt;
    &lt;globmapper from="a*.java" to="*.java.bak" casesensitive="no"/&gt;
  &lt;/chainedmapper&gt;
  &lt;/mapper&gt;
&lt;/pathconvert&gt;
&lt;echo&gt;x is ${x}&lt;/echo&gt;</pre>
<p>will output <code>x is j.java.bak</code>, and</p>
<pre>
&lt;pathconvert property="x" targetos="unix"&gt;
  &lt;path path="d/e/f/j.java"/&gt;
  &lt;mapper&gt;
    &lt;globmapper from="${basedir}\d/e\*" to="*" handledirsep="yes"/&gt;
  &lt;/mapper&gt;
&lt;/pathconvert&gt;
&lt;echo&gt;x is ${x}&lt;/echo&gt;</pre>
<p>will output <code>x is f/j.java</code>.</p>

    <!--                                        -->
    <!--             RegExp Mapper              -->
    <!--                                        -->

<h4 id="regexp-mapper">regexp</h4>

<p>Both <var>to</var> and <var>from</var> are required and define regular expressions. If the source
file name (as a whole or in part) matches the <var>from</var> pattern, the target file name will be
constructed from the <var>to</var> pattern, using <code>\0</code> to <code>\9</code> as
back-references for the full match (<code>\0</code>) or the matches of the subexpressions in
parentheses.  The <var>to</var> pattern determines the <strong>whole</strong> file name, so if you
wanted to replace the extension of a file you should not use <code>from="\.old$" to=".new"</code>
but rather <code>from="(.*)\.old$" to="\1.new"</code> (or rather use a glob mapper in this
case).</p>

<p>Source files not matching the <var>from</var> pattern will be ignored.</p>

<p>Note that you need to escape a dollar-sign (<q>$</q>) with another dollar-sign in Ant.</p>

<p>For information about using <a href="https://savannah.gnu.org/projects/gnu-regexp/"
target="_top">gnu.regexp</a> or <a href="https://github.com/kzn/regex4j/" target="_top">gnu.rex</a>
with Ant, see <a href="https://marc.info/?l=ant-dev&m=97550753813481&w=2" target="_top">this</a>
article. Please keep in mind that <a href="http://tusker.org/regex/regex_benchmark.html"
target="_top">your mileage may vary</a> with different regexp engines.</p>

<p>If you want to use one of the <a href="../install.html#librarydependencies">regular expression
libraries</a> other than <code>java.util.regex</code> you need to also use the
corresponding <samp>ant-[apache-oro, apache-regexp].jar</samp> from the Ant release you are using.
Make sure that both will be loaded from the same classpath, that is either put them into
your <code>CLASSPATH</code>, <samp>ANT_HOME/lib</samp> directory or a
nested <code>&lt;classpath&gt;</code> element of the mapper&mdash;you cannot
have <samp>ant-[apache-oro, apache-regexp].jar</samp> in <samp>ANT_HOME/lib</samp> and the library
in a nested <code>&lt;classpath&gt;</code>.</p>
<p>Ant will choose the regular expression library based on the following algorithm:</p>
<ul>
<li>If the system property <code>ant.regexp.matcherimpl</code> has been set, it is taken as the name
of the class implementing <code class="code">org.apache.tools.ant.util.regexp.RegexpMatcher</code>
that should be used.</li>
<li>If it has not been set, uses the default from the <code>java.util.regex</code> package.</li>
</ul>

<h5>Examples</h5>
<pre>
&lt;mapper type=&quot;regexp&quot; from=&quot;^(.*)\.java$$&quot; to=&quot;\1.java.bak&quot;/&gt;
&lt;regexpmapper from=&quot;^(.*)\.java$$&quot; to=&quot;\1.java.bak&quot;/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>A.java.bak</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>foo/bar/B.java.bak</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td>ignored</td>
  </tr>
</table>
<pre>
&lt;mapper type=&quot;regexp&quot; from=&quot;^(.*)/([^/]+)/([^/]*)$$&quot; to=&quot;\1/\2/\2-\3&quot;/&gt;
&lt;regexpmapper from=&quot;^(.*)/([^/]+)/([^/]*)$$&quot; to=&quot;\1/\2/\2-\3&quot;/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>foo/bar/bar-B.java</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td>ignored</td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>Classes/dir/dir2/dir2-A.properties</code></td>
  </tr>
</table>
<pre>
&lt;mapper type="regexp" from="^(.*)\.(.*)$$" to="\2.\1"/&gt;
&lt;regexpmapper from="^(.*)\.(.*)$$" to="\2.\1"/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>A.java</code></td>
    <td><code>java.A</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/B.java</code></td>
    <td><code>java.foo/bar/B</code></td>
  </tr>
  <tr>
    <td><code>C.properties</code></td>
    <td><code>properties.C</code></td>
  </tr>
  <tr>
    <td><code>Classes/dir/dir2/A.properties</code></td>
    <td><code>properties.Classes/dir/dir2/A</code></td>
  </tr>
</table>
<pre>
&lt;mapper type="regexp" from="^(.*?)(\$$[^/\\\.]*)?\.class$$" to="\1.java"/&gt;
&lt;regexpmapper from="^(.*?)(\$$[^/\\\.]*)?\.class$$" to="\1.java"/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>ClassLoader.class</code></td>
    <td><code>ClassLoader.java</code></td>
  </tr>
  <tr>
    <td><code>java/lang/ClassLoader.class</code></td>
    <td><code>java/lang/ClassLoader.java</code></td>
  </tr>
  <tr>
    <td><code>java\lang\ClassLoader$1.class</code></td>
    <td><code>java\lang\ClassLoader.java</code></td>
  </tr>
  <tr>
    <td><code>java/lang/ClassLoader$foo$1.class</code></td>
    <td><code>java/lang/ClassLoader.java</code></td>
  </tr>
</table>
<p>The regexp mapper can take the following extra attributes.</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>casesensitive</td>
    <td>
      This attribute can be <q>true</q> or <q>false</q>.  If this is <q>false</q>, the mapper will
      ignore case when matching the pattern.
      <em>Since Ant 1.6.3</em>
    </td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>handledirsep</td>
    <td>
      This attribute can be <q>true</q> or <q>false</q>.  If this is specified, the mapper will
      treat a <q>\</q> character in a filename as a <q>/</q> for the purposes of matching.  This
      attribute is useful for cross-platform build files.
      <em>Since Ant 1.6.3</em>
    <td>No; default is <q>false</q></td>
  </tr>
</table>
<p>An example:</p>
<pre>
&lt;pathconvert property="x" targetos="unix"&gt;
  &lt;path path="Aj.Java"/&gt;
  &lt;chainedmapper&gt;
    &lt;flattenmapper/&gt;
    &lt;regexpmapper from="a(.*)\.java" to="\1.java.bak" casesensitive="no"/&gt;
  &lt;/chainedmapper&gt;
&lt;/pathconvert&gt;
&lt;echo&gt;x is ${x}&lt;/echo&gt;</pre>
<p>will output <code>x is j.java.bak</code>, and</p>
<pre>
&lt;pathconvert property="hd.prop" targetos="windows"&gt;
  &lt;path path="d\e/f\j.java"/&gt;
  &lt;chainedmapper&gt;
    &lt;regexpmapper from="${basedir}/d/e/(.*)" to="\1" handledirsep="yes"/&gt;
  &lt;/chainedmapper&gt;
&lt;/pathconvert&gt;</pre>
<p>will set <code>hd.prop</code> to <code>f\j.java</code>.</p>

    <!--                                        -->
    <!--             Package Mapper             -->
    <!--                                        -->

<h4 id="package-mapper">package</h4>
<p>Sharing the same syntax as the <a href="#glob-mapper">glob mapper</a>, the package mapper
replaces directory separators found in the matched source pattern with dots in the target pattern
placeholder. This mapper is particularly useful in combination with <code>&lt;uptodate&gt;</code>
and <code>&lt;junit&gt;</code> output.</p>
<p>The <var>to</var> and <var>from</var> attributes are both required.</p>
<h5>Example</h5>
<pre>
&lt;mapper type="package" from="*Test.java" to="TEST-*Test.xml"/&gt;
&lt;packagemapper from="*Test.java" to="TEST-*Test.xml"/&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>org/apache/tools/ant/util/PackageMapperTest.java</code></td>
    <td><code>TEST-org.apache.tools.ant.util.PackageMapperTest.xml</code></td>
  </tr>
  <tr>
    <td><code>org/apache/tools/ant/util/Helper.java</code></td>
    <td>ignored</td>
  </tr>
</table>

    <!--                                        -->
    <!--           Unpackage Mapper             -->
    <!--                                        -->

<h4 id="unpackage-mapper">unpackage</h4>
<p><em>Since Ant 1.6.0</em></p>
<p>This mapper is the inverse of the <a href="#package-mapper">package</a> mapper.  It replaces the
dots in a package name with directory separators. This is useful for matching XML formatter results
against their JUnit test test cases. The mapper shares the sample syntax as
the <a href="#glob-mapper">glob mapper</a>.</p>
<p>The <var>to</var> and <var>from</var> attributes are both required.</p>
<h5>Example</h5>
<pre>
&lt;mapper type="unpackage" from="TEST-*Test.xml" to="${test.src.dir}/*Test.java"&gt;
&lt;unpackagemapper from="TEST-*Test.xml" to="${test.src.dir}/*Test.java"&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file name</th>
  </tr>
  <tr>
    <td><code>TEST-org.acme.AcmeTest.xml</code></td>
    <td><code>${test.src.dir}/org/acme/AcmeTest.java</code></td>
  </tr>
</table>

    <!--                                        -->
    <!--           Composite Mapper             -->
    <!--                                        -->

<h4 id="composite-mapper">composite</h4>
<p><em>Since Ant 1.7.0</em></p>
<p>This mapper implementation can contain multiple nested mappers.  File mapping is performed by
passing the source filename to each nested <code>&lt;mapper&gt;</code> in turn, returning all
results.  The <var>to</var> and <var>from</var> attributes are ignored.</p>
<p><em>Since Ant 1.8.0</em>, the order of the mapped results is the same as the order of the nested
mappers; prior to Ant 1.8.0 the order has been undefined.</p>
<h5>Examples</h5>
<pre>
&lt;compositemapper&gt;
  &lt;identitymapper/&gt;
  &lt;packagemapper from="*.java" to="*"/&gt;
&lt;/compositemapper&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td rowspan="2"><code>foo/bar/A.java</code></td>
    <td><code>foo/bar/A.java</code></td>
  </tr>
  <tr>
    <td><code>foo.bar.A</code></td>
  </tr>
</table>
<p>The composite mapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var>
attribute.</p>

    <!--                                        -->
    <!--            Chained Mapper              -->
    <!--                                        -->

<h4 id="chained-mapper">chained</h4>
<p><em>Since Ant 1.7.0</em></p>
<p>This mapper implementation can contain multiple nested mappers.  File mapping is performed by
passing the source filename to the first nested mapper, its results to the second, and so on.  The
target filenames generated by the last nested mapper comprise the ultimate results of the mapping
operation.  The <var>to</var> and <var>from</var> attributes are ignored.</p>
<h5>Examples</h5>
<pre>
&lt;chainedmapper&gt;
  &lt;flattenmapper/&gt;
  &lt;globmapper from="*" to="new/path/*"/&gt;
  &lt;mapper&gt;
    &lt;globmapper from="*" to="*1"/&gt;
    &lt;globmapper from="*" to="*2"/&gt;
  &lt;/mapper&gt;
&lt;/chainedmapper&gt;</pre>
<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td rowspan="2"><code>foo/bar/A.java</code></td>
    <td><code>new/path/A.java1</code></td>
  </tr>
  <tr>
    <td><code>new/path/A.java2</code></td>
  </tr>
  <tr>
    <td rowspan="2"><code>boo/far/B.java</code></td>
    <td><code>new/path/B.java1</code></td>
  </tr>
  <tr>
    <td><code>new/path/B.java2</code></td>
  </tr>
</table>
<p>The chained mapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var>
attribute.</p>

    <!--                                        -->
    <!--             Filter Mapper              -->
    <!--                                        -->

<h4 id="filter-mapper">filtermapper</h4>
<p><em>Since Ant 1.6.3</em></p>
<p>This mapper implementation applies a <a href="filterchain.html">filterchain</a> to the source
file name.</p>
<h5>Examples</h5>
<pre>
&lt;filtermapper&gt;
  &lt;replacestring from="\" to="/"/&gt;
&lt;/filtermapper&gt;</pre>

<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td><code>foo\bar\A.java</code></td>
    <td><code>foo/bar/A.java</code></td>
  </tr>
</table>
<pre>
&lt;filtermapper&gt;
  &lt;scriptfilter language="beanshell"&gt;
    self.setToken(self.getToken().toUpperCase());
  &lt;/scriptfilter&gt;
&lt;/filtermapper&gt;</pre>

<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td><code>foo\bar\A.java</code></td>
    <td><code>FOO\BAR\A.JAVA</code></td>
  </tr>
</table>

<p>The filtermapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var> attribute.</p>

    <!--                                        -->
    <!--             Script Mapper              -->
    <!--                                        -->

<h4 id="script-mapper">scriptmapper</h4>
<p><em>Since Ant 1.7</em></p>
<p>This mapper executes a script written in <a href="https://jakarta.apache.org/bsf"
target="_top">Apache BSF</a>
or <a href="https://jcp.org/aboutJava/communityprocess/maintenance/jsr223/223ChangeLog.html"
target="_top">JSR 223</a> supported language, once per file to map.</p>
<p>The script can be declared inline or in a specified file.</p>
<p>See the <a href="../Tasks/script.html">Script</a> task for an explanation of scripts and
dependencies.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>language</td>
    <td>Scripting language</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>manager</td>
    <td>The script engine manager to use. See the <a href="../Tasks/script.html">script</a> task
      for using this attribute.</td>
    <td>No; default is <q>auto</q></td>
  </tr>
  <tr>
    <td>src</td>
    <td>File containing the script</td>
    <td>No</td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the script as a file. <em>Since Ant 1.10.2</em></td>
    <td>No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>setbeans</td>
    <td>whether to have all properties, references and targets as global variables in the
      script.  <em>Since Ant 1.8.0</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>The classpath to pass into the script.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>The classpath to use, given as a <a href="../using.html#references">reference</a> to a
      path defined elsewhere.
    <td>No</td>
  </tr>
</table>
<p>This filename mapper can take a nested <code>&lt;classpath&gt;</code> element.  See
the <a href="../Tasks/script.html">script</a> task on how to use this element.</p>

<h5>Example</h5>
<pre>
&lt;scriptmapper language="javascript"&gt;
  self.addMappedName(source.toUpperCase());
  self.addMappedName(source.toLowerCase());
&lt;/scriptmapper&gt;</pre>

<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td rowspan="2"><code>foo\bar\A.java</code></td>
    <td><code>FOO\BAR\A.JAVA</code></td>
  </tr>
  <tr>
    <td><code>foo\bar\a.java</code></td>
  </tr>
</table>

<p>To use this mapper, the scripts need access to the source file, and the ability to return
multiple mappings. Here are the relevant beans and their methods. The script is called once for
every source file, with the list of mapped names reset after every invocation.</p>

<table>
  <tr>
    <th scope="col">Script bean</th>
    <th scope="col">Description</th>
  </tr>
  <tr>
    <td><code>source: String</code></td>
    <td>The file/path to map</td>
  </tr>
  <tr>
    <td><code>self</code></td>
    <td>The scriptmapper itself</td>
  </tr>
  <tr>
    <td><code>self.addMappedName(String name)</code></td>
    <td>Add a new mapping</td>
  </tr>
  <tr>
    <td><code>self.clear()</code></td>
    <td>Reset the list of files</td>
  </tr>
</table>

<p>The scriptmapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var> attribute.</p>

<h4 id="firstmatch-mapper">firstmatchmapper</h4>
<p><em>Since Ant 1.8.0</em></p>
<p>This mapper supports an arbitrary number of nested mappers and returns the results of the first
mapper that matches.  This is different from <a href="#composite-mapper">composite mapper</a> which
collects the results of all matching children.</p>
<h5>Examples</h5>
<pre>
&lt;firstmatchmapper&gt;
  &lt;globmapper from="*.txt" to="*.bak"/&gt;
  &lt;globmapper from="*A.*" to="*B.*"/&gt;
&lt;/firstmatchmapper&gt;</pre>

<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td><code>foo/bar/A.txt</code></td>
    <td><code>foo/bar/A.bak</code></td>
  </tr>
  <tr>
    <td><code>foo/bar/A.java</code></td>
    <td><code>foo/bar/B.java</code></td>
  </tr>
</table>

<p>The firstmatchmapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var>
attribute.</p>

<h4 id="cutdirs-mapper">cutdirsmapper</h4>
<p><em>Since Ant 1.8.2</em></p>
<p>This mapper strips a configured number of leading directories from the source file name.</p>

<h5>Examples</h5>
<pre>&lt;cutdirsmapper dirs="1"/&gt;</pre>

<table>
  <tr>
    <th scope="col">Source file name</th>
    <th scope="col">Target file names</th>
  </tr>
  <tr>
    <td><code>foo/bar/A.txt</code></td>
    <td><code>bar/A.txt</code></td>
  </tr>
</table>

<p>The cutdirsmapper has no corresponding <code>&lt;mapper&gt;</code> <var>type</var> attribute.</p>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>dirs</td>
    <td>Number of directories to strip (must be a positive number).</td>
    <td>Yes</td>
  </tr>
</table>

</body>
</html>
