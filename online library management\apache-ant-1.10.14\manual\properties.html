<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
  <link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
  <title>Properties and PropertyHelpers</title>
</head>

<body>
  <h1>Properties</h1>

  <p>Properties are key-value pairs where Apache Ant tries to expand <code>${key}</code>
    to <code>value</code> at run time.</p>

  <p>There are many tasks that can set properties; the most common one is
    the <a href="Tasks/property.html">property</a> task.  In addition properties can be defined
    via <a href="running.html">command line arguments</a> or similar mechanisms from outside of
    Ant.</p>

  <p>Normally property values can not be changed: once a property is set, most tasks will not allow
    its value to be modified.  In general properties are of global scope, i.e., once they have been
    defined they are available for any task or target invoked subsequently&mdash;it is not possible
    to set a property in a child build process created via
    the <a href="Tasks/ant.html">ant</a>, <a href="Tasks/antcall.html">antcall</a>
    or <a href="Tasks/subant.html">subant</a> tasks and make it available to the calling build
    process, though.</p>

  <p><em>Since Ant 1.8.0</em> the <a href="Tasks/local.html">local</a> task can be used to create
    properties that are locally scoped to a target or
    a <a href="Tasks/sequential.html">sequential</a> element like the one of
    the <a href="Tasks/macrodef.html">macrodef</a> task.</p>

  <h2 id="built-in-props">Built-in Properties</h2>

  <p>Ant provides access to all system properties as if they had been defined using
    a <code>&lt;property&gt;</code> task.  For example, <samp>${os.name}</samp> expands to the name
    of the operating system.</p>

  <p>For a list of system properties,
    see <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/System.html#getProperties--"
    target="_top">the javadoc of System.getProperties</a>.
  </p>

  <p>In addition, Ant has some built-in properties:</p>
  <dl>
    <dt><code>basedir</code></dt>
    <dd>the absolute path of the project's basedir (as set with
      the <var>basedir</var> attribute
      of <a href="using.html#projects">&lt;project&gt;</a>).</dd>
    <dt><code>ant.file</code></dt>
    <dd>the absolute path of the buildfile.</dd>
    <dt><code>ant.version</code></dt>
    <dd>the version of Ant</dd>
    <dt><code>ant.project.name</code></dt>
    <dd>the name of the project that is currently executing; it is set in the <var>name</var>
      attribute of <code>&lt;project&gt;</code>.</dd>
    <dt><code>ant.project.default-target</code></dt>
    <dd>the name of the currently executing project's default target; it is set via
      the <var>default</var> attribute of <code>&lt;project&gt;</code>.</dd>
    <dt><code>ant.project.invoked-targets</code></dt>
    <dd>a comma-separated list of the targets that have been specified (on the command line, inside
      of an IDE, by an <code>&lt;ant&gt;</code> task, etc...) when invoking the current
      project.<br/>This property is set properly when the first target is executed. If you use it in
      the implicit target (directly under the <code>&lt;project&gt;</code> tag) the list will be
      empty if no target has been specified while it will contain the project's default target in
      this case for tasks nested into targets.</dd>
    <dt><code>ant.java.version</code></dt>
    <dd>the JVM version Ant detected; currently it can hold the
      values <q>9</q>, <q>1.8</q>, <q>1.7</q>, <q>1.6</q>, <q>1.5</q>, <q>1.4</q>, <q>1.3</q>
      and <q>1.2</q>.</dd>
    <dt><code>ant.core.lib</code></dt>
    <dd>the absolute path of the <samp>ant.jar</samp> file.</dd>
  </dl>

  <p>There is also another property, but this is set by the launcher script and therefore maybe not
    set inside IDEs:</p>
  <dl>
    <dt><code>ant.home</code></dt>
    <dd>home directory of Ant</dd>
  </dl>

  <p>The following property is only set if Ant is started via the Launcher class (which means it may
    not be set inside IDEs either):</p>
  <dl>
    <dt><code>ant.library.dir</code></dt>
    <dd>the directory that has been used to load Ant's jars from.  In most cases this
      is <samp>ANT_HOME/lib</samp>.</dd>
  </dl>

  <h1 id="propertyHelper">PropertyHelpers</h1>

  <p>Ant's property handling is accomplished by an instance
    of <code class="code">org.apache.tools.ant.PropertyHelper</code> associated with the current
    Project.  You can learn more about this class by examining Ant's Java API. In Ant 1.8 the
    <code class="code">PropertyHelper</code> class was much reworked and now itself employs a number
    of helper classes (actually instances of
    the <code class="code">org.apache.tools.ant.PropertyHelper$Delegate</code> marker interface) to
    take care of discrete tasks such as property setting, retrieval, parsing, etc. This makes Ant's
    property handling highly extensible; also of interest is the
    new <a href="Tasks/propertyhelper.html">propertyhelper</a> task used to manipulate the
    PropertyHelper and its delegates from the context of the Ant buildfile.</p>

  <p>There are three sub-interfaces of <code class="code">Delegate</code> that may be useful to
  implement:</p>

  <ul>
    <li><code>org.apache.tools.ant.property.PropertyExpander</code> is responsible for finding the
      property name inside a string in the first place (the default extracts <samp>foo</samp>
      from <samp>${foo}</samp>).

      <p>This is the interface you'd implement if you wanted to invent your own property
        syntax&mdash;or allow nested property expansions since the default implementation doesn't
        balance braces
        (see <a href="https://gitbox.apache.org/repos/asf?p=ant-antlibs-props.git;a=blob;f=src/main/org/apache/ant/props/NestedPropertyExpander.java;hb=HEAD"
        target="_top"><code class="code">NestedPropertyExpander</code> in the <samp>props</samp> Antlib</a> for
        an example).</p>
    </li>

    <li><code class="code">org.apache.tools.ant.PropertyHelper$PropertyEvaluator</code> is used to
      expand <samp>${some-string}</samp> into an <code>Object</code>.

      <p>This is the interface you'd implement if you want to provide your own storage independent
        of Ant's project instance&mdash;the interface represents the reading end.  An example for
        this would be <code class="code">org.apache.tools.ant.property.LocalProperties</code> which
        implements storage for <a href="Tasks/local.html">local properties</a>.</p>

      <p>Another reason to implement this interface is if you wanted to provide your own "property
        protocol" like expanding <code>toString:foo</code> by looking up the project
        reference <samp>foo</samp> and invoking <code class="code">toString()</code> on it (which is
        already implemented in Ant, see below).</p>
    </li>

    <li><code class="code">org.apache.tools.ant.PropertyHelper$PropertySetter</code> is responsible
      for setting properties.

      <p>This is the interface you'd implement if you want to provide your own storage independent
        of Ant's project instance&mdash;the interface represents the writing end.  An example for
        this would be <code class="code">org.apache.tools.ant.property.LocalProperties</code> which
        implements storage for <a href="Tasks/local.html">local properties</a>.</p>
    </li>

    <li><code class="code">org.apache.tools.ant.PropertyHelper$PropertyEnumerator</code>
      is responsible for enumerating property names.

      <p>This is the interface you'd implement if you want to provide
        your own storage independent of Ant's project
        instance&mdash;the interface represents part of the reading
        end.  An example for this would
        be <code class="code">org.apache.tools.ant.property.LocalProperties</code>
        which implements storage for <a href="Tasks/local.html">local
        properties</a>.</p>

      <p><em>This interface has been added with Ant 1.10.9.</em></p>
    </li>
  </ul>

  <p>The default <code class="code">PropertyExpander</code> looks similar to:</p>

<pre>
public class DefaultExpander implements PropertyExpander {
    public String parsePropertyName(String s, ParsePosition pos,
                                    ParseNextProperty notUsed) {
        int index = pos.getIndex();
        if (s.indexOf("${", index) == index) {
            int end = s.indexOf('}', index);
            if (end &lt; 0) {
                throw new BuildException("Syntax error in property: " + s);
            }
            int start = index + 2;
            pos.setIndex(end + 1);
            return s.substring(start, end);
        }
        return null;
    }
}</pre>

  <p>The logic that replaces <samp>${toString:<i>some-id</i>}</samp> with the stringified
    representation of the object with <var>id</var> <samp><i>some-id</i></samp> inside the current
    build is contained in a <code class="code">PropertyEvaluator</code> similar to the following
    code:</p>

<pre>
public class ToStringEvaluator implements PropertyHelper.PropertyEvaluator {
    private static final String prefix = "toString:";
    public Object evaluate(String property, PropertyHelper propertyHelper) {
        Object o = null;
        if (property.startsWith(prefix) && propertyHelper.getProject() != null) {
            o = propertyHelper.getProject().getReference(
                    property.substring(prefix.length()));
        }
        return o == null ? null : o.toString();
    }
}</pre>

  <h1>Property Expansion</h1>

  <p>When Ant encounters a construct <samp>${some-text}</samp> the exact parsing semantics are
    subject to the configured property helper delegates.</p>

  <h2><code>$$</code> Expansion</h2>

  <p>In its default configuration Ant will expand the text <q>$$</q> to a single <q>$</q> and
    suppress the normal property expansion mechanism for the text immediately following it,
    i.e., <samp>$${key}</samp> expands to <samp>${key}</samp> and not <code>value</code> even though
    a property named <code>key</code> was defined and had the value <samp>value</samp>.  This can be
    used to escape literal <q>$</q> characters and is useful in constructs that only look like
    property expansions or when you want to provide diagnostic output like in</p>

<pre>&lt;echo&gt;$${builddir}=${builddir}&lt;/echo&gt;</pre>

  <p>which will echo this message:</p>

<pre class="output">${builddir}=build/classes</pre>

  <p>if the property <code>builddir</code> has the value <samp>build/classes</samp>.</p>

  <p>In order to maintain backward compatibility with older Ant releases, a single <q>$</q>
    character encountered apart from a property-like construct (including a matched pair of french
    braces) will be interpreted literally, that is, as <q>$</q>.  The "correct" way to specify this
    literal character, however, is by using the escaping mechanism unconditionally, so
    that <q>$$</q> is obtained by specifying <q>$$$$</q>.  Mixing the two approaches yields
    unpredictable results, as <q>$$$</q> results in <q>$$</q>.</p>

  <h2>Nesting of Braces</h2>

  <p>In its default configuration Ant will not try to balance braces in property expansions, it will
    only consume the text up to the first closing brace when creating a property name.  I.e. when
    expanding something like <samp>${a${b}}</samp> it will be translated into two parts:</p>

  <ol>
    <li>the expansion of property <samp>a${b</samp>&mdash;likely nothing useful.</li>
    <li>the literal text <samp>}</samp> resulting from the second closing brace</li>
  </ol>

  <p>This means you can't use easily expand properties whose names are stored in properties, but
    there are <a href="https://ant.apache.org/faq.html#propertyvalue-as-name-for-property"
    target="_top">some workarounds</a> for older versions of Ant. <em>Since Ant 1.8.0</em> using
    the <a href="https://ant.apache.org/antlibs/props/" target="_top">props Antlib</a> you can
    configure Ant to use the <code class="code">NestedPropertyExpander</code> defined there if you
    need such a feature.</p>

  <h2>Expanding a Property Reference</h2>

  <p>In its most simple form <samp>${key}</samp> is supposed to look up a property
    named <code>key</code> and expand to the value of the property.
    Additional <code>PropertyEvaluator</code>s may result in a different interpretation
    of <code>key</code>, though.</p>

  <p>The <a href="https://ant.apache.org/antlibs/props/" target="_top">props Antlib</a> provides a
    few interesting evaluators but there are also a few built-in ones.</p>

  <h3 id="toString">Getting the value of a Reference with <samp>${toString:}</samp></h3>

  <p>Any Ant type item which has been declared with a reference can also its string value extracted
    by using the <samp>${toString:}</samp> operation, with the name of the reference listed after
    the <code>toString:</code> text.  The <code class="code">toString()</code> method of the Java
    class instance that is referenced is invoked&mdash;all built in types strive to produce useful
    and relevant output in such an instance.</p>

  <p>For example, here is how to get a listing of the files in a fileset:<p>

<pre>
&lt;fileset id=&quot;sourcefiles&quot; dir=&quot;src&quot; includes=&quot;**/*.java&quot;/&gt;
&lt;echo&gt; sourcefiles = ${toString:sourcefiles} &lt;/echo&gt;</pre>

  <p>There is no guarantee that external types provide meaningful information in such a
    situation</p>

  <h3 id="ant.refid">Getting the value of a Reference with <samp>${ant.refid:}</samp></h3>

  <p>Any Ant type item which has been declared with a reference can also be used as a property by
    using the <samp>${ant.refid:}</samp> operation, with the name of the reference listed after
    the <code>ant.refid:</code> text.  The difference between this operation
    and <a href="#toString"><samp>${toString:}</samp></a> is that <samp>${ant.refid:}</samp> will
    expand to the referenced object itself.  In most circumstances
    the <code class="code">toString()</code> method will be invoked anyway, for example if
    the <samp>${ant.refid:}</samp> is surrounded by other text.</p>

  <p>This syntax is most useful when using a task with attribute setters that accept objects other
    than <code class="code">String</code>.  For example, if the setter accepts
    a <code class="code">Resource</code> object as in</p>

  <pre>public void setAttr(Resource r) { ... }</pre>

  <p>then the syntax can be used to pass in resource subclasses previously defined as references
    like</p>
<pre>
&lt;url url="https://ant.apache.org/" id="anturl"/&gt;
&lt;my:task attr="${ant.refid:anturl}"/&gt;</pre>

  <h2 id="if+unless">If/Unless Attributes</h2>
  <p>
    The <code>&lt;target&gt;</code> element and various tasks (such as <code>&lt;fail&gt;</code>)
    and task elements (such as <code>&lt;test&gt;</code> in <code>&lt;junit&gt;</code>)
    support <var>if</var> and <var>unless</var> attributes which can be used to control whether the
    item is run or otherwise takes effect.
  </p>
  <p>
    In Ant 1.7.1 and earlier, these attributes could only be property names.  The item was enabled
    if a property with that name was defined&mdash;even to be the empty string
    or <q>false</q>&mdash;and disabled if the property was not defined. For example, the following
    works but there is no way to override the file existence check negatively (only positively):
  </p>
  <pre>
&lt;target name="-check-use-file"&gt;
    &lt;available property="file.exists" file="some-file"/&gt;
&lt;/target&gt;
&lt;target name="use-file" depends="-check-use-file" <strong>if="file.exists"</strong>&gt;
    &lt;!-- do something requiring that file... --&gt;
&lt;/target>
&lt;target name="lots-of-stuff" depends="use-file,other-unconditional-stuff"/&gt;</pre>
  <p>
    <em>Since Ant 1.8.0</em>, you may instead use property expansion; a value of <q>true</q>
    (or <q>on</q> or <q>yes</q>) will enable the item, while <q>false</q> (or <q>off</q>
    or <q>no</q>) will disable it. Other values are still assumed to be property names and so the
    item is enabled only if the named property is defined.
  </p>
  <p>
    Compared to the older style, this gives you additional flexibility, because you can override the
    condition from the command line or parent scripts:
  </p>
  <pre>
&lt;target name="-check-use-file" <strong>unless="file.exists"</strong>&gt;
    &lt;available property="file.exists" file="some-file"/&gt;
&lt;/target>
&lt;target name="use-file" depends="-check-use-file" <strong>if="${file.exists}"</strong>&gt;
    &lt;!-- do something requiring that file... --&gt;
&lt;/target>
&lt;target name="lots-of-stuff" depends="use-file,other-unconditional-stuff"/&gt;</pre>
  <p>
    Now <kbd>ant -Dfile.exists=false lots-of-stuff</kbd> will run <q>other-unconditional-stuff</q>
    but not <q>use-file</q>, as you might expect, and you can disable the condition from another
    script too:
  </p>
  <pre>
&lt;antcall target="lots-of-stuff"&gt;
    &lt;param name="file.exists" value="false"/&gt;
&lt;/antcall&gt;</pre>
  <p>
    Similarly, an <var>unless</var> attribute disables the item if it is either the name of property
    which is defined, or if it evaluates to a <q>true</q>-like value. For example, the following
    allows you to define <code>skip.printing.message=true</code> in <samp>my-prefs.properties</samp>
    with the results you might expect:
  </p>
  <pre>
&lt;property file="my-prefs.properties"/&gt;
&lt;target name="print-message" <strong>unless="${skip.printing.message}"</strong>&gt;
    &lt;echo>hello!&lt;/echo>
&lt;/target&gt;</pre>

</body>
</html>
