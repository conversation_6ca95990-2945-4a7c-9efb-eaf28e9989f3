# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file declares the libraries for use in a given release of the components

# If you change this, change the checksum to match
m2.version=1.4.0
m2.url=https://repo1.maven.org/maven2/org/apache/maven/resolver
m2.artifact-name=maven-resolver-ant-tasks
m2.jar.name=${m2.artifact-name}-${m2.version}-uber.jar
#this is the URL of the antlib library, that is pulled down for everything else.
m2.antlib.url=${m2.url}/${m2.artifact-name}/${m2.version}/${m2.jar.name}
#this is the sha1 checksum of the artifact
m2.sha1.checksum=c4642858aa22465650ad2a469b24e22696177441

# Repository to use by default for fetching dependencies.
m2.repo=https://repo1.maven.org/maven2/

# hashes of libraries loaded over insecure connections
.sha256=2443dfe8d8974a38a390ee55c2757396f3e2350d4b18cc8dc489709bf5593b61

# Versions of different libraries. Please keep in alphabetical order, except
# when a specific dependency forces them to be out-of-order
ivy.version=2.5.1
ant-antunit.version=1.4.1
antlr.version=2.7.7
bcel.version=6.7.0
bsf.version=2.4.0
bsh.version=2.0b5
commons-net.version=3.9.0
commons-logging.version=1.1
commons-logging-api.version=${commons-logging.version}
js.version=20.1.0
js-scriptengine.version=${js.version}
# Note - When updating the hamcrest versions here, make sure to also update the
# "src-dist" target in build.xml to copy the correct hamcrest jars
# into the source distribution. Also update the hamcrest dependency
# version in src/etc/poms/pom.xml.
hamcrest-core.version=1.3
hamcrest-library.version=${hamcrest-core.version}
jai-core.version=1.1.3
jai-codec.version=1.1.3
netrexx.version=2.05
# Later 1.6 versions call themselves "jakarta.mail" but do not use the namespace yet
javax.mail.version=1.6.2
jakarta.mail.version=2.0.1
jakarta-regexp.version=1.4
# Later versions of Tomcat provide a jspc task
jasper-compiler.version=4.1.36
jasper-runtime.version=${jasper-compiler.version}
jdepend.version=2.9.1
jruby.version=1.6.8
# Note - When updating the junit.version here, make sure to also update the
# "src-dist" target in build.xml to copy the correct junit 4.x jar
# into the source distribution. Also update the junit dependency's version
# in src/etc/poms/pom.xml.
junit.version=4.13.2
rhino.version=1.7.14
junit-platform-launcher.version=1.10.0
# Only used for internal tests in Ant project
junit-vintage-engine.version=5.10.0
# Only used for internal tests in Ant project
junit-jupiter-engine.version=5.10.0
# Only used for internal tests in Ant project
junit-jupiter-params.version=${junit-jupiter-engine.version}
jsch.version=0.1.55
jython.version=2.7.3
# log4j 1.2.15 requires JMS and a few other Sun jars that are not in the m2 repo
log4j.version=1.2.14
oro.version=2.0.8
servlet-api.version=2.3
which.version=1.0
xalan.version=2.7.3
serializer.version=${xalan.version}
xml-resolver.version=1.2
xz.version=1.9
# paired
jacl.version=1.2.6
tcljava.version=${jacl.version}
