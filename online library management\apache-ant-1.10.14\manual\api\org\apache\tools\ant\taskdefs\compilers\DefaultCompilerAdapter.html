<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DefaultCompilerAdapter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.compilers, class: DefaultCompilerAdapter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.compilers</a></div>
<h1 title="Class DefaultCompilerAdapter" class="title">Class DefaultCompilerAdapter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></code>, <code><a href="CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="Gcj.html" title="class in org.apache.tools.ant.taskdefs.compilers">Gcj</a></code>, <code><a href="Javac12.html" title="class in org.apache.tools.ant.taskdefs.compilers">Javac12</a></code>, <code><a href="Javac13.html" title="class in org.apache.tools.ant.taskdefs.compilers">Javac13</a></code>, <code><a href="JavacExternal.html" title="class in org.apache.tools.ant.taskdefs.compilers">JavacExternal</a></code>, <code><a href="Jikes.html" title="class in org.apache.tools.ant.taskdefs.compilers">Jikes</a></code>, <code><a href="Jvc.html" title="class in org.apache.tools.ant.taskdefs.compilers">Jvc</a></code>, <code><a href="Kjc.html" title="class in org.apache.tools.ant.taskdefs.compilers">Kjc</a></code>, <code><a href="Sj.html" title="class in org.apache.tools.ant.taskdefs.compilers">Sj</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">DefaultCompilerAdapter</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a>, <a href="CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></span></div>
<div class="block">This is the default implementation for the CompilerAdapter interface.
 Currently, this is a cut-and-paste of the original javac task.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></code></div>
<div class="col-second even-row-color"><code><a href="#attributes" class="member-name-link">attributes</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color"><code><a href="#bootclasspath" class="member-name-link">bootclasspath</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#compileClasspath" class="member-name-link">compileClasspath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#compileList" class="member-name-link">compileList</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#compileSourcepath" class="member-name-link">compileSourcepath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#debug" class="member-name-link">debug</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#depend" class="member-name-link">depend</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#deprecation" class="member-name-link">deprecation</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#destDir" class="member-name-link">destDir</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#encoding" class="member-name-link">encoding</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#extdirs" class="member-name-link">extdirs</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#includeAntRuntime" class="member-name-link">includeAntRuntime</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#includeJavaRuntime" class="member-name-link">includeJavaRuntime</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../../Location.html" title="class in org.apache.tools.ant">Location</a></code></div>
<div class="col-second odd-row-color"><code><a href="#location" class="member-name-link">location</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#lSep" class="member-name-link">lSep</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#memoryInitialSize" class="member-name-link">memoryInitialSize</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#memoryMaximumSize" class="member-name-link">memoryMaximumSize</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color"><code><a href="#modulepath" class="member-name-link">modulepath</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#moduleSourcepath" class="member-name-link">moduleSourcepath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#optimize" class="member-name-link">optimize</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color"><code><a href="#project" class="member-name-link">project</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#release" class="member-name-link">release</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#src" class="member-name-link">src</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#target" class="member-name-link">target</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color"><code><a href="#upgrademodulepath" class="member-name-link">upgrademodulepath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#verbose" class="member-name-link">verbose</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DefaultCompilerAdapter</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCurrentCompilerArgs(org.apache.tools.ant.types.Commandline)" class="member-name-link">addCurrentCompilerArgs</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the command line arguments specific to the current implementation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addExtdirsToClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">addExtdirsToClasspath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_1Plus()" class="member-name-link">assumeJava1_1Plus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.1+ command line switches?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_2Plus()" class="member-name-link">assumeJava1_2Plus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.2+ command line switches?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_3Plus()" class="member-name-link">assumeJava1_3Plus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.3+ command line switches?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_4Plus()" class="member-name-link">assumeJava1_4Plus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.4+ command line switches?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_5Plus()" class="member-name-link">assumeJava1_5Plus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.5+ command line switches?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_6Plus()" class="member-name-link">assumeJava1_6Plus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.6+ command line switches?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_7Plus()" class="member-name-link">assumeJava1_7Plus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.7+ command line switches?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava1_8Plus()" class="member-name-link">assumeJava1_8Plus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 1.8+ command line switches?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava10Plus()" class="member-name-link">assumeJava10Plus</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 10+ command line switches?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava11()" class="member-name-link">assumeJava11</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_1Plus, if necessary combined with !assumeJava1_2Plus</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava12()" class="member-name-link">assumeJava12</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_2Plus, if necessary combined with !assumeJava1_3Plus</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava13()" class="member-name-link">assumeJava13</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_3Plus, if necessary combined with !assumeJava1_4Plus</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava14()" class="member-name-link">assumeJava14</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_4Plus, if necessary combined with !assumeJava1_5Plus</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava15()" class="member-name-link">assumeJava15</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_5Plus, if necessary combined with !assumeJava1_6Plus</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava16()" class="member-name-link">assumeJava16</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_6Plus, if necessary combined with !assumeJava1_7Plus</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava17()" class="member-name-link">assumeJava17</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_7Plus, if necessary combined with !assumeJava1_8Plus</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava18()" class="member-name-link">assumeJava18</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_8Plus, if necessary combined with !assumeJava9Plus</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava19()" class="member-name-link">assumeJava19</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #assumeJava9 instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#assumeJava9()" class="member-name-link">assumeJava9</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava9Plus, in the future if necessary combined with !assumeJava10Plus</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#assumeJava9Plus()" class="member-name-link">assumeJava9Plus</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shall we assume JDK 9+ command line switches?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeExternalCompile(java.lang.String%5B%5D,int)" class="member-name-link">executeExternalCompile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args,
 int&nbsp;firstFileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do the compile with the specified arguments.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeExternalCompile(java.lang.String%5B%5D,int,boolean)" class="member-name-link">executeExternalCompile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args,
 int&nbsp;firstFileName,
 boolean&nbsp;quoteFiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do the compile with the specified arguments.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBootClassPath()" class="member-name-link">getBootClassPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Combines a user specified bootclasspath with the system
 bootclasspath taking build.sysclasspath into account.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompileClasspath()" class="member-name-link">getCompileClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds the compilation classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJavac()" class="member-name-link">getJavac</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Javac task instance associated with this compiler adapter</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulepath()" class="member-name-link">getModulepath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds the modulepath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulesourcepath()" class="member-name-link">getModulesourcepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds the modulesourcepath for multi module compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNoDebugArgument()" class="member-name-link">getNoDebugArgument</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The argument the compiler wants to see if the debug attribute
 has been set to false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the project this compiler adapter was created in.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSupportedFileExtensions()" class="member-name-link">getSupportedFileExtensions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">By default, only recognize files with a Java extension,
 but specialized compilers can recognize multiple kinds
 of files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUpgrademodulepath()" class="member-name-link">getUpgrademodulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds the upgrademodulepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)" class="member-name-link">logAndAddFilesToCompile</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavac(org.apache.tools.ant.taskdefs.Javac)" class="member-name-link">setJavac</a><wbr>(<a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a>&nbsp;attributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Javac instance which contains the configured compilation
 attributes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupJavacCommand()" class="member-name-link">setupJavacCommand</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set up the command line.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupJavacCommand(boolean)" class="member-name-link">setupJavacCommand</a><wbr>(boolean&nbsp;debugLevelCheck)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Does the command line argument processing for classic and adds
 the files to compile as well.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)" class="member-name-link">setupJavacCommandlineSwitches</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the command line arguments for the switches.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline,boolean)" class="member-name-link">setupJavacCommandlineSwitches</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 boolean&nbsp;useDebugLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Does the command line argument processing common to classic and
 modern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupModernJavacCommand()" class="member-name-link">setupModernJavacCommand</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Does the command line argument processing for modern and adds
 the files to compile as well.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupModernJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)" class="member-name-link">setupModernJavacCommandlineSwitches</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Does the command line argument processing for modern.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.compilers.CompilerAdapter">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.compilers.<a href="CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></h3>
<code><a href="CompilerAdapter.html#execute()">execute</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="lSep">
<h3>lSep</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">lSep</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="src">
<h3>src</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">src</span></div>
</section>
</li>
<li>
<section class="detail" id="destDir">
<h3>destDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">destDir</span></div>
</section>
</li>
<li>
<section class="detail" id="encoding">
<h3>encoding</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">encoding</span></div>
</section>
</li>
<li>
<section class="detail" id="debug">
<h3>debug</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">debug</span></div>
</section>
</li>
<li>
<section class="detail" id="optimize">
<h3>optimize</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">optimize</span></div>
</section>
</li>
<li>
<section class="detail" id="deprecation">
<h3>deprecation</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">deprecation</span></div>
</section>
</li>
<li>
<section class="detail" id="depend">
<h3>depend</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">depend</span></div>
</section>
</li>
<li>
<section class="detail" id="verbose">
<h3>verbose</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">verbose</span></div>
</section>
</li>
<li>
<section class="detail" id="target">
<h3>target</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">target</span></div>
</section>
</li>
<li>
<section class="detail" id="release">
<h3>release</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">release</span></div>
</section>
</li>
<li>
<section class="detail" id="bootclasspath">
<h3>bootclasspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">bootclasspath</span></div>
</section>
</li>
<li>
<section class="detail" id="extdirs">
<h3>extdirs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">extdirs</span></div>
</section>
</li>
<li>
<section class="detail" id="compileClasspath">
<h3>compileClasspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">compileClasspath</span></div>
</section>
</li>
<li>
<section class="detail" id="modulepath">
<h3>modulepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">modulepath</span></div>
</section>
</li>
<li>
<section class="detail" id="upgrademodulepath">
<h3>upgrademodulepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">upgrademodulepath</span></div>
</section>
</li>
<li>
<section class="detail" id="compileSourcepath">
<h3>compileSourcepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">compileSourcepath</span></div>
</section>
</li>
<li>
<section class="detail" id="moduleSourcepath">
<h3>moduleSourcepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">moduleSourcepath</span></div>
</section>
</li>
<li>
<section class="detail" id="project">
<h3>project</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">project</span></div>
</section>
</li>
<li>
<section class="detail" id="location">
<h3>location</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../Location.html" title="class in org.apache.tools.ant">Location</a></span>&nbsp;<span class="element-name">location</span></div>
</section>
</li>
<li>
<section class="detail" id="includeAntRuntime">
<h3>includeAntRuntime</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">includeAntRuntime</span></div>
</section>
</li>
<li>
<section class="detail" id="includeJavaRuntime">
<h3>includeJavaRuntime</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">includeJavaRuntime</span></div>
</section>
</li>
<li>
<section class="detail" id="memoryInitialSize">
<h3>memoryInitialSize</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">memoryInitialSize</span></div>
</section>
</li>
<li>
<section class="detail" id="memoryMaximumSize">
<h3>memoryMaximumSize</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">memoryMaximumSize</span></div>
</section>
</li>
<li>
<section class="detail" id="compileList">
<h3>compileList</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</span>&nbsp;<span class="element-name">compileList</span></div>
</section>
</li>
<li>
<section class="detail" id="attributes">
<h3>attributes</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></span>&nbsp;<span class="element-name">attributes</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DefaultCompilerAdapter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DefaultCompilerAdapter</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setJavac(org.apache.tools.ant.taskdefs.Javac)">
<h3>setJavac</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavac</span><wbr><span class="parameters">(<a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a>&nbsp;attributes)</span></div>
<div class="block">Set the Javac instance which contains the configured compilation
 attributes.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="CompilerAdapter.html#setJavac(org.apache.tools.ant.taskdefs.Javac)">setJavac</a></code>&nbsp;in interface&nbsp;<code><a href="CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>attributes</code> - a configured Javac task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJavac()">
<h3>getJavac</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></span>&nbsp;<span class="element-name">getJavac</span>()</div>
<div class="block">Get the Javac task instance associated with this compiler adapter</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the configured Javac task instance used by this adapter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSupportedFileExtensions()">
<h3>getSupportedFileExtensions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getSupportedFileExtensions</span>()</div>
<div class="block">By default, only recognize files with a Java extension,
 but specialized compilers can recognize multiple kinds
 of files.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="CompilerAdapterExtension.html#getSupportedFileExtensions()">getSupportedFileExtensions</a></code>&nbsp;in interface&nbsp;<code><a href="CompilerAdapterExtension.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapterExtension</a></code></dd>
<dt>Returns:</dt>
<dd>list of source file extensions recognized by this
 compiler adapter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">Get the project this compiler adapter was created in.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the owner project</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompileClasspath()">
<h3>getCompileClasspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getCompileClasspath</span>()</div>
<div class="block">Builds the compilation classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the compilation class path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModulepath()">
<h3>getModulepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulepath</span>()</div>
<div class="block">Builds the modulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the modulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUpgrademodulepath()">
<h3>getUpgrademodulepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getUpgrademodulepath</span>()</div>
<div class="block">Builds the upgrademodulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the upgrademodulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModulesourcepath()">
<h3>getModulesourcepath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulesourcepath</span>()</div>
<div class="block">Builds the modulesourcepath for multi module compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the modulesourcepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">
<h3>setupJavacCommandlineSwitches</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupJavacCommandlineSwitches</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</span></div>
<div class="block">Get the command line arguments for the switches.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline,boolean)">
<h3>setupJavacCommandlineSwitches</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupJavacCommandlineSwitches</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 boolean&nbsp;useDebugLevel)</span></div>
<div class="block">Does the command line argument processing common to classic and
 modern.  Doesn't add the files to compile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dd><code>useDebugLevel</code> - if true set set the debug level with the -g switch</dd>
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupModernJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">
<h3>setupModernJavacCommandlineSwitches</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupModernJavacCommandlineSwitches</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</span></div>
<div class="block">Does the command line argument processing for modern.  Doesn't
 add the files to compile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupModernJavacCommand()">
<h3>setupModernJavacCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupModernJavacCommand</span>()</div>
<div class="block">Does the command line argument processing for modern and adds
 the files to compile as well.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupJavacCommand()">
<h3>setupJavacCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupJavacCommand</span>()</div>
<div class="block">Set up the command line.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupJavacCommand(boolean)">
<h3>setupJavacCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupJavacCommand</span><wbr><span class="parameters">(boolean&nbsp;debugLevelCheck)</span></div>
<div class="block">Does the command line argument processing for classic and adds
 the files to compile as well.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debugLevelCheck</code> - if true set the debug level with the -g switch</dd>
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">
<h3>logAndAddFilesToCompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logAndAddFilesToCompile</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</span></div>
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="executeExternalCompile(java.lang.String[],int)">
<h3>executeExternalCompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">executeExternalCompile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args,
 int&nbsp;firstFileName)</span></div>
<div class="block">Do the compile with the specified arguments.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - - arguments to pass to process on command line</dd>
<dd><code>firstFileName</code> - - index of the first source file in args,
 if the index is negative, no temporary file will ever be
 created, but this may hit the command line length limit on your
 system.</dd>
<dt>Returns:</dt>
<dd>the exit code of the compilation</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="executeExternalCompile(java.lang.String[],int,boolean)">
<h3>executeExternalCompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">executeExternalCompile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;args,
 int&nbsp;firstFileName,
 boolean&nbsp;quoteFiles)</span></div>
<div class="block">Do the compile with the specified arguments.

 <p>The working directory if the executed process will be the
 project's base directory.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - - arguments to pass to process on command line</dd>
<dd><code>firstFileName</code> - - index of the first source file in args,
 if the index is negative, no temporary file will ever be
 created, but this may hit the command line length limit on your
 system.</dd>
<dd><code>quoteFiles</code> - - if set to true, filenames containing
 spaces will be quoted when they appear in the external file.
 This is necessary when running JDK 1.4's javac and probably
 others.</dd>
<dt>Returns:</dt>
<dd>the exit code of the compilation</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addExtdirsToClasspath(org.apache.tools.ant.types.Path)">
<h3>addExtdirsToClasspath</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExtdirsToClasspath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use org.apache.tools.ant.types.Path#addExtdirs instead.</div>
</div>
<div class="block">Add extdirs to classpath</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addCurrentCompilerArgs(org.apache.tools.ant.types.Commandline)">
<h3>addCurrentCompilerArgs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addCurrentCompilerArgs</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</span></div>
<div class="block">Adds the command line arguments specific to the current implementation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava11()">
<h3>assumeJava11</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava11</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_1Plus, if necessary combined with !assumeJava1_2Plus</div>
</div>
<div class="block">Shall we assume JDK 1.1 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.1</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_1Plus()">
<h3>assumeJava1_1Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_1Plus</span>()</div>
<div class="block">Shall we assume JDK 1.1+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.1 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava12()">
<h3>assumeJava12</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava12</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_2Plus, if necessary combined with !assumeJava1_3Plus</div>
</div>
<div class="block">Shall we assume JDK 1.2 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.2</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_2Plus()">
<h3>assumeJava1_2Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_2Plus</span>()</div>
<div class="block">Shall we assume JDK 1.2+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.2 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava13()">
<h3>assumeJava13</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava13</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_3Plus, if necessary combined with !assumeJava1_4Plus</div>
</div>
<div class="block">Shall we assume JDK 1.3 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.3</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_3Plus()">
<h3>assumeJava1_3Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_3Plus</span>()</div>
<div class="block">Shall we assume JDK 1.3+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.3 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava14()">
<h3>assumeJava14</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava14</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_4Plus, if necessary combined with !assumeJava1_5Plus</div>
</div>
<div class="block">Shall we assume JDK 1.4 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.4</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_4Plus()">
<h3>assumeJava1_4Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_4Plus</span>()</div>
<div class="block">Shall we assume JDK 1.4+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.4 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava15()">
<h3>assumeJava15</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava15</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_5Plus, if necessary combined with !assumeJava1_6Plus</div>
</div>
<div class="block">Shall we assume JDK 1.5 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 1.5</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_5Plus()">
<h3>assumeJava1_5Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_5Plus</span>()</div>
<div class="block">Shall we assume JDK 1.5+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.5 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava16()">
<h3>assumeJava16</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava16</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_6Plus, if necessary combined with !assumeJava1_7Plus</div>
</div>
<div class="block">Shall we assume JDK 1.6 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 1.6</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_6Plus()">
<h3>assumeJava1_6Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_6Plus</span>()</div>
<div class="block">Shall we assume JDK 1.6+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.6 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava17()">
<h3>assumeJava17</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava17</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_7Plus, if necessary combined with !assumeJava1_8Plus</div>
</div>
<div class="block">Shall we assume JDK 1.7 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 1.7</dd>
<dt>Since:</dt>
<dd>Ant 1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_7Plus()">
<h3>assumeJava1_7Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_7Plus</span>()</div>
<div class="block">Shall we assume JDK 1.7+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.7 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava18()">
<h3>assumeJava18</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava18</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava1_8Plus, if necessary combined with !assumeJava9Plus</div>
</div>
<div class="block">Shall we assume JDK 1.8 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 1.8</dd>
<dt>Since:</dt>
<dd>Ant 1.8.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava1_8Plus()">
<h3>assumeJava1_8Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava1_8Plus</span>()</div>
<div class="block">Shall we assume JDK 1.8+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if jdk 1.8 and above</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava19()">
<h3>assumeJava19</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava19</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use #assumeJava9 instead</div>
</div>
<div class="block">Shall we assume JDK 9 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 9</dd>
<dt>Since:</dt>
<dd>Ant 1.9.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava9()">
<h3>assumeJava9</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava9</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since Ant 1.10.7, use assumeJava9Plus, in the future if necessary combined with !assumeJava10Plus</div>
</div>
<div class="block">Shall we assume JDK 9 command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 9</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava9Plus()">
<h3>assumeJava9Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava9Plus</span>()</div>
<div class="block">Shall we assume JDK 9+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 9+</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="assumeJava10Plus()">
<h3>assumeJava10Plus</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">assumeJava10Plus</span>()</div>
<div class="block">Shall we assume JDK 10+ command line switches?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if JDK 10+</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBootClassPath()">
<h3>getBootClassPath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getBootClassPath</span>()</div>
<div class="block">Combines a user specified bootclasspath with the system
 bootclasspath taking build.sysclasspath into account.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a non-null Path instance that combines the user
 specified and the system bootclasspath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNoDebugArgument()">
<h3>getNoDebugArgument</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getNoDebugArgument</span>()</div>
<div class="block">The argument the compiler wants to see if the debug attribute
 has been set to false.

 <p>A return value of <code>null</code> means no argument at all.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>"-g:none" unless we expect to invoke a JDK 1.1 compiler.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
