<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DefaultNative2Ascii (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.native2ascii, class: DefaultNative2Ascii">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.native2ascii</a></div>
<h1 title="Class DefaultNative2Ascii" class="title">Class DefaultNative2Ascii</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.native2ascii.DefaultNative2Ascii</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="KaffeNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">KaffeNative2Ascii</a></code>, <code><a href="SunNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">SunNative2Ascii</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">DefaultNative2Ascii</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a></span></div>
<div class="block">encapsulates the handling common to different Native2AsciiAdapter
 implementations.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DefaultNative2Ascii</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">No-arg constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFiles(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent,java.io.File,java.io.File)" class="member-name-link">addFiles</a><wbr>(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;log,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds source and dest files to the command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#convert(org.apache.tools.ant.taskdefs.optional.Native2Ascii,java.io.File,java.io.File)" class="member-name-link">convert</a><wbr>(<a href="../Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a>&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Splits the task into setting up the command line switches</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>protected abstract boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#run(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent)" class="member-name-link">run</a><wbr>(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;log)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Executes the command.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setup(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.taskdefs.optional.Native2Ascii)" class="member-name-link">setup</a><wbr>(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a>&nbsp;args)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets up the initial command line.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DefaultNative2Ascii</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DefaultNative2Ascii</span>()</div>
<div class="block">No-arg constructor.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="convert(org.apache.tools.ant.taskdefs.optional.Native2Ascii,java.io.File,java.io.File)">
<h3>convert</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">convert</span><wbr><span class="parameters">(<a href="../Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a>&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destFile)</span>
                      throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Splits the task into setting up the command line switches</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="Native2AsciiAdapter.html#convert(org.apache.tools.ant.taskdefs.optional.Native2Ascii,java.io.File,java.io.File)">convert</a></code>&nbsp;in interface&nbsp;<code><a href="Native2AsciiAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.native2ascii">Native2AsciiAdapter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>args</code> - the native 2 ascii arguments.</dd>
<dd><code>srcFile</code> - the source file.</dd>
<dd><code>destFile</code> - the destination file.</dd>
<dt>Returns:</dt>
<dd>run if the conversion was successful.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.
 (delegated to <a href="#setup(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.taskdefs.optional.Native2Ascii)"><code>setup</code></a>), adding the file names
 (delegated to <a href="#addFiles(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent,java.io.File,java.io.File)"><code>addFiles</code></a>) and running the tool
 (delegated to <a href="#run(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent)"><code>run</code></a>).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setup(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.taskdefs.optional.Native2Ascii)">
<h3>setup</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setup</span><wbr><span class="parameters">(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a>&nbsp;args)</span>
              throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sets up the initial command line.

 <p>only the -encoding argument and nested arg elements get
 handled here.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - Command line to add to</dd>
<dd><code>args</code> - provides the user-setting and access to Ant's
 logging system.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there was a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFiles(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent,java.io.File,java.io.File)">
<h3>addFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFiles</span><wbr><span class="parameters">(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;log,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dest)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds source and dest files to the command line.

 <p>This implementation adds them without any leading
 qualifiers, source first.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - Command line to add to</dd>
<dd><code>log</code> - provides access to Ant's logging system.</dd>
<dd><code>src</code> - the source file</dd>
<dd><code>dest</code> - the destination file</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there was a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="run(org.apache.tools.ant.types.Commandline,org.apache.tools.ant.ProjectComponent)">
<h3>run</h3>
<div class="member-signature"><span class="modifiers">protected abstract</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">run</span><wbr><span class="parameters">(<a href="../../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd,
 <a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a>&nbsp;log)</span>
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the command.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - Command line to execute</dd>
<dd><code>log</code> - provides access to Ant's logging system.</dd>
<dt>Returns:</dt>
<dd>whether execution was successful</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there was a problem.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
