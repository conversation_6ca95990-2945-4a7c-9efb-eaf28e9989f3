<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
<title>Tutorials</title>
<base target="mainFrame"/>
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Tutorials</h3>

<p><a href="tutorial-HelloWorldWithAnt.html">Hello World with Apache Ant</a><br/>
A step by step tutorial for starting Java programming with Ant.</p>

<p><a href="tutorial-writing-tasks.html">Writing Tasks</a><br/>
A step by step tutorial for writing tasks.</p>

<p><a href="tutorial-tasks-filesets-properties.html">Tasks using Properties, Filesets &amp; Paths</a><br/>
How to get and set properties and how to use nested filesets and paths
while writing tasks. Finally it explains how to contribute tasks to Ant.</p>

</body>
</html>
