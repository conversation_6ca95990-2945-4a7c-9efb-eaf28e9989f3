<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>XMLConstants (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, interface: XMLConstants">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Interface XMLConstants" class="title">Interface XMLConstants</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="XMLJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLJUnitResultFormatter</a></code>, <code><a href="XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">XMLConstants</span></div>
<div class="block"><p>Interface groups XML constants.
 Interface that groups all constants used throughout the <code>XML</code>
 documents that are generated by the <code>XMLJUnitResultFormatter</code>.
 </p>
 As of now the DTD is:
 <pre>
 &lt;!ELEMENT testsuites (testsuite*)&gt;

 &lt;!ELEMENT testsuite (properties, testcase*,
                     failure?, error?,
                     system-out?, system-err?)&gt;
 &lt;!ATTLIST testsuite name      CDATA #REQUIRED&gt;
 &lt;!ATTLIST testsuite tests     CDATA #REQUIRED&gt;
 &lt;!ATTLIST testsuite failures  CDATA #REQUIRED&gt;
 &lt;!ATTLIST testsuite errors    CDATA #REQUIRED&gt;
 &lt;!ATTLIST testsuite time      CDATA #REQUIRED&gt;
 &lt;!ATTLIST testsuite package   CDATA #IMPLIED&gt;
 &lt;!ATTLIST testsuite id        CDATA #IMPLIED&gt;


 &lt;!ELEMENT properties (property*)&gt;

 &lt;!ELEMENT property EMPTY&gt;
   &lt;!ATTLIST property name  CDATA #REQUIRED&gt;
   &lt;!ATTLIST property value CDATA #REQUIRED&gt;

 &lt;!ELEMENT testcase (failure?, error?)&gt;
   &lt;!ATTLIST testcase name       CDATA #REQUIRED&gt;
   &lt;!ATTLIST testcase classname  CDATA #IMPLIED&gt;
   &lt;!ATTLIST testcase time       CDATA #REQUIRED&gt;

 &lt;!ELEMENT failure (#PCDATA)&gt;
  &lt;!ATTLIST failure message CDATA #IMPLIED&gt;
  &lt;!ATTLIST failure type    CDATA #REQUIRED&gt;

 &lt;!ELEMENT error (#PCDATA)&gt;
   &lt;!ATTLIST error message CDATA #IMPLIED&gt;
   &lt;!ATTLIST error type    CDATA #REQUIRED&gt;

 &lt;!ELEMENT system-err (#PCDATA)&gt;

 &lt;!ELEMENT system-out (#PCDATA)&gt;

 </pre></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="XMLJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>XMLJUnitResultFormatter</code></a></li>
<li><a href="XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><code>XMLResultAggregator</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_CLASSNAME" class="member-name-link">ATTR_CLASSNAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">classname attribute for testcase elements</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_ERRORS" class="member-name-link">ATTR_ERRORS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">errors attribute for testsuite elements</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_FAILURES" class="member-name-link">ATTR_FAILURES</a></code></div>
<div class="col-last even-row-color">
<div class="block">failures attribute for testsuite elements</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_ID" class="member-name-link">ATTR_ID</a></code></div>
<div class="col-last odd-row-color">
<div class="block">id attribute</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_MESSAGE" class="member-name-link">ATTR_MESSAGE</a></code></div>
<div class="col-last even-row-color">
<div class="block">message attribute for failure elements</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_NAME" class="member-name-link">ATTR_NAME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">name attribute for property, testcase and testsuite elements</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_PACKAGE" class="member-name-link">ATTR_PACKAGE</a></code></div>
<div class="col-last even-row-color">
<div class="block">package attribute for the aggregate document</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_SKIPPED" class="member-name-link">ATTR_SKIPPED</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_TESTS" class="member-name-link">ATTR_TESTS</a></code></div>
<div class="col-last even-row-color">
<div class="block">tests attribute for testsuite elements</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_TIME" class="member-name-link">ATTR_TIME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">time attribute for testcase and testsuite elements</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTR_TYPE" class="member-name-link">ATTR_TYPE</a></code></div>
<div class="col-last even-row-color">
<div class="block">type attribute for failure and error elements</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTR_VALUE" class="member-name-link">ATTR_VALUE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">value attribute for property elements</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR" class="member-name-link">ERROR</a></code></div>
<div class="col-last even-row-color">
<div class="block">the error element</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FAILURE" class="member-name-link">FAILURE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the failure element</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#HOSTNAME" class="member-name-link">HOSTNAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">name of host running the tests</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PROPERTIES" class="member-name-link">PROPERTIES</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the properties element</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROPERTY" class="member-name-link">PROPERTY</a></code></div>
<div class="col-last even-row-color">
<div class="block">the property element</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SYSTEM_ERR" class="member-name-link">SYSTEM_ERR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the system-err element</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#SYSTEM_OUT" class="member-name-link">SYSTEM_OUT</a></code></div>
<div class="col-last even-row-color">
<div class="block">the system-out element</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TESTCASE" class="member-name-link">TESTCASE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the testcase element</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TESTSUITE" class="member-name-link">TESTSUITE</a></code></div>
<div class="col-last even-row-color">
<div class="block">the testsuite element</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TESTSUITES" class="member-name-link">TESTSUITES</a></code></div>
<div class="col-last odd-row-color">
<div class="block">the testsuites element for the aggregate document</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TIMESTAMP" class="member-name-link">TIMESTAMP</a></code></div>
<div class="col-last even-row-color">
<div class="block">timestamp of test cases</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="TESTSUITES">
<h3>TESTSUITES</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TESTSUITES</span></div>
<div class="block">the testsuites element for the aggregate document</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTSUITES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TESTSUITE">
<h3>TESTSUITE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TESTSUITE</span></div>
<div class="block">the testsuite element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTSUITE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TESTCASE">
<h3>TESTCASE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TESTCASE</span></div>
<div class="block">the testcase element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTCASE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR">
<h3>ERROR</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR</span></div>
<div class="block">the error element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ERROR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="FAILURE">
<h3>FAILURE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FAILURE</span></div>
<div class="block">the failure element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.FAILURE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SYSTEM_ERR">
<h3>SYSTEM_ERR</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SYSTEM_ERR</span></div>
<div class="block">the system-err element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.SYSTEM_ERR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SYSTEM_OUT">
<h3>SYSTEM_OUT</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SYSTEM_OUT</span></div>
<div class="block">the system-out element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.SYSTEM_OUT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_PACKAGE">
<h3>ATTR_PACKAGE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_PACKAGE</span></div>
<div class="block">package attribute for the aggregate document</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_PACKAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_NAME">
<h3>ATTR_NAME</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_NAME</span></div>
<div class="block">name attribute for property, testcase and testsuite elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_NAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_TIME">
<h3>ATTR_TIME</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_TIME</span></div>
<div class="block">time attribute for testcase and testsuite elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TIME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_ERRORS">
<h3>ATTR_ERRORS</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_ERRORS</span></div>
<div class="block">errors attribute for testsuite elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_ERRORS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_FAILURES">
<h3>ATTR_FAILURES</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_FAILURES</span></div>
<div class="block">failures attribute for testsuite elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_FAILURES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_TESTS">
<h3>ATTR_TESTS</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_TESTS</span></div>
<div class="block">tests attribute for testsuite elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TESTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_SKIPPED">
<h3>ATTR_SKIPPED</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_SKIPPED</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_SKIPPED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_TYPE">
<h3>ATTR_TYPE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_TYPE</span></div>
<div class="block">type attribute for failure and error elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_MESSAGE">
<h3>ATTR_MESSAGE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_MESSAGE</span></div>
<div class="block">message attribute for failure elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_MESSAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROPERTIES">
<h3>PROPERTIES</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROPERTIES</span></div>
<div class="block">the properties element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.PROPERTIES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROPERTY">
<h3>PROPERTY</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROPERTY</span></div>
<div class="block">the property element</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.PROPERTY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_VALUE">
<h3>ATTR_VALUE</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_VALUE</span></div>
<div class="block">value attribute for property elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_VALUE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_CLASSNAME">
<h3>ATTR_CLASSNAME</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_CLASSNAME</span></div>
<div class="block">classname attribute for testcase elements</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_CLASSNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTR_ID">
<h3>ATTR_ID</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTR_ID</span></div>
<div class="block">id attribute</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TIMESTAMP">
<h3>TIMESTAMP</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TIMESTAMP</span></div>
<div class="block">timestamp of test cases</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TIMESTAMP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="HOSTNAME">
<h3>HOSTNAME</h3>
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">HOSTNAME</span></div>
<div class="block">name of host running the tests</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.HOSTNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
