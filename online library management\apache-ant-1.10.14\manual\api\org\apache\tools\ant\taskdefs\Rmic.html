<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Rmic (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Rmic">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Rmic" class="title">Class Rmic</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Rmic</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Rmic</span>
<span class="extends-implements">extends <a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></span></div>
<div class="block"><p>Runs the rmic compiler against classes.</p>

 <p>Rmic can be run on a single class (as specified with the classname
 attribute) or a number of classes at once (all classes below base that
 are neither _Stub nor _Skel classes).  If you want to rmic a single
 class and this class is a class nested into another class, you have to
 specify the classname in the form <code>Outer$$Inner</code> instead of
 <code>Outer.Inner</code>.</p>

 <p>It is possible to refine the set of files that are being rmiced. This can
 be done with the <i>includes</i>, <i>includesfile</i>, <i>excludes</i>,
 <i>excludesfile</i> and <i>defaultexcludes</i>
 attributes. With the <i>includes</i> or <i>includesfile</i> attribute you
 specify the files you want to have included by using patterns. The
 <i>exclude</i> or <i>excludesfile</i> attribute is used to specify
 the files you want to have excluded. This is also done with patterns. And
 finally with the <i>defaultexcludes</i> attribute, you can specify whether
 you want to use default exclusions or not. See the section on
 directory based tasks, on how the
 inclusion/exclusion of files works, and how to write patterns.</p>

 <p>This task forms an implicit FileSet and
 supports all attributes of <code>&lt;fileset&gt;</code>
 (<code>dir</code> becomes <code>base</code>) as well as the nested
 <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code> and
 <code>&lt;patternset&gt;</code> elements.</p>

 <p>It is possible to use different compilers. This can be selected
 with the &quot;build.rmic&quot; property or the <code>compiler</code>
 attribute. <span id="compilervalues">There are three choices</span>:</p>

 <ul>
   <li>sun (the standard compiler of the JDK)</li>
   <li>kaffe (the standard compiler of
       <a href="https://github.com/kaffe/kaffe">Kaffe</a>)</li>
   <li>weblogic</li>
 </ul>

 <p>The miniRMI
 project contains a compiler implementation for this task as well,
 please consult miniRMI's documentation to learn how to use it.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Rmic.ImplementationSpecificArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</a></code></div>
<div class="col-last even-row-color">
<div class="block">Adds an "compiler" attribute to Commandline$Attribute used to
 filter command line attributes based on the current
 implementation.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_BASE_NOT_SET" class="member-name-link">ERROR_BASE_NOT_SET</a></code></div>
<div class="col-last even-row-color">
<div class="block">base attribute not set message</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_LOADING_CAUSED_EXCEPTION" class="member-name-link">ERROR_LOADING_CAUSED_EXCEPTION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">loaded error message</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_NO_BASE_EXISTS" class="member-name-link">ERROR_NO_BASE_EXISTS</a></code></div>
<div class="col-last even-row-color">
<div class="block">base not exists message</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NOT_A_DIR" class="member-name-link">ERROR_NOT_A_DIR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">base not a directory message</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_NOT_DEFINED" class="member-name-link">ERROR_NOT_DEFINED</a></code></div>
<div class="col-last even-row-color">
<div class="block">not defined message</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NOT_FOUND" class="member-name-link">ERROR_NOT_FOUND</a></code></div>
<div class="col-last odd-row-color">
<div class="block">could not be found message</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_RMIC_FAILED" class="member-name-link">ERROR_RMIC_FAILED</a></code></div>
<div class="col-last even-row-color">
<div class="block">rmic failed message</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_UNABLE_TO_VERIFY_CLASS" class="member-name-link">ERROR_UNABLE_TO_VERIFY_CLASS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">unable to verify message</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Rmic</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for Rmic.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.taskdefs.rmic.RmicAdapter)" class="member-name-link">add</a><wbr>(<a href="rmic/RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a>&nbsp;adapter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the compiler adapter explicitly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanup()" class="member-name-link">cleanup</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans up resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a nested classpath element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompilerArg()" class="member-name-link">createCompilerArg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an implementation specific command line argument.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompilerClasspath()" class="member-name-link">createCompilerClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The classpath to use when loading the compiler implementation
 if it is not a built-in one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExtdirs()" class="member-name-link">createExtdirs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Maybe creates a nested extdirs element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">execute by creating an instance of an implementation
 class and getting to do the work</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBase()" class="member-name-link">getBase</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the base directory to output generated class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassname()" class="member-name-link">getClassname</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the class name to compile.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompileList()" class="member-name-link">getCompileList</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompiler()" class="member-name-link">getCompiler</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the name of the current compiler</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentCompilerArgs()" class="member-name-link">getCurrentCompilerArgs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the additional implementation specific command line arguments.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDebug()" class="member-name-link">getDebug</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the debug flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestdir()" class="member-name-link">getDestdir</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the base directory to output the generated files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExecutable()" class="member-name-link">getExecutable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Explicitly specified name of the executable to use when forking
 - if any.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtdirs()" class="member-name-link">getExtdirs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the extension directories that will be used during the
 compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileList()" class="member-name-link">getFileList</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets file list to compile.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFiltering()" class="member-name-link">getFiltering</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets whether token filtering is set</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIdl()" class="member-name-link">getIdl</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets IDL flags.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIdlopts()" class="member-name-link">getIdlopts</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets additional arguments for idl compile.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIiop()" class="member-name-link">getIiop</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets iiop flags.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIiopopts()" class="member-name-link">getIiopopts</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets additional arguments for iiop.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeantruntime()" class="member-name-link">getIncludeantruntime</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets whether or not the ant classpath is to be included in the
 task's classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludejavaruntime()" class="member-name-link">getIncludejavaruntime</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets whether or not the java runtime should be included in this
 task's classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLoader()" class="member-name-link">getLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Classloader for the user-specified classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputDir()" class="member-name-link">getOutputDir</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the base directory to output the generated files,
 favoring destdir if set, otherwise defaulting to basedir.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRemoteInterface(java.lang.Class)" class="member-name-link">getRemoteInterface</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;testClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the topmost interface that extends Remote for a given
 class - if one exists.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSourceBase()" class="member-name-link">getSourceBase</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the source dirs to find the source java files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStubVersion()" class="member-name-link">getStubVersion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the JDK version for the generated stub code.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVerify()" class="member-name-link">getVerify</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get verify flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isValidRmiRemote(java.lang.String)" class="member-name-link">isValidRmiRemote</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Load named class and test whether it can be rmic'ed</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scanDir(java.io.File,java.lang.String%5B%5D,org.apache.tools.ant.util.FileNameMapper)" class="member-name-link">scanDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files,
 <a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;mapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scans the directory looking for class files to be compiled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBase(java.io.File)" class="member-name-link">setBase</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;base)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the location to store the compiled files; required</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassname(java.lang.String)" class="member-name-link">setClassname</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the class to run <code>rmic</code> against;
 optional</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;pathRef)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds to the classpath a reference to
 a &lt;path&gt; defined elsewhere.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompiler(java.lang.String)" class="member-name-link">setCompiler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the compiler implementation to use; optional,
 defaults to the value of the <code>build.rmic</code> property,
 or failing that, default compiler for the current VM</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate debug info (passes -g to rmic);
 optional, defaults to false</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destdir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the base directory to output the generated files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExecutable(java.lang.String)" class="member-name-link">setExecutable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name of the executable to use when forking.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtdirs(org.apache.tools.ant.types.Path)" class="member-name-link">setExtdirs</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;extDirs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the extension directories that will be used during the
 compilation; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFiltering(boolean)" class="member-name-link">setFiltering</a><wbr>(boolean&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets token filtering [optional], default=false</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIdl(boolean)" class="member-name-link">setIdl</a><wbr>(boolean&nbsp;idl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates that IDL output should be
 generated.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIdlopts(java.lang.String)" class="member-name-link">setIdlopts</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;idlOpts)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">pass additional arguments for IDL compile</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIiop(boolean)" class="member-name-link">setIiop</a><wbr>(boolean&nbsp;iiop)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates that IIOP compatible stubs should
 be generated; optional, defaults to false
 if not set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIiopopts(java.lang.String)" class="member-name-link">setIiopopts</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;iiopOpts)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set additional arguments for iiop</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeantruntime(boolean)" class="member-name-link">setIncludeantruntime</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether or not to include ant's own classpath in this task's
 classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludejavaruntime(boolean)" class="member-name-link">setIncludejavaruntime</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">task's classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setListfiles(boolean)" class="member-name-link">setListfiles</a><wbr>(boolean&nbsp;list)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, list the source files being handed off to the compiler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSourceBase(java.io.File)" class="member-name-link">setSourceBase</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceBase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">optional directory to save generated source files to.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStubVersion(java.lang.String)" class="member-name-link">setStubVersion</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stubVersion)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify the JDK version for the generated stub code.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerify(boolean)" class="member-name-link">setVerify</a><wbr>(boolean&nbsp;verify)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to enable verification so that the classes
 found by the directory match are
 checked to see if they implement java.rmi.Remote.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ERROR_RMIC_FAILED">
<h3>ERROR_RMIC_FAILED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_RMIC_FAILED</span></div>
<div class="block">rmic failed message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_RMIC_FAILED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_UNABLE_TO_VERIFY_CLASS">
<h3>ERROR_UNABLE_TO_VERIFY_CLASS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_UNABLE_TO_VERIFY_CLASS</span></div>
<div class="block">unable to verify message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_UNABLE_TO_VERIFY_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NOT_FOUND">
<h3>ERROR_NOT_FOUND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NOT_FOUND</span></div>
<div class="block">could not be found message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_FOUND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NOT_DEFINED">
<h3>ERROR_NOT_DEFINED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NOT_DEFINED</span></div>
<div class="block">not defined message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_DEFINED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_LOADING_CAUSED_EXCEPTION">
<h3>ERROR_LOADING_CAUSED_EXCEPTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_LOADING_CAUSED_EXCEPTION</span></div>
<div class="block">loaded error message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_LOADING_CAUSED_EXCEPTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_BASE_EXISTS">
<h3>ERROR_NO_BASE_EXISTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_BASE_EXISTS</span></div>
<div class="block">base not exists message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NO_BASE_EXISTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NOT_A_DIR">
<h3>ERROR_NOT_A_DIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NOT_A_DIR</span></div>
<div class="block">base not a directory message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_A_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_BASE_NOT_SET">
<h3>ERROR_BASE_NOT_SET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_BASE_NOT_SET</span></div>
<div class="block">base attribute not set message</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_BASE_NOT_SET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Rmic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Rmic</span>()</div>
<div class="block">Constructor for Rmic.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setBase(java.io.File)">
<h3>setBase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBase</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;base)</span></div>
<div class="block">Sets the location to store the compiled files; required</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>base</code> - the location to store the compiled files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destdir)</span></div>
<div class="block">Sets the base directory to output the generated files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destdir</code> - the base directory to output the generated files.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDestdir()">
<h3>getDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestdir</span>()</div>
<div class="block">Gets the base directory to output the generated files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the base directory to output the generated files.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputDir()">
<h3>getOutputDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getOutputDir</span>()</div>
<div class="block">Gets the base directory to output the generated files,
 favoring destdir if set, otherwise defaulting to basedir.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the actual directory to output to (either destdir or basedir)</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBase()">
<h3>getBase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBase</span>()</div>
<div class="block">Gets the base directory to output generated class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the location of the compiled files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassname(java.lang.String)">
<h3>setClassname</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassname</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span></div>
<div class="block">Sets the class to run <code>rmic</code> against;
 optional</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - the name of the class for rmic to create code for</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassname()">
<h3>getClassname</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClassname</span>()</div>
<div class="block">Gets the class name to compile.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the class to compile</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSourceBase(java.io.File)">
<h3>setSourceBase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSourceBase</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;sourceBase)</span></div>
<div class="block">optional directory to save generated source files to.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceBase</code> - the directory to save source files to.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSourceBase()">
<h3>getSourceBase</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getSourceBase</span>()</div>
<div class="block">Gets the source dirs to find the source java files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>sourceBase the directory containing the source files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStubVersion(java.lang.String)">
<h3>setStubVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStubVersion</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stubVersion)</span></div>
<div class="block">Specify the JDK version for the generated stub code.
 Specify &quot;1.1&quot; to pass the &quot;-v1.1&quot; option to rmic.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stubVersion</code> - the JDK version</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStubVersion()">
<h3>getStubVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStubVersion</span>()</div>
<div class="block">Gets the JDK version for the generated stub code.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>stubVersion</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFiltering(boolean)">
<h3>setFiltering</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFiltering</span><wbr><span class="parameters">(boolean&nbsp;filter)</span></div>
<div class="block">Sets token filtering [optional], default=false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - turn on token filtering</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFiltering()">
<h3>getFiltering</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getFiltering</span>()</div>
<div class="block">Gets whether token filtering is set</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>filtering</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">Generate debug info (passes -g to rmic);
 optional, defaults to false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - turn on debug info</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDebug()">
<h3>getDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDebug</span>()</div>
<div class="block">Gets the debug flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>debug</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath used for this compilation</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Creates a nested classpath element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;pathRef)</span></div>
<div class="block">Adds to the classpath a reference to
 a &lt;path&gt; defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pathRef</code> - the reference to add to the classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">Gets the classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerify(boolean)">
<h3>setVerify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerify</span><wbr><span class="parameters">(boolean&nbsp;verify)</span></div>
<div class="block">Flag to enable verification so that the classes
 found by the directory match are
 checked to see if they implement java.rmi.Remote.
 optional; This defaults to false if not set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verify</code> - turn on verification for classes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVerify()">
<h3>getVerify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getVerify</span>()</div>
<div class="block">Get verify flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>verify</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIiop(boolean)">
<h3>setIiop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIiop</span><wbr><span class="parameters">(boolean&nbsp;iiop)</span></div>
<div class="block">Indicates that IIOP compatible stubs should
 be generated; optional, defaults to false
 if not set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>iiop</code> - generate IIOP compatible stubs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIiop()">
<h3>getIiop</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIiop</span>()</div>
<div class="block">Gets iiop flags.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>iiop</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIiopopts(java.lang.String)">
<h3>setIiopopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIiopopts</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;iiopOpts)</span></div>
<div class="block">Set additional arguments for iiop</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>iiopOpts</code> - additional arguments for iiop</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIiopopts()">
<h3>getIiopopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getIiopopts</span>()</div>
<div class="block">Gets additional arguments for iiop.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>iiopOpts</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIdl(boolean)">
<h3>setIdl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIdl</span><wbr><span class="parameters">(boolean&nbsp;idl)</span></div>
<div class="block">Indicates that IDL output should be
 generated.  This defaults to false
 if not set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>idl</code> - generate IDL output</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIdl()">
<h3>getIdl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIdl</span>()</div>
<div class="block">Gets IDL flags.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the idl flag</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIdlopts(java.lang.String)">
<h3>setIdlopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIdlopts</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;idlOpts)</span></div>
<div class="block">pass additional arguments for IDL compile</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>idlOpts</code> - additional IDL arguments</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIdlopts()">
<h3>getIdlopts</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getIdlopts</span>()</div>
<div class="block">Gets additional arguments for idl compile.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the idl options</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileList()">
<h3>getFileList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getFileList</span>()</div>
<div class="block">Gets file list to compile.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the list of files to compile.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludeantruntime(boolean)">
<h3>setIncludeantruntime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeantruntime</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">Sets whether or not to include ant's own classpath in this task's
 classpath.
 Optional; default is <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - if true include ant's classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeantruntime()">
<h3>getIncludeantruntime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeantruntime</span>()</div>
<div class="block">Gets whether or not the ant classpath is to be included in the
 task's classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if ant's classpath is to be included</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludejavaruntime(boolean)">
<h3>setIncludejavaruntime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludejavaruntime</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">task's classpath.
 Enables or disables including the default run-time
 libraries from the executing VM; optional,
 defaults to false</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - if true include default run-time libraries</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludejavaruntime()">
<h3>getIncludejavaruntime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludejavaruntime</span>()</div>
<div class="block">Gets whether or not the java runtime should be included in this
 task's classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if default run-time libraries are included</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExtdirs(org.apache.tools.ant.types.Path)">
<h3>setExtdirs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtdirs</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;extDirs)</span></div>
<div class="block">Sets the extension directories that will be used during the
 compilation; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extDirs</code> - the extension directories to be used</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExtdirs()">
<h3>createExtdirs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createExtdirs</span>()</div>
<div class="block">Maybe creates a nested extdirs element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path object to be configured with the extension directories</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExtdirs()">
<h3>getExtdirs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getExtdirs</span>()</div>
<div class="block">Gets the extension directories that will be used during the
 compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the extension directories to be used</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompileList()">
<h3>getCompileList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getCompileList</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the compile list.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompiler(java.lang.String)">
<h3>setCompiler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompiler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</span></div>
<div class="block">Sets the compiler implementation to use; optional,
 defaults to the value of the <code>build.rmic</code> property,
 or failing that, default compiler for the current VM</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compiler</code> - the compiler implementation to use</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompiler()">
<h3>getCompiler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCompiler</span>()</div>
<div class="block">get the name of the current compiler</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the compiler</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCompilerArg()">
<h3>createCompilerArg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</a></span>&nbsp;<span class="element-name">createCompilerArg</span>()</div>
<div class="block">Adds an implementation specific command line argument.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an object to be configured with a command line argument</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentCompilerArgs()">
<h3>getCurrentCompilerArgs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getCurrentCompilerArgs</span>()</div>
<div class="block">Get the additional implementation specific command line arguments.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>array of command line arguments, guaranteed to be non-null.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExecutable(java.lang.String)">
<h3>setExecutable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExecutable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ex)</span></div>
<div class="block">Name of the executable to use when forking.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ex</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExecutable()">
<h3>getExecutable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getExecutable</span>()</div>
<div class="block">Explicitly specified name of the executable to use when forking
 - if any.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCompilerClasspath()">
<h3>createCompilerClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createCompilerClasspath</span>()</div>
<div class="block">The classpath to use when loading the compiler implementation
 if it is not a built-in one.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Path</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setListfiles(boolean)">
<h3>setListfiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setListfiles</span><wbr><span class="parameters">(boolean&nbsp;list)</span></div>
<div class="block">If true, list the source files being handed off to the compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - if true list the source files</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.taskdefs.rmic.RmicAdapter)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="rmic/RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a>&nbsp;adapter)</span></div>
<div class="block">Set the compiler adapter explicitly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>adapter</code> - RmicAdapter</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">execute by creating an instance of an implementation
 class and getting to do the work</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there's a problem with baseDir or RMIC</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanup()">
<h3>cleanup</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanup</span>()</div>
<div class="block">Cleans up resources.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="scanDir(java.io.File,java.lang.String[],org.apache.tools.ant.util.FileNameMapper)">
<h3>scanDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scanDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files,
 <a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;mapper)</span></div>
<div class="block">Scans the directory looking for class files to be compiled.
 The result is returned in the class variable compileList.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the base direction</dd>
<dd><code>files</code> - the list of files to scan</dd>
<dd><code>mapper</code> - the mapper of files to target files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isValidRmiRemote(java.lang.String)">
<h3>isValidRmiRemote</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isValidRmiRemote</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span></div>
<div class="block">Load named class and test whether it can be rmic'ed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - the name of the class to be tested</dd>
<dt>Returns:</dt>
<dd>true if the class can be rmic'ed</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRemoteInterface(java.lang.Class)">
<h3>getRemoteInterface</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getRemoteInterface</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;testClass)</span></div>
<div class="block">Returns the topmost interface that extends Remote for a given
 class - if one exists.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>testClass</code> - the class to be tested</dd>
<dt>Returns:</dt>
<dd>the topmost interface that extends Remote, or null if there
         is none.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLoader()">
<h3>getLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getLoader</span>()</div>
<div class="block">Classloader for the user-specified classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classloader</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
