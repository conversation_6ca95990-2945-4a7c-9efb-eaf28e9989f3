<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Length Task</title>
</head>

<body>

<h2>Length</h2>
<p><em>Since Apache Ant 1.6.3</em></p>
<h3>Description</h3>
<p>Display or set a property containing length information for a string, a file, or one or more
nested <a href="../Types/resources.html#collection">resource collections</a>.  Can also be used as
a <code>condition</code>.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col" rowspan="2">Attribute</th>
    <th scope="col" rowspan="2">Description</th>
    <th scope="col" colspan="2">Required</th>
  </tr>
  <tr>
    <th scope="col">Task</th>
    <th scope="col">Condition</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The property to set.</td>
    <td class="center">No; by default, output value to the log</td>
    <td rowspan="2">Ignored</td>
  </tr>
  <tr>
    <td>mode</td>
    <td>File length mode; when <q>all</q> the resulting value is the sum of all included resources'
      lengths; when <q>each</q> the task outputs the absolute path and length of each included
      resource, one per line.</td>
    <td class="center">No; default is <q>all</q></td>
  </tr>
  <tr>
    <td>file</td>
    <td>Single file whose length to report.</td>
    <td rowspan="3" colspan="2">One of these, or one or more nested filesets</td>
  </tr>
  <tr>
    <td>resource</td>
    <td class="left">Single resource whose length to report (using
      extended <a href="../properties.html#propertyHelper">properties handling</a>).  <em>Since Ant
      1.8.1</em>
    </td>
  </tr>
  <tr>
    <td>string</td>
    <td class="left">The string whose length to report.</td>
  </tr>
  <tr>
    <td>trim</td>
    <td>Whether to trim when operating on a string.</td>
    <td colspan="2">No; default is <q>false</q>, ignored unless <var>string</var> is set</td>
  </tr>
  <tr>
    <td>length</td>
    <td>Comparison length.</td>
    <td class="center" rowspan="2">Ignored</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>when</td>
    <td>Comparison
      type: <q>equal</q>, <q>eq</q>, <q>greater</q>, <q>gt</q>, <q>less</q>, <q>lt</q>, <q>ge</q>
      (greater or equal), <q>ne</q> (not equal), <q>le</q> (less or equal).</td>
    <td>No; default is <q>equal</q></td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>

<h4>resource collections</h4>
<p>You can include resources via nested <a href="../Types/resources.html#collection">resource
collection</a>s.</p>

<h3>Examples</h3>

<p>Store the length of the string <q>foo</q> in the property named <code>length.foo</code>.</p>
<pre>&lt;length string=&quot;foo&quot; property=&quot;length.foo&quot;/&gt;</pre>

<p>Store the length of file <samp>bar</samp> in the property named <code>length.bar</code>.</p>
<pre>&lt;length file=&quot;bar&quot; property=&quot;length.bar&quot;/&gt;</pre>

<p>Store the file paths of <samp>foo</samp> and <samp>bar</samp> and their length into the
property <code>length</code>.</p>
<pre>
&lt;length property=&quot;length&quot; mode=&quot;each&quot;&gt;
    &lt;fileset dir=&quot;.&quot; includes=&quot;foo,bar&quot;/&gt;
&lt;/length&gt;
</pre>

<p>Add the length of <samp>foo</samp> and <samp>bar</samp> and store the result in
property <code>length</code>.</p>
<pre>
&lt;length property=&quot;length&quot; mode=&quot;all&quot;&gt;
    &lt;fileset dir=&quot;.&quot; includes=&quot;foo,bar&quot;/&gt;
&lt;/length&gt;
</pre>

</body>
</html>
