<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ResourceSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources.selectors, interface: ResourceSelector">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resources.selectors</a></div>
<h1 title="Interface ResourceSelector" class="title">Interface ResourceSelector</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Subinterfaces:</dt>
<dd><code><a href="../../selectors/ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a></code>, <code><a href="../../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="And.html" title="class in org.apache.tools.ant.types.resources.selectors">And</a></code>, <code><a href="../../selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></code>, <code><a href="../../selectors/BaseExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a></code>, <code><a href="../../selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code>, <code><a href="../../selectors/BaseSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a></code>, <code><a href="Compare.html" title="class in org.apache.tools.ant.types.resources.selectors">Compare</a></code>, <code><a href="../../selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a></code>, <code><a href="../../selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a></code>, <code><a href="Date.html" title="class in org.apache.tools.ant.types.resources.selectors">Date</a></code>, <code><a href="../../selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></code>, <code><a href="../../selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></code>, <code><a href="../../selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></code>, <code><a href="../../selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></code>, <code><a href="../../selectors/ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a></code>, <code><a href="Exists.html" title="class in org.apache.tools.ant.types.resources.selectors">Exists</a></code>, <code><a href="../../selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></code>, <code><a href="../../selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></code>, <code><a href="InstanceOf.html" title="class in org.apache.tools.ant.types.resources.selectors">InstanceOf</a></code>, <code><a href="Majority.html" title="class in org.apache.tools.ant.types.resources.selectors">Majority</a></code>, <code><a href="../../selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></code>, <code><a href="../../selectors/MappingSelector.html" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a></code>, <code><a href="../../selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a></code>, <code><a href="Name.html" title="class in org.apache.tools.ant.types.resources.selectors">Name</a></code>, <code><a href="None.html" title="class in org.apache.tools.ant.types.resources.selectors">None</a></code>, <code><a href="../../selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></code>, <code><a href="Not.html" title="class in org.apache.tools.ant.types.resources.selectors">Not</a></code>, <code><a href="../../selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></code>, <code><a href="Or.html" title="class in org.apache.tools.ant.types.resources.selectors">Or</a></code>, <code><a href="../../selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></code>, <code><a href="../../selectors/OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a></code>, <code><a href="../../selectors/PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a></code>, <code><a href="../../selectors/PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a></code>, <code><a href="../../selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></code>, <code><a href="../../selectors/ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a></code>, <code><a href="../../optional/ScriptSelector.html" title="class in org.apache.tools.ant.types.optional">ScriptSelector</a></code>, <code><a href="../../selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></code>, <code><a href="../../selectors/SignedSelector.html" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a></code>, <code><a href="Size.html" title="class in org.apache.tools.ant.types.resources.selectors">Size</a></code>, <code><a href="../../selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></code>, <code><a href="../../selectors/SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a></code>, <code><a href="Type.html" title="class in org.apache.tools.ant.types.resources.selectors">Type</a></code>, <code><a href="../../selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></code>, <code><a href="../../selectors/WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">ResourceSelector</span></div>
<div class="block">Interface for a Resource selector.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#isSelected(org.apache.tools.ant.types.Resource)" class="member-name-link">isSelected</a><wbr>(<a href="../../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Return true if this Resource is selected.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="isSelected(org.apache.tools.ant.types.Resource)">
<h3>isSelected</h3>
<div class="member-signature"><span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="../../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</span></div>
<div class="block">Return true if this Resource is selected.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - the Resource to check.</dd>
<dt>Returns:</dt>
<dd>whether the Resource was selected.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
