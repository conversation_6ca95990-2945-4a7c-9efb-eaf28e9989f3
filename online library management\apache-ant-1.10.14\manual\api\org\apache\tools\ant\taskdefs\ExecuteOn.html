<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ExecuteOn (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: ExecuteOn">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class ExecuteOn" class="title">Class ExecuteOn</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.ExecuteOn</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="optional/unix/AbstractAccessTask.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">AbstractAccessTask</a></code>, <code><a href="optional/windows/Attrib.html" title="class in org.apache.tools.ant.taskdefs.optional.windows">Attrib</a></code>, <code><a href="Chmod.html" title="class in org.apache.tools.ant.taskdefs">Chmod</a></code>, <code><a href="Transform.html" title="class in org.apache.tools.ant.taskdefs">Transform</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ExecuteOn</span>
<span class="extends-implements">extends <a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></span></div>
<div class="block">Executes a given command, supplying a set of files as arguments.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ExecuteOn.FileDirBoth.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerated attribute with the values "file", "dir" and "both"
 for the type attribute.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#destDir" class="member-name-link">destDir</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#filesets" class="member-name-link">filesets</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></code></div>
<div class="col-second even-row-color"><code><a href="#mapper" class="member-name-link">mapper</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></code></div>
<div class="col-second odd-row-color"><code><a href="#mapperElement" class="member-name-link">mapperElement</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></code></div>
<div class="col-second even-row-color"><code><a href="#srcFilePos" class="member-name-link">srcFilePos</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#srcIsFirst" class="member-name-link">srcIsFirst</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Has &lt;srcfile&gt; been specified before &lt;targetfile&gt;</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></code></div>
<div class="col-second even-row-color"><code><a href="#targetFilePos" class="member-name-link">targetFilePos</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#type" class="member-name-link">type</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.ExecTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></h3>
<code><a href="ExecTask.html#cmdl">cmdl</a>, <a href="ExecTask.html#failOnError">failOnError</a>, <a href="ExecTask.html#newEnvironment">newEnvironment</a>, <a href="ExecTask.html#redirector">redirector</a>, <a href="ExecTask.html#redirectorElement">redirectorElement</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ExecuteOn</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">add</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a collection of resources upon which to operate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.util.FileNameMapper)" class="member-name-link">add</a><wbr>(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested FileNameMapper.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDirset(org.apache.tools.ant.types.DirSet)" class="member-name-link">addDirset</a><wbr>(<a href="../types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</a>&nbsp;set)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a set of directories upon which to operate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFilelist(org.apache.tools.ant.types.FileList)" class="member-name-link">addFilelist</a><wbr>(<a href="../types/FileList.html" title="class in org.apache.tools.ant.types">FileList</a>&nbsp;list)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a list of source files upon which to operate.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileset(org.apache.tools.ant.types.FileSet)" class="member-name-link">addFileset</a><wbr>(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a set of files upon which to operate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkConfiguration()" class="member-name-link">checkConfiguration</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check the configuration of this ExecuteOn instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createHandler()" class="member-name-link">createHandler</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the ExecuteStreamHandler instance that will be used
 during execution.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createMapper()" class="member-name-link">createMapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested Mapper element to use for mapping
 source files to target files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSrcfile()" class="member-name-link">createSrcfile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a placeholder indicating where on the command line
 the name of the source file should be inserted.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createTargetfile()" class="member-name-link">createTargetfile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a placeholder indicating where on the command line
 the name of the target file should be inserted.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCommandline(java.lang.String%5B%5D,java.io.File%5B%5D)" class="member-name-link">getCommandline</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;srcFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;baseDirs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Construct the command line for parallel execution.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCommandline(java.lang.String,java.io.File)" class="member-name-link">getCommandline</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;srcFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Construct the command line for serial execution.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirs(java.io.File,org.apache.tools.ant.DirectoryScanner)" class="member-name-link">getDirs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;ds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the list of Directories from this DirectoryScanner that
 should be included on the command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFiles(java.io.File,org.apache.tools.ant.DirectoryScanner)" class="member-name-link">getFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;ds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the list of files from this DirectoryScanner that should
 be included on the command line.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFilesAndDirs(org.apache.tools.ant.types.FileList)" class="member-name-link">getFilesAndDirs</a><wbr>(<a href="../types/FileList.html" title="class in org.apache.tools.ant.types">FileList</a>&nbsp;list)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the list of files or directories from this FileList that
 should be included on the command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runExec(org.apache.tools.ant.taskdefs.Execute)" class="member-name-link">runExec</a><wbr>(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the specified Execute object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runParallel(org.apache.tools.ant.taskdefs.Execute,java.util.Vector,java.util.Vector)" class="member-name-link">runParallel</a><wbr>(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fileNames,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;baseDirs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the command in "parallel" mode, making sure that at most
 maxParallel sourcefiles get passed on the command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAddsourcefile(boolean)" class="member-name-link">setAddsourcefile</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to send the source file name on the command line.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDest(java.io.File)" class="member-name-link">setDest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify the directory where target files are to be placed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForce(boolean)" class="member-name-link">setForce</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to bypass timestamp comparisons for target files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setForwardslash(boolean)" class="member-name-link">setForwardslash</a><wbr>(boolean&nbsp;forwardSlash)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether the source and target file names on Windows and OS/2
 must use the forward slash as file separator.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIgnoremissing(boolean)" class="member-name-link">setIgnoremissing</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to ignore nonexistent files from filelists.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxParallel(int)" class="member-name-link">setMaxParallel</a><wbr>(int&nbsp;max)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Limit the command line length by passing at maximum this many
 sourcefiles at once to the command.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParallel(boolean)" class="member-name-link">setParallel</a><wbr>(boolean&nbsp;parallel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to execute in parallel mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRelative(boolean)" class="member-name-link">setRelative</a><wbr>(boolean&nbsp;relative)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether the filenames should be passed on the command line as
 absolute or relative pathnames.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSkipEmptyFilesets(boolean)" class="member-name-link">setSkipEmptyFilesets</a><wbr>(boolean&nbsp;skip)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether empty filesets will be skipped.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)" class="member-name-link">setType</a><wbr>(<a href="ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether the command works only on files, directories or both.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupRedirector()" class="member-name-link">setupRedirector</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set up the I/O Redirector.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(boolean)" class="member-name-link">setVerbose</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to operate in verbose mode.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.ExecTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></h3>
<code><a href="ExecTask.html#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">addConfiguredRedirector</a>, <a href="ExecTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</a>, <a href="ExecTask.html#createArg()">createArg</a>, <a href="ExecTask.html#createWatchdog()">createWatchdog</a>, <a href="ExecTask.html#execute()">execute</a>, <a href="ExecTask.html#getOs()">getOs</a>, <a href="ExecTask.html#getOsFamily()">getOsFamily</a>, <a href="ExecTask.html#getResolveExecutable()">getResolveExecutable</a>, <a href="ExecTask.html#isValidOs()">isValidOs</a>, <a href="ExecTask.html#logFlush()">logFlush</a>, <a href="ExecTask.html#maybeSetResultPropertyValue(int)">maybeSetResultPropertyValue</a>, <a href="ExecTask.html#prepareExec()">prepareExec</a>, <a href="ExecTask.html#resolveExecutable(java.lang.String,boolean)">resolveExecutable</a>, <a href="ExecTask.html#runExecute(org.apache.tools.ant.taskdefs.Execute)">runExecute</a>, <a href="ExecTask.html#setAppend(boolean)">setAppend</a>, <a href="ExecTask.html#setCommand(org.apache.tools.ant.types.Commandline)">setCommand</a>, <a href="ExecTask.html#setDir(java.io.File)">setDir</a>, <a href="ExecTask.html#setDiscardError(boolean)">setDiscardError</a>, <a href="ExecTask.html#setDiscardOutput(boolean)">setDiscardOutput</a>, <a href="ExecTask.html#setError(java.io.File)">setError</a>, <a href="ExecTask.html#setErrorProperty(java.lang.String)">setErrorProperty</a>, <a href="ExecTask.html#setExecutable(java.lang.String)">setExecutable</a>, <a href="ExecTask.html#setFailIfExecutionFails(boolean)">setFailIfExecutionFails</a>, <a href="ExecTask.html#setFailonerror(boolean)">setFailonerror</a>, <a href="ExecTask.html#setInput(java.io.File)">setInput</a>, <a href="ExecTask.html#setInputString(java.lang.String)">setInputString</a>, <a href="ExecTask.html#setLogError(boolean)">setLogError</a>, <a href="ExecTask.html#setNewenvironment(boolean)">setNewenvironment</a>, <a href="ExecTask.html#setOs(java.lang.String)">setOs</a>, <a href="ExecTask.html#setOsFamily(java.lang.String)">setOsFamily</a>, <a href="ExecTask.html#setOutput(java.io.File)">setOutput</a>, <a href="ExecTask.html#setOutputproperty(java.lang.String)">setOutputproperty</a>, <a href="ExecTask.html#setResolveExecutable(boolean)">setResolveExecutable</a>, <a href="ExecTask.html#setResultProperty(java.lang.String)">setResultProperty</a>, <a href="ExecTask.html#setSearchPath(boolean)">setSearchPath</a>, <a href="ExecTask.html#setSpawn(boolean)">setSpawn</a>, <a href="ExecTask.html#setTimeout(java.lang.Integer)">setTimeout</a>, <a href="ExecTask.html#setTimeout(java.lang.Long)">setTimeout</a>, <a href="ExecTask.html#setVMLauncher(boolean)">setVMLauncher</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="filesets">
<h3>filesets</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="../types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</a>&gt;</span>&nbsp;<span class="element-name">filesets</span></div>
</section>
</li>
<li>
<section class="detail" id="type">
<h3>type</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">type</span></div>
</section>
</li>
<li>
<section class="detail" id="srcFilePos">
<h3>srcFilePos</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></span>&nbsp;<span class="element-name">srcFilePos</span></div>
</section>
</li>
<li>
<section class="detail" id="targetFilePos">
<h3>targetFilePos</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></span>&nbsp;<span class="element-name">targetFilePos</span></div>
</section>
</li>
<li>
<section class="detail" id="mapperElement">
<h3>mapperElement</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></span>&nbsp;<span class="element-name">mapperElement</span></div>
</section>
</li>
<li>
<section class="detail" id="mapper">
<h3>mapper</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></span>&nbsp;<span class="element-name">mapper</span></div>
</section>
</li>
<li>
<section class="detail" id="destDir">
<h3>destDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">destDir</span></div>
</section>
</li>
<li>
<section class="detail" id="srcIsFirst">
<h3>srcIsFirst</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">srcIsFirst</span></div>
<div class="block">Has &lt;srcfile&gt; been specified before &lt;targetfile&gt;</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ExecuteOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExecuteOn</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addFileset(org.apache.tools.ant.types.FileSet)">
<h3>addFileset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileset</span><wbr><span class="parameters">(<a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a>&nbsp;set)</span></div>
<div class="block">Add a set of files upon which to operate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the FileSet to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDirset(org.apache.tools.ant.types.DirSet)">
<h3>addDirset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDirset</span><wbr><span class="parameters">(<a href="../types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</a>&nbsp;set)</span></div>
<div class="block">Add a set of directories upon which to operate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>set</code> - the DirSet to add.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFilelist(org.apache.tools.ant.types.FileList)">
<h3>addFilelist</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFilelist</span><wbr><span class="parameters">(<a href="../types/FileList.html" title="class in org.apache.tools.ant.types">FileList</a>&nbsp;list)</span></div>
<div class="block">Add a list of source files upon which to operate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - the FileList to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.ResourceCollection)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;rc)</span></div>
<div class="block">Add a collection of resources upon which to operate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rc</code> - resource collection to add.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRelative(boolean)">
<h3>setRelative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRelative</span><wbr><span class="parameters">(boolean&nbsp;relative)</span></div>
<div class="block">Set whether the filenames should be passed on the command line as
 absolute or relative pathnames. Paths are relative to the base
 directory of the corresponding fileset for source files or the
 dest attribute for target files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>relative</code> - whether to pass relative pathnames.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParallel(boolean)">
<h3>setParallel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParallel</span><wbr><span class="parameters">(boolean&nbsp;parallel)</span></div>
<div class="block">Set whether to execute in parallel mode.
 If true, run the command only once, appending all files as arguments.
 If false, command will be executed once for every file. Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parallel</code> - whether to run in parallel.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)">
<h3>setType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setType</span><wbr><span class="parameters">(<a href="ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a>&nbsp;type)</span></div>
<div class="block">Set whether the command works only on files, directories or both.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - a FileDirBoth EnumeratedAttribute.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSkipEmptyFilesets(boolean)">
<h3>setSkipEmptyFilesets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSkipEmptyFilesets</span><wbr><span class="parameters">(boolean&nbsp;skip)</span></div>
<div class="block">Set whether empty filesets will be skipped.  If true and
 no source files have been found or are newer than their
 corresponding target files, the command will not be run.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>skip</code> - whether to skip empty filesets.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDest(java.io.File)">
<h3>setDest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</span></div>
<div class="block">Specify the directory where target files are to be placed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destDir</code> - the File object representing the destination directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForwardslash(boolean)">
<h3>setForwardslash</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForwardslash</span><wbr><span class="parameters">(boolean&nbsp;forwardSlash)</span></div>
<div class="block">Set whether the source and target file names on Windows and OS/2
 must use the forward slash as file separator.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>forwardSlash</code> - whether the forward slash will be forced.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMaxParallel(int)">
<h3>setMaxParallel</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxParallel</span><wbr><span class="parameters">(int&nbsp;max)</span></div>
<div class="block">Limit the command line length by passing at maximum this many
 sourcefiles at once to the command.

 <p>Set to &lt;= 0 for unlimited - this is the default.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max</code> - <code>int</code> maximum number of sourcefiles
            passed to the executable.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAddsourcefile(boolean)">
<h3>setAddsourcefile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAddsourcefile</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set whether to send the source file name on the command line.

 <p>Defaults to <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - whether to add the source file to the command line.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVerbose(boolean)">
<h3>setVerbose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set whether to operate in verbose mode.
 If true, a verbose summary will be printed after execution.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - whether to operate in verbose mode.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIgnoremissing(boolean)">
<h3>setIgnoremissing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIgnoremissing</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set whether to ignore nonexistent files from filelists.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - whether to ignore missing files.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setForce(boolean)">
<h3>setForce</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setForce</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set whether to bypass timestamp comparisons for target files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - whether to bypass timestamp comparisons.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createSrcfile()">
<h3>createSrcfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></span>&nbsp;<span class="element-name">createSrcfile</span>()</div>
<div class="block">Create a placeholder indicating where on the command line
 the name of the source file should be inserted.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>Commandline.Marker</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTargetfile()">
<h3>createTargetfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</a></span>&nbsp;<span class="element-name">createTargetfile</span>()</div>
<div class="block">Create a placeholder indicating where on the command line
 the name of the target file should be inserted.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>Commandline.Marker</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMapper()">
<h3>createMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></span>&nbsp;<span class="element-name">createMapper</span>()
                    throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create a nested Mapper element to use for mapping
 source files to target files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>Mapper</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if more than one mapper is defined.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.util.FileNameMapper)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</span></div>
<div class="block">Add a nested FileNameMapper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileNameMapper</code> - the mapper to add.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkConfiguration()">
<h3>checkConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkConfiguration</span>()</div>
<div class="block">Check the configuration of this ExecuteOn instance.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ExecTask.html#checkConfiguration()">checkConfiguration</a></code>&nbsp;in class&nbsp;<code><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createHandler()">
<h3>createHandler</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></span>&nbsp;<span class="element-name">createHandler</span>()
                                      throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the ExecuteStreamHandler instance that will be used
 during execution.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ExecTask.html#createHandler()">createHandler</a></code>&nbsp;in class&nbsp;<code><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></code></dd>
<dt>Returns:</dt>
<dd><code>ExecuteStreamHandler</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupRedirector()">
<h3>setupRedirector</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setupRedirector</span>()</div>
<div class="block">Set up the I/O Redirector.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ExecTask.html#setupRedirector()">setupRedirector</a></code>&nbsp;in class&nbsp;<code><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runExec(org.apache.tools.ant.taskdefs.Execute)">
<h3>runExec</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">runExec</span><wbr><span class="parameters">(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</span>
                throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the specified Execute object.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ExecTask.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</a></code>&nbsp;in class&nbsp;<code><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></code></dd>
<dt>Parameters:</dt>
<dd><code>exe</code> - the Execute instance representing the external process.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCommandline(java.lang.String[],java.io.File[])">
<h3>getCommandline</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getCommandline</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;srcFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;baseDirs)</span></div>
<div class="block">Construct the command line for parallel execution.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcFiles</code> - The filenames to add to the commandline.</dd>
<dd><code>baseDirs</code> - filenames are relative to this dir.</dd>
<dt>Returns:</dt>
<dd>the command line in the form of a String[].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCommandline(java.lang.String,java.io.File)">
<h3>getCommandline</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getCommandline</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;srcFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span></div>
<div class="block">Construct the command line for serial execution.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcFile</code> - The filename to add to the commandline.</dd>
<dd><code>baseDir</code> - filename is relative to this dir.</dd>
<dt>Returns:</dt>
<dd>the command line in the form of a String[].</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFiles(java.io.File,org.apache.tools.ant.DirectoryScanner)">
<h3>getFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;ds)</span></div>
<div class="block">Return the list of files from this DirectoryScanner that should
 be included on the command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the File base directory.</dd>
<dd><code>ds</code> - the DirectoryScanner to use for file scanning.</dd>
<dt>Returns:</dt>
<dd>a String[] containing the filenames.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDirs(java.io.File,org.apache.tools.ant.DirectoryScanner)">
<h3>getDirs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getDirs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir,
 <a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;ds)</span></div>
<div class="block">Return the list of Directories from this DirectoryScanner that
 should be included on the command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the File base directory.</dd>
<dd><code>ds</code> - the DirectoryScanner to use for file scanning.</dd>
<dt>Returns:</dt>
<dd>a String[] containing the directory names.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFilesAndDirs(org.apache.tools.ant.types.FileList)">
<h3>getFilesAndDirs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getFilesAndDirs</span><wbr><span class="parameters">(<a href="../types/FileList.html" title="class in org.apache.tools.ant.types">FileList</a>&nbsp;list)</span></div>
<div class="block">Return the list of files or directories from this FileList that
 should be included on the command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - the FileList to check.</dd>
<dt>Returns:</dt>
<dd>a String[] containing the directory names.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runParallel(org.apache.tools.ant.taskdefs.Execute,java.util.Vector,java.util.Vector)">
<h3>runParallel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">runParallel</span><wbr><span class="parameters">(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fileNames,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;baseDirs)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the command in "parallel" mode, making sure that at most
 maxParallel sourcefiles get passed on the command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exe</code> - the Executable to use.</dd>
<dd><code>fileNames</code> - the Vector of filenames.</dd>
<dd><code>baseDirs</code> - the Vector of base directories corresponding to fileNames.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O errors.</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
