<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Zip64ExtendedInformationExtraField (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: Zip64ExtendedInformationExtraField">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class Zip64ExtendedInformationExtraField" class="title">Class Zip64ExtendedInformationExtraField</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.zip.Zip64ExtendedInformationExtraField</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></code>, <code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Zip64ExtendedInformationExtraField</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></span></div>
<div class="block">Holds size and other extended information for entries that use Zip64
 features.

 <p>See <a href="https://www.pkware.com/documents/casestudies/APPNOTE.TXT">PKWARE's
 APPNOTE.TXT, section 4.5.3</a>.</p>

 <p>Currently Ant doesn't support encrypting the
 central directory so the note about masking doesn't apply.</p>

 <p>The implementation relies on data being read from the local file
 header and assumes that both size values are always present.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Zip64ExtendedInformationExtraField</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">This constructor should only be used by the code that reads
 archives inside of Ant.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger)" class="member-name-link">Zip64ExtendedInformationExtraField</a><wbr>(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates an extra field based on the original and compressed size.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipLong)" class="member-name-link">Zip64ExtendedInformationExtraField</a><wbr>(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;relativeHeaderOffset,
 <a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a>&nbsp;diskStart)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates an extra field based on all four possible values.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryData()" class="member-name-link">getCentralDirectoryData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The actual data to put into central directory - without Header-ID or
 length specifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryLength()" class="member-name-link">getCentralDirectoryLength</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Length of the extra field in the central directory - without
 Header-ID or length specifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompressedSize()" class="member-name-link">getCompressedSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The compressed size stored in this extra field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDiskStartNumber()" class="member-name-link">getDiskStartNumber</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The disk start number stored in this extra field.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeaderId()" class="member-name-link">getHeaderId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The Header-ID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataData()" class="member-name-link">getLocalFileDataData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataLength()" class="member-name-link">getLocalFileDataLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRelativeHeaderOffset()" class="member-name-link">getRelativeHeaderOffset</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The relative header offset stored in this extra field.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The uncompressed size stored in this extra field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseFromCentralDirectoryData(byte%5B%5D,int,int)" class="member-name-link">parseFromCentralDirectoryData</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Populate data from this array as if it was in central directory data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseFromLocalFileData(byte%5B%5D,int,int)" class="member-name-link">parseFromLocalFileData</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Populate data from this array as if it was in local file data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reparseCentralDirectoryData(boolean,boolean,boolean,boolean)" class="member-name-link">reparseCentralDirectoryData</a><wbr>(boolean&nbsp;hasUncompressedSize,
 boolean&nbsp;hasCompressedSize,
 boolean&nbsp;hasRelativeHeaderOffset,
 boolean&nbsp;hasDiskStart)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the raw bytes read from the central directory extra
 field with knowledge which fields are expected to be there.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompressedSize(org.apache.tools.zip.ZipEightByteInteger)" class="member-name-link">setCompressedSize</a><wbr>(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The uncompressed size stored in this extra field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDiskStartNumber(org.apache.tools.zip.ZipLong)" class="member-name-link">setDiskStartNumber</a><wbr>(<a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a>&nbsp;ds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The disk start number stored in this extra field.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRelativeHeaderOffset(org.apache.tools.zip.ZipEightByteInteger)" class="member-name-link">setRelativeHeaderOffset</a><wbr>(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;rho)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The relative header offset stored in this extra field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(org.apache.tools.zip.ZipEightByteInteger)" class="member-name-link">setSize</a><wbr>(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The uncompressed size stored in this extra field.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Zip64ExtendedInformationExtraField</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Zip64ExtendedInformationExtraField</span>()</div>
<div class="block">This constructor should only be used by the code that reads
 archives inside of Ant.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger)">
<h3>Zip64ExtendedInformationExtraField</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Zip64ExtendedInformationExtraField</span><wbr><span class="parameters">(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize)</span></div>
<div class="block">Creates an extra field based on the original and compressed size.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - the entry's original size</dd>
<dd><code>compressedSize</code> - the entry's compressed size</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if size or compressedSize is null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipEightByteInteger,org.apache.tools.zip.ZipLong)">
<h3>Zip64ExtendedInformationExtraField</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Zip64ExtendedInformationExtraField</span><wbr><span class="parameters">(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize,
 <a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;relativeHeaderOffset,
 <a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a>&nbsp;diskStart)</span></div>
<div class="block">Creates an extra field based on all four possible values.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - the entry's original size</dd>
<dd><code>compressedSize</code> - the entry's compressed size</dd>
<dd><code>relativeHeaderOffset</code> - ZipEightByteInteger</dd>
<dd><code>diskStart</code> - ZipLong</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code> - if size or compressedSize is null</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getHeaderId()">
<h3>getHeaderId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getHeaderId</span>()</div>
<div class="block">The Header-ID.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getHeaderId()">getHeaderId</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the header id</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataLength()">
<h3>getLocalFileDataLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getLocalFileDataLength</span>()</div>
<div class="block">Length of the extra field in the local file data - without
 Header-ID or length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataLength()">getLocalFileDataLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the length of the field in the local file data</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryLength()">
<h3>getCentralDirectoryLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getCentralDirectoryLength</span>()</div>
<div class="block">Length of the extra field in the central directory - without
 Header-ID or length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryLength()">getCentralDirectoryLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the length of the field in the central directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataData()">
<h3>getLocalFileDataData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLocalFileDataData</span>()</div>
<div class="block">The actual data to put into local file data - without Header-ID
 or length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataData()">getLocalFileDataData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the data</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryData()">
<h3>getCentralDirectoryData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getCentralDirectoryData</span>()</div>
<div class="block">The actual data to put into central directory - without Header-ID or
 length specifier.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryData()">getCentralDirectoryData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the data</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseFromLocalFileData(byte[],int,int)">
<h3>parseFromLocalFileData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseFromLocalFileData</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Populate data from this array as if it was in local file data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#parseFromLocalFileData(byte%5B%5D,int,int)">parseFromLocalFileData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buffer</code> - an array of bytes</dd>
<dd><code>offset</code> - the start offset</dd>
<dd><code>length</code> - the number of bytes in the array from offset</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseFromCentralDirectoryData(byte[],int,int)">
<h3>parseFromCentralDirectoryData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseFromCentralDirectoryData</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Populate data from this array as if it was in central directory data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="CentralDirectoryParsingZipExtraField.html#parseFromCentralDirectoryData(byte%5B%5D,int,int)">parseFromCentralDirectoryData</a></code>&nbsp;in interface&nbsp;<code><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buffer</code> - an array of bytes</dd>
<dd><code>offset</code> - the start offset</dd>
<dd><code>length</code> - the number of bytes in the array from offset</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reparseCentralDirectoryData(boolean,boolean,boolean,boolean)">
<h3>reparseCentralDirectoryData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">reparseCentralDirectoryData</span><wbr><span class="parameters">(boolean&nbsp;hasUncompressedSize,
 boolean&nbsp;hasCompressedSize,
 boolean&nbsp;hasRelativeHeaderOffset,
 boolean&nbsp;hasDiskStart)</span>
                                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></span></div>
<div class="block">Parses the raw bytes read from the central directory extra
 field with knowledge which fields are expected to be there.

 <p>All four fields inside the zip64 extended information extra
 field are optional and must only be present if their corresponding
 entry inside the central directory contains the correct magic
 value.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>hasUncompressedSize</code> - boolean</dd>
<dd><code>hasCompressedSize</code> - boolean</dd>
<dd><code>hasRelativeHeaderOffset</code> - boolean</dd>
<dd><code>hasDiskStart</code> - boolean</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a></code> - if expected length of central directory data is incorrect</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></span>&nbsp;<span class="element-name">getSize</span>()</div>
<div class="block">The uncompressed size stored in this extra field.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSize(org.apache.tools.zip.ZipEightByteInteger)">
<h3>setSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;size)</span></div>
<div class="block">The uncompressed size stored in this extra field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>size</code> - ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompressedSize()">
<h3>getCompressedSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></span>&nbsp;<span class="element-name">getCompressedSize</span>()</div>
<div class="block">The compressed size stored in this extra field.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCompressedSize(org.apache.tools.zip.ZipEightByteInteger)">
<h3>setCompressedSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompressedSize</span><wbr><span class="parameters">(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;compressedSize)</span></div>
<div class="block">The uncompressed size stored in this extra field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compressedSize</code> - ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRelativeHeaderOffset()">
<h3>getRelativeHeaderOffset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></span>&nbsp;<span class="element-name">getRelativeHeaderOffset</span>()</div>
<div class="block">The relative header offset stored in this extra field.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRelativeHeaderOffset(org.apache.tools.zip.ZipEightByteInteger)">
<h3>setRelativeHeaderOffset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRelativeHeaderOffset</span><wbr><span class="parameters">(<a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a>&nbsp;rho)</span></div>
<div class="block">The relative header offset stored in this extra field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rho</code> - ZipEightByteInteger</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDiskStartNumber()">
<h3>getDiskStartNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></span>&nbsp;<span class="element-name">getDiskStartNumber</span>()</div>
<div class="block">The disk start number stored in this extra field.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>ZipLong</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDiskStartNumber(org.apache.tools.zip.ZipLong)">
<h3>setDiskStartNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDiskStartNumber</span><wbr><span class="parameters">(<a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a>&nbsp;ds)</span></div>
<div class="block">The disk start number stored in this extra field.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ds</code> - ZipLong</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
