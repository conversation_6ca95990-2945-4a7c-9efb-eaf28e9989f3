<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Serialized Form (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#serialized-form">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/package-summary.html">org.apache.tools.ant</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.BuildEvent">
<h3>Class&nbsp;<a href="org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.BuildEvent</a></h3>
<div class="type-signature">class BuildEvent extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventObject.html" title="class or interface in java.util" class="external-link">EventObject</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>4538050075952288486L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>exception</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a> exception</pre>
<div class="block">The exception associated with this event, if any.
 This is only used for "messageLogged", "taskFinished", "targetFinished",
 and "buildFinished" events.</div>
</li>
<li class="block-list">
<h5>message</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> message</pre>
<div class="block">Message associated with the event. This is only used for
 "messageLogged" events.</div>
</li>
<li class="block-list">
<h5>priority</h5>
<pre>int priority</pre>
<div class="block">The priority of the message, for "messageLogged" events.</div>
</li>
<li class="block-list">
<h5>project</h5>
<pre><a href="org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</a> project</pre>
<div class="block">Project which emitted the event.</div>
</li>
<li class="block-list">
<h5>target</h5>
<pre><a href="org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</a> target</pre>
<div class="block">Target which emitted the event, if specified.</div>
</li>
<li class="block-list">
<h5>task</h5>
<pre><a href="org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</a> task</pre>
<div class="block">Task which emitted the event, if specified.</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.BuildException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.BuildException</a></h3>
<div class="type-signature">class BuildException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-5419014565354664240L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>location</h5>
<pre><a href="org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</a> location</pre>
<div class="block">Location in the build file where the exception occurred</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.ExitException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/ExitException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ExitException</a></h3>
<div class="type-signature">class ExitException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/SecurityException.html" title="class or interface in java.lang" class="external-link">SecurityException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>2772487854280543363L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>status</h5>
<pre>int status</pre>
<div class="block">Status code</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.ExitStatusException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ExitStatusException</a></h3>
<div class="type-signature">class ExitStatusException extends <a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>7760846806886585968L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>status</h5>
<pre>int status</pre>
<div class="block">Status code</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.Location">
<h3>Class&nbsp;<a href="org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Location</a></h3>
<div class="type-signature">class Location extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>columnNumber</h5>
<pre>int columnNumber</pre>
<div class="block">Column number within the file.</div>
</li>
<li class="block-list">
<h5>fileName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> fileName</pre>
<div class="block">Name of the file.</div>
</li>
<li class="block-list">
<h5>lineNumber</h5>
<pre>int lineNumber</pre>
<div class="block">Line number within the file.</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.RuntimeConfigurable">
<h3>Class&nbsp;<a href="org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">org.apache.tools.ant.RuntimeConfigurable</a></h3>
<div class="type-signature">class RuntimeConfigurable extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>attributeMap</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/LinkedHashMap.html" title="class or interface in java.util" class="external-link">LinkedHashMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt; attributeMap</pre>
<div class="block">Attribute names and values. While the XML spec doesn't require
  preserving the order (AFAIK), some ant tests do rely on the
  exact order.
 The only exception to this order is the treatment of
 refid. A number of datatypes check if refid is set
 when other attributes are set. This check will not
 work if the build script has the other attribute before
 the "refid" attribute, so now (ANT 1.7) the refid
 attribute will be processed first.</div>
</li>
<li class="block-list">
<h5>characters</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/StringBuffer.html" title="class or interface in java.lang" class="external-link">StringBuffer</a> characters</pre>
<div class="block">Text appearing within the element.</div>
</li>
<li class="block-list">
<h5>children</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&gt; children</pre>
<div class="block">List of child element wrappers.</div>
</li>
<li class="block-list">
<h5>elementTag</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> elementTag</pre>
<div class="block">Name of the element to configure.</div>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> id</pre>
<div class="block">the "id" of this Element if it has one</div>
</li>
<li class="block-list">
<h5>polyType</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> polyType</pre>
<div class="block">the polymorphic type</div>
</li>
<li class="block-list">
<h5>proxyConfigured</h5>
<pre>boolean proxyConfigured</pre>
<div class="block">Indicates if the wrapped object has been configured</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.UnsupportedAttributeException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.UnsupportedAttributeException</a></h3>
<div class="type-signature">class UnsupportedAttributeException extends <a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>attribute</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> attribute</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.UnsupportedElementException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.UnsupportedElementException</a></h3>
<div class="type-signature">class UnsupportedElementException extends <a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>element</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> element</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/launch/package-summary.html">org.apache.tools.ant.launch</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.launch.LaunchException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/launch/LaunchException.html" title="class in org.apache.tools.ant.launch">org.apache.tools.ant.launch.LaunchException</a></h3>
<div class="type-signature">class LaunchException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/taskdefs/package-summary.html">org.apache.tools.ant.taskdefs</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.ManifestException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ManifestException</a></h3>
<div class="type-signature">class ManifestException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>7685634200457515207L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/ejb/package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.ejb.IPlanetEjbc.EjbcException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.EjbcException.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.IPlanetEjbc.EjbcException</a></h3>
<div class="type-signature">class EjbcException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/junit/package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.junit.CustomJUnit4TestAdapterCache">
<h3>Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/junit/CustomJUnit4TestAdapterCache.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.CustomJUnit4TestAdapterCache</a></h3>
<div class="type-signature">class CustomJUnit4TestAdapterCache extends junit.framework.JUnit4TestAdapterCache implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl">
<h3>Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeListImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl</a></h3>
<div class="type-signature">class NodeListImpl extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/w3c/dom/Node.html" title="class or interface in org.w3c.dom" class="external-link">Node</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>3175749150080946423L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/net/package-summary.html">org.apache.tools.ant.taskdefs.optional.net</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.net.FTP.FTPFileProxy">
<h3>Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/net/FTP.FTPFileProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">org.apache.tools.ant.taskdefs.optional.net.FTP.FTPFileProxy</a></h3>
<div class="type-signature">class FTPFileProxy extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>file</h5>
<pre>org.apache.commons.net.ftp.FTPFile file</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> name</pre>
</li>
<li class="block-list">
<h5>parts</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[] parts</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl.FTPFileProxy">
<h3>Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/net/FTPTaskMirrorImpl.FTPFileProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl.FTPFileProxy</a></h3>
<div class="type-signature">class FTPFileProxy extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>file</h5>
<pre>org.apache.commons.net.ftp.FTPFile file</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> name</pre>
</li>
<li class="block-list">
<h5>parts</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[] parts</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/testing/package-summary.html">org.apache.tools.ant.taskdefs.optional.testing</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.taskdefs.optional.testing.BuildTimeoutException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/taskdefs/optional/testing/BuildTimeoutException.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">org.apache.tools.ant.taskdefs.optional.testing.BuildTimeoutException</a></h3>
<div class="type-signature">class BuildTimeoutException extends <a href="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-8057644603246297562L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/types/resources/package-summary.html">org.apache.tools.ant.types.resources</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.types.resources.ImmutableResourceException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/types/resources/ImmutableResourceException.html" title="class in org.apache.tools.ant.types.resources">org.apache.tools.ant.types.resources.ImmutableResourceException</a></h3>
<div class="type-signature">class ImmutableResourceException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/ant/util/package-summary.html">org.apache.tools.ant.util</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.IdentityStack">
<h3>Class&nbsp;<a href="org/apache/tools/ant/util/IdentityStack.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.IdentityStack</a></h3>
<div class="type-signature">class IdentityStack extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="org/apache/tools/ant/util/IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-5555522620060077046L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.LayoutPreservingProperties">
<h3>Class&nbsp;<a href="org/apache/tools/ant/util/LayoutPreservingProperties.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LayoutPreservingProperties</a></h3>
<div class="type-signature">class LayoutPreservingProperties extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>eol</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> eol</pre>
</li>
<li class="block-list">
<h5>keyedPairLines</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; keyedPairLines</pre>
<div class="block">Position in the <code>logicalLines</code> list, keyed by property name.</div>
</li>
<li class="block-list">
<h5>logicalLines</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;org.apache.tools.ant.util.LayoutPreservingProperties.LogicalLine&gt; logicalLines</pre>
<div class="block">Logical lines have escaping and line continuation taken care
 of. Comments and blank lines are logical lines; they are not
 removed.</div>
</li>
<li class="block-list">
<h5>removeComments</h5>
<pre>boolean removeComments</pre>
<div class="block">Flag to indicate that, when we remove a property from the file, we
 also want to remove the comments that precede it.</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.LazyHashtable">
<h3>Class&nbsp;<a href="org/apache/tools/ant/util/LazyHashtable.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LazyHashtable</a></h3>
<div class="type-signature">class LazyHashtable extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="org/apache/tools/ant/util/LazyHashtable.html" title="type parameter in LazyHashtable">K</a>,<wbr><a href="org/apache/tools/ant/util/LazyHashtable.html" title="type parameter in LazyHashtable">V</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>initAllDone</h5>
<pre>boolean initAllDone</pre>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.LinkedHashtable">
<h3>Class&nbsp;<a href="org/apache/tools/ant/util/LinkedHashtable.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LinkedHashtable</a></h3>
<div class="type-signature">class LinkedHashtable extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="org/apache/tools/ant/util/LinkedHashtable.html" title="type parameter in LinkedHashtable">K</a>,<wbr><a href="org/apache/tools/ant/util/LinkedHashtable.html" title="type parameter in LinkedHashtable">V</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>map</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/LinkedHashMap.html" title="class or interface in java.util" class="external-link">LinkedHashMap</a>&lt;<a href="org/apache/tools/ant/util/LinkedHashtable.html" title="type parameter in LinkedHashtable">K</a>,<wbr><a href="org/apache/tools/ant/util/LinkedHashtable.html" title="type parameter in LinkedHashtable">V</a>&gt; map</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.ResourceUtils.ReadOnlyTargetFileException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/ant/util/ResourceUtils.ReadOnlyTargetFileException.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.ResourceUtils.ReadOnlyTargetFileException</a></h3>
<div class="type-signature">class ReadOnlyTargetFileException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.ant.util.VectorSet">
<h3>Class&nbsp;<a href="org/apache/tools/ant/util/VectorSet.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.VectorSet</a></h3>
<div class="type-signature">class VectorSet extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="org/apache/tools/ant/util/VectorSet.html" title="type parameter in VectorSet">E</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>set</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/HashSet.html" title="class or interface in java.util" class="external-link">HashSet</a>&lt;<a href="org/apache/tools/ant/util/VectorSet.html" title="type parameter in VectorSet">E</a>&gt; set</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/mail/package-summary.html">org.apache.tools.mail</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.mail.ErrorInQuitException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/mail/ErrorInQuitException.html" title="class in org.apache.tools.mail">org.apache.tools.mail.ErrorInQuitException</a></h3>
<div class="type-signature">class ErrorInQuitException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="org/apache/tools/zip/package-summary.html">org.apache.tools.zip</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="org.apache.tools.zip.UnsupportedZipFeatureException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/zip/UnsupportedZipFeatureException.html" title="class in org.apache.tools.zip">org.apache.tools.zip.UnsupportedZipFeatureException</a></h3>
<div class="type-signature">class UnsupportedZipFeatureException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>20161221L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>reason</h5>
<pre><a href="org/apache/tools/zip/UnsupportedZipFeatureException.Feature.html" title="class in org.apache.tools.zip">UnsupportedZipFeatureException.Feature</a> reason</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.zip.UnsupportedZipFeatureException.Feature">
<h3>Class&nbsp;<a href="org/apache/tools/zip/UnsupportedZipFeatureException.Feature.html" title="class in org.apache.tools.zip">org.apache.tools.zip.UnsupportedZipFeatureException.Feature</a></h3>
<div class="type-signature">class Feature extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> name</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="org.apache.tools.zip.Zip64RequiredException">
<h3>Exception Class&nbsp;<a href="org/apache/tools/zip/Zip64RequiredException.html" title="class in org.apache.tools.zip">org.apache.tools.zip.Zip64RequiredException</a></h3>
<div class="type-signature">class Zip64RequiredException extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/zip/ZipException.html" title="class or interface in java.util.zip" class="external-link">ZipException</a> implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>20110809L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
</div>
</div>
</body>
</html>
