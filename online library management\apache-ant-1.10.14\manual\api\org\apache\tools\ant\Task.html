<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Task (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: Task">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class Task" class="title">Class Task</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance">org.apache.tools.ant.Task</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</a></code>, <code><a href="taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></code>, <code><a href="taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs">Ant</a></code>, <code><a href="taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</a></code>, <code><a href="taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</a></code>, <code><a href="taskdefs/optional/ANTLR.html" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</a></code>, <code><a href="taskdefs/AntStructure.html" title="class in org.apache.tools.ant.taskdefs">AntStructure</a></code>, <code><a href="taskdefs/condition/AntVersion.html" title="class in org.apache.tools.ant.taskdefs.condition">AntVersion</a></code>, <code><a href="taskdefs/AugmentReference.html" title="class in org.apache.tools.ant.taskdefs">AugmentReference</a></code>, <code><a href="taskdefs/Available.html" title="class in org.apache.tools.ant.taskdefs">Available</a></code>, <code><a href="taskdefs/Basename.html" title="class in org.apache.tools.ant.taskdefs">Basename</a></code>, <code><a href="taskdefs/BindTargets.html" title="class in org.apache.tools.ant.taskdefs">BindTargets</a></code>, <code><a href="taskdefs/optional/ejb/BorlandGenerateClient.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandGenerateClient</a></code>, <code><a href="taskdefs/BuildNumber.html" title="class in org.apache.tools.ant.taskdefs">BuildNumber</a></code>, <code><a href="taskdefs/CallTarget.html" title="class in org.apache.tools.ant.taskdefs">CallTarget</a></code>, <code><a href="taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</a></code>, <code><a href="taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</a></code>, <code><a href="taskdefs/CloseResources.html" title="class in org.apache.tools.ant.taskdefs">CloseResources</a></code>, <code><a href="taskdefs/CommandLauncherTask.html" title="class in org.apache.tools.ant.taskdefs">CommandLauncherTask</a></code>, <code><a href="taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</a></code>, <code><a href="taskdefs/optional/ccm/Continuus.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">Continuus</a></code>, <code><a href="taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code>, <code><a href="taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">Copyfile</a></code>, <code><a href="taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</a></code>, <code><a href="taskdefs/CVSPass.html" title="class in org.apache.tools.ant.taskdefs">CVSPass</a></code>, <code><a href="taskdefs/DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</a></code>, <code><a href="taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">Deltree</a></code>, <code><a href="taskdefs/DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</a></code>, <code><a href="taskdefs/Dirname.html" title="class in org.apache.tools.ant.taskdefs">Dirname</a></code>, <code><a href="dispatch/DispatchTask.html" title="class in org.apache.tools.ant.dispatch">DispatchTask</a></code>, <code><a href="taskdefs/Echo.html" title="class in org.apache.tools.ant.taskdefs">Echo</a></code>, <code><a href="taskdefs/optional/EchoProperties.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</a></code>, <code><a href="taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</a></code>, <code><a href="taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">Exec</a></code>, <code><a href="taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></code>, <code><a href="taskdefs/Exit.html" title="class in org.apache.tools.ant.taskdefs">Exit</a></code>, <code><a href="taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</a></code>, <code><a href="taskdefs/Filter.html" title="class in org.apache.tools.ant.taskdefs">Filter</a></code>, <code><a href="taskdefs/optional/net/FTP.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</a></code>, <code><a href="taskdefs/optional/net/FTPTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTask</a></code>, <code><a href="taskdefs/optional/testing/Funtest.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">Funtest</a></code>, <code><a href="taskdefs/GenerateKey.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey</a></code>, <code><a href="taskdefs/Get.html" title="class in org.apache.tools.ant.taskdefs">Get</a></code>, <code><a href="taskdefs/HostInfo.html" title="class in org.apache.tools.ant.taskdefs">HostInfo</a></code>, <code><a href="taskdefs/ImportTask.html" title="class in org.apache.tools.ant.taskdefs">ImportTask</a></code>, <code><a href="taskdefs/Input.html" title="class in org.apache.tools.ant.taskdefs">Input</a></code>, <code><a href="taskdefs/optional/ejb/IPlanetEjbcTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbcTask</a></code>, <code><a href="taskdefs/optional/extension/JarLibAvailableTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibAvailableTask</a></code>, <code><a href="taskdefs/optional/extension/JarLibDisplayTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibDisplayTask</a></code>, <code><a href="taskdefs/optional/extension/JarLibManifestTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibManifestTask</a></code>, <code><a href="taskdefs/optional/extension/JarLibResolveTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibResolveTask</a></code>, <code><a href="taskdefs/Java.html" title="class in org.apache.tools.ant.taskdefs">Java</a></code>, <code><a href="taskdefs/optional/javacc/JavaCC.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JavaCC</a></code>, <code><a href="taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</a></code>, <code><a href="taskdefs/optional/Javah.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah</a></code>, <code><a href="taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</a></code>, <code><a href="taskdefs/optional/jdepend/JDependTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask</a></code>, <code><a href="taskdefs/optional/javacc/JJDoc.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJDoc</a></code>, <code><a href="taskdefs/optional/javacc/JJTree.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJTree</a></code>, <code><a href="taskdefs/modules/Jmod.html" title="class in org.apache.tools.ant.taskdefs.modules">Jmod</a></code>, <code><a href="taskdefs/optional/junitlauncher/confined/JUnitLauncherTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">JUnitLauncherTask</a></code>, <code><a href="taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a></code>, <code><a href="taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">KeySubst</a></code>, <code><a href="taskdefs/Length.html" title="class in org.apache.tools.ant.taskdefs">Length</a></code>, <code><a href="taskdefs/modules/Link.html" title="class in org.apache.tools.ant.taskdefs.modules">Link</a></code>, <code><a href="taskdefs/LoadProperties.html" title="class in org.apache.tools.ant.taskdefs">LoadProperties</a></code>, <code><a href="taskdefs/LoadResource.html" title="class in org.apache.tools.ant.taskdefs">LoadResource</a></code>, <code><a href="taskdefs/Local.html" title="class in org.apache.tools.ant.taskdefs">Local</a></code>, <code><a href="taskdefs/MacroInstance.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance</a></code>, <code><a href="taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</a></code>, <code><a href="taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</a></code>, <code><a href="taskdefs/ManifestTask.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask</a></code>, <code><a href="taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></code>, <code><a href="taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs">Mkdir</a></code>, <code><a href="taskdefs/optional/vss/MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</a></code>, <code><a href="taskdefs/Nice.html" title="class in org.apache.tools.ant.taskdefs">Nice</a></code>, <code><a href="taskdefs/Pack.html" title="class in org.apache.tools.ant.taskdefs">Pack</a></code>, <code><a href="taskdefs/Parallel.html" title="class in org.apache.tools.ant.taskdefs">Parallel</a></code>, <code><a href="taskdefs/Patch.html" title="class in org.apache.tools.ant.taskdefs">Patch</a></code>, <code><a href="taskdefs/PathConvert.html" title="class in org.apache.tools.ant.taskdefs">PathConvert</a></code>, <code><a href="taskdefs/ProjectHelperTask.html" title="class in org.apache.tools.ant.taskdefs">ProjectHelperTask</a></code>, <code><a href="taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs">Property</a></code>, <code><a href="taskdefs/optional/PropertyFile.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</a></code>, <code><a href="taskdefs/PropertyHelperTask.html" title="class in org.apache.tools.ant.taskdefs">PropertyHelperTask</a></code>, <code><a href="taskdefs/optional/pvcs/Pvcs.html" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">Pvcs</a></code>, <code><a href="taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</a></code>, <code><a href="taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">Rename</a></code>, <code><a href="taskdefs/optional/ReplaceRegExp.html" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</a></code>, <code><a href="taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</a></code>, <code><a href="taskdefs/Retry.html" title="class in org.apache.tools.ant.taskdefs">Retry</a></code>, <code><a href="taskdefs/optional/net/RExecTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask</a></code>, <code><a href="taskdefs/optional/Rpm.html" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</a></code>, <code><a href="taskdefs/optional/Script.html" title="class in org.apache.tools.ant.taskdefs.optional">Script</a></code>, <code><a href="taskdefs/optional/script/ScriptDefBase.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDefBase</a></code>, <code><a href="taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a></code>, <code><a href="taskdefs/optional/j2ee/ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</a></code>, <code><a href="taskdefs/SetPermissions.html" title="class in org.apache.tools.ant.taskdefs">SetPermissions</a></code>, <code><a href="taskdefs/optional/net/SetProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">SetProxy</a></code>, <code><a href="taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs">Sleep</a></code>, <code><a href="taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></code>, <code><a href="taskdefs/optional/sound/SoundTask.html" title="class in org.apache.tools.ant.taskdefs.optional.sound">SoundTask</a></code>, <code><a href="taskdefs/optional/splash/SplashTask.html" title="class in org.apache.tools.ant.taskdefs.optional.splash">SplashTask</a></code>, <code><a href="taskdefs/optional/ssh/SSHBase.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHBase</a></code>, <code><a href="taskdefs/SubAnt.html" title="class in org.apache.tools.ant.taskdefs">SubAnt</a></code>, <code><a href="taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</a></code>, <code><a href="TaskAdapter.html" title="class in org.apache.tools.ant">TaskAdapter</a></code>, <code><a href="taskdefs/optional/net/TelnetTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask</a></code>, <code><a href="taskdefs/TempFile.html" title="class in org.apache.tools.ant.taskdefs">TempFile</a></code>, <code><a href="taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs">Touch</a></code>, <code><a href="taskdefs/Truncate.html" title="class in org.apache.tools.ant.taskdefs">Truncate</a></code>, <code><a href="taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs">Tstamp</a></code>, <code><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></code>, <code><a href="taskdefs/Unpack.html" title="class in org.apache.tools.ant.taskdefs">Unpack</a></code>, <code><a href="taskdefs/UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</a></code>, <code><a href="taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs">WhichResource</a></code>, <code><a href="taskdefs/XmlProperty.html" title="class in org.apache.tools.ant.taskdefs">XmlProperty</a></code>, <code><a href="taskdefs/optional/junit/XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</a></code>, <code><a href="taskdefs/optional/XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">Task</span>
<span class="extends-implements">extends <a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></span></div>
<div class="block">Base class for all tasks.

 Use Project.createTask to create a new task instance rather than
 using this class directly for construction.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="Project.html#createTask(java.lang.String)"><code>Project.createTask(java.lang.String)</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="Target.html" title="class in org.apache.tools.ant">Target</a></code></div>
<div class="col-second even-row-color"><code><a href="#target" class="member-name-link">target</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#taskName" class="member-name-link">taskName</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#taskType" class="member-name-link">taskType</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second odd-row-color"><code><a href="#wrapper" class="member-name-link">wrapper</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="ProjectComponent.html#description">description</a>, <a href="ProjectComponent.html#location">location</a>, <a href="ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Task</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#bindToOwner(org.apache.tools.ant.Task)" class="member-name-link">bindToOwner</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;owner)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Bind a task to another; use this when configuring a newly created
 task to do work on behalf of another.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called by the project to let the task do its work.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Target.html" title="class in org.apache.tools.ant">Target</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOwningTarget()" class="member-name-link">getOwningTarget</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the container target of this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRuntimeConfigurableWrapper()" class="member-name-link">getRuntimeConfigurableWrapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the wrapper used for runtime configuration.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskName()" class="member-name-link">getTaskName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the name to use in logging messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskType()" class="member-name-link">getTaskType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the type of task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWrapper()" class="member-name-link">getWrapper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the runtime configurable structure for this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorFlush(java.lang.String)" class="member-name-link">handleErrorFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles an error line by logging it with the WARN priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleErrorOutput(java.lang.String)" class="member-name-link">handleErrorOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles an error output by logging it with the WARN priority.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleFlush(java.lang.String)" class="member-name-link">handleFlush</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles output by logging it with the INFO priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleInput(byte%5B%5D,int,int)" class="member-name-link">handleInput</a><wbr>(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle an input request by this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleOutput(java.lang.String)" class="member-name-link">handleOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles output by logging it with the INFO priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called by the project to let the task initialize properly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isInvalid()" class="member-name-link">isInvalid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Has this task been marked invalid?</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the default (INFO) priority.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the given priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,java.lang.Throwable,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t,
 int&nbsp;msgLevel)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the given priority.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.Throwable,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t,
 int&nbsp;msgLevel)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message with the given priority.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#maybeConfigure()" class="member-name-link">maybeConfigure</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configures this task - if it hasn't been done already.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#perform()" class="member-name-link">perform</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs this task if it's still valid, or gets a replacement
 version and performs that otherwise.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reconfigure()" class="member-name-link">reconfigure</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Force the task to be reconfigured from its RuntimeConfigurable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOwningTarget(org.apache.tools.ant.Target)" class="member-name-link">setOwningTarget</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the target container of this task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">setRuntimeConfigurableWrapper</a><wbr>(<a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;wrapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the wrapper to be used for runtime configuration.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTaskName(java.lang.String)" class="member-name-link">setTaskName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name to use in logging messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTaskType(java.lang.String)" class="member-name-link">setTaskType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name with which the task has been invoked.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="ProjectComponent.html#clone()">clone</a>, <a href="ProjectComponent.html#getDescription()">getDescription</a>, <a href="ProjectComponent.html#getLocation()">getLocation</a>, <a href="ProjectComponent.html#getProject()">getProject</a>, <a href="ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="target">
<h3>target</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Target.html" title="class in org.apache.tools.ant">Target</a></span>&nbsp;<span class="element-name">target</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="#getOwningTarget()"><code>getOwningTarget()</code></a> method.</div>
</div>
<div class="block">Target this task belongs to, if any.</div>
</section>
</li>
<li>
<section class="detail" id="taskName">
<h3>taskName</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">taskName</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="#getTaskName()"><code>getTaskName()</code></a> method.</div>
</div>
<div class="block">Name of this task to be used for logging purposes.
 This defaults to the same as the type, but may be
 overridden by the user. For instance, the name "java"
 isn't terribly descriptive for a task used within
 another task - the outer task code can probably
 provide a better one.</div>
</section>
</li>
<li>
<section class="detail" id="taskType">
<h3>taskType</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">taskType</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="#getTaskType()"><code>getTaskType()</code></a> method.</div>
</div>
<div class="block">Type of this task.</div>
</section>
</li>
<li>
<section class="detail" id="wrapper">
<h3>wrapper</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">wrapper</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <a href="#getWrapper()"><code>getWrapper()</code></a> method.</div>
</div>
<div class="block">Wrapper for this object, used to configure it at runtime.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Task</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Task</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setOwningTarget(org.apache.tools.ant.Target)">
<h3>setOwningTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOwningTarget</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;target)</span></div>
<div class="block">Sets the target container of this task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - Target in whose scope this task belongs.
               May be <code>null</code>, indicating a top-level task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOwningTarget()">
<h3>getOwningTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Target.html" title="class in org.apache.tools.ant">Target</a></span>&nbsp;<span class="element-name">getOwningTarget</span>()</div>
<div class="block">Returns the container target of this task.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The target containing this task, or <code>null</code> if
         this task is a top-level task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTaskName(java.lang.String)">
<h3>setTaskName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTaskName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Sets the name to use in logging messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name to use in logging messages.
             Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTaskName()">
<h3>getTaskName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTaskName</span>()</div>
<div class="block">Returns the name to use in logging messages.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name to use in logging messages.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTaskType(java.lang.String)">
<h3>setTaskType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTaskType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type)</span></div>
<div class="block">Sets the name with which the task has been invoked.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - The name the task has been invoked as.
             Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()
          throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called by the project to let the task initialize properly.
 The default implementation is a no-op.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong with the build</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called by the project to let the task do its work. This method may be
 called more than once, if the task is invoked more than once.
 For example,
 if target1 and target2 both depend on target3, then running
 "ant target1 target2" will run all tasks in target3 twice.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong with the build.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRuntimeConfigurableWrapper()">
<h3>getRuntimeConfigurableWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">getRuntimeConfigurableWrapper</span>()</div>
<div class="block">Returns the wrapper used for runtime configuration.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the wrapper used for runtime configuration. This
         method will generate a new wrapper (and cache it)
         if one isn't set already.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">
<h3>setRuntimeConfigurableWrapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRuntimeConfigurableWrapper</span><wbr><span class="parameters">(<a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;wrapper)</span></div>
<div class="block">Sets the wrapper to be used for runtime configuration.

 This method should be used only by the ProjectHelper and Ant internals.
 It is public to allow helper plugins to operate on tasks, normal tasks
 should never use it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>wrapper</code> - The wrapper to be used for runtime configuration.
                May be <code>null</code>, in which case the next call
                to getRuntimeConfigurableWrapper will generate a new
                wrapper.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="maybeConfigure()">
<h3>maybeConfigure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">maybeConfigure</span>()
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Configures this task - if it hasn't been done already.
 If the task has been invalidated, it is replaced with an
 UnknownElement task which uses the new definition in the project.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the task cannot be configured.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reconfigure()">
<h3>reconfigure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">reconfigure</span>()</div>
<div class="block">Force the task to be reconfigured from its RuntimeConfigurable.</div>
</section>
</li>
<li>
<section class="detail" id="handleOutput(java.lang.String)">
<h3>handleOutput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles output by logging it with the INFO priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - The output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleFlush(java.lang.String)">
<h3>handleFlush</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles output by logging it with the INFO priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - The output to log. Should not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleInput(byte[],int,int)">
<h3>handleInput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">handleInput</span><wbr><span class="parameters">(byte[]&nbsp;buffer,
 int&nbsp;offset,
 int&nbsp;length)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Handle an input request by this task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buffer</code> - the buffer into which data is to be read.</dd>
<dd><code>offset</code> - the offset into the buffer at which data is stored.</dd>
<dd><code>length</code> - the amount of data to read.</dd>
<dt>Returns:</dt>
<dd>the number of bytes read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the data cannot be read.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorOutput(java.lang.String)">
<h3>handleErrorOutput</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles an error output by logging it with the WARN priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - The error output to log. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleErrorFlush(java.lang.String)">
<h3>handleErrorFlush</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">handleErrorFlush</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;output)</span></div>
<div class="block">Handles an error line by logging it with the WARN priority.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - The error output to log. Should not be <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg)</span></div>
<div class="block">Logs a message with the default (INFO) priority.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProjectComponent.html#log(java.lang.String)">log</a></code>&nbsp;in class&nbsp;<code><a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></code></dd>
<dt>Parameters:</dt>
<dd><code>msg</code> - The message to be logged. Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 int&nbsp;msgLevel)</span></div>
<div class="block">Logs a message with the given priority. This delegates
 the actual logging to the project.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ProjectComponent.html#log(java.lang.String,int)">log</a></code>&nbsp;in class&nbsp;<code><a href="ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></code></dd>
<dt>Parameters:</dt>
<dd><code>msg</code> - The message to be logged. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The message priority at which this message is to
                 be logged.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.Throwable,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t,
 int&nbsp;msgLevel)</span></div>
<div class="block">Logs a message with the given priority. This delegates
 the actual logging to the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>t</code> - The exception to be logged. Should not be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The message priority at which this message is to
                 be logged.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,java.lang.Throwable,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;msg,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t,
 int&nbsp;msgLevel)</span></div>
<div class="block">Logs a message with the given priority. This delegates
 the actual logging to the project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msg</code> - The message to be logged. Should not be <code>null</code>.</dd>
<dd><code>t</code> - The exception to be logged. May be <code>null</code>.</dd>
<dd><code>msgLevel</code> - The message priority at which this message is to
                 be logged.</dd>
<dt>Since:</dt>
<dd>1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="perform()">
<h3>perform</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">perform</span>()</div>
<div class="block">Performs this task if it's still valid, or gets a replacement
 version and performs that otherwise.

 Performing a task consists of firing a task started event,
 configuring the task, executing it, and then firing task finished
 event. If a runtime exception is thrown, the task finished event
 is still fired, but with the exception as the cause.</div>
</section>
</li>
<li>
<section class="detail" id="isInvalid()">
<h3>isInvalid</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isInvalid</span>()</div>
<div class="block">Has this task been marked invalid?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this task is no longer valid. A new task should be
 configured in this case.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTaskType()">
<h3>getTaskType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTaskType</span>()</div>
<div class="block">Return the type of task.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the type of task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWrapper()">
<h3>getWrapper</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a></span>&nbsp;<span class="element-name">getWrapper</span>()</div>
<div class="block">Return the runtime configurable structure for this task.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the runtime structure for this task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="bindToOwner(org.apache.tools.ant.Task)">
<h3>bindToOwner</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">bindToOwner</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;owner)</span></div>
<div class="block">Bind a task to another; use this when configuring a newly created
 task to do work on behalf of another.
 Project, OwningTarget, TaskName, Location and Description are all copied

 Important: this method does not call <a href="#init()"><code>init()</code></a>.
 If you are creating a task to delegate work to, call <a href="#init()"><code>init()</code></a>
 to initialize it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>owner</code> - owning target</dd>
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
