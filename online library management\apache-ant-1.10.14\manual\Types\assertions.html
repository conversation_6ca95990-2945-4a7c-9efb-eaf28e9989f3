<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Assertions type</title>
</head>

<body>

<h2 id="assertions">Assertions</h2>
<p>
The <code>assertions</code> type enables or disables the Java 1.4 assertions feature, on a whole
Java program, or components of a program. It can be used
in <a href="../Tasks/java.html"><code>&lt;java&gt;</code></a>
and <a href="../Tasks/junit.html"><code>&lt;junit&gt;</code></a> to add extra validation to code.

<p>
Assertions are covered in
the <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/language/assert.html"
target="_top">Java SE documentation</a>, and
the <a href="https://docs.oracle.com/javase/specs/jls/se8/html/jls-14.html#jls-14.10"
target="_top">Java Language Specification</a>.

<p>
The key points to note are that a <code>java.lang.AssertionError</code> is thrown when an assertion
fails, and that the facility is only available on Java 1.4 and later. To enable assertions one must
set <var>source</var>=<q>1.4</q> (or later) in <code>&lt;javac&gt;</code> when the source is being
compiled, and that the code must contain <code>assert</code> statements to be tested. The result of
such an action is code that neither compiles or runs on earlier versions of Java. For this reason
Apache Ant itself currently contains no assertions.
<p>

<p>
When assertions are enabled (or disabled) in a task through nested assertions elements, the class
loader or command line is modified with the appropriate options. This means that the JVM executed
must be of version 1.4 or later, even if there are no assertions in the code. Attempting to enable
assertions on earlier JVMs will result in an "Unrecognized option" error and the JVM will not start.
</p>

<h4>Attributes</h4>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>enableSystemAssertions</td>
    <td>Flag to turn system assertions on or off.</td>
    <td>No; default is <q>unspecified</q></td>
  </tr>
</table>
<p>
When system assertions have been neither enabled nor disabled, then the JVM is not given any
assertion information&mdash;the default action of the current JVMs is to disable system assertions.
</p>
<p>
Note also that there is no apparent documentation for what parts of the JRE come with useful
assertions.
</p>

<h3>Nested elements</h3>

<h4>enable</h4>
<p>
Enable assertions in portions of code.  If neither a package nor class is specified, assertions are
turned on in <em>all</em> (user) code.
</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>class</td>
    <td>The name of a class on which to enable assertions.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>package</td>
    <td>The name of a package in which to enable assertions on all classes. (Includes subpackages.)
      Use <q>...</q> for the anonymous package.</td>
    <td>No</td>
  </tr>
</table>

<h4>disable</h4>
<p>
Disable assertions in portions of code.
</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>class</td>
    <td>The name of a class on which to disable assertions.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>package</td>
    <td>The name of a package in which to disable assertions on all classes. (Includes subpackages.)
      Use <q>...</q> for the anonymous package.</td>
    <td>No</td>
  </tr>
</table>
<p>
Because assertions are disabled by default, it only makes sense to disable assertions where they
have been enabled in a parent package.
</p>
<h4>Examples</h4>

<h5>Example: enable assertions in all user classes</h5>

<p>All classes not in the JRE (i.e. all non-system classes) will have assertions turned on.</p>
<pre>
&lt;assertions&gt;
  &lt;enable/&gt;
&lt;/assertions&gt;</pre>

<h5>Example: enable a single class</h5>

<p>Enable assertions in a class called Test</p>
<pre>
&lt;assertions&gt;
  &lt;enable class="Test"/&gt;
&lt;/assertions&gt;</pre>

<h5>Example: enable a package</h5>

<p>Enable assertions in the <code>org.apache</code> package and all packages starting with
the <code>org.apache.</code> prefix</p>
<pre>
&lt;assertions&gt;
  &lt;enable package="org.apache"/&gt;
&lt;/assertions&gt;</pre>

<h5>Example: System assertions</h5>

<p>Enable system assertions and assertions in all <code>org.apache</code> packages except for Ant
(but including <code>org.apache.tools.ant.Main</code>)</p>
<pre>
&lt;assertions enableSystemAssertions="true"&gt;
  &lt;enable package="org.apache"/&gt;
  &lt;disable package="org.apache.tools.ant"/&gt;
  &lt;enable class="org.apache.tools.ant.Main"/&gt;
&lt;/assertions&gt;</pre>

<h5>Example: disabled and anonymous package assertions</h5>

<p>Disable system assertions; enable those in the anonymous package</p>
<pre>
&lt;assertions enableSystemAssertions="false"&gt;
  &lt;enable package="..."/&gt;
&lt;/assertions&gt;</pre>

<h5>Example: referenced assertions</h5>

<p>This type is a datatype, so you can declare assertions and use them later</p>
<pre>
&lt;assertions id="project.assertions"&gt;
  &lt;enable package="org.apache.test"/&gt;
&lt;/assertions&gt;

&lt;assertions refid="project.assertions"/&gt;</pre>

</body>
</html>
