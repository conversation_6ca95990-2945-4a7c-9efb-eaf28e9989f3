<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectHelper (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: ProjectHelper">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class ProjectHelper" class="title">Class ProjectHelper</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.ProjectHelper</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="helper/ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></code>, <code><a href="helper/ProjectHelperImpl.html" title="class in org.apache.tools.ant.helper">ProjectHelperImpl</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ProjectHelper</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Configures a Project (complete with Targets and Tasks) based on
 a build file. It'll rely on a plugin to do the actual processing
 of the file.
 <p>
 This class also provide static wrappers for common introspection.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="ProjectHelper.OnMissingExtensionPoint.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectHelper.OnMissingExtensionPoint</a></code></div>
<div class="col-last even-row-color">
<div class="block">Possible value for target's onMissingExtensionPoint attribute.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_ATTRIBUTE_URI" class="member-name-link">ANT_ATTRIBUTE_URI</a></code></div>
<div class="col-last even-row-color">
<div class="block">The URI for ant specific attributes</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_CORE_URI" class="member-name-link">ANT_CORE_URI</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The URI for ant name space</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_CURRENT_URI" class="member-name-link">ANT_CURRENT_URI</a></code></div>
<div class="col-last even-row-color">
<div class="block">The URI for antlib current definitions</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_TYPE" class="member-name-link">ANT_TYPE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Polymorphic attribute</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANTLIB_URI" class="member-name-link">ANTLIB_URI</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.ANTLIB_PREFIX</div>
</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#HELPER_PROPERTY" class="member-name-link">HELPER_PROPERTY</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.PROJECT_HELPER_CLASS</div>
</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROJECTHELPER_REFERENCE" class="member-name-link">PROJECTHELPER_REFERENCE</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.REFID_PROJECT_HELPER</div>
</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SERVICE_ID" class="member-name-link">SERVICE_ID</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.PROJECT_HELPER_SERVICE</div>
</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#USE_PROJECT_NAME_AS_TARGET_PREFIX" class="member-name-link">USE_PROJECT_NAME_AS_TARGET_PREFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">constant to denote use project name as target prefix</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ProjectHelper</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addLocationToBuildException(org.apache.tools.ant.BuildException,org.apache.tools.ant.Location)" class="member-name-link">addLocationToBuildException</a><wbr>(<a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a>&nbsp;ex,
 <a href="Location.html" title="class in org.apache.tools.ant">Location</a>&nbsp;newLocation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Add location to build exception.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addText(org.apache.tools.ant.Project,java.lang.Object,char%5B%5D,int,int)" class="member-name-link">addText</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Adds the content of #PCDATA sections to an element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addText(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)" class="member-name-link">addText</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Adds the content of #PCDATA sections to an element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#canParseAntlibDescriptor(org.apache.tools.ant.types.Resource)" class="member-name-link">canParseAntlibDescriptor</a><wbr>(<a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether this instance of ProjectHelper can parse an Antlib
 descriptor given by the URL and return its content as an
 UnknownElement ready to be turned into an Antlib task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#canParseBuildFile(org.apache.tools.ant.types.Resource)" class="member-name-link">canParseBuildFile</a><wbr>(<a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;buildFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the helper supports the kind of file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#configure(java.lang.Object,org.xml.sax.AttributeList,org.apache.tools.ant.Project)" class="member-name-link">configure</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/AttributeList.html" title="class or interface in org.xml.sax" class="external-link">AttributeList</a>&nbsp;attrs,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#configureProject(org.apache.tools.ant.Project,java.io.File)" class="member-name-link">configureProject</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;buildFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Configures the project with the contents of the specified build file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#extractNameFromComponentName(java.lang.String)" class="member-name-link">extractNameFromComponentName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;componentName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">extract the element name from a component name</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#extractUriFromComponentName(java.lang.String)" class="member-name-link">extractUriFromComponentName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;componentName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">extract a uri from a component name</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#genComponentName(java.lang.String,java.lang.String)" class="member-name-link">genComponentName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Map a namespaced {uri,name} to an internal string format.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getContextClassLoader()" class="member-name-link">getContextClassLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getCurrentPrefixSeparator()" class="member-name-link">getCurrentPrefixSeparator</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The separator between the prefix and the target name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getCurrentTargetPrefix()" class="member-name-link">getCurrentTargetPrefix</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">The prefix to prepend to imported target names.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultBuildFile()" class="member-name-link">getDefaultBuildFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The file name of the build script to be parsed if none specified on the command line</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtensionStack()" class="member-name-link">getExtensionStack</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Extension stack.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImportStack()" class="member-name-link">getImportStack</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Import stack.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getProjectHelper()" class="member-name-link">getProjectHelper</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get the first project helper found in the classpath</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isInIncludeMode()" class="member-name-link">isInIncludeMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Whether the current file should be read in include as opposed
 to import mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#nsToComponentName(java.lang.String)" class="member-name-link">nsToComponentName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Convert an attribute namespace to a "component name".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parse(org.apache.tools.ant.Project,java.lang.Object)" class="member-name-link">parse</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;source)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the project file, configuring the project as it goes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseAntlibDescriptor(org.apache.tools.ant.Project,org.apache.tools.ant.types.Resource)" class="member-name-link">parseAntlibDescriptor</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;containingProject,
 <a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parse the given URL as an antlib descriptor and return the
 content as something that can be turned into an Antlib task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)" class="member-name-link">parsePropertyString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fragments,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;propertyRefs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#replaceProperties(org.apache.tools.ant.Project,java.lang.String)" class="member-name-link">replaceProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#replaceProperties(org.apache.tools.ant.Project,java.lang.String,java.util.Hashtable)" class="member-name-link">replaceProperties</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;keys)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveExtensionOfAttributes(org.apache.tools.ant.Project)" class="member-name-link">resolveExtensionOfAttributes</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check extensionStack and inject all targets having extensionOf attributes
 into extensionPoint.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setCurrentPrefixSeparator(java.lang.String)" class="member-name-link">setCurrentPrefixSeparator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sep)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Sets the separator between the prefix and the target name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setCurrentTargetPrefix(java.lang.String)" class="member-name-link">setCurrentTargetPrefix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Sets the prefix to prepend to imported target names.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#setInIncludeMode(boolean)" class="member-name-link">setInIncludeMode</a><wbr>(boolean&nbsp;includeMode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Sets whether the current file should be read in include as
 opposed to import mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#storeChild(org.apache.tools.ant.Project,java.lang.Object,java.lang.Object,java.lang.String)" class="member-name-link">storeChild</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;child,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Stores a configured child element within its parent object.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ANT_CORE_URI">
<h3>ANT_CORE_URI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_CORE_URI</span></div>
<div class="block">The URI for ant name space</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_CORE_URI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_CURRENT_URI">
<h3>ANT_CURRENT_URI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_CURRENT_URI</span></div>
<div class="block">The URI for antlib current definitions</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_CURRENT_URI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_ATTRIBUTE_URI">
<h3>ANT_ATTRIBUTE_URI</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_ATTRIBUTE_URI</span></div>
<div class="block">The URI for ant specific attributes</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_ATTRIBUTE_URI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANTLIB_URI">
<h3>ANTLIB_URI</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANTLIB_URI</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.ANTLIB_PREFIX</div>
</div>
<div class="block">The URI for defined types/tasks - the format is antlib:&lt;package&gt;</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANTLIB_URI">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_TYPE">
<h3>ANT_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_TYPE</span></div>
<div class="block">Polymorphic attribute</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="HELPER_PROPERTY">
<h3>HELPER_PROPERTY</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">HELPER_PROPERTY</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.PROJECT_HELPER_CLASS</div>
</div>
<div class="block">Name of JVM system property which provides the name of the
 ProjectHelper class to use.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.HELPER_PROPERTY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SERVICE_ID">
<h3>SERVICE_ID</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SERVICE_ID</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.PROJECT_HELPER_SERVICE</div>
</div>
<div class="block">The service identifier in jars which provide Project Helper
 implementations.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.SERVICE_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECTHELPER_REFERENCE">
<h3>PROJECTHELPER_REFERENCE</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECTHELPER_REFERENCE</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use MagicNames.REFID_PROJECT_HELPER</div>
</div>
<div class="block">name of project helper reference that we add to a project</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.PROJECTHELPER_REFERENCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="USE_PROJECT_NAME_AS_TARGET_PREFIX">
<h3>USE_PROJECT_NAME_AS_TARGET_PREFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">USE_PROJECT_NAME_AS_TARGET_PREFIX</span></div>
<div class="block">constant to denote use project name as target prefix</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.USE_PROJECT_NAME_AS_TARGET_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ProjectHelper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ProjectHelper</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="configureProject(org.apache.tools.ant.Project,java.io.File)">
<h3>configureProject</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureProject</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;buildFile)</span>
                             throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Configures the project with the contents of the specified build file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project to configure. Must not be <code>null</code>.</dd>
<dd><code>buildFile</code> - A build file giving the project's configuration.
                  Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the configuration is invalid or cannot be read</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImportStack()">
<h3>getImportStack</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getImportStack</span>()</div>
<div class="block">Import stack.
  Used to keep track of imported files. Error reporting should
  display the import path.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the stack of import source objects.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExtensionStack()">
<h3>getExtensionStack</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&gt;</span>&nbsp;<span class="element-name">getExtensionStack</span>()</div>
<div class="block">Extension stack.
 Used to keep track of targets that extend extension points.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a list of three element string arrays where the first
 element is the name of the extensionpoint, the second the name
 of the target and the third the name of the enum like class
 <a href="ProjectHelper.OnMissingExtensionPoint.html" title="class in org.apache.tools.ant"><code>ProjectHelper.OnMissingExtensionPoint</code></a>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentTargetPrefix()">
<h3>getCurrentTargetPrefix</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentTargetPrefix</span>()</div>
<div class="block">The prefix to prepend to imported target names.

 <p>May be set by &lt;import&gt;'s as attribute.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the configured prefix or null</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentTargetPrefix(java.lang.String)">
<h3>setCurrentTargetPrefix</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentTargetPrefix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Sets the prefix to prepend to imported target names.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentPrefixSeparator()">
<h3>getCurrentPrefixSeparator</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentPrefixSeparator</span>()</div>
<div class="block">The separator between the prefix and the target name.

 <p>May be set by &lt;import&gt;'s prefixSeparator attribute.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurrentPrefixSeparator(java.lang.String)">
<h3>setCurrentPrefixSeparator</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurrentPrefixSeparator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sep)</span></div>
<div class="block">Sets the separator between the prefix and the target name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sep</code> - String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isInIncludeMode()">
<h3>isInIncludeMode</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isInIncludeMode</span>()</div>
<div class="block">Whether the current file should be read in include as opposed
 to import mode.

 <p>In include mode included targets are only known by their
 prefixed names and their depends lists get rewritten so that
 all dependencies get the prefix as well.</p>

 <p>In import mode imported targets are known by an adorned as
 well as a prefixed name and the unadorned target may be
 overwritten in the importing build file.  The depends list of
 the imported targets is not modified at all.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInIncludeMode(boolean)">
<h3>setInIncludeMode</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInIncludeMode</span><wbr><span class="parameters">(boolean&nbsp;includeMode)</span></div>
<div class="block">Sets whether the current file should be read in include as
 opposed to import mode.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includeMode</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parse(org.apache.tools.ant.Project,java.lang.Object)">
<h3>parse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parse</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;source)</span>
           throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Parses the project file, configuring the project as it goes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project for the resulting ProjectHelper to configure.
                Must not be <code>null</code>.</dd>
<dd><code>source</code> - The source for XML configuration. A helper must support
               at least File, for backward compatibility. Helpers may
               support URL, InputStream, etc or specialized types.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the configuration is invalid or cannot
                           be read</dd>
<dt>Since:</dt>
<dd>Ant1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProjectHelper()">
<h3>getProjectHelper</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></span>&nbsp;<span class="element-name">getProjectHelper</span>()</div>
<div class="block">Get the first project helper found in the classpath</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an project helper, never <code>null</code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="ProjectHelperRepository.html#getHelpers()"><code>ProjectHelperRepository.getHelpers()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getContextClassLoader()">
<h3>getContextClassLoader</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getContextClassLoader</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use LoaderUtils.getContextClassLoader()</div>
</div>
<div class="block">JDK1.1 compatible access to the context class loader. Cut &amp; paste from JAXP.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the current context class loader, or <code>null</code>
 if the context class loader is unavailable.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configure(java.lang.Object,org.xml.sax.AttributeList,org.apache.tools.ant.Project)">
<h3>configure</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/AttributeList.html" title="class or interface in org.xml.sax" class="external-link">AttributeList</a>&nbsp;attrs,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span>
                      throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use IntrospectionHelper for each property.</div>
</div>
<div class="block">Configures an object using an introspection handler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - The target object to be configured.
               Must not be <code>null</code>.</dd>
<dd><code>attrs</code> - A list of attributes to configure within the target.
               Must not be <code>null</code>.</dd>
<dd><code>project</code> - The project containing the target.
                Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if any of the attributes can't be handled by
                           the target</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addText(org.apache.tools.ant.Project,java.lang.Object,char[],int,int)">
<h3>addText</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count)</span>
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds the content of #PCDATA sections to an element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project containing the target.
                Must not be <code>null</code>.</dd>
<dd><code>target</code> - The target object to be configured.
                Must not be <code>null</code>.</dd>
<dd><code>buf</code> - A character array of the text within the element.
            Will not be <code>null</code>.</dd>
<dd><code>start</code> - The start element in the array.</dd>
<dd><code>count</code> - The number of characters to read from the array.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the target object doesn't accept text</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addText(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">
<h3>addText</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;target,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span>
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds the content of #PCDATA sections to an element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project containing the target.
                Must not be <code>null</code>.</dd>
<dd><code>target</code> - The target object to be configured.
                Must not be <code>null</code>.</dd>
<dd><code>text</code> - Text to add to the target.
                May be <code>null</code>, in which case this
                method call is a no-op.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the target object doesn't accept text</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="storeChild(org.apache.tools.ant.Project,java.lang.Object,java.lang.Object,java.lang.String)">
<h3>storeChild</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">storeChild</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;child,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag)</span></div>
<div class="block">Stores a configured child element within its parent object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - Project containing the objects.
                May be <code>null</code>.</dd>
<dd><code>parent</code> - Parent object to add child to.
                Must not be <code>null</code>.</dd>
<dd><code>child</code> - Child object to store in parent.
                Should not be <code>null</code>.</dd>
<dd><code>tag</code> - Name of element which generated the child.
                May be <code>null</code>, in which case
                the child is not stored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="replaceProperties(org.apache.tools.ant.Project,java.lang.String)">
<h3>replaceProperties</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">replaceProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use project.replaceProperties().</div>
</div>
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value with
 the string value of the corresponding properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project containing the properties to replace.
                Must not be <code>null</code>.</dd>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="replaceProperties(org.apache.tools.ant.Project,java.lang.String,java.util.Hashtable)">
<h3>replaceProperties</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">replaceProperties</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;keys)</span>
                                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use PropertyHelper.</div>
</div>
<div class="block">Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The container project. This is used solely for
                logging purposes. Must not be <code>null</code>.</dd>
<dd><code>value</code> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.</dd>
<dd><code>keys</code> - Mapping (String to Object) of property names to their
              values. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)">
<h3>parsePropertyString</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parsePropertyString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;fragments,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;propertyRefs)</span>
                                throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use PropertyHelper.</div>
</div>
<div class="block">Parses a string containing <code>${xxx}</code> style property
 references into two lists. The first list is a collection
 of text fragments, while the other is a set of string property names.
 <code>null</code> entries in the first list indicate a property
 reference from the second list.

 <p>As of Ant 1.8.0 this method is never invoked by any code
 inside of Ant itself.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - Text to parse. Must not be <code>null</code>.</dd>
<dd><code>fragments</code> - List to add text fragments to.
                  Must not be <code>null</code>.</dd>
<dd><code>propertyRefs</code> - List to add property names to.
                     Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the string contains an opening
                           <code>${</code> without a closing <code>}</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="genComponentName(java.lang.String,java.lang.String)">
<h3>genComponentName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">genComponentName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Map a namespaced {uri,name} to an internal string format.
 For BC purposes the names from the ant core uri will be
 mapped to "name", other names will be mapped to
 uri + ":" + name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace URI</dd>
<dd><code>name</code> - The localname</dd>
<dt>Returns:</dt>
<dd>The stringified form of the ns name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extractUriFromComponentName(java.lang.String)">
<h3>extractUriFromComponentName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">extractUriFromComponentName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;componentName)</span></div>
<div class="block">extract a uri from a component name</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>componentName</code> - The stringified form for {uri, name}</dd>
<dt>Returns:</dt>
<dd>The uri or "" if not present</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="extractNameFromComponentName(java.lang.String)">
<h3>extractNameFromComponentName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">extractNameFromComponentName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;componentName)</span></div>
<div class="block">extract the element name from a component name</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>componentName</code> - The stringified form for {uri, name}</dd>
<dt>Returns:</dt>
<dd>The element name of the component</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="nsToComponentName(java.lang.String)">
<h3>nsToComponentName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">nsToComponentName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ns)</span></div>
<div class="block">Convert an attribute namespace to a "component name".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ns</code> - the xml namespace uri.</dd>
<dt>Returns:</dt>
<dd>the converted value.</dd>
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addLocationToBuildException(org.apache.tools.ant.BuildException,org.apache.tools.ant.Location)">
<h3>addLocationToBuildException</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">addLocationToBuildException</span><wbr><span class="parameters">(<a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a>&nbsp;ex,
 <a href="Location.html" title="class in org.apache.tools.ant">Location</a>&nbsp;newLocation)</span></div>
<div class="block">Add location to build exception.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ex</code> - the build exception, if the build exception
           does not include</dd>
<dd><code>newLocation</code> - the location of the calling task (may be null)</dd>
<dt>Returns:</dt>
<dd>a new build exception based in the build exception with
         location set to newLocation. If the original exception
         did not have a location, just return the build exception</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="canParseAntlibDescriptor(org.apache.tools.ant.types.Resource)">
<h3>canParseAntlibDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">canParseAntlibDescriptor</span><wbr><span class="parameters">(<a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;r)</span></div>
<div class="block">Whether this instance of ProjectHelper can parse an Antlib
 descriptor given by the URL and return its content as an
 UnknownElement ready to be turned into an Antlib task.

 <p>This method should not try to parse the content of the
 descriptor, the URL is only given as an argument to allow
 subclasses to decide whether they can support a given URL
 scheme or not.</p>

 <p>Subclasses that return true in this method must also
 override <a href="#parseAntlibDescriptor(org.apache.tools.ant.Project,org.apache.tools.ant.types.Resource)"><code>parseAntlibDescriptor</code></a>.</p>

 <p>This implementation returns false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - Resource</dd>
<dt>Returns:</dt>
<dd>boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseAntlibDescriptor(org.apache.tools.ant.Project,org.apache.tools.ant.types.Resource)">
<h3>parseAntlibDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a></span>&nbsp;<span class="element-name">parseAntlibDescriptor</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;containingProject,
 <a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;source)</span></div>
<div class="block">Parse the given URL as an antlib descriptor and return the
 content as something that can be turned into an Antlib task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>containingProject</code> - Project</dd>
<dd><code>source</code> - Resource</dd>
<dt>Returns:</dt>
<dd>UnknownElement</dd>
<dt>Since:</dt>
<dd>ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="canParseBuildFile(org.apache.tools.ant.types.Resource)">
<h3>canParseBuildFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">canParseBuildFile</span><wbr><span class="parameters">(<a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;buildFile)</span></div>
<div class="block">Check if the helper supports the kind of file. Some basic check on the
 extension's file should be done here.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buildFile</code> - the file expected to be parsed (never <code>null</code>)</dd>
<dt>Returns:</dt>
<dd>true if the helper supports it</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultBuildFile()">
<h3>getDefaultBuildFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultBuildFile</span>()</div>
<div class="block">The file name of the build script to be parsed if none specified on the command line</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the default file (never <code>null</code>)</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveExtensionOfAttributes(org.apache.tools.ant.Project)">
<h3>resolveExtensionOfAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resolveExtensionOfAttributes</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span>
                                  throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check extensionStack and inject all targets having extensionOf attributes
 into extensionPoint.
 <p>
 This method allow you to defer injection and have a powerful control of
 extensionPoint wiring.
 </p>
 <p>
 This should be invoked by each concrete implementation of ProjectHelper
 when the root "buildfile" and all imported/included buildfile are loaded.
 </p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project containing the target. Must not be
            <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if OnMissingExtensionPoint.FAIL and
                extensionPoint does not exist</dd>
<dt>Since:</dt>
<dd>1.9</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="ProjectHelper.OnMissingExtensionPoint.html" title="class in org.apache.tools.ant"><code>ProjectHelper.OnMissingExtensionPoint</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
