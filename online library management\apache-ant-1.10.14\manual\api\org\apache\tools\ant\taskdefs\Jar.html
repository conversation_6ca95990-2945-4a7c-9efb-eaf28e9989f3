<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Jar (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Jar">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Jar" class="title">Class Jar</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance"><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Zip</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Jar</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="Ear.html" title="class in org.apache.tools.ant.taskdefs">Ear</a></code>, <code><a href="War.html" title="class in org.apache.tools.ant.taskdefs">War</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Jar</span>
<span class="extends-implements">extends <a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></span></div>
<div class="block">Creates a JAR archive.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Jar.FilesetManifestConfig.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a></code></div>
<div class="col-last even-row-color">
<div class="block">The manifest config enumerated type.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Jar.StrictMode.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The strict enumerated type.</div>
</div>
</div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h2>
<code><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a>, <a href="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a>, <a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a>, <a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>, <a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h3>
<code><a href="Zip.html#addedDirs">addedDirs</a>, <a href="Zip.html#archiveType">archiveType</a>, <a href="Zip.html#doubleFilePass">doubleFilePass</a>, <a href="Zip.html#duplicate">duplicate</a>, <a href="Zip.html#emptyBehavior">emptyBehavior</a>, <a href="Zip.html#entries">entries</a>, <a href="Zip.html#skipWriting">skipWriting</a>, <a href="Zip.html#zipFile">zipFile</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Jar</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredIndexJars(org.apache.tools.ant.types.Path)" class="member-name-link">addConfiguredIndexJars</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a path to index jars.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredIndexJarsMapper(org.apache.tools.ant.types.Mapper)" class="member-name-link">addConfiguredIndexJarsMapper</a><wbr>(<a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;mapper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a mapper used to convert the jars to entries in the index.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredManifest(org.apache.tools.ant.taskdefs.Manifest)" class="member-name-link">addConfiguredManifest</a><wbr>(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;newManifest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Allows the manifest for the archive file to be provided inline
 in the build file rather than in an external file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredService(org.apache.tools.ant.types.spi.Service)" class="member-name-link">addConfiguredService</a><wbr>(<a href="../types/spi/Service.html" title="class in org.apache.tools.ant.types.spi">Service</a>&nbsp;service)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A nested SPI service element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addMetainf(org.apache.tools.ant.types.ZipFileSet)" class="member-name-link">addMetainf</a><wbr>(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;fs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a zipfileset to include in the META-INF directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanUp()" class="member-name-link">cleanUp</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make sure we don't think we already have a MANIFEST next time this task
 gets executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createEmptyZip(java.io.File)" class="member-name-link">createEmptyZip</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an empty jar file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)" class="member-name-link">finalizeZipOutputStream</a><wbr>(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finalize the zip output stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findJarName(java.lang.String,java.lang.String%5B%5D)" class="member-name-link">findJarName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;classpath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">try to guess the name of the given file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIndexJarsMapper()" class="member-name-link">getIndexJarsMapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the mapper used to convert the jars to entries in the index.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)" class="member-name-link">getResourcesToAdd</a><wbr>(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#grabFilesAndDirs(java.lang.String,java.util.List,java.util.List)" class="member-name-link">grabFilesAndDirs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;dirs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;files)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Grab lists of all root-level files and all directories
 contained in the given archive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)" class="member-name-link">initZipOutputStream</a><wbr>(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize the zip output stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reset()" class="member-name-link">reset</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">reset to default values.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFilesetmanifest(org.apache.tools.ant.taskdefs.Jar.FilesetManifestConfig)" class="member-name-link">setFilesetmanifest</a><wbr>(<a href="Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a>&nbsp;config)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Behavior when a Manifest is found in a zipfileset or zipgroupfileset file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFlattenAttributes(boolean)" class="member-name-link">setFlattenAttributes</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to flatten multi-valued attributes (i.e.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIndex(boolean)" class="member-name-link">setIndex</a><wbr>(boolean&nbsp;flag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not to create an index list for classes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIndexMetaInf(boolean)" class="member-name-link">setIndexMetaInf</a><wbr>(boolean&nbsp;flag)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not to add META-INF and its children to the index.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setJarfile(java.io.File)" class="member-name-link">setJarfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManifest(java.io.File)" class="member-name-link">setManifest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;manifestFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The manifest file to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManifestEncoding(java.lang.String)" class="member-name-link">setManifestEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;manifestEncoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The character encoding to use in the manifest file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMergeClassPathAttributes(boolean)" class="member-name-link">setMergeClassPathAttributes</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to merge Class-Path attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStrict(org.apache.tools.ant.taskdefs.Jar.StrictMode)" class="member-name-link">setStrict</a><wbr>(<a href="Jar.StrictMode.html" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a>&nbsp;strict)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Activate the strict mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)" class="member-name-link">setWhenempty</a><wbr>(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Not used for jar files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWhenmanifestonly(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)" class="member-name-link">setWhenmanifestonly</a><wbr>(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates if a jar file should be created when it would only contain a
 manifest file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeIndexLikeList(java.util.List,java.util.List,java.io.PrintWriter)" class="member-name-link">writeIndexLikeList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;dirs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Writes the directory entries from the first and the filenames
 from the second list to the given writer, one entry per line.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)" class="member-name-link">zipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overridden from Zip class to deal with manifests and index lists.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h3>
<code><a href="Zip.html#add(org.apache.tools.ant.types.ResourceCollection)">add</a>, <a href="Zip.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="Zip.html#addParentDirs(java.io.File,java.lang.String,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">addParentDirs</a>, <a href="Zip.html#addResources(org.apache.tools.ant.types.FileSet,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)">addResources</a>, <a href="Zip.html#addResources(org.apache.tools.ant.types.ResourceCollection,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)">addResources</a>, <a href="Zip.html#addZipfileset(org.apache.tools.ant.types.ZipFileSet)">addZipfileset</a>, <a href="Zip.html#addZipGroupFileset(org.apache.tools.ant.types.FileSet)">addZipGroupFileset</a>, <a href="Zip.html#execute()">execute</a>, <a href="Zip.html#executeMain()">executeMain</a>, <a href="Zip.html#getComment()">getComment</a>, <a href="Zip.html#getCreateUnicodeExtraFields()">getCreateUnicodeExtraFields</a>, <a href="Zip.html#getCurrentExtraFields()">getCurrentExtraFields</a>, <a href="Zip.html#getDestFile()">getDestFile</a>, <a href="Zip.html#getEncoding()">getEncoding</a>, <a href="Zip.html#getFallBackToUTF8()">getFallBackToUTF8</a>, <a href="Zip.html#getLevel()">getLevel</a>, <a href="Zip.html#getModificationtime()">getModificationtime</a>, <a href="Zip.html#getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)">getNonFileSetResourcesToAdd</a>, <a href="Zip.html#getPreserve0Permissions()">getPreserve0Permissions</a>, <a href="Zip.html#getResourcesToAdd(org.apache.tools.ant.types.FileSet%5B%5D,java.io.File,boolean)">getResourcesToAdd</a>, <a href="Zip.html#getUseLanguageEnodingFlag()">getUseLanguageEnodingFlag</a>, <a href="Zip.html#getZip64Mode()">getZip64Mode</a>, <a href="Zip.html#grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection%5B%5D)">grabNonFileSetResources</a>, <a href="Zip.html#grabResources(org.apache.tools.ant.types.FileSet%5B%5D)">grabResources</a>, <a href="Zip.html#hasUpdatedFile()">hasUpdatedFile</a>, <a href="Zip.html#isAddingNewFiles()">isAddingNewFiles</a>, <a href="Zip.html#isCompress()">isCompress</a>, <a href="Zip.html#isEmpty(org.apache.tools.ant.types.Resource%5B%5D%5B%5D)">isEmpty</a>, <a href="Zip.html#isFirstPass()">isFirstPass</a>, <a href="Zip.html#isInUpdateMode()">isInUpdateMode</a>, <a href="Zip.html#logWhenWriting(java.lang.String,int)">logWhenWriting</a>, <a href="Zip.html#selectDirectoryResources(org.apache.tools.ant.types.Resource%5B%5D)">selectDirectoryResources</a>, <a href="Zip.html#selectFileResources(org.apache.tools.ant.types.Resource%5B%5D)">selectFileResources</a>, <a href="Zip.html#selectResources(org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.ant.types.resources.selectors.ResourceSelector)">selectResources</a>, <a href="Zip.html#setBasedir(java.io.File)">setBasedir</a>, <a href="Zip.html#setComment(java.lang.String)">setComment</a>, <a href="Zip.html#setCompress(boolean)">setCompress</a>, <a href="Zip.html#setCreateUnicodeExtraFields(org.apache.tools.ant.taskdefs.Zip.UnicodeExtraField)">setCreateUnicodeExtraFields</a>, <a href="Zip.html#setCurrentExtraFields(org.apache.tools.zip.ZipExtraField%5B%5D)">setCurrentExtraFields</a>, <a href="Zip.html#setDestFile(java.io.File)">setDestFile</a>, <a href="Zip.html#setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)">setDuplicate</a>, <a href="Zip.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="Zip.html#setFallBackToUTF8(boolean)">setFallBackToUTF8</a>, <a href="Zip.html#setFile(java.io.File)">setFile</a>, <a href="Zip.html#setFilesonly(boolean)">setFilesonly</a>, <a href="Zip.html#setKeepCompression(boolean)">setKeepCompression</a>, <a href="Zip.html#setLevel(int)">setLevel</a>, <a href="Zip.html#setModificationtime(java.lang.String)">setModificationtime</a>, <a href="Zip.html#setPreserve0Permissions(boolean)">setPreserve0Permissions</a>, <a href="Zip.html#setRoundUp(boolean)">setRoundUp</a>, <a href="Zip.html#setUpdate(boolean)">setUpdate</a>, <a href="Zip.html#setUseLanguageEncodingFlag(boolean)">setUseLanguageEncodingFlag</a>, <a href="Zip.html#setZip64Mode(org.apache.tools.ant.taskdefs.Zip.Zip64ModeAttribute)">setZip64Mode</a>, <a href="Zip.html#setZipfile(java.io.File)">setZipfile</a>, <a href="Zip.html#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">zipDir</a>, <a href="Zip.html#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipDir</a>, <a href="Zip.html#zipDir(org.apache.tools.ant.types.Resource,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipDir</a>, <a href="Zip.html#zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">zipFile</a>, <a href="Zip.html#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipFile</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Jar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Jar</span>()</div>
<div class="block">constructor</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">
<h3>setWhenempty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWhenempty</span><wbr><span class="parameters">(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</span></div>
<div class="block">Not used for jar files.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">setWhenempty</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>we</code> - not used</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWhenmanifestonly(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">
<h3>setWhenmanifestonly</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWhenmanifestonly</span><wbr><span class="parameters">(<a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>&nbsp;we)</span></div>
<div class="block">Indicates if a jar file should be created when it would only contain a
 manifest file.
 Possible values are: <code>fail</code> (throw an exception
 and halt the build); <code>skip</code> (do not create
 any archive, but issue a warning); <code>create</code>
 (make an archive with only a manifest file).
 Default is <code>create</code>;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>we</code> - a <code>WhenEmpty</code> enumerated value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStrict(org.apache.tools.ant.taskdefs.Jar.StrictMode)">
<h3>setStrict</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStrict</span><wbr><span class="parameters">(<a href="Jar.StrictMode.html" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a>&nbsp;strict)</span></div>
<div class="block">Activate the strict mode. When set to <i>true</i> a BuildException
 will be thrown if the Jar-Packaging specification was broken.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>strict</code> - New value of the strict mode.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJarfile(java.io.File)">
<h3>setJarfile</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJarfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use setDestFile(File) instead.</div>
</div>
<div class="block">Set the destination file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jarFile</code> - the destination file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIndex(boolean)">
<h3>setIndex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIndex</span><wbr><span class="parameters">(boolean&nbsp;flag)</span></div>
<div class="block">Set whether or not to create an index list for classes.
 This may speed up classloading in some cases.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - a <code>boolean</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIndexMetaInf(boolean)">
<h3>setIndexMetaInf</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIndexMetaInf</span><wbr><span class="parameters">(boolean&nbsp;flag)</span></div>
<div class="block">Set whether or not to add META-INF and its children to the index.

 <p>Doesn't have any effect if index is false.</p>

 <p>Sun's jar implementation used to skip the META-INF directory
 and Ant followed that example.  The behavior has been changed
 with Java 5.  In order to avoid problems with Ant generated
 jars on Java 1.4 or earlier Ant will not include META-INF
 unless explicitly asked to.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - a <code>boolean</code> value, defaults to false</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://bugs.openjdk.java.net/browse/JDK-4408526">
 jar -i omits service providers in index.list</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setManifestEncoding(java.lang.String)">
<h3>setManifestEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManifestEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;manifestEncoding)</span></div>
<div class="block">The character encoding to use in the manifest file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifestEncoding</code> - the character encoding</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredManifest(org.apache.tools.ant.taskdefs.Manifest)">
<h3>addConfiguredManifest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredManifest</span><wbr><span class="parameters">(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;newManifest)</span>
                           throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Allows the manifest for the archive file to be provided inline
 in the build file rather than in an external file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newManifest</code> - an embedded manifest element</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setManifest(java.io.File)">
<h3>setManifest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManifest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;manifestFile)</span></div>
<div class="block">The manifest file to use. This can be either the location of a manifest,
 or the name of a jar added through a fileset. If its the name of an added
 jar, the task expects the manifest to be in the jar at META-INF/MANIFEST.MF.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifestFile</code> - the manifest file to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFilesetmanifest(org.apache.tools.ant.taskdefs.Jar.FilesetManifestConfig)">
<h3>setFilesetmanifest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFilesetmanifest</span><wbr><span class="parameters">(<a href="Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a>&nbsp;config)</span></div>
<div class="block">Behavior when a Manifest is found in a zipfileset or zipgroupfileset file.
 Valid values are "skip", "merge", and "mergewithoutmain".
 "merge" will merge all of manifests together, and merge this into any
 other specified manifests.
 "mergewithoutmain" merges everything but the Main section of the manifests.
 Default value is "skip".

 Note: if this attribute's value is not "skip", the created jar will not
 be readable by using java.util.jar.JarInputStream</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>config</code> - setting for found manifest behavior.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addMetainf(org.apache.tools.ant.types.ZipFileSet)">
<h3>addMetainf</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addMetainf</span><wbr><span class="parameters">(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;fs)</span></div>
<div class="block">Adds a zipfileset to include in the META-INF directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fs</code> - zipfileset to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredIndexJars(org.apache.tools.ant.types.Path)">
<h3>addConfiguredIndexJars</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredIndexJars</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;p)</span></div>
<div class="block">Add a path to index jars.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - a path</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredIndexJarsMapper(org.apache.tools.ant.types.Mapper)">
<h3>addConfiguredIndexJarsMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredIndexJarsMapper</span><wbr><span class="parameters">(<a href="../types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;mapper)</span></div>
<div class="block">Add a mapper used to convert the jars to entries in the index.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mapper</code> - a mapper</dd>
<dt>Since:</dt>
<dd>Ant 1.10.9</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIndexJarsMapper()">
<h3>getIndexJarsMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></span>&nbsp;<span class="element-name">getIndexJarsMapper</span>()</div>
<div class="block">Returns the mapper used to convert the jars to entries in the index. May be null.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.9</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredService(org.apache.tools.ant.types.spi.Service)">
<h3>addConfiguredService</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredService</span><wbr><span class="parameters">(<a href="../types/spi/Service.html" title="class in org.apache.tools.ant.types.spi">Service</a>&nbsp;service)</span></div>
<div class="block">A nested SPI service element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>service</code> - the nested element.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMergeClassPathAttributes(boolean)">
<h3>setMergeClassPathAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMergeClassPathAttributes</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to merge Class-Path attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFlattenAttributes(boolean)">
<h3>setFlattenAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFlattenAttributes</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to flatten multi-valued attributes (i.e. Class-Path)
 into a single one.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">
<h3>initZipOutputStream</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initZipOutputStream</span><wbr><span class="parameters">(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Initialize the zip output stream.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">initZipOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>zOut</code> - the zip output stream</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O errors</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)">
<h3>finalizeZipOutputStream</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">finalizeZipOutputStream</span><wbr><span class="parameters">(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Finalize the zip output stream.
 This creates an index list if the index attribute is true.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)">finalizeZipOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>zOut</code> - the zip output stream</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O errors</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 long&nbsp;lastModified,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromArchive,
 int&nbsp;mode)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Overridden from Zip class to deal with manifests and index lists.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)">zipFile</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>is</code> - the stream to read data for the entry from.  The
 caller of the method is responsible for closing the stream.</dd>
<dd><code>zOut</code> - the zip output stream</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>lastModified</code> - last modification time for the entry.</dd>
<dd><code>fromArchive</code> - the original archive we are copying this
                    entry from, will be null if we are not copying from an archive.</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[],java.io.File,boolean)">
<h3>getResourcesToAdd</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a></span>&nbsp;<span class="element-name">getResourcesToAdd</span><wbr><span class="parameters">(<a href="../types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>[]&nbsp;rcs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 boolean&nbsp;needsUpdate)</span>
                                      throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)">getResourcesToAdd</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rcs</code> - The resource collections to grab resources from</dd>
<dd><code>zipFile</code> - intended archive file (may or may not exist)</dd>
<dd><code>needsUpdate</code> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 super.getResourcesToAdd.</dd>
<dt>Returns:</dt>
<dd>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if it likes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createEmptyZip(java.io.File)">
<h3>createEmptyZip</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">createEmptyZip</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile)</span>
                          throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create an empty jar file.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#createEmptyZip(java.io.File)">createEmptyZip</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>zipFile</code> - the file to create</dd>
<dt>Returns:</dt>
<dd>true for historic reasons</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanUp()">
<h3>cleanUp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanUp</span>()</div>
<div class="block">Make sure we don't think we already have a MANIFEST next time this task
 gets executed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#cleanUp()">cleanUp</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Zip.html#cleanUp()"><code>Zip.cleanUp()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reset()">
<h3>reset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">reset</span>()</div>
<div class="block">reset to default values.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#reset()">reset</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Since:</dt>
<dd>1.44, Ant 1.5</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Zip.html#reset()"><code>Zip.reset()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeIndexLikeList(java.util.List,java.util.List,java.io.PrintWriter)">
<h3>writeIndexLikeList</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeIndexLikeList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;dirs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer)</span></div>
<div class="block">Writes the directory entries from the first and the filenames
 from the second list to the given writer, one entry per line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dirs</code> - a list of directories</dd>
<dd><code>files</code> - a list of files</dd>
<dd><code>writer</code> - the writer to write to</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findJarName(java.lang.String,java.lang.String[])">
<h3>findJarName</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">findJarName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;classpath)</span></div>
<div class="block">try to guess the name of the given file.

 <p>If this jar has a classpath attribute in its manifest, we
 can assume that it will only require an index of jars listed
 there.  try to find which classpath entry is most likely the
 one the given file name points to.</p>

 <p>In the absence of a classpath attribute, assume the other
 files will be placed inside the same directory as this jar and
 use their basename.</p>

 <p>if there is a classpath and the given file doesn't match any
 of its entries, return null.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileName</code> - the name to look for</dd>
<dd><code>classpath</code> - the classpath to look in (may be null)</dd>
<dt>Returns:</dt>
<dd>the matching entry, or null if the file is not found</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="grabFilesAndDirs(java.lang.String,java.util.List,java.util.List)">
<h3>grabFilesAndDirs</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">grabFilesAndDirs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;dirs,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;files)</span>
                                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Grab lists of all root-level files and all directories
 contained in the given archive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the zip file to examine</dd>
<dd><code>dirs</code> - where to place the directories found</dd>
<dd><code>files</code> - where to place the files found</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
