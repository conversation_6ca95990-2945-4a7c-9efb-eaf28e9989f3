<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>LineContains (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters, class: LineContains">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.filters</a></div>
<h1 title="Class LineContains" class="title">Class LineContains</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">java.io.Reader</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">java.io.FilterReader</a>
<div class="inheritance"><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseFilterReader</a>
<div class="inheritance"><a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseParamFilterReader</a>
<div class="inheritance">org.apache.tools.ant.filters.LineContains</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Readable.html" title="class or interface in java.lang" class="external-link">Readable</a></code>, <code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code>, <code><a href="../types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">LineContains</span>
<span class="extends-implements">extends <a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a>
implements <a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></span></div>
<div class="block">Filter which includes only those lines that contain the user-specified
 strings.

 Example:

 <pre>&lt;linecontains&gt;
   &lt;contains value=&quot;foo&quot;&gt;
   &lt;contains value=&quot;bar&quot;&gt;
 &lt;/linecontains&gt;</pre>

 Or:

 <pre>&lt;filterreader classname=&quot;org.apache.tools.ant.filters.LineContains&quot;&gt;
    &lt;param type=&quot;contains&quot; value=&quot;foo&quot;/&gt;
    &lt;param type=&quot;contains&quot; value=&quot;bar&quot;/&gt;
 &lt;/filterreader&gt;</pre>

 This will include only those lines that contain <code>foo</code> and
 <code>bar</code>.

 Starting Ant 1.10.4, the <code>matchAny</code> attribute can be used to control whether any one
 of the user-specified strings is expected to be contained in the line or all
 of them are expected to be contained.

 For example:

 <pre>&lt;linecontains matchAny=&quot;true&quot;&gt;
  *   &lt;contains value=&quot;foo&quot;&gt;
  *   &lt;contains value=&quot;bar&quot;&gt;
  * &lt;/linecontains&gt;</pre>

 This will include only those lines that contain either <code>foo</code> or <code>bar</code>.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="LineContains.Contains.html" class="type-name-link" title="class in org.apache.tools.ant.filters">LineContains.Contains</a></code></div>
<div class="col-last even-row-color">
<div class="block">Holds a contains element</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterReader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#in" title="class or interface in java.io" class="external-link">in</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.Reader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#lock" title="class or interface in java.io" class="external-link">lock</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">LineContains</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for "dummy" instances.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.Reader)" class="member-name-link">LineContains</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new filtered reader.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredContains(org.apache.tools.ant.filters.LineContains.Contains)" class="member-name-link">addConfiguredContains</a><wbr>(<a href="LineContains.Contains.html" title="class in org.apache.tools.ant.filters">LineContains.Contains</a>&nbsp;contains)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a <code>contains</code> element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new LineContains using the passed in
 Reader for instantiation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isMatchAny()" class="member-name-link">isMatchAny</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isNegated()" class="member-name-link">isNegated</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find out whether we have been negated.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read()" class="member-name-link">read</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the next character in the filtered stream, only including
 lines from the original stream which contain all of the specified words.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMatchAny(boolean)" class="member-name-link">setMatchAny</a><wbr>(boolean&nbsp;matchAny)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNegate(boolean)" class="member-name-link">setNegate</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the negation mode.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseParamFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</a></h3>
<code><a href="BaseParamFilterReader.html#getParameters()">getParameters</a>, <a href="BaseParamFilterReader.html#setParameters(org.apache.tools.ant.types.Parameter...)">setParameters</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></h3>
<code><a href="BaseFilterReader.html#getInitialized()">getInitialized</a>, <a href="BaseFilterReader.html#getProject()">getProject</a>, <a href="BaseFilterReader.html#read(char%5B%5D,int,int)">read</a>, <a href="BaseFilterReader.html#readFully()">readFully</a>, <a href="BaseFilterReader.html#readLine()">readLine</a>, <a href="BaseFilterReader.html#setInitialized(boolean)">setInitialized</a>, <a href="BaseFilterReader.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="BaseFilterReader.html#skip(long)">skip</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterReader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#close()" title="class or interface in java.io" class="external-link">close</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#mark(int)" title="class or interface in java.io" class="external-link">mark</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#markSupported()" title="class or interface in java.io" class="external-link">markSupported</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#ready()" title="class or interface in java.io" class="external-link">ready</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#reset()" title="class or interface in java.io" class="external-link">reset</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.Reader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#nullReader()" title="class or interface in java.io" class="external-link">nullReader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(char%5B%5D)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(java.nio.CharBuffer)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#transferTo(java.io.Writer)" title="class or interface in java.io" class="external-link">transferTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>LineContains</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LineContains</span>()</div>
<div class="block">Constructor for "dummy" instances.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BaseFilterReader.html#%3Cinit%3E()"><code>BaseFilterReader()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.Reader)">
<h3>LineContains</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">LineContains</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</span></div>
<div class="block">Creates a new filtered reader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - A Reader object providing the underlying stream.
           Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="read()">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span>()
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns the next character in the filtered stream, only including
 lines from the original stream which contain all of the specified words.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#read()" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></code></dd>
<dt>Returns:</dt>
<dd>the next character in the resulting stream, or -1
 if the end of the resulting stream has been reached</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the underlying stream throws an IOException
 during reading</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredContains(org.apache.tools.ant.filters.LineContains.Contains)">
<h3>addConfiguredContains</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredContains</span><wbr><span class="parameters">(<a href="LineContains.Contains.html" title="class in org.apache.tools.ant.filters">LineContains.Contains</a>&nbsp;contains)</span></div>
<div class="block">Adds a <code>contains</code> element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>contains</code> - The <code>contains</code> element to add.
                 Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNegate(boolean)">
<h3>setNegate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNegate</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Set the negation mode.  Default false (no negation).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - the boolean negation mode to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNegated()">
<h3>isNegated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNegated</span>()</div>
<div class="block">Find out whether we have been negated.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean negation flag.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMatchAny(boolean)">
<h3>setMatchAny</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMatchAny</span><wbr><span class="parameters">(boolean&nbsp;matchAny)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>matchAny</code> - True if this <a href="LineContains.html" title="class in org.apache.tools.ant.filters"><code>LineContains</code></a> is considered a match,
                 if <code>any</code> of the <code>contains</code> value match. False
                 if <code>all</code> of the <code>contains</code> value are expected
                 to match</dd>
<dt>Since:</dt>
<dd>Ant 1.10.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isMatchAny()">
<h3>isMatchAny</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isMatchAny</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns true if this <a href="LineContains.html" title="class in org.apache.tools.ant.filters"><code>LineContains</code></a> is considered a match,
 if <code>any</code> of the <code>contains</code> value match. False
 if <code>all</code> of the <code>contains</code> value are expected
 to match</dd>
<dt>Since:</dt>
<dd>Ant 1.10.4</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</span></div>
<div class="block">Creates a new LineContains using the passed in
 Reader for instantiation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ChainableReader.html#chain(java.io.Reader)">chain</a></code>&nbsp;in interface&nbsp;<code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rdr</code> - A Reader object providing the underlying stream.
            Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a new filter based on this configuration, but filtering
         the specified reader</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
