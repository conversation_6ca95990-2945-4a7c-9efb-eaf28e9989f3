<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.types.resources.comparators (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources.comparators">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.types.resources.comparators" class="title">Package org.apache.tools.ant.types.resources.comparators</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.types.resources.comparators</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.types.resources</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../selectors/package-summary.html">org.apache.tools.ant.types.resources.selectors</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Content.html" title="class in org.apache.tools.ant.types.resources.comparators">Content</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by content.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Date.html" title="class in org.apache.tools.ant.types.resources.comparators">Date</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by last modification date.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DelegatedResourceComparator.html" title="class in org.apache.tools.ant.types.resources.comparators">DelegatedResourceComparator</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Delegates to other ResourceComparators or, if none specified,
 uses Resources' natural ordering.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Exists.html" title="class in org.apache.tools.ant.types.resources.comparators">Exists</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by existence.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="FileSystem.html" title="class in org.apache.tools.ant.types.resources.comparators">FileSystem</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compares filesystem Resources.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Name.html" title="class in org.apache.tools.ant.types.resources.comparators">Name</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by name.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ResourceComparator.html" title="class in org.apache.tools.ant.types.resources.comparators">ResourceComparator</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Abstract Resource Comparator.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Reverse.html" title="class in org.apache.tools.ant.types.resources.comparators">Reverse</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Reverses another ResourceComparator.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Size.html" title="class in org.apache.tools.ant.types.resources.comparators">Size</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by size.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Type.html" title="class in org.apache.tools.ant.types.resources.comparators">Type</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compares Resources by is-directory status.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
