<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JUnitLauncherTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junitlauncher.confined, class: JUnitLauncherTask">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined</a></div>
<h1 title="Class JUnitLauncherTask" class="title">Class JUnitLauncherTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.JUnitLauncherTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JUnitLauncherTask</span>
<span class="extends-implements">extends <a href="../../../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">An Ant <a href="../../../../Task.html" title="class in org.apache.tools.ant"><code>Task</code></a> responsible for launching the JUnit platform for running tests.
 This requires a minimum of JUnit 5, since that's the version in which the JUnit platform launcher
 APIs were introduced.
 <p>
 This task in itself doesn't run the JUnit tests, instead the sole responsibility of
 this task is to setup the JUnit platform launcher, build requests, launch those requests and then parse the
 result of the execution to present in a way that's been configured on this Ant task.
 </p>
 <p>
 Furthermore, this task allows users control over which classes to select for passing on to the JUnit 5
 platform for test execution. It however, is solely the JUnit 5 platform, backed by test engines that
 decide and execute the tests.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://junit.org/junit5/">JUnit 5 documentation</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../../Task.html#target">target</a>, <a href="../../../../Task.html#taskName">taskName</a>, <a href="../../../../Task.html#taskType">taskType</a>, <a href="../../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../../ProjectComponent.html#description">description</a>, <a href="../../../../ProjectComponent.html#location">location</a>, <a href="../../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JUnitLauncherTask</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredClassPath(org.apache.tools.ant.types.Path)" class="member-name-link">addConfiguredClassPath</a><wbr>(<a href="../../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the <a href="../../../../types/Path.html" title="class in org.apache.tools.ant.types"><code>Path</code></a> to the classpath which will be used for execution of the tests</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredListener(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.ListenerDefinition)" class="member-name-link">addConfiguredListener</a><wbr>(<a href="ListenerDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition</a>&nbsp;listener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a <a href="ListenerDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>listener</code></a> which will be enrolled for listening to test
 execution events</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredTest(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.SingleTestClass)" class="member-name-link">addConfiguredTest</a><wbr>(<a href="SingleTestClass.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">SingleTestClass</a>&nbsp;test)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a <a href="SingleTestClass.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>SingleTestClass</code></a> that will be passed on to the underlying JUnit platform
 for possible execution of the test</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredTestClasses(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestClasses)" class="member-name-link">addConfiguredTestClasses</a><wbr>(<a href="TestClasses.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestClasses</a>&nbsp;testClasses)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds <a href="TestClasses.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>TestClasses</code></a> that will be passed on to the underlying JUnit platform for
 possible execution of the tests</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExecuteWatchdog(long)" class="member-name-link">createExecuteWatchdog</a><wbr>(long&nbsp;timeout)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called by the project to let the task do its work.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludeTags(java.lang.String)" class="member-name-link">setExcludeTags</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tags to exclude.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailureProperty(java.lang.String)" class="member-name-link">setFailureProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureProperty)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHaltonfailure(boolean)" class="member-name-link">setHaltonfailure</a><wbr>(boolean&nbsp;haltonfailure)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeTags(java.lang.String)" class="member-name-link">setIncludeTags</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tags to include.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrintSummary(boolean)" class="member-name-link">setPrintSummary</a><wbr>(boolean&nbsp;printSummary)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../../Task.html#init()">init</a>, <a href="../../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../../Task.html#perform()">perform</a>, <a href="../../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JUnitLauncherTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitLauncherTask</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../../Task.html#execute()">Task</a></code></span></div>
<div class="block">Called by the project to let the task do its work. This method may be
 called more than once, if the task is invoked more than once.
 For example,
 if target1 and target2 both depend on target3, then running
 "ant target1 target2" will run all tasks in target3 twice.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong with the build.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredClassPath(org.apache.tools.ant.types.Path)">
<h3>addConfiguredClassPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredClassPath</span><wbr><span class="parameters">(<a href="../../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Adds the <a href="../../../../types/Path.html" title="class in org.apache.tools.ant.types"><code>Path</code></a> to the classpath which will be used for execution of the tests</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - The classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredTest(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.SingleTestClass)">
<h3>addConfiguredTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredTest</span><wbr><span class="parameters">(<a href="SingleTestClass.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">SingleTestClass</a>&nbsp;test)</span></div>
<div class="block">Adds a <a href="SingleTestClass.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>SingleTestClass</code></a> that will be passed on to the underlying JUnit platform
 for possible execution of the test</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - The test</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredTestClasses(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.TestClasses)">
<h3>addConfiguredTestClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredTestClasses</span><wbr><span class="parameters">(<a href="TestClasses.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">TestClasses</a>&nbsp;testClasses)</span></div>
<div class="block">Adds <a href="TestClasses.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>TestClasses</code></a> that will be passed on to the underlying JUnit platform for
 possible execution of the tests</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>testClasses</code> - The test classes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredListener(org.apache.tools.ant.taskdefs.optional.junitlauncher.confined.ListenerDefinition)">
<h3>addConfiguredListener</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredListener</span><wbr><span class="parameters">(<a href="ListenerDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined">ListenerDefinition</a>&nbsp;listener)</span></div>
<div class="block">Adds a <a href="ListenerDefinition.html" title="class in org.apache.tools.ant.taskdefs.optional.junitlauncher.confined"><code>listener</code></a> which will be enrolled for listening to test
 execution events</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>listener</code> - The listener</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHaltonfailure(boolean)">
<h3>setHaltonfailure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHaltonfailure</span><wbr><span class="parameters">(boolean&nbsp;haltonfailure)</span></div>
</section>
</li>
<li>
<section class="detail" id="setFailureProperty(java.lang.String)">
<h3>setFailureProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailureProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureProperty)</span></div>
</section>
</li>
<li>
<section class="detail" id="setPrintSummary(boolean)">
<h3>setPrintSummary</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrintSummary</span><wbr><span class="parameters">(boolean&nbsp;printSummary)</span></div>
</section>
</li>
<li>
<section class="detail" id="setIncludeTags(java.lang.String)">
<h3>setIncludeTags</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeTags</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</span></div>
<div class="block">Tags to include. Will trim each tag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includes</code> - comma separated list of tags to include while running the tests.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludeTags(java.lang.String)">
<h3>setExcludeTags</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludeTags</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</span></div>
<div class="block">Tags to exclude. Will trim each tag.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - comma separated list of tags to exclude while running the tests.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createExecuteWatchdog(long)">
<h3>createExecuteWatchdog</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></span>&nbsp;<span class="element-name">createExecuteWatchdog</span><wbr><span class="parameters">(long&nbsp;timeout)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
