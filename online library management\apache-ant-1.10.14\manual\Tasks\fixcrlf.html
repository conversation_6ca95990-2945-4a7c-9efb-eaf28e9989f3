<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>FixCRLF Task</title>
</head>

<body>

<h2 id="fixcrlf">FixCRLF</h2>
<h3>Description</h3>
  <p>Adjusts a text file to local conventions.</p>

  <p>The set of files to be adjusted can be refined with
    the <var>includes</var>, <var>includesfile</var>, <var>excludes</var>, <var>excludesfile</var>
    and <var>defaultexcludes</var> attributes. Patterns provided through the <var>includes</var>
    or <var>includesfile</var> attributes specify files to be included. Patterns provided through
    the <var>exclude</var> or <var>excludesfile</var> attribute specify files to be
    excluded. Additionally, default exclusions can be specified with the <var>defaultexcludes</var>
    attribute. See the section on <a href="../dirtasks.html#directorybasedtasks">directory-based
    tasks</a>, for details of file inclusion/exclusion patterns and their usage.</p>

  <p>This task forms an implicit <a href="../Types/fileset.html">FileSet</a> and supports most
    attributes of <code>&lt;fileset&gt;</code> (<var>dir</var> becomes <var>srcdir</var>) as well as
    the nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>
    and <code>&lt;patternset&gt;</code> elements.</p>

  <p>
    The output file is only written if it is a new file, or if it
    differs from the existing file.  This prevents spurious
    rebuilds based on unchanged files which have been regenerated
    by this task. In order to assess whether a file has changed, this
    task will create a pre-processed version of the source file inside of
    the <a href="../running.html#tmpdir">temporary directory</a>.
  </p>

  <p><em>Since Apache Ant 1.7</em>, this task can be used in
    a <a href="../Types/filterchain.html">filterchain</a>.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col" rowspan="2">Attribute</th>
    <th scope="col" rowspan="2">Description</th>
    <th scope="col" colspan="2">Required</th>
  </tr>
  <tr>
    <th scope="col">As Task</th>
    <th scope="col">As Filter</th>
  </tr>
  <tr>
    <td>srcDir</td>
    <td>Where to find the files to be fixed up.</td>
    <td rowspan="2" class="center">One of these</td>
    <td rowspan="11">N/A</td>
  </tr>
  <tr>
    <td>file</td>
    <td class="left">Name of a single file to fix. <em>Since Ant 1.7</em></td>
  </tr>
  <tr>
    <td>destDir</td>
    <td>Where to place the corrected files.</td>
    <td class="center">No; defaults to <var>srcDir</var> (replace the original files)</td>
  </tr>
  <tr>
    <td>includes</td>
    <td>comma- or space-separated list of patterns of files that must be included.</td>
    <td class="center">No; defaults to all (<q>**</q>)</td>
  </tr>
  <tr>
    <td>includesfile</td>
    <td>name of a file. Each line of this file is taken to be an include pattern.</td>
    <td class="center">No</td>
  </tr>
  <tr>
    <td>excludes</td>
    <td>comma- or space-separated list of patterns of files that must be excluded.</td>
    <td class="center">No; defaults to default excludes or none if <var>defaultexcludes</var>
      is <q>no</q></td>
  </tr>
  <tr>
    <td>excludesfile</td>
    <td>name of a file. Each line of this file is taken to be an exclude pattern.</td>
    <td class="center">No</td>
  </tr>
  <tr>
    <td>defaultexcludes</td>
    <td>indicates whether default excludes should be used or not (<q>yes|no</q>).</td>
    <td class="center">No; defaults to <q>yes</q></td>
  </tr>
  <tr>
    <td>encoding</td>
    <td>The encoding of the files.</td>
    <td class="center">No; defaults to default JVM character encoding</td>
  </tr>
  <tr>
    <td>outputencoding</td>
    <td>The encoding to use when writing the files.  <em>Since Ant 1.7</em></td>
    <td class="center">No; defaults to <var>encoding</var> if set or default JVM character encoding
      otherwise</td>
  </tr>
  <tr>
    <td>preservelastmodified</td>
    <td>Whether to preserve the last modified date of source files. <em>Since Ant 1.6.3</em></td>
    <td class="center">No; default is <q>false</q></td>
  </tr>
  <tr>
    <td>eol</td>
    <td>Specifies how end-of-line (EOL) characters are to be handled.  The EOL characters are CR, LF
      and the pair CRLF.  Valid values for this property are:
      <ul>
        <li><q>asis</q>: leave EOL characters alone</li>
        <li><q>cr</q>: convert all EOLs to a single CR</li>
        <li><q>lf</q>: convert all EOLs to a single LF</li>
        <li><q>crlf</q>: convert all EOLs to the pair CRLF</li>
        <li><q>mac</q>: convert all EOLs to a single CR</li>
        <li><q>unix</q>: convert all EOLs to a single LF</li>
        <li><q>dos</q>: convert all EOLs to the pair CRLF</li>
      </ul>
      <p>This is the preferred method for specifying EOL.  The <var>cr</var> attribute (see below)
      is now deprecated.</p>
      <p><strong>Note</strong>: One special case is recognized. The three characters CR-CR-LF are
        regarded as a single EOL.  Unless this property is specified as <q>asis</q>, this sequence
        will be converted into the specified EOL type.</p>
    </td>
    <td colspan="2">No; default is platform-specific: <q>lf</q> for Unix platforms (including Mac OS
    X/macOS), <q>crlf</q> for DOS-based systems (including Windows), <q>cr</q> for Mac environments
    other than OS X</td>
  </tr>
  <tr>
    <td>cr</td>
    <td><em><u>Deprecated</u></em>. Specifies how CR characters are to be handled at end-of-line
      (EOL).  Valid values for this property are:
      <ul>
        <li><q>asis</q>: leave EOL characters alone.</li>
        <li><q>add</q>: add a CR before any single LF characters. The intent is to convert all EOLs
          to the pair CRLF.</li>
        <li><q>remove</q>: remove all CRs from the file.  The intent is to convert all EOLs to a
          single LF.</li>
      </ul>
      <p><strong>Note</strong>: One special case is recognized. The three characters CR-CR-LF are
        regarded as a single EOL.  Unless this property is specified as <q>asis</q>, this sequence
        will be converted into the specified EOL type.</p>
    </td>
    <td colspan="2">No; default is platform-specific: <q>remove</q> for Unix platforms, <q>add</q>
      for DOS based systems (including Windows)</td>
  </tr>
  <tr>
    <td>javafiles</td>
    <td>Used only in association with the <var>tab</var> attribute (see below), this boolean
      attribute indicates whether the fileset is a set of Java source files (<q>yes|no</q>). See
      notes in section on <var>tab</var>.</td>
    <td colspan="2">No; defaults to <q>no</q></td>
  </tr>
  <tr>
    <td>tab</td>
    <td>Specifies how tab characters are to be handled.  Valid values for this property are:
      <ul>
        <li><q>add</q>: convert sequences of spaces which span a tab stop to tabs</li>
        <li><q>asis</q>: leave tab and space characters alone</li>
        <li><q>remove</q>: convert tabs to spaces</li>
      </ul>
      <p><strong>Note</strong>: When the attribute <var>javafiles</var> (see above) is <q>true</q>,
        literal TAB characters occurring within Java string or character constants are never
        modified.  This functionality also requires the recognition of Java-style comments.</p>
      <p><strong>Note</strong>: There is an incompatibility between this and the previous version in
        the handling of white space at the end of lines.  This version
        does <em><strong>not</strong></em> remove trailing whitespace on lines.</p>
    </td>
    <td colspan="2">No; default is <q>asis</q></td>
  </tr>
  <tr>
    <td>tablength</td>
    <td>TAB character interval. Valid values are between <q>2</q> and <q>80</q> inclusive.</td>
    <td colspan="2">No; default is <q>8</q></td>
  </tr>
  <tr>
    <td>eof</td>
    <td>Specifies how DOS end of file (control-Z) characters are to be handled.  Valid values for
      this property are:
      <ul>
        <li><q>add</q>: ensure that there is an EOF character at the end of the file</li>
        <li><q>asis</q>: leave EOF characters alone</li>
        <li><q>remove</q>: remove any EOF character found at the end</li>
      </ul>
    </td>
    <td colspan="2">No; default is platform-specific: <q>remove</q> for Unix platforms, <q>asis</q>
      for DOS based systems (including Windows)</td>
  </tr>
  <tr>
    <td>fixlast</td>
    <td>Whether to add a missing EOL to the last line of a processed file.<br/>Ignored
      if <var>eof</var> is <q>asis</q>.<br/><em>Since Ant 1.6.1</em></td>
    <td colspan="2">No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>
<p>Replace EOLs with LF characters and remove EOF characters from the shell scripts. Tabs and spaces
are left as is.</p>
<pre>
&lt;fixcrlf srcdir=&quot;${src}&quot; includes=&quot;**/*.sh&quot;
         eol=&quot;lf&quot; eof=&quot;remove&quot;/&gt;</pre>

<p>Replace all EOLs with cr-lf pairs in the batch files. Tabs and spaces are left as is. EOF
characters are left alone if run on DOS systems, and are removed if run on Unix systems.</p>
<pre>
&lt;fixcrlf srcdir=&quot;${src}&quot;
         includes=&quot;**/*.bat&quot; eol=&quot;crlf&quot;/&gt;</pre>

<p>Set EOLs according to local OS conventions, and convert sequences of spaces and tabs into the
minimal set of spaces and tabs which will preserve spacing within the line. Tabs are set at 8
character intervals. EOF characters are left alone if run on DOS systems, and are removed if run on
Unix systems. Many versions of make require tabs prior to commands.</p>
<pre>
&lt;fixcrlf srcdir=&quot;${src}&quot;
         includes=&quot;**/Makefile&quot; tab=&quot;add&quot;/&gt;</pre>

<p>Convert all EOLs in the included Java source files to a single LF. Replace all tab characters
except those in string or character constants with spaces, assuming a tab width of 3. If run on a
Unix system, any CTRL-Z EOF characters at the end of the file are removed. On DOS/Windows, any such
EOF characters will be left untouched.
</p>
<pre>
&lt;fixcrlf srcdir=&quot;${src}&quot; includes=&quot;**/*.java&quot;
         tab=&quot;remove&quot; tablength=&quot;3&quot;
         eol=&quot;lf&quot; javafiles=&quot;yes&quot;/&gt;</pre>

<p>Set EOLs according to local OS conventions, and convert all tabs to spaces, assuming a tab width
of 8. EOF characters are left alone if run on DOS systems, and are removed if run on Unix
systems. You never know what editor a user will use to browse READMEs.</p>
<pre>
&lt;fixcrlf srcdir=&quot;${src}&quot;
         includes=&quot;**/README*&quot; tab=&quot;remove&quot;/&gt;</pre>

</body>
</html>
