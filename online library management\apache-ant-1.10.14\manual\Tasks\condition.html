<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Condition Task</title>
</head>

<body>

<h2 id="condition">Condition</h2>
<h3>Description</h3>
<p>Sets a property if a certain condition holds true&mdash;this is a generalization
of <a href="available.html">Available</a> and <a href="uptodate.html">Uptodate</a>.</p>
<p>If the condition holds true, the property value is set to <q>true</q> by default; otherwise, the
property is not set. You can set the value to something other than the default by specifying
the <var>value</var> attribute.</p>
<p>Conditions are specified as <a href="#nested">nested elements</a>, you must specify exactly one
condition.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The name of the property to set.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The value to set the property to.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>else</td>
    <td>The value to set the property to if the condition evaluates to <q>false</q>. <em>Since
      Apache Ant 1.6.3</em>
    </td>
    <td>No; by default the property will remain unset</td>
  </tr>
</table>
<h3 id="nested">Parameters specified as nested elements</h3>
<p>All conditions to test are specified as nested elements, for a complete list
see <a href="conditions.html">here</a>.</p>

<h3>Examples</h3>

<p>Set the property <code>javamail.complete</code> if both the JavaBeans Activation Framework and
JavaMail are available in the classpath.</p>
<pre>
&lt;condition property=&quot;javamail.complete&quot;&gt;
  &lt;and&gt;
    &lt;available classname=&quot;javax.activation.DataHandler&quot;/&gt;
    &lt;available classname=&quot;javax.mail.Transport&quot;/&gt;
  &lt;/and&gt;
&lt;/condition&gt;</pre>

<p>Set the property <code>isMacOsButNotMacOsX</code> if the current operating system is MacOS, but
not MacOS X/macOS&mdash;which Ant considers to be in the Unix family as well.</p>
<pre>
&lt;condition property=&quot;isMacOsButNotMacOsX&quot;&gt;
  &lt;and&gt;
    &lt;os family=&quot;mac&quot;/&gt;
    &lt;not&gt;
      &lt;os family=&quot;unix&quot;/&gt;
    &lt;/not&gt;
  &lt;/and&gt;
&lt;/condition&gt;</pre>

<p>Set the property <code>isSunOSonSparc</code> if the current operating system is SunOS and if it
is running on a SPARC architecture.</p>
<pre>
&lt;condition property=&quot;isSunOSonSparc&quot;&gt;
  &lt;os name=&quot;SunOS&quot; arch=&quot;sparc&quot;/&gt;
&lt;/condition&gt;</pre>

</body>
</html>
