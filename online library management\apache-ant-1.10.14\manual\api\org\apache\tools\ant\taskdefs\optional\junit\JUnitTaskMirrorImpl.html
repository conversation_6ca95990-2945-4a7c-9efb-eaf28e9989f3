<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JUnitTaskMirrorImpl (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: JUnitTaskMirrorImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class JUnitTaskMirrorImpl" class="title">Class JUnitTaskMirrorImpl</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirrorImpl</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">JUnitTaskMirrorImpl</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></span></div>
<div class="block">Implementation of the part of the junit task which can directly refer to junit.* classes.
 Public only to permit use of reflection; do not use directly.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit"><code>JUnitTaskMirror</code></a></li>
<li>"bug #38799"</li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror">Nested classes/interfaces inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.junit.<a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></h2>
<code><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>, <a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a>, <a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask)" class="member-name-link">JUnitTaskMirrorImpl</a><wbr>(<a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a>&nbsp;task)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVmExit(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror,java.io.OutputStream,java.lang.String,java.lang.String)" class="member-name-link">addVmExit</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;aFormatter,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;testCase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the formatter to be called when the jvm exits before
 the test suite finishes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newJUnitTestRunner(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String%5B%5D,boolean,boolean,boolean,boolean,boolean,org.apache.tools.ant.AntClassLoader)" class="member-name-link">newJUnitTestRunner</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filterTrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="../../../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a>&nbsp;classLoader)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new test runner for a test.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#newSummaryJUnitResultFormatter()" class="member-name-link">newSummaryJUnitResultFormatter</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a summary result formatter.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask)">
<h3>JUnitTaskMirrorImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JUnitTaskMirrorImpl</span><wbr><span class="parameters">(<a href="JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</a>&nbsp;task)</span></div>
<div class="block">Constructor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - the junittask that uses this mirror.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addVmExit(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror,java.io.OutputStream,java.lang.String,java.lang.String)">
<h3>addVmExit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVmExit</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a>&nbsp;aFormatter,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;testCase)</span></div>
<div class="block">Add the formatter to be called when the jvm exits before
 the test suite finishes..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.html#addVmExit(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitResultFormatterMirror,java.io.OutputStream,java.lang.String,java.lang.String)">addVmExit</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - the test.</dd>
<dd><code>aFormatter</code> - the formatter to use.</dd>
<dd><code>out</code> - the output stream to use.</dd>
<dd><code>message</code> - the message to write out.</dd>
<dd><code>testCase</code> - the name of the test.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newJUnitTestRunner(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String[],boolean,boolean,boolean,boolean,boolean,org.apache.tools.ant.AntClassLoader)">
<h3>newJUnitTestRunner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</a></span>&nbsp;<span class="element-name">newJUnitTestRunner</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;methods,
 boolean&nbsp;haltOnError,
 boolean&nbsp;filterTrace,
 boolean&nbsp;haltOnFailure,
 boolean&nbsp;showOutput,
 boolean&nbsp;logTestListenerEvents,
 <a href="../../../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a>&nbsp;classLoader)</span></div>
<div class="block">Create a new test runner for a test..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.html#newJUnitTestRunner(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest,java.lang.String%5B%5D,boolean,boolean,boolean,boolean,boolean,org.apache.tools.ant.AntClassLoader)">newJUnitTestRunner</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - the test to run.</dd>
<dd><code>methods</code> - names of the test methods to be run.</dd>
<dd><code>haltOnError</code> - if true halt the tests if an error occurs.</dd>
<dd><code>filterTrace</code> - if true filter the stack traces.</dd>
<dd><code>haltOnFailure</code> - if true halt the test if a failure occurs.</dd>
<dd><code>showOutput</code> - if true show output.</dd>
<dd><code>logTestListenerEvents</code> - if true log test listener events.</dd>
<dd><code>classLoader</code> - the classloader to use to create the runner.</dd>
<dt>Returns:</dt>
<dd>the test runner.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newSummaryJUnitResultFormatter()">
<h3>newSummaryJUnitResultFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></span>&nbsp;<span class="element-name">newSummaryJUnitResultFormatter</span>()</div>
<div class="block">Create a summary result formatter..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.html#newSummaryJUnitResultFormatter()">newSummaryJUnitResultFormatter</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror</a></code></dd>
<dt>Returns:</dt>
<dd>the created formatter.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
