<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JavaCC Task</title>
</head>

<body>

<h2 id="javacc">JavaCC</h2>
<h3>Description</h3>
<p>Invokes the <a href="https://javacc.org/" target="_top">JavaCC</a> compiler compiler on a grammar
file.</p>
<p>To use the <code>javacc</code> task, set the <var>target</var> attribute to the name of the
grammar file to process.  You also need to specify the directory containing the JavaCC installation
using the <var>javacchome</var> attribute, so that Apache Ant can find the JavaCC classes.
Optionally, you can also set the <var>outputdirectory</var> to write the generated file to a
specific directory.  Otherwise JavaCC writes the generated files to the directory containing the
grammar file.</p>
<p>This task only invokes JavaCC if the grammar file is newer than the generated <samp>.java</samp>
files.  JavaCC assumes that the Java class name of the generated parser is the same as the name of
the grammar file, ignoring the <samp>.jj</samp>.  If this is not the case, the <code>javacc</code>
task will still work, but it will always generate the output files.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>target</td>
    <td>The grammar file to process.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>javacchome</td>
    <td>The directory containing the JavaCC distribution.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>outputdirectory</td>
    <td>The directory to write the generated files to.  If not set, the files are written to the
      directory containing the grammar file.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>buildparser</td>
    <td>Sets the BUILD_PARSER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>buildtokenmanager</td>
    <td>Sets the BUILD_TOKEN_MANAGER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>cachetokens</td>
    <td>Sets the CACHE_TOKENS grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>choiceambiguitycheck</td>
    <td>Sets the CHOICE_AMBIGUITY_CHECK grammar option.  This is an integer option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>commontokenaction</td>
    <td>Sets the COMMON_TOKEN_ACTION grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>debuglookahead</td>
    <td>Sets the DEBUG_LOOKAHEAD grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>debugparser</td>
    <td>Sets the DEBUG_PARSER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>debugtokenmanager</td>
    <td>Sets the DEBUG_TOKEN_MANAGER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>errorreporting</td>
    <td>Sets the ERROR_REPORTING grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>forcelacheck</td>
    <td>Sets the FORCE_LA_CHECK grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ignorecase</td>
    <td>Sets the IGNORE_CASE grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>javaunicodeescape</td>
    <td>Sets the JAVA_UNICODE_ESCAPE grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>jdkversion</td>
    <td>Sets the JDK_VERSION option. This is a string option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keeplinecolumn</td>
    <td>Sets the KEEP_LINE_COLUMN grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>lookahead</td>
    <td>Sets the LOOKAHEAD grammar option.  This is an integer option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>optimizetokenmanager</td>
    <td>Sets the OPTIMIZE_TOKEN_MANAGER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>otherambiguitycheck</td>
    <td>Sets the OTHER_AMBIGUITY_CHECK grammar option.  This is an integer option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>sanitycheck</td>
    <td>Sets the SANITY_CHECK grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>static</td>
    <td>Sets the STATIC grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>unicodeinput</td>
    <td>Sets the UNICODE_INPUT grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>usercharstream</td>
    <td>Sets the USER_CHAR_STREAM grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>usertokenmanager</td>
    <td>Sets the USER_TOKEN_MANAGER grammar option.  This is a boolean option.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>maxmemory</td>
    <td>Max amount of memory to allocate to the forked JVM.  <em>since Ant 1.8.3</em></td>
    <td>No</td>
  </tr>
</table>
<h3>Example</h3>

<p>Invoke JavaCC on grammar file <samp>src/Parser.jj</samp>, writing the generated files
to <samp>build/src</samp>. The grammar option STATIC is set to <q>true</q> when invoking JavaCC.</p>
<pre>
&lt;javacc target=&quot;src/Parser.jj&quot;
        outputdirectory=&quot;build/src&quot;
        javacchome=&quot;c:/program files/JavaCC&quot;
        static=&quot;true&quot;/&gt;</pre>

</body>
</html>
