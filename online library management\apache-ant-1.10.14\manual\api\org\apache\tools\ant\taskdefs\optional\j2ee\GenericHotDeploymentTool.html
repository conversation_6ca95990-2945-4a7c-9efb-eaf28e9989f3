<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>GenericHotDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.j2ee, class: GenericHotDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.j2ee</a></div>
<h1 title="Class GenericHotDeploymentTool" class="title">Class GenericHotDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">org.apache.tools.ant.taskdefs.optional.j2ee.AbstractHotDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.j2ee.GenericHotDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="JonasHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">JonasHotDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">GenericHotDeploymentTool</span>
<span class="extends-implements">extends <a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></span></div>
<div class="block">A generic tool for J2EE server hot deployment.
  <p>The simple implementation spawns a JVM with the supplied
  class name, jvm args, and arguments.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee"><code>HotDeploymentTool</code></a></li>
<li><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee"><code>AbstractHotDeploymentTool</code></a></li>
<li><a href="ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee"><code>ServerDeploy</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></h3>
<code><a href="HotDeploymentTool.html#ACTION_DELETE">ACTION_DELETE</a>, <a href="HotDeploymentTool.html#ACTION_DEPLOY">ACTION_DEPLOY</a>, <a href="HotDeploymentTool.html#ACTION_LIST">ACTION_LIST</a>, <a href="HotDeploymentTool.html#ACTION_UNDEPLOY">ACTION_UNDEPLOY</a>, <a href="HotDeploymentTool.html#ACTION_UPDATE">ACTION_UPDATE</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">GenericHotDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createArg()" class="member-name-link">createArg</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested argument element to hand to the deployment tool; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createJvmarg()" class="member-name-link">createJvmarg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a nested argument element to hand to the JVM running the
  deployment tool.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deploy()" class="member-name-link">deploy</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform the actual deployment.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassName()" class="member-name-link">getClassName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classname attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../Java.html" title="class in org.apache.tools.ant.taskdefs">Java</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJava()" class="member-name-link">getJava</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the java attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isActionValid()" class="member-name-link">isActionValid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determines if the "action" attribute defines a valid action.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassName(java.lang.String)" class="member-name-link">setClassName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The name of the class to execute to perform
  deployment; required.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)" class="member-name-link">setTask</a><wbr>(<a href="ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</a>&nbsp;task)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the parent task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateAttributes()" class="member-name-link">validateAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Validates the passed in attributes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.AbstractHotDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></h3>
<code><a href="AbstractHotDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="AbstractHotDeploymentTool.html#getClasspath()">getClasspath</a>, <a href="AbstractHotDeploymentTool.html#getPassword()">getPassword</a>, <a href="AbstractHotDeploymentTool.html#getServer()">getServer</a>, <a href="AbstractHotDeploymentTool.html#getTask()">getTask</a>, <a href="AbstractHotDeploymentTool.html#getUserName()">getUserName</a>, <a href="AbstractHotDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="AbstractHotDeploymentTool.html#setPassword(java.lang.String)">setPassword</a>, <a href="AbstractHotDeploymentTool.html#setServer(java.lang.String)">setServer</a>, <a href="AbstractHotDeploymentTool.html#setUserName(java.lang.String)">setUserName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>GenericHotDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GenericHotDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createArg()">
<h3>createArg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></span>&nbsp;<span class="element-name">createArg</span>()</div>
<div class="block">Add a nested argument element to hand to the deployment tool; optional.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A Commandline.Argument object representing the
  command line argument being passed when the deployment
  tool is run.  IE: "-user=mark", "-password=venture"...</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createJvmarg()">
<h3>createJvmarg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></span>&nbsp;<span class="element-name">createJvmarg</span>()</div>
<div class="block">Add a nested argument element to hand to the JVM running the
  deployment tool.
  Creates a nested arg element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A Commandline.Argument object representing the
  JVM command line argument being passed when the deployment
  tool is run.  IE: "-ms64m", "-mx128m"...</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isActionValid()">
<h3>isActionValid</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isActionValid</span>()</div>
<div class="block">Determines if the "action" attribute defines a valid action.
  <p>Subclasses should determine if the action passed in is
  supported by the vendor's deployment tool.
  For this generic implementation, the only valid action is "deploy"</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="AbstractHotDeploymentTool.html#isActionValid()">isActionValid</a></code>&nbsp;in class&nbsp;<code><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></code></dd>
<dt>Returns:</dt>
<dd>true if the "action" attribute is valid, false if not.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)">
<h3>setTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTask</span><wbr><span class="parameters">(<a href="ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</a>&nbsp;task)</span></div>
<div class="block">Sets the parent task.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="HotDeploymentTool.html#setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)">setTask</a></code>&nbsp;in interface&nbsp;<code><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="AbstractHotDeploymentTool.html#setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)">setTask</a></code>&nbsp;in class&nbsp;<code><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>task</code> - An ServerDeploy object representing the parent task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deploy()">
<h3>deploy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deploy</span>()
            throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Perform the actual deployment.
  For this generic implementation, a JVM is spawned using the
  supplied classpath, classname, JVM args, and command line arguments.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the attributes are invalid or incomplete.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validateAttributes()">
<h3>validateAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateAttributes</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Validates the passed in attributes.
  Ensures the className and arguments attribute have been set.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="HotDeploymentTool.html#validateAttributes()">validateAttributes</a></code>&nbsp;in interface&nbsp;<code><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="AbstractHotDeploymentTool.html#validateAttributes()">validateAttributes</a></code>&nbsp;in class&nbsp;<code><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the attributes are invalid or incomplete.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassName(java.lang.String)">
<h3>setClassName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span></div>
<div class="block">The name of the class to execute to perform
  deployment; required.
  Example: "com.foobar.tools.deploy.DeployTool"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>className</code> - The fully qualified class name of the class
  to perform deployment.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJava()">
<h3>getJava</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../Java.html" title="class in org.apache.tools.ant.taskdefs">Java</a></span>&nbsp;<span class="element-name">getJava</span>()</div>
<div class="block">get the java attribute.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the java attribute.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassName()">
<h3>getClassName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClassName</span>()</div>
<div class="block">Get the classname attribute.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classname value.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
