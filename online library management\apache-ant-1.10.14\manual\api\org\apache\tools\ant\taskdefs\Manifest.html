<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Manifest (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Manifest">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Manifest" class="title">Class Manifest</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Manifest</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Manifest</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Holds the data of a jar manifest.

 Manifests are processed according to the
 <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/jar/jar.html">Jar
 file specification</a>.
 Specifically, a manifest element consists of
 a set of attributes and sections. These sections in turn may contain
 attributes. Note in particular that this may result in manifest lines
 greater than 72 bytes being wrapped and continued on the next
 line. If an application can not handle the continuation mechanism, it
 is a defect in the application, not this task.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Manifest.Attribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a></code></div>
<div class="col-last even-row-color">
<div class="block">An attribute for the manifest.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Manifest.Section.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A manifest section - you can nest attribute elements into sections.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTRIBUTE_CLASSPATH" class="member-name-link">ATTRIBUTE_CLASSPATH</a></code></div>
<div class="col-last even-row-color">
<div class="block">The Class-Path Header is special - it can be duplicated</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTRIBUTE_FROM" class="member-name-link">ATTRIBUTE_FROM</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The From Header is disallowed in a Manifest</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTRIBUTE_MANIFEST_VERSION" class="member-name-link">ATTRIBUTE_MANIFEST_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">The standard manifest version header</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ATTRIBUTE_NAME" class="member-name-link">ATTRIBUTE_NAME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The Name Attribute is the first in a named section</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTRIBUTE_SIGNATURE_VERSION" class="member-name-link">ATTRIBUTE_SIGNATURE_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">The standard Signature Version header</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_MANIFEST_VERSION" class="member-name-link">DEFAULT_MANIFEST_VERSION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Default Manifest version if one is not specified</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#EOL" class="member-name-link">EOL</a></code></div>
<div class="col-last even-row-color">
<div class="block">The End-Of-Line marker in manifests</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_FROM_FORBIDDEN" class="member-name-link">ERROR_FROM_FORBIDDEN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Error for attributes</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/charset/Charset.html" title="class or interface in java.nio.charset" class="external-link">Charset</a></code></div>
<div class="col-second even-row-color"><code><a href="#JAR_CHARSET" class="member-name-link">JAR_CHARSET</a></code></div>
<div class="col-last even-row-color">
<div class="block">Charset to be used for JAR files.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#JAR_ENCODING" class="member-name-link">JAR_ENCODING</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MAX_LINE_LENGTH" class="member-name-link">MAX_LINE_LENGTH</a></code></div>
<div class="col-last even-row-color">
<div class="block">The max length of a line in a Manifest</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MAX_SECTION_LENGTH" class="member-name-link">MAX_SECTION_LENGTH</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Max length of a line section which is continued.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Manifest</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Construct an empty manifest</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.Reader)" class="member-name-link">Manifest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color">
<div class="block">Read a manifest file from the given reader</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)" class="member-name-link">addConfiguredAttribute</a><wbr>(<a href="Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a>&nbsp;attribute)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an attribute to the manifest - it is added to the main section.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredSection(org.apache.tools.ant.taskdefs.Manifest.Section)" class="member-name-link">addConfiguredSection</a><wbr>(<a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a>&nbsp;section)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a section to the manifest</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(java.lang.Object)" class="member-name-link">equals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;rhs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDefaultManifest()" class="member-name-link">getDefaultManifest</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Construct a manifest from Ant's default manifest file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMainSection()" class="member-name-link">getMainSection</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the main section of the manifest</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getManifestVersion()" class="member-name-link">getManifestVersion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the version of the manifest</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSection(java.lang.String)" class="member-name-link">getSection</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a particular section from the manifest</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSectionNames()" class="member-name-link">getSectionNames</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the section names in this manifest.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWarnings()" class="member-name-link">getWarnings</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the warnings for this manifest.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#merge(org.apache.tools.ant.taskdefs.Manifest)" class="member-name-link">merge</a><wbr>(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Merge the contents of the given manifest into this manifest
 without merging Class-Path attributes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#merge(org.apache.tools.ant.taskdefs.Manifest,boolean)" class="member-name-link">merge</a><wbr>(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other,
 boolean&nbsp;overwriteMain)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Merge the contents of the given manifest into this manifest
 without merging Class-Path attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#merge(org.apache.tools.ant.taskdefs.Manifest,boolean,boolean)" class="member-name-link">merge</a><wbr>(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other,
 boolean&nbsp;overwriteMain,
 boolean&nbsp;mergeClassPaths)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Merge the contents of the given manifest into this manifest</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert the manifest to its string representation</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(java.io.PrintWriter)" class="member-name-link">write</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write the manifest out to a print writer without flattening
 multi-values attributes (i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#write(java.io.PrintWriter,boolean)" class="member-name-link">write</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer,
 boolean&nbsp;flatten)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Write the manifest out to a print writer.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ATTRIBUTE_MANIFEST_VERSION">
<h3>ATTRIBUTE_MANIFEST_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_MANIFEST_VERSION</span></div>
<div class="block">The standard manifest version header</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_MANIFEST_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTRIBUTE_SIGNATURE_VERSION">
<h3>ATTRIBUTE_SIGNATURE_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_SIGNATURE_VERSION</span></div>
<div class="block">The standard Signature Version header</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_SIGNATURE_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTRIBUTE_NAME">
<h3>ATTRIBUTE_NAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_NAME</span></div>
<div class="block">The Name Attribute is the first in a named section</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_NAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTRIBUTE_FROM">
<h3>ATTRIBUTE_FROM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_FROM</span></div>
<div class="block">The From Header is disallowed in a Manifest</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_FROM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTRIBUTE_CLASSPATH">
<h3>ATTRIBUTE_CLASSPATH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_CLASSPATH</span></div>
<div class="block">The Class-Path Header is special - it can be duplicated</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_CLASSPATH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_MANIFEST_VERSION">
<h3>DEFAULT_MANIFEST_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_MANIFEST_VERSION</span></div>
<div class="block">Default Manifest version if one is not specified</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.DEFAULT_MANIFEST_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAX_LINE_LENGTH">
<h3>MAX_LINE_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAX_LINE_LENGTH</span></div>
<div class="block">The max length of a line in a Manifest</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.MAX_LINE_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MAX_SECTION_LENGTH">
<h3>MAX_SECTION_LENGTH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAX_SECTION_LENGTH</span></div>
<div class="block">Max length of a line section which is continued. Need to allow
 for the CRLF.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.MAX_SECTION_LENGTH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EOL">
<h3>EOL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EOL</span></div>
<div class="block">The End-Of-Line marker in manifests</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.EOL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_FROM_FORBIDDEN">
<h3>ERROR_FROM_FORBIDDEN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_FROM_FORBIDDEN</span></div>
<div class="block">Error for attributes</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ERROR_FROM_FORBIDDEN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JAR_CHARSET">
<h3>JAR_CHARSET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/nio/charset/Charset.html" title="class or interface in java.nio.charset" class="external-link">Charset</a></span>&nbsp;<span class="element-name">JAR_CHARSET</span></div>
<div class="block">Charset to be used for JAR files.</div>
</section>
</li>
<li>
<section class="detail" id="JAR_ENCODING">
<h3>JAR_ENCODING</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">JAR_ENCODING</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Encoding to be used for JAR files.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Manifest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Manifest</span>()</div>
<div class="block">Construct an empty manifest</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.Reader)">
<h3>Manifest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Manifest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;r)</span>
         throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a>,
<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Read a manifest file from the given reader</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - is the reader from which the Manifest is read</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if the manifest is not valid according
         to the JAR spec</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the manifest cannot be read from the reader.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getDefaultManifest()">
<h3>getDefaultManifest</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a></span>&nbsp;<span class="element-name">getDefaultManifest</span>()
                                   throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Construct a manifest from Ant's default manifest file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the default manifest.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem loading the
            default manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredSection(org.apache.tools.ant.taskdefs.Manifest.Section)">
<h3>addConfiguredSection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredSection</span><wbr><span class="parameters">(<a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a>&nbsp;section)</span>
                          throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Add a section to the manifest</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>section</code> - the manifest section to be added</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if the secti0on is not valid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)">
<h3>addConfiguredAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredAttribute</span><wbr><span class="parameters">(<a href="Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</a>&nbsp;attribute)</span>
                            throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Add an attribute to the manifest - it is added to the main section.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attribute</code> - the attribute to be added.</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if the attribute is not valid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="merge(org.apache.tools.ant.taskdefs.Manifest)">
<h3>merge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">merge</span><wbr><span class="parameters">(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other)</span>
           throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Merge the contents of the given manifest into this manifest
 without merging Class-Path attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the Manifest to be merged with this one.</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if there is a problem merging the
         manifest according to the Manifest spec.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="merge(org.apache.tools.ant.taskdefs.Manifest,boolean)">
<h3>merge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">merge</span><wbr><span class="parameters">(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other,
 boolean&nbsp;overwriteMain)</span>
           throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Merge the contents of the given manifest into this manifest
 without merging Class-Path attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the Manifest to be merged with this one.</dd>
<dd><code>overwriteMain</code> - whether to overwrite the main section
        of the current manifest</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if there is a problem merging the
         manifest according to the Manifest spec.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="merge(org.apache.tools.ant.taskdefs.Manifest,boolean,boolean)">
<h3>merge</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">merge</span><wbr><span class="parameters">(<a href="Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</a>&nbsp;other,
 boolean&nbsp;overwriteMain,
 boolean&nbsp;mergeClassPaths)</span>
           throws <span class="exceptions"><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></span></div>
<div class="block">Merge the contents of the given manifest into this manifest</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the Manifest to be merged with this one.</dd>
<dd><code>overwriteMain</code> - whether to overwrite the main section
        of the current manifest</dd>
<dd><code>mergeClassPaths</code> - whether Class-Path attributes should be
        merged.</dd>
<dt>Throws:</dt>
<dd><code><a href="ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</a></code> - if there is a problem merging the
         manifest according to the Manifest spec.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="write(java.io.PrintWriter)">
<h3>write</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write the manifest out to a print writer without flattening
 multi-values attributes (i.e. Class-Path).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>writer</code> - the Writer to which the manifest is written</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the manifest cannot be written</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="write(java.io.PrintWriter,boolean)">
<h3>write</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">write</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintWriter.html" title="class or interface in java.io" class="external-link">PrintWriter</a>&nbsp;writer,
 boolean&nbsp;flatten)</span>
           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Write the manifest out to a print writer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>writer</code> - the Writer to which the manifest is written</dd>
<dd><code>flatten</code> - whether to collapse multi-valued attributes
        (i.e. potentially Class-Path) Class-Path into a single
        attribute.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the manifest cannot be written</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Convert the manifest to its string representation</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>a multiline string with the Manifest as it
         appears in a Manifest file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWarnings()">
<h3>getWarnings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getWarnings</span>()</div>
<div class="block">Get the warnings for this manifest.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an enumeration of warning strings</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>a hashcode based on the version, main and sections.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link"><code>Object.hashCode()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="equals(java.lang.Object)">
<h3>equals</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;rhs)</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rhs</code> - the object to check for equality.</dd>
<dt>Returns:</dt>
<dd>true if the version, main and sections are the same.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link"><code>Object.equals(java.lang.Object)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getManifestVersion()">
<h3>getManifestVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getManifestVersion</span>()</div>
<div class="block">Get the version of the manifest</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the manifest's version string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMainSection()">
<h3>getMainSection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></span>&nbsp;<span class="element-name">getMainSection</span>()</div>
<div class="block">Get the main section of the manifest</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the main section of the manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSection(java.lang.String)">
<h3>getSection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</a></span>&nbsp;<span class="element-name">getSection</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Get a particular section from the manifest</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the section desired.</dd>
<dt>Returns:</dt>
<dd>the specified section or null if that section
 does not exist in the manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSectionNames()">
<h3>getSectionNames</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getSectionNames</span>()</div>
<div class="block">Get the section names in this manifest.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an Enumeration of section names</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
