<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>GenericDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: GenericDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class GenericDeploymentTool" class="title">Class GenericDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a></code>, <code><a href="IPlanetDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></code>, <code><a href="JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></code>, <code><a href="JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></code>, <code><a href="OrionDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></code>, <code><a href="WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a></code>, <code><a href="WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">GenericDeploymentTool</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></span></div>
<div class="block">A deployment tool which creates generic EJB jars. Generic jars contains
 only those classes and META-INF entries specified in the EJB 1.1 standard

 This class is also used as a framework for the creation of vendor specific
 deployment tools. A number of template methods are provided through which the
 vendor specific tool can hook into the EJB creation process.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANALYZER_CLASS_FULL" class="member-name-link">ANALYZER_CLASS_FULL</a></code></div>
<div class="col-last even-row-color">
<div class="block">The analyzer class for the super analyzer</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANALYZER_CLASS_SUPER" class="member-name-link">ANALYZER_CLASS_SUPER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The analyzer class for the super analyzer</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANALYZER_FULL" class="member-name-link">ANALYZER_FULL</a></code></div>
<div class="col-last even-row-color">
<div class="block">A dependency analyzer name to find all related classes</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANALYZER_NONE" class="member-name-link">ANALYZER_NONE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A dependency analyzer name for no analyzer</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANALYZER_SUPER" class="member-name-link">ANALYZER_SUPER</a></code></div>
<div class="col-last even-row-color">
<div class="block">A dependency analyzer name to find ancestor classes</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULT_ANALYZER" class="member-name-link">DEFAULT_ANALYZER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The default analyzer</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_BUFFER_SIZE" class="member-name-link">DEFAULT_BUFFER_SIZE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The default buffer byte size to use for IO</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#EJB_DD" class="member-name-link">EJB_DD</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Name for EJB Deployment descriptor within EJB jars</div>
</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#JAR_COMPRESS_LEVEL" class="member-name-link">JAR_COMPRESS_LEVEL</a></code></div>
<div class="col-last even-row-color">
<div class="block">The level to use for compression</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#MANIFEST" class="member-name-link">MANIFEST</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The standard MANIFEST file</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#META_DIR" class="member-name-link">META_DIR</a></code></div>
<div class="col-last even-row-color">
<div class="block">The standard META-INF directory in jar files</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">GenericDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)" class="member-name-link">addFileToJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/JarOutputStream.html" title="class or interface in java.util.jar" class="external-link">JarOutputStream</a>&nbsp;jStream,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;logicalFilename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Utility method that encapsulates the logic of adding a file entry to
 a .jar file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSupportClasses(java.util.Hashtable)" class="member-name-link">addSupportClasses</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds any classes the user specifies using <i>support</i> nested elements
 to the <code>ejbFiles</code> Hashtable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkAndAddDependants(java.util.Hashtable)" class="member-name-link">checkAndAddDependants</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;checkEntries)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add all available classes, that depend on Remote, Home, Bean, PK</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">checkConfiguration</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method is called as the first step in the processDescriptor method
 to allow vendor-specific subclasses to validate the task configuration
 prior to processing the descriptor.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)" class="member-name-link">configure</a><wbr>(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config&nbsp;config)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configure this tool for use in the ejbjar task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the classpath for the user classes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLoaderForBuild()" class="member-name-link">getClassLoaderForBuild</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a Classloader object which parses the passed in generic EjbJar classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCombinedClasspath()" class="member-name-link">getCombinedClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classpath by combining the one from the surrounding task, if any
 and the one from this tool.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConfig()" class="member-name-link">getConfig</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the basename terminator.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDescriptorHandler(java.io.File)" class="member-name-link">getDescriptorHandler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a descriptionHandler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestDir()" class="member-name-link">getDestDir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the destination directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJarBaseName(java.lang.String)" class="member-name-link">getJarBaseName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Using the EJB descriptor file name passed from the <code>ejbjar</code>
 task, this method returns the "basename" which will be used to name the
 completed JAR file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../Location.html" title="class in org.apache.tools.ant">Location</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocation()" class="member-name-link">getLocation</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the build file location associated with this element's task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getManifestFile(java.lang.String)" class="member-name-link">getManifestFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the manifest file to use for building the generic jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPublicId()" class="member-name-link">getPublicId</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the Public ID of the DTD specified in the EJB descriptor.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTask()" class="member-name-link">getTask</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the task for this tool.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVendorDDPrefix(java.lang.String,java.lang.String)" class="member-name-link">getVendorDDPrefix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the prefix for vendor deployment descriptors.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;level)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log a message to the Ant output.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#needToRebuild(java.util.Hashtable,java.io.File)" class="member-name-link">needToRebuild</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method checks the timestamp on each file listed in the <code>
 ejbFiles</code> and compares them to the timestamp on the <code>jarFile
 </code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">parseEjbFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">processDescriptor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)" class="member-name-link">registerKnownDTDs</a><wbr>(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register the locations of all known DTDs.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory; required.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGenericJarSuffix(java.lang.String)" class="member-name-link">setGenericJarSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the suffix for the generated jar file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTask(org.apache.tools.ant.Task)" class="member-name-link">setTask</a><wbr>(<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the task which owns this tool</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#usingBaseJarName()" class="member-name-link">usingBaseJarName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this build is using the base jar name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateConfigured()" class="member-name-link">validateConfigured</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called to validate that the tool parameters have been configured.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)" class="member-name-link">writeJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarfile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method used to encapsulate the writing of the JAR file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_BUFFER_SIZE">
<h3>DEFAULT_BUFFER_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DEFAULT_BUFFER_SIZE</span></div>
<div class="block">The default buffer byte size to use for IO</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.DEFAULT_BUFFER_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="JAR_COMPRESS_LEVEL">
<h3>JAR_COMPRESS_LEVEL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">JAR_COMPRESS_LEVEL</span></div>
<div class="block">The level to use for compression</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.JAR_COMPRESS_LEVEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="META_DIR">
<h3>META_DIR</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">META_DIR</span></div>
<div class="block">The standard META-INF directory in jar files</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.META_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MANIFEST">
<h3>MANIFEST</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">MANIFEST</span></div>
<div class="block">The standard MANIFEST file</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.MANIFEST">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EJB_DD">
<h3>EJB_DD</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">EJB_DD</span></div>
<div class="block">Name for EJB Deployment descriptor within EJB jars</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.EJB_DD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANALYZER_SUPER">
<h3>ANALYZER_SUPER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANALYZER_SUPER</span></div>
<div class="block">A dependency analyzer name to find ancestor classes</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_SUPER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANALYZER_FULL">
<h3>ANALYZER_FULL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANALYZER_FULL</span></div>
<div class="block">A dependency analyzer name to find all related classes</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_FULL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANALYZER_NONE">
<h3>ANALYZER_NONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANALYZER_NONE</span></div>
<div class="block">A dependency analyzer name for no analyzer</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_NONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DEFAULT_ANALYZER">
<h3>DEFAULT_ANALYZER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_ANALYZER</span></div>
<div class="block">The default analyzer</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.DEFAULT_ANALYZER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANALYZER_CLASS_SUPER">
<h3>ANALYZER_CLASS_SUPER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANALYZER_CLASS_SUPER</span></div>
<div class="block">The analyzer class for the super analyzer</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_CLASS_SUPER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANALYZER_CLASS_FULL">
<h3>ANALYZER_CLASS_FULL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANALYZER_CLASS_FULL</span></div>
<div class="block">The analyzer class for the super analyzer</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_CLASS_FULL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>GenericDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">GenericDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</span></div>
<div class="block">Set the destination directory; required.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inDir</code> - the destination directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDestDir()">
<h3>getDestDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestDir</span>()</div>
<div class="block">Get the destination directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the destination directory into which EJB jars are to be written</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTask(org.apache.tools.ant.Task)">
<h3>setTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTask</span><wbr><span class="parameters">(<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</span></div>
<div class="block">Set the task which owns this tool</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>task</code> - the Task to which this deployment tool is associated.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTask()">
<h3>getTask</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></span>&nbsp;<span class="element-name">getTask</span>()</div>
<div class="block">Get the task for this tool.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the Task instance this tool is associated with.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getConfig()">
<h3>getConfig</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config</span>&nbsp;<span class="element-name">getConfig</span>()</div>
<div class="block">Get the basename terminator.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an ejbjar task configuration</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="usingBaseJarName()">
<h3>usingBaseJarName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">usingBaseJarName</span>()</div>
<div class="block">Indicate if this build is using the base jar name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the name of the generated jar is coming from the
              basejarname attribute</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGenericJarSuffix(java.lang.String)">
<h3>setGenericJarSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGenericJarSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Set the suffix for the generated jar file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Add the classpath for the user classes</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a Path instance to be configured by Ant.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to be used for this build.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCombinedClasspath()">
<h3>getCombinedClasspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getCombinedClasspath</span>()</div>
<div class="block">Get the classpath by combining the one from the surrounding task, if any
 and the one from this tool.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the combined classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;level)</span></div>
<div class="block">Log a message to the Ant output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - the message to be logged.</dd>
<dd><code>level</code> - the severity of this message.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocation()">
<h3>getLocation</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../../Location.html" title="class in org.apache.tools.ant">Location</a></span>&nbsp;<span class="element-name">getLocation</span>()</div>
<div class="block">Get the build file location associated with this element's task.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the task's location instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">
<h3>configure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config&nbsp;config)</span></div>
<div class="block">Configure this tool for use in the ejbjar task.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>config</code> - the configuration from the surrounding ejbjar task.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">
<h3>addFileToJar</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileToJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/JarOutputStream.html" title="class or interface in java.util.jar" class="external-link">JarOutputStream</a>&nbsp;jStream,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inputFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;logicalFilename)</span>
                     throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Utility method that encapsulates the logic of adding a file entry to
 a .jar file.  Used by execute() to add entries to the jar file as it is
 constructed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jStream</code> - A JarOutputStream into which to write the
        jar entry.</dd>
<dd><code>inputFile</code> - A File from which to read the
        contents the file being added.</dd>
<dd><code>logicalFilename</code> - A String representing the name, including
        all relevant path information, that should be stored for the entry
        being added.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDescriptorHandler(java.io.File)">
<h3>getDescriptorHandler</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></span>&nbsp;<span class="element-name">getDescriptorHandler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Get a descriptionHandler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
<dt>Returns:</dt>
<dd>a handler.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">
<h3>registerKnownDTDs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerKnownDTDs</span><wbr><span class="parameters">(<a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a>&nbsp;handler)</span></div>
<div class="block">Register the locations of all known DTDs.

 vendor-specific subclasses should override this method to define
 the vendor-specific locations of the EJB DTDs</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>handler</code> - no used in this class.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>processDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processDescriptor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span></div>
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - the name of the deployment descriptor</dd>
<dd><code>saxParser</code> - a SAX parser which can be used to parse the deployment descriptor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>checkConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkConfiguration</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span>
                           throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">This method is called as the first step in the processDescriptor method
 to allow vendor-specific subclasses to validate the task configuration
 prior to processing the descriptor.  If the configuration is invalid,
 a BuildException should be thrown.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dd><code>saxParser</code> - SAXParser which may be used to parse the XML
                           descriptor</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>parseEjbFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</span>&nbsp;<span class="element-name">parseEjbFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span>
                                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dd><code>saxParser</code> - SAXParser which may be used to parse the XML
                           descriptor</dd>
<dt>Returns:</dt>
<dd>Hashtable of EJB class (and other) files to be
                           added to the completed JAR file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - Any SAX exception, possibly wrapping another
                           exception</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - An IOException from the parser, possibly from a
                           the byte stream or character stream</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSupportClasses(java.util.Hashtable)">
<h3>addSupportClasses</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSupportClasses</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles)</span></div>
<div class="block">Adds any classes the user specifies using <i>support</i> nested elements
 to the <code>ejbFiles</code> Hashtable.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - Hashtable of EJB classes (and other) files that will be
                 added to the completed JAR file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJarBaseName(java.lang.String)">
<h3>getJarBaseName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getJarBaseName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</span></div>
<div class="block">Using the EJB descriptor file name passed from the <code>ejbjar</code>
 task, this method returns the "basename" which will be used to name the
 completed JAR file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dt>Returns:</dt>
<dd>The "basename" which will be used to name the
                           completed JAR file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVendorDDPrefix(java.lang.String,java.lang.String)">
<h3>getVendorDDPrefix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVendorDDPrefix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName)</span></div>
<div class="block">Get the prefix for vendor deployment descriptors.

 This will contain the path and the start of the descriptor name,
 depending on the naming scheme</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name to use.</dd>
<dd><code>descriptorFileName</code> - the file name to use.</dd>
<dt>Returns:</dt>
<dd>the prefix.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</span></div>
<div class="block">Add any vendor specific files which should be included in the
 EJB Jar.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - a hashtable entryname -&gt; file.</dd>
<dd><code>ddPrefix</code> - a prefix to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="needToRebuild(java.util.Hashtable,java.io.File)">
<h3>needToRebuild</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">needToRebuild</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile)</span></div>
<div class="block">This method checks the timestamp on each file listed in the <code>
 ejbFiles</code> and compares them to the timestamp on the <code>jarFile
 </code>.  If the <code>jarFile</code>'s timestamp is more recent than
 each EJB file, <code>true</code> is returned.  Otherwise, <code>false
 </code> is returned.
 TODO: find a way to check the manifest-file, that is found by naming convention</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - Hashtable of EJB classes (and other) files that will be
                 added to the completed JAR file</dd>
<dd><code>jarFile</code> - JAR file which will contain all of the EJB classes (and
                 other) files</dd>
<dt>Returns:</dt>
<dd>boolean indicating whether or not the <code>jarFile</code>
                 is up to date</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPublicId()">
<h3>getPublicId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPublicId</span>()</div>
<div class="block">Returns the Public ID of the DTD specified in the EJB descriptor.  Not
 every vendor-specific <code>DeploymentTool</code> will need to reference
 this value or may want to determine this value in a vendor-specific way.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Public ID of the DTD specified in the EJB descriptor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getManifestFile(java.lang.String)">
<h3>getManifestFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getManifestFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Get the manifest file to use for building the generic jar.

 If the file does not exist the global manifest from the config is used
 otherwise the default Ant manifest will be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - the prefix where to llook for the manifest file based on
        the naming convention.</dd>
<dt>Returns:</dt>
<dd>the manifest file or null if the manifest file does not exist</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">
<h3>writeJar</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarfile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method used to encapsulate the writing of the JAR file. Iterates over the
 filenames/java.io.Files in the Hashtable stored on the instance variable
 ejbFiles.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name to use.</dd>
<dd><code>jarfile</code> - the jar file to write to.</dd>
<dd><code>files</code> - the files to write to the jar.</dd>
<dd><code>publicId</code> - the id to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkAndAddDependants(java.util.Hashtable)">
<h3>checkAndAddDependants</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkAndAddDependants</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;checkEntries)</span>
                              throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Add all available classes, that depend on Remote, Home, Bean, PK</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>checkEntries</code> - files, that are extracted from the deployment descriptor</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassLoaderForBuild()">
<h3>getClassLoaderForBuild</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getClassLoaderForBuild</span>()</div>
<div class="block">Returns a Classloader object which parses the passed in generic EjbJar classpath.
 The loader is used to dynamically load classes from javax.ejb.* and the classes
 being added to the jar.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a classloader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validateConfigured()">
<h3>validateConfigured</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateConfigured</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called to validate that the tool parameters have been configured.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#validateConfigured()">validateConfigured</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - If the Deployment Tool's configuration isn't
                        valid</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
