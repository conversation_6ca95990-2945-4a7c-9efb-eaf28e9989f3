<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DirectoryScanner (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: DirectoryScanner">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class DirectoryScanner" class="title">Class DirectoryScanner</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.DirectoryScanner</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code>, <code><a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code>, <code><a href="types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></code>, <code><a href="types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</a></code>, <code><a href="taskdefs/optional/net/FTP.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</a></code>, <code><a href="taskdefs/optional/net/FTPTaskMirrorImpl.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DirectoryScanner</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a>, <a href="types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a>, <a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></span></div>
<div class="block">Class for scanning a directory for files/directories which match certain
 criteria.
 <p>
 These criteria consist of selectors and patterns which have been specified.
 With the selectors you can select which files you want to have included.
 Files which are not selected are excluded. With patterns you can include
 or exclude files based on their filename.
 </p>
 <p>
 The idea is simple. A given directory is recursively scanned for all files
 and directories. Each file/directory is matched against a set of selectors,
 including special support for matching against filenames with include and
 and exclude patterns. Only files/directories which match at least one
 pattern of the include pattern list or other file selector, and don't match
 any pattern of the exclude pattern list or fail to match against a required
 selector will be placed in the list of files/directories found.
 </p>
 <p>
 When no list of include patterns is supplied, "**" will be used, which
 means that everything will be matched. When no list of exclude patterns is
 supplied, an empty list is used, such that nothing will be excluded. When
 no selectors are supplied, none are applied.
 </p>
 <p>
 The filename pattern matching is done as follows:
 The name to be matched is split up in path segments. A path segment is the
 name of a directory or file, which is bounded by
 <code>File.separator</code> ('/' under UNIX, '\' under Windows).
 For example, "abc/def/ghi/xyz.java" is split up in the segments "abc",
 "def","ghi" and "xyz.java".
 The same is done for the pattern against which should be matched.
 </p>
 <p>
 The segments of the name and the pattern are then matched against each
 other. When '**' is used for a path segment in the pattern, it matches
 zero or more path segments of the name.
 </p>
 <p>
 There is a special case regarding the use of <code>File.separator</code>s
 at the beginning of the pattern and the string to match:
 </p>
 <ul>
 <li>When a pattern starts with a <code>File.separator</code>, the string
 to match must also start with a <code>File.separator</code>.</li>
 <li>When a pattern does not start with a <code>File.separator</code>, the
 string to match may not start with a <code>File.separator</code>.</li>
 <li>When one of the above rules is not obeyed, the string will not
 match.</li>
 </ul>
 <p>
 When a name path segment is matched against a pattern path segment, the
 following special characters can be used:<br>
 '*' matches zero or more characters<br>
 '?' matches one character.
 </p>
 <p>
 Examples:
 </p>
 <p>
 "**\*.class" matches all .class files/dirs in a directory tree.
 </p>
 <p>
 "test\a??.java" matches all files/dirs which start with an 'a', then two
 more characters and then ".java", in a directory called test.
 </p>
 <p>
 "**" matches everything in a directory tree.
 </p>
 <p>
 "**\test\**\XYZ*" matches all files/dirs which start with "XYZ" and where
 there is a parent directory called test (e.g. "abc\test\def\ghi\XYZ123").
 </p>
 <p>
 Case sensitivity may be turned off if necessary. By default, it is
 turned on.
 </p>
 <p>
 Example of usage:
 </p>
 <pre>
   String[] includes = {"**\\*.class"};
   String[] excludes = {"modules\\*\\**"};
   ds.setIncludes(includes);
   ds.setExcludes(excludes);
   ds.setBasedir(new File("test"));
   ds.setCaseSensitive(true);
   ds.scan();

   System.out.println("FILES:");
   String[] files = ds.getIncludedFiles();
   for (int i = 0; i &lt; files.length; i++) {
     System.out.println(files[i]);
   }
 </pre>
 This will scan a directory called test for .class files, but excludes all
 files in all proper subdirectories of a directory called "modules".</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color"><code><a href="#basedir" class="member-name-link">basedir</a></code></div>
<div class="col-last even-row-color">
<div class="block">The base directory to be scanned.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#DEFAULTEXCLUDES" class="member-name-link">DEFAULTEXCLUDES</a></code></div>
<div class="col-last odd-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#dirsDeselected" class="member-name-link">dirsDeselected</a></code></div>
<div class="col-last even-row-color">
<div class="block">The directories which matched at least one include and no excludes
 but which a selector discarded.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#dirsExcluded" class="member-name-link">dirsExcluded</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The directories which matched at least one include and at least one
 exclude.</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#dirsIncluded" class="member-name-link">dirsIncluded</a></code></div>
<div class="col-last even-row-color">
<div class="block">The directories which matched at least one include and no excludes
 and were selected.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#dirsNotIncluded" class="member-name-link">dirsNotIncluded</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The directories which were found and did not match any includes.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DOES_NOT_EXIST_POSTFIX" class="member-name-link">DOES_NOT_EXIST_POSTFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">The end of the exception message if something that should be
 there doesn't exist.</div>
</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#errorOnMissingDir" class="member-name-link">errorOnMissingDir</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Whether a missing base directory is an error.</div>
</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#everythingIncluded" class="member-name-link">everythingIncluded</a></code></div>
<div class="col-last even-row-color">
<div class="block">Whether or not everything tested so far has been included.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#excludes" class="member-name-link">excludes</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The patterns for the files to be excluded.</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#filesDeselected" class="member-name-link">filesDeselected</a></code></div>
<div class="col-last even-row-color">
<div class="block">The files which matched at least one include and no excludes and
 which a selector discarded.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#filesExcluded" class="member-name-link">filesExcluded</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The files which matched at least one include and at least
 one exclude.</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#filesIncluded" class="member-name-link">filesIncluded</a></code></div>
<div class="col-last even-row-color">
<div class="block">The files which matched at least one include and no excludes
 and were selected.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#filesNotIncluded" class="member-name-link">filesNotIncluded</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The files which did not match any includes or selectors.</div>
</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#haveSlowResults" class="member-name-link">haveSlowResults</a></code></div>
<div class="col-last even-row-color">
<div class="block">Whether or not our results were built by a slow scan.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color"><code><a href="#includes" class="member-name-link">includes</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The patterns for the files to be included.</div>
</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#isCaseSensitive" class="member-name-link">isCaseSensitive</a></code></div>
<div class="col-last even-row-color">
<div class="block">Whether or not the file system should be treated as a case sensitive
 one.</div>
</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MAX_LEVELS_OF_SYMLINKS" class="member-name-link">MAX_LEVELS_OF_SYMLINKS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">default value for <code>maxLevelsOfSymlinks</code></div>
</div>
<div class="col-first even-row-color"><code>protected <a href="types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</code></div>
<div class="col-second even-row-color"><code><a href="#selectors" class="member-name-link">selectors</a></code></div>
<div class="col-last even-row-color">
<div class="block">Selectors that will filter which files are in our candidate list.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DirectoryScanner</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addDefaultExclude(java.lang.String)" class="member-name-link">addDefaultExclude</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Add a pattern to the default excludes unless it is already a
 default exclude.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDefaultExcludes()" class="member-name-link">addDefaultExcludes</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add default exclusions to the current exclusions set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addExcludes(java.lang.String%5B%5D)" class="member-name-link">addExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add to the list of exclude patterns to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearResults()" class="member-name-link">clearResults</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clear the result caches for a scan.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#couldHoldIncluded(java.lang.String)" class="member-name-link">couldHoldIncluded</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test whether or not a name matches the start of at least one include
 pattern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBasedir()" class="member-name-link">getBasedir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the base directory to be scanned.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getDefaultExcludes()" class="member-name-link">getDefaultExcludes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get the list of patterns that should be excluded by default.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeselectedDirectories()" class="member-name-link">getDeselectedDirectories</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which were selected out and
 therefore not ultimately included.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeselectedFiles()" class="member-name-link">getDeselectedFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which were selected out and
 therefore not ultimately included.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExcludedDirectories()" class="member-name-link">getExcludedDirectories</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExcludedFiles()" class="member-name-link">getExcludedFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedDirectories()" class="member-name-link">getIncludedDirectories</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedDirsCount()" class="member-name-link">getIncludedDirsCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the count of included directories.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedFiles()" class="member-name-link">getIncludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedFilesCount()" class="member-name-link">getIncludedFilesCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the count of included files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotFollowedSymlinks()" class="member-name-link">getNotFollowedSymlinks</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Absolute paths of all symbolic links that haven't been followed
 but would have been followed had followsymlinks been true or
 maxLevelsOfSymlinks been bigger.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotIncludedDirectories()" class="member-name-link">getNotIncludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched none of the include
 patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotIncludedFiles()" class="member-name-link">getNotIncludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which matched none of the include
 patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResource(java.lang.String)" class="member-name-link">getResource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the named resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCaseSensitive()" class="member-name-link">isCaseSensitive</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find out whether include exclude patterns are matched in a
 case sensitive way.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEverythingIncluded()" class="member-name-link">isEverythingIncluded</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return whether or not the scanner has included all the files or
 directories it has come across so far.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isExcluded(java.lang.String)" class="member-name-link">isExcluded</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test whether or not a name matches against at least one exclude
 pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFollowSymlinks()" class="member-name-link">isFollowSymlinks</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get whether or not a DirectoryScanner follows symbolic links.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isIncluded(java.lang.String)" class="member-name-link">isIncluded</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test whether or not a name matches against at least one include
 pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected(java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test whether a file should be selected.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#match(java.lang.String,java.lang.String)" class="member-name-link">match</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a string matches against a pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#match(java.lang.String,java.lang.String,boolean)" class="member-name-link">match</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a string matches against a pattern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPath(java.lang.String,java.lang.String)" class="member-name-link">matchPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a given path matches a given pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPath(java.lang.String,java.lang.String,boolean)" class="member-name-link">matchPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a given path matches a given pattern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPatternStart(java.lang.String,java.lang.String)" class="member-name-link">matchPatternStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a given path matches the start of a given
 pattern up to the first "**".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>protected static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPatternStart(java.lang.String,java.lang.String,boolean)" class="member-name-link">matchPatternStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Test whether or not a given path matches the start of a given
 pattern up to the first "**".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#removeDefaultExclude(java.lang.String)" class="member-name-link">removeDefaultExclude</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Remove a string if it is a default exclude.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#resetDefaultExcludes()" class="member-name-link">resetDefaultExcludes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Go back to the hardwired default exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scan()" class="member-name-link">scan</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scan for files which match at least one include pattern and don't match
 any exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scandir(java.io.File,java.lang.String,boolean)" class="member-name-link">scandir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vpath,
 boolean&nbsp;fast)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scan the given directory for files and directories.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasedir(java.io.File)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base directory to be scanned.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasedir(java.lang.String)" class="member-name-link">setBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;basedir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base directory to be scanned.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCaseSensitive(boolean)" class="member-name-link">setCaseSensitive</a><wbr>(boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not include and exclude patterns are matched
 in a case sensitive way.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorOnMissingDir(boolean)" class="member-name-link">setErrorOnMissingDir</a><wbr>(boolean&nbsp;errorOnMissingDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether or not a missing base directory is an error</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludes(java.lang.String%5B%5D)" class="member-name-link">setExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the list of exclude patterns to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFollowSymlinks(boolean)" class="member-name-link">setFollowSymlinks</a><wbr>(boolean&nbsp;followSymlinks)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not symbolic links should be followed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludes(java.lang.String%5B%5D)" class="member-name-link">setIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the list of include patterns to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMaxLevelsOfSymlinks(int)" class="member-name-link">setMaxLevelsOfSymlinks</a><wbr>(int&nbsp;max)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The maximum number of times a symbolic link may be followed
 during a scan.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSelectors(org.apache.tools.ant.types.selectors.FileSelector%5B%5D)" class="member-name-link">setSelectors</a><wbr>(<a href="types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]&nbsp;selectors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the selectors that will select the filelist.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#slowScan()" class="member-name-link">slowScan</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Top level invocation for a slow scan.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULTEXCLUDES">
<h3>DEFAULTEXCLUDES</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">DEFAULTEXCLUDES</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use the <a href="#getDefaultExcludes()"><code>getDefaultExcludes</code></a>
             method instead.</div>
</div>
<div class="block">Patterns which should be excluded by default.

 <p>Note that you can now add patterns to the list of default
 excludes.  Added patterns will not become part of this array
 that has only been kept around for backwards compatibility
 reasons.</p></div>
</section>
</li>
<li>
<section class="detail" id="MAX_LEVELS_OF_SYMLINKS">
<h3>MAX_LEVELS_OF_SYMLINKS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MAX_LEVELS_OF_SYMLINKS</span></div>
<div class="block">default value for <code>maxLevelsOfSymlinks</code></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.DirectoryScanner.MAX_LEVELS_OF_SYMLINKS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DOES_NOT_EXIST_POSTFIX">
<h3>DOES_NOT_EXIST_POSTFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DOES_NOT_EXIST_POSTFIX</span></div>
<div class="block">The end of the exception message if something that should be
 there doesn't exist.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.DirectoryScanner.DOES_NOT_EXIST_POSTFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="basedir">
<h3>basedir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">basedir</span></div>
<div class="block">The base directory to be scanned.</div>
</section>
</li>
<li>
<section class="detail" id="includes">
<h3>includes</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">includes</span></div>
<div class="block">The patterns for the files to be included.</div>
</section>
</li>
<li>
<section class="detail" id="excludes">
<h3>excludes</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">excludes</span></div>
<div class="block">The patterns for the files to be excluded.</div>
</section>
</li>
<li>
<section class="detail" id="selectors">
<h3>selectors</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</span>&nbsp;<span class="element-name">selectors</span></div>
<div class="block">Selectors that will filter which files are in our candidate list.</div>
</section>
</li>
<li>
<section class="detail" id="filesIncluded">
<h3>filesIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">filesIncluded</span></div>
<div class="block">The files which matched at least one include and no excludes
 and were selected.</div>
</section>
</li>
<li>
<section class="detail" id="filesNotIncluded">
<h3>filesNotIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">filesNotIncluded</span></div>
<div class="block">The files which did not match any includes or selectors.</div>
</section>
</li>
<li>
<section class="detail" id="filesExcluded">
<h3>filesExcluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">filesExcluded</span></div>
<div class="block">The files which matched at least one include and at least
 one exclude.</div>
</section>
</li>
<li>
<section class="detail" id="dirsIncluded">
<h3>dirsIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">dirsIncluded</span></div>
<div class="block">The directories which matched at least one include and no excludes
 and were selected.</div>
</section>
</li>
<li>
<section class="detail" id="dirsNotIncluded">
<h3>dirsNotIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">dirsNotIncluded</span></div>
<div class="block">The directories which were found and did not match any includes.</div>
</section>
</li>
<li>
<section class="detail" id="dirsExcluded">
<h3>dirsExcluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">dirsExcluded</span></div>
<div class="block">The directories which matched at least one include and at least one
 exclude.</div>
</section>
</li>
<li>
<section class="detail" id="filesDeselected">
<h3>filesDeselected</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">filesDeselected</span></div>
<div class="block">The files which matched at least one include and no excludes and
 which a selector discarded.</div>
</section>
</li>
<li>
<section class="detail" id="dirsDeselected">
<h3>dirsDeselected</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">dirsDeselected</span></div>
<div class="block">The directories which matched at least one include and no excludes
 but which a selector discarded.</div>
</section>
</li>
<li>
<section class="detail" id="haveSlowResults">
<h3>haveSlowResults</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">haveSlowResults</span></div>
<div class="block">Whether or not our results were built by a slow scan.</div>
</section>
</li>
<li>
<section class="detail" id="isCaseSensitive">
<h3>isCaseSensitive</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCaseSensitive</span></div>
<div class="block">Whether or not the file system should be treated as a case sensitive
 one.</div>
</section>
</li>
<li>
<section class="detail" id="errorOnMissingDir">
<h3>errorOnMissingDir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">errorOnMissingDir</span></div>
<div class="block">Whether a missing base directory is an error.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="everythingIncluded">
<h3>everythingIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">everythingIncluded</span></div>
<div class="block">Whether or not everything tested so far has been included.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DirectoryScanner</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DirectoryScanner</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="matchPatternStart(java.lang.String,java.lang.String)">
<h3>matchPatternStart</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPatternStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Test whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>whether or not a given path matches the start of a given
 pattern up to the first "**".</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matchPatternStart(java.lang.String,java.lang.String,boolean)">
<h3>matchPatternStart</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPatternStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Test whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dd><code>isCaseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd>whether or not a given path matches the start of a given
 pattern up to the first "**".</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matchPath(java.lang.String,java.lang.String)">
<h3>matchPath</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Test whether or not a given path matches a given pattern.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="matchPath(java.lang.String,java.lang.String,boolean)">
<h3>matchPath</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Test whether or not a given path matches a given pattern.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dd><code>isCaseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(java.lang.String,java.lang.String)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Test whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against.
                Must not be <code>null</code>.</dd>
<dd><code>str</code> - The string which must be matched against the pattern.
                Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="match(java.lang.String,java.lang.String,boolean)">
<h3>match</h3>
<div class="member-signature"><span class="modifiers">protected static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Test whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against.
                Must not be <code>null</code>.</dd>
<dd><code>str</code> - The string which must be matched against the pattern.
                Must not be <code>null</code>.</dd>
<dd><code>isCaseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultExcludes()">
<h3>getDefaultExcludes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getDefaultExcludes</span>()</div>
<div class="block">Get the list of patterns that should be excluded by default.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>An array of <code>String</code> based on the current
         contents of the <code>defaultExcludes</code>
         <code>Set</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDefaultExclude(java.lang.String)">
<h3>addDefaultExclude</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">addDefaultExclude</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</span></div>
<div class="block">Add a pattern to the default excludes unless it is already a
 default exclude.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - A string to add as an exclude pattern.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the string was added;
            <code>false</code> if it already existed.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeDefaultExclude(java.lang.String)">
<h3>removeDefaultExclude</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">removeDefaultExclude</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;s)</span></div>
<div class="block">Remove a string if it is a default exclude.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>s</code> - The string to attempt to remove.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if <code>s</code> was a default
            exclude (and thus was removed);
            <code>false</code> if <code>s</code> was not
            in the default excludes list to begin with.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resetDefaultExcludes()">
<h3>resetDefaultExcludes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resetDefaultExcludes</span>()</div>
<div class="block">Go back to the hardwired default exclude patterns.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.lang.String)">
<h3>setBasedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;basedir)</span></div>
<div class="block">Set the base directory to be scanned. This is the directory which is
 scanned recursively. All '/' and '\' characters are replaced by
 <code>File.separatorChar</code>, so the separator used need not match
 <code>File.separatorChar</code>.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#setBasedir(java.lang.String)">setBasedir</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - The base directory to scan.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBasedir(java.io.File)">
<h3>setBasedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir)</span></div>
<div class="block">Set the base directory to be scanned. This is the directory which is
 scanned recursively.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#setBasedir(java.io.File)">setBasedir</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - The base directory for scanning.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBasedir()">
<h3>getBasedir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBasedir</span>()</div>
<div class="block">Return the base directory to be scanned.
 This is the directory which is scanned recursively.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getBasedir()">getBasedir</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the base directory to be scanned.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCaseSensitive()">
<h3>isCaseSensitive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCaseSensitive</span>()</div>
<div class="block">Find out whether include exclude patterns are matched in a
 case sensitive way.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether or not the scanning is case sensitive.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCaseSensitive(boolean)">
<h3>setCaseSensitive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCaseSensitive</span><wbr><span class="parameters">(boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Set whether or not include and exclude patterns are matched
 in a case sensitive way.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#setCaseSensitive(boolean)">setCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>isCaseSensitive</code> - whether or not the file system should be
                        regarded as a case sensitive one.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorOnMissingDir(boolean)">
<h3>setErrorOnMissingDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorOnMissingDir</span><wbr><span class="parameters">(boolean&nbsp;errorOnMissingDir)</span></div>
<div class="block">Sets whether or not a missing base directory is an error</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorOnMissingDir</code> - whether or not a missing base directory
                        is an error</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFollowSymlinks()">
<h3>isFollowSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFollowSymlinks</span>()</div>
<div class="block">Get whether or not a DirectoryScanner follows symbolic links.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>flag indicating whether symbolic links should be followed.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFollowSymlinks(boolean)">
<h3>setFollowSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFollowSymlinks</span><wbr><span class="parameters">(boolean&nbsp;followSymlinks)</span></div>
<div class="block">Set whether or not symbolic links should be followed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>followSymlinks</code> - whether or not symbolic links should be followed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMaxLevelsOfSymlinks(int)">
<h3>setMaxLevelsOfSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMaxLevelsOfSymlinks</span><wbr><span class="parameters">(int&nbsp;max)</span></div>
<div class="block">The maximum number of times a symbolic link may be followed
 during a scan.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max</code> - int</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIncludes(java.lang.String[])">
<h3>setIncludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</span></div>
<div class="block">Set the list of include patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#setIncludes(java.lang.String%5B%5D)">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>includes</code> - A list of include patterns.
                 May be <code>null</code>, indicating that all files
                 should be included. If a non-<code>null</code>
                 list is given, all elements must be
                 non-<code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExcludes(java.lang.String[])">
<h3>setExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</span></div>
<div class="block">Set the list of exclude patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#setExcludes(java.lang.String%5B%5D)">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>excludes</code> - A list of exclude patterns.
                 May be <code>null</code>, indicating that no files
                 should be excluded. If a non-<code>null</code> list is
                 given, all elements must be non-<code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addExcludes(java.lang.String[])">
<h3>addExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</span></div>
<div class="block">Add to the list of exclude patterns to use. All '/' and '\'
 characters are replaced by <code>File.separatorChar</code>, so
 the separator used need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - A list of exclude patterns.
                 May be <code>null</code>, in which case the
                 exclude patterns don't get changed at all.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSelectors(org.apache.tools.ant.types.selectors.FileSelector[])">
<h3>setSelectors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSelectors</span><wbr><span class="parameters">(<a href="types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]&nbsp;selectors)</span></div>
<div class="block">Set the selectors that will select the filelist.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="types/selectors/SelectorScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector%5B%5D)">setSelectors</a></code>&nbsp;in interface&nbsp;<code><a href="types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selectors</code> - specifies the selectors to be invoked on a scan.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEverythingIncluded()">
<h3>isEverythingIncluded</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEverythingIncluded</span>()</div>
<div class="block">Return whether or not the scanner has included all the files or
 directories it has come across so far.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if all files and directories which have
         been found so far have been included.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="scan()">
<h3>scan</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scan</span>()
          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></span></div>
<div class="block">Scan for files which match at least one include pattern and don't match
 any exclude patterns. If there are selectors then the files must pass
 muster there, as well.  Scans under basedir, if set; otherwise the
 include patterns without leading wildcards specify the absolute paths of
 the files that may be included.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#scan()">scan</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></code> - if the base directory was set
            incorrectly (i.e. if it doesn't exist or isn't a directory).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearResults()">
<h3>clearResults</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearResults</span>()</div>
<div class="block">Clear the result caches for a scan.</div>
</section>
</li>
<li>
<section class="detail" id="slowScan()">
<h3>slowScan</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">slowScan</span>()</div>
<div class="block">Top level invocation for a slow scan. A slow scan builds up a full
 list of excluded/included files/directories, whereas a fast scan
 will only have full results for included files, as it ignores
 directories which can't possibly hold any included files/directories.
 <p>
 Returns immediately if a slow scan has already been completed.</div>
</section>
</li>
<li>
<section class="detail" id="scandir(java.io.File,java.lang.String,boolean)">
<h3>scandir</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scandir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;dir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vpath,
 boolean&nbsp;fast)</span></div>
<div class="block">Scan the given directory for files and directories. Found files and
 directories are placed in their respective collections, based on the
 matching of includes, excludes, and the selectors.  When a directory
 is found, it is scanned recursively.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dir</code> - The directory to scan. Must not be <code>null</code>.</dd>
<dd><code>vpath</code> - The path relative to the base directory (needed to
              prevent problems with an absolute path when using
              dir). Must not be <code>null</code>.</dd>
<dd><code>fast</code> - Whether or not this call is part of a fast scan.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#filesIncluded"><code>filesIncluded</code></a></li>
<li><a href="#filesNotIncluded"><code>filesNotIncluded</code></a></li>
<li><a href="#filesExcluded"><code>filesExcluded</code></a></li>
<li><a href="#dirsIncluded"><code>dirsIncluded</code></a></li>
<li><a href="#dirsNotIncluded"><code>dirsNotIncluded</code></a></li>
<li><a href="#dirsExcluded"><code>dirsExcluded</code></a></li>
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isIncluded(java.lang.String)">
<h3>isIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isIncluded</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Test whether or not a name matches against at least one include
 pattern.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The path to match. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> when the name matches against at least one
         include pattern, or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="couldHoldIncluded(java.lang.String)">
<h3>couldHoldIncluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">couldHoldIncluded</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Test whether or not a name matches the start of at least one include
 pattern.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name to match. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> when the name matches against the start of at
         least one include pattern, or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isExcluded(java.lang.String)">
<h3>isExcluded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isExcluded</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Test whether or not a name matches against at least one exclude
 pattern.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name to match. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> when the name matches against at least one
         exclude pattern, or <code>false</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSelected(java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Test whether a file should be selected.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the filename to check for selecting.</dd>
<dd><code>file</code> - the java.io.File object for this filename.</dd>
<dt>Returns:</dt>
<dd><code>false</code> when the selectors says that the file
         should not be selected, <code>true</code> otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludedFiles()">
<h3>getIncludedFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedFiles</span>()</div>
<div class="block">Return the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getIncludedFiles()">getIncludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which matched at least one of the
         include patterns and none of the exclude patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludedFilesCount()">
<h3>getIncludedFilesCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getIncludedFilesCount</span>()</div>
<div class="block">Return the count of included files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>int</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedFiles()">
<h3>getNotIncludedFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedFiles</span>()</div>
<div class="block">Return the names of the files which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getNotIncludedFiles()">getNotIncludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which matched none of the include
         patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExcludedFiles()">
<h3>getExcludedFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedFiles</span>()</div>
<div class="block">Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getExcludedFiles()">getExcludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which matched at least one of the
         include patterns and at least one of the exclude patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDeselectedFiles()">
<h3>getDeselectedFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getDeselectedFiles</span>()</div>
<div class="block"><p>Return the names of the files which were selected out and
 therefore not ultimately included.</p>

 <p>The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</p></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="types/selectors/SelectorScanner.html#getDeselectedFiles()">getDeselectedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which were deselected.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludedDirectories()">
<h3>getIncludedDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedDirectories</span>()</div>
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getIncludedDirectories()">getIncludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludedDirsCount()">
<h3>getIncludedDirsCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getIncludedDirsCount</span>()</div>
<div class="block">Return the count of included directories.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>int</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedDirectories()">
<h3>getNotIncludedDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedDirectories</span>()</div>
<div class="block">Return the names of the directories which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched none of the include
 patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExcludedDirectories()">
<h3>getExcludedDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedDirectories</span>()</div>
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#getExcludedDirectories()">getExcludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDeselectedDirectories()">
<h3>getDeselectedDirectories</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getDeselectedDirectories</span>()</div>
<div class="block"><p>Return the names of the directories which were selected out and
 therefore not ultimately included.</p>

 <p>The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</p></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="types/selectors/SelectorScanner.html#getDeselectedDirectories()">getDeselectedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which were deselected.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#slowScan()"><code>slowScan()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNotFollowedSymlinks()">
<h3>getNotFollowedSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotFollowedSymlinks</span>()</div>
<div class="block">Absolute paths of all symbolic links that haven't been followed
 but would have been followed had followsymlinks been true or
 maxLevelsOfSymlinks been bigger.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>sorted array of not followed symlinks</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><code>notFollowedSymlinks</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDefaultExcludes()">
<h3>addDefaultExcludes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDefaultExcludes</span>()</div>
<div class="block">Add default exclusions to the current exclusions set.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileScanner.html#addDefaultExcludes()">addDefaultExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResource(java.lang.String)">
<h3>getResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a></span>&nbsp;<span class="element-name">getResource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Get the named resource.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="types/ResourceFactory.html#getResource(java.lang.String)">getResource</a></code>&nbsp;in interface&nbsp;<code><a href="types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - path name of the file relative to the dir attribute.</dd>
<dt>Returns:</dt>
<dd>the resource with the given name.</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
