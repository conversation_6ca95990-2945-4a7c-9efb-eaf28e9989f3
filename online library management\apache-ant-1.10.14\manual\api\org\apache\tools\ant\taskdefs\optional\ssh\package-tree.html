<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional.ssh Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="tree: package: org.apache.tools.ant.taskdefs.optional.ssh">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.taskdefs.optional.ssh</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="AbstractSshMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">AbstractSshMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="ScpFromMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="ScpFromMessageBySftp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessageBySftp</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="ScpToMessage.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessage</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="ScpToMessageBySftp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessageBySftp</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="Directory.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Directory</a></li>
<li class="circle">org.apache.tools.ant.<a href="../../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.<a href="../../../Task.html" class="type-name-link" title="class in org.apache.tools.ant">Task</a>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHBase.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHBase</a> (implements org.apache.tools.ant.taskdefs.optional.ssh.<a href="LogListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a>)
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="Scp.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">Scp</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHExec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHExec</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHSession.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHSession.LocalTunnel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.LocalTunnel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHSession.NestedSequential.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.NestedSequential</a> (implements org.apache.tools.ant.<a href="../../../TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a>)</li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHSession.RemoteTunnel.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHSession.RemoteTunnel</a></li>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="SSHUserInfo.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHUserInfo</a> (implements com.jcraft.jsch.UIKeyboardInteractive, com.jcraft.jsch.UserInfo)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.taskdefs.optional.ssh.<a href="LogListener.html" class="type-name-link" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
