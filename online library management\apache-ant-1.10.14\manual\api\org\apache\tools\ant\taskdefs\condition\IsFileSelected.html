<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>IsFileSelected (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.condition, class: IsFileSelected">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.condition</a></div>
<h1 title="Class IsFileSelected" class="title">Class IsFileSelected</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../../types/selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.AbstractSelectorContainer</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.condition.IsFileSelected</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></code>, <code><a href="../../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">IsFileSelected</span>
<span class="extends-implements">extends <a href="../../types/selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a>
implements <a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></span></div>
<div class="block">This is a condition that checks to see if a file passes an embedded selector.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../types/DataType.html#checked">checked</a>, <a href="../../types/DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">IsFileSelected</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#eval()" class="member-name-link">eval</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Evaluate the selector with the file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBaseDir(java.io.File)" class="member-name-link">setBaseDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The base directory to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFile(java.io.File)" class="member-name-link">setFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The file to check.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validate()" class="member-name-link">validate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">validate the parameters.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.AbstractSelectorContainer">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../../types/selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></h3>
<code><a href="../../types/selectors/AbstractSelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">addExecutable</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">addOwnedBy</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">addPosixGroup</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">addPosixPermissions</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">addReadable</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">addSymlink</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">addWritable</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#clone()">clone</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#hasSelectors()">hasSelectors</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#selectorCount()">selectorCount</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#selectorElements()">selectorElements</a>, <a href="../../types/selectors/AbstractSelectorContainer.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../types/DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../../types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../../types/DataType.html#circularReference()">circularReference</a>, <a href="../../types/DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../../types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../types/DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../../types/DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../../types/DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../../types/DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../types/DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../../types/DataType.html#getRefid()">getRefid</a>, <a href="../../types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../../types/DataType.html#isChecked()">isChecked</a>, <a href="../../types/DataType.html#isReference()">isReference</a>, <a href="../../types/DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../../types/DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../../types/DataType.html#setChecked(boolean)">setChecked</a>, <a href="../../types/DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../../types/DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>IsFileSelected</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">IsFileSelected</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setFile(java.io.File)">
<h3>setFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">The file to check.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to check if if passes the embedded selector.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBaseDir(java.io.File)">
<h3>setBaseDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBaseDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span></div>
<div class="block">The base directory to use.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the base directory to use, if null use the project's
                basedir.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validate()">
<h3>validate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validate</span>()</div>
<div class="block">validate the parameters.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../types/selectors/AbstractSelectorContainer.html#validate()">validate</a></code>&nbsp;in class&nbsp;<code><a href="../../types/selectors/AbstractSelectorContainer.html" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="eval()">
<h3>eval</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">eval</span>()</div>
<div class="block">Evaluate the selector with the file.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="Condition.html#eval()">eval</a></code>&nbsp;in interface&nbsp;<code><a href="Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</a></code></dd>
<dt>Returns:</dt>
<dd>true if the file is selected by the embedded selector.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
