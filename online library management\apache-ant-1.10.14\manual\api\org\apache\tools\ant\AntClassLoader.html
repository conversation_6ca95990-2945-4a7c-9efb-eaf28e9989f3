<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>AntClassLoader (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: AntClassLoader">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class AntClassLoader" class="title">Class AntClassLoader</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">java.lang.ClassLoader</a>
<div class="inheritance">org.apache.tools.ant.AntClassLoader</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">AntClassLoader2</a></code>, <code><a href="loader/AntClassLoader5.html" title="class in org.apache.tools.ant.loader">AntClassLoader5</a></code>, <code><a href="util/SplitClassLoader.html" title="class in org.apache.tools.ant.util">SplitClassLoader</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">AntClassLoader</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>
implements <a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></span></div>
<div class="block">Used to load classes within ant with a different classpath from
 that used to start ant. Note that it is possible to force a class
 into this loader even when that class is on the system classpath by
 using the forceLoadClass method. Any subsequent classes loaded by that
 class will then use this loader rather than the system class loader.

 <p>
 Note that this classloader has a feature to allow loading
 in reverse order and for "isolation".
 Due to the fact that a number of
 methods in java.lang.ClassLoader are final (at least
 in java 1.4 getResources) this means that the
 class has to fake the given parent.
 </p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">AntClassLoader</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Create an Ant Class Loader</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,boolean)" class="member-name-link">AntClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 boolean&nbsp;parentFirst)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates an empty class loader.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path)" class="member-name-link">AntClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last even-row-color">
<div class="block">Create an Ant ClassLoader for a given project, with
 a parent classloader and an initial classpath.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)" class="member-name-link">AntClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath,
 boolean&nbsp;parentFirst)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a classloader for the given project using the classpath given.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Project,org.apache.tools.ant.types.Path)" class="member-name-link">AntClassLoader</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a classloader for the given project using the classpath given.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)" class="member-name-link">AntClassLoader</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath,
 boolean&nbsp;parentFirst)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a classloader for the given project using the classpath given.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addJavaLibraries()" class="member-name-link">addJavaLibraries</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add any libraries that come with different java versions
 here</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addLoaderPackageRoot(java.lang.String)" class="member-name-link">addLoaderPackageRoot</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageRoot)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a package root to the list of packages which must be loaded using
 this loader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPathComponent(java.io.File)" class="member-name-link">addPathComponent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a path component.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPathElement(java.lang.String)" class="member-name-link">addPathElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pathElement)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an element to the classpath to be searched.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPathFile(java.io.File)" class="member-name-link">addPathFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;pathComponent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a file to the path.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSystemPackageRoot(java.lang.String)" class="member-name-link">addSystemPackageRoot</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageRoot)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a package root to the list of packages which must be loaded on the
 parent loader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans up any resources held by this classloader at the end
 of a build.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#buildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">buildStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanup()" class="member-name-link">cleanup</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans up any resources held by this classloader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"></div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#defineClassFromData(java.io.File,byte%5B%5D,java.lang.String)" class="member-name-link">defineClassFromData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 byte[]&nbsp;classData,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Define a class given its bytes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#definePackage(java.io.File,java.lang.String)" class="member-name-link">definePackage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Define the package information associated with a class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#definePackage(java.io.File,java.lang.String,java.util.jar.Manifest)" class="member-name-link">definePackage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Define the package information when the class comes from a
 jar with a manifest</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findClass(java.lang.String)" class="member-name-link">findClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Searches for and load a class on the classpath of this class loader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findResource(java.lang.String)" class="member-name-link">findResource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the resource with the given name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findResources(java.lang.String)" class="member-name-link">findResources</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findResources(java.lang.String,boolean)" class="member-name-link">findResources</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;skipParent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forceLoadClass(java.lang.String)" class="member-name-link">forceLoadClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads a class through this class loader even if that class is available
 on the parent classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#forceLoadSystemClass(java.lang.String)" class="member-name-link">forceLoadSystemClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads a class through this class loader but defer to the parent class
 loader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the classpath this classloader will consult.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConfiguredParent()" class="member-name-link">getConfiguredParent</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the parent as has been specified in the constructor or via
 setParent.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNamedResources(java.lang.String)" class="member-name-link">getNamedResources</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds all the resources with the given name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResource(java.lang.String)" class="member-name-link">getResource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the resource with the given name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourceAsStream(java.lang.String)" class="member-name-link">getResourceAsStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a stream to read the requested resource name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResources(java.lang.String)" class="member-name-link">getResources</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"></div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResourceURL(java.io.File,java.lang.String)" class="member-name-link">getResourceURL</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resourceName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the URL of a given resource in the given file which may
 either be a directory or a zip file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#initializeClass(java.lang.Class)" class="member-name-link">initializeClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;theClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isInPath(java.io.File)" class="member-name-link">isInPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;component)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if the given file is in this loader's path</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,boolean)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 boolean&nbsp;resolve)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads a class with this class loader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String,int)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs a message through the project object if one has been provided.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageLogged(org.apache.tools.ant.BuildEvent)" class="member-name-link">messageLogged</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#newAntClassLoader(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)" class="member-name-link">newAntClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 boolean&nbsp;parentFirst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Factory method</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resetThreadContextLoader()" class="member-name-link">resetThreadContextLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resets the current thread's context loader to its original value.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClassPath(org.apache.tools.ant.types.Path)" class="member-name-link">setClassPath</a><wbr>(<a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to search for classes to load.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIsolated(boolean)" class="member-name-link">setIsolated</a><wbr>(boolean&nbsp;isolated)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether this classloader should run in isolated mode.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParent(java.lang.ClassLoader)" class="member-name-link">setParent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the parent for this class loader.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParentFirst(boolean)" class="member-name-link">setParentFirst</a><wbr>(boolean&nbsp;parentFirst)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Control whether class lookup is delegated to the parent loader first
 or after this loader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProject(org.apache.tools.ant.Project)" class="member-name-link">setProject</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the project associated with this class loader</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setThreadContextLoader()" class="member-name-link">setThreadContextLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the current thread's context loader to this classloader, storing
 the current loader value for later resetting.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subBuildFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">subBuildFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans up any resources held by this classloader at the end of
 a subbuild if it has been created for the subbuild's project
 instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subBuildStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">subBuildStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#targetStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">targetStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskFinished(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskFinished</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#taskStarted(org.apache.tools.ant.BuildEvent)" class="member-name-link">taskStarted</a><wbr>(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a <code>String</code> representing this loader.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.ClassLoader">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#clearAssertionStatus()" title="class or interface in java.lang" class="external-link">clearAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#defineClass(byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,java.nio.ByteBuffer,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#definePackage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.net.URL)" title="class or interface in java.lang" class="external-link">definePackage</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findClass(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findLibrary(java.lang.String)" title="class or interface in java.lang" class="external-link">findLibrary</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findLoadedClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findLoadedClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findResource(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findResource</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findSystemClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findSystemClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getClassLoadingLock(java.lang.String)" title="class or interface in java.lang" class="external-link">getClassLoadingLock</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getDefinedPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getDefinedPackage</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getDefinedPackages()" title="class or interface in java.lang" class="external-link">getDefinedPackages</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getName()" title="class or interface in java.lang" class="external-link">getName</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getPackage</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getPackages()" title="class or interface in java.lang" class="external-link">getPackages</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getParent()" title="class or interface in java.lang" class="external-link">getParent</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getPlatformClassLoader()" title="class or interface in java.lang" class="external-link">getPlatformClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getSystemClassLoader()" title="class or interface in java.lang" class="external-link">getSystemClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getSystemResource(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResource</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getSystemResourceAsStream(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResourceAsStream</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getSystemResources(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResources</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getUnnamedModule()" title="class or interface in java.lang" class="external-link">getUnnamedModule</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#isRegisteredAsParallelCapable()" title="class or interface in java.lang" class="external-link">isRegisteredAsParallelCapable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#loadClass(java.lang.String)" title="class or interface in java.lang" class="external-link">loadClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#registerAsParallelCapable()" title="class or interface in java.lang" class="external-link">registerAsParallelCapable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#resolveClass(java.lang.Class)" title="class or interface in java.lang" class="external-link">resolveClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#resources(java.lang.String)" title="class or interface in java.lang" class="external-link">resources</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#setClassAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setClassAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#setDefaultAssertionStatus(boolean)" title="class or interface in java.lang" class="external-link">setDefaultAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#setPackageAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setPackageAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#setSigners(java.lang.Class,java.lang.Object%5B%5D)" title="class or interface in java.lang" class="external-link">setSigners</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path)">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Create an Ant ClassLoader for a given project, with
 a parent classloader and an initial classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent for this classloader.</dd>
<dd><code>project</code> - The project to which this classloader is to
                belong.</dd>
<dd><code>classpath</code> - The classpath to use to load classes.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span>()</div>
<div class="block">Create an Ant Class Loader</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Project,org.apache.tools.ant.types.Path)">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Creates a classloader for the given project using the classpath given.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project to which this classloader is to belong.
                Must not be <code>null</code>.</dd>
<dd><code>classpath</code> - The classpath to use to load the classes.  This
                is combined with the system classpath in a manner
                determined by the value of ${build.sysclasspath}.
                May be <code>null</code>, in which case no path
                elements are set up to start with.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath,
 boolean&nbsp;parentFirst)</span></div>
<div class="block">Creates a classloader for the given project using the classpath given.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - The parent classloader to which unsatisfied loading
               attempts are delegated. May be <code>null</code>,
               in which case the classloader which loaded this
               class is used as the parent.</dd>
<dd><code>project</code> - The project to which this classloader is to belong.
                Must not be <code>null</code>.</dd>
<dd><code>classpath</code> - the classpath to use to load the classes.
                  May be <code>null</code>, in which case no path
                  elements are set up to start with.</dd>
<dd><code>parentFirst</code> - If <code>true</code>, indicates that the parent
                    classloader should be consulted  before trying to
                    load the a class through this loader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath,
 boolean&nbsp;parentFirst)</span></div>
<div class="block">Creates a classloader for the given project using the classpath given.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project to which this classloader is to belong.
                Must not be <code>null</code>.</dd>
<dd><code>classpath</code> - The classpath to use to load the classes. May be
                  <code>null</code>, in which case no path
                  elements are set up to start with.</dd>
<dd><code>parentFirst</code> - If <code>true</code>, indicates that the parent
                    classloader should be consulted before trying to
                    load the a class through this loader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,boolean)">
<h3>AntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 boolean&nbsp;parentFirst)</span></div>
<div class="block">Creates an empty class loader. The classloader should be configured
 with path elements to specify where the loader is to look for
 classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - The parent classloader to which unsatisfied loading
               attempts are delegated. May be <code>null</code>,
               in which case the classloader which loaded this
               class is used as the parent.</dd>
<dd><code>parentFirst</code> - If <code>true</code>, indicates that the parent
                    classloader should be consulted before trying to
                    load the a class through this loader.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setProject(org.apache.tools.ant.Project)">
<h3>setProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProject</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Set the project associated with this class loader</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the project instance</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClassPath(org.apache.tools.ant.types.Path)">
<h3>setClassPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClassPath</span><wbr><span class="parameters">(<a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to search for classes to load. This should not be
 changed once the classloader starts to server classes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the search classpath consisting of directories and
        jar/zip files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParent(java.lang.ClassLoader)">
<h3>setParent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent)</span></div>
<div class="block">Set the parent for this class loader. This is the class loader to which
 this class loader will delegate to load classes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent class loader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParentFirst(boolean)">
<h3>setParentFirst</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParentFirst</span><wbr><span class="parameters">(boolean&nbsp;parentFirst)</span></div>
<div class="block">Control whether class lookup is delegated to the parent loader first
 or after this loader. Use with extreme caution. Setting this to
 false violates the class loader hierarchy and can lead to Linkage errors</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentFirst</code> - if true, delegate initial class search to the parent
                    classloader.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String,int)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 int&nbsp;priority)</span></div>
<div class="block">Logs a message through the project object if one has been provided.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - The message to log.
                Should not be <code>null</code>.</dd>
<dd><code>priority</code> - The logging priority of the message.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setThreadContextLoader()">
<h3>setThreadContextLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setThreadContextLoader</span>()</div>
<div class="block">Sets the current thread's context loader to this classloader, storing
 the current loader value for later resetting.</div>
</section>
</li>
<li>
<section class="detail" id="resetThreadContextLoader()">
<h3>resetThreadContextLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resetThreadContextLoader</span>()</div>
<div class="block">Resets the current thread's context loader to its original value.</div>
</section>
</li>
<li>
<section class="detail" id="addPathElement(java.lang.String)">
<h3>addPathElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPathElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pathElement)</span>
                    throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds an element to the classpath to be searched.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pathElement</code> - The path element to add. Must not be
                    <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the given path element cannot be resolved
                           against the project.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addPathComponent(java.io.File)">
<h3>addPathComponent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPathComponent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Add a path component.
 This simply adds the file, unlike addPathElement
 it does not open jar files and load files from
 their CLASSPATH entry in the manifest file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the jar file or directory to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addPathFile(java.io.File)">
<h3>addPathFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPathFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;pathComponent)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Add a file to the path.
 Reads the manifest, if available, and adds any additional class path jars
 specified in the manifest.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pathComponent</code> - the file which is to be added to the path for
                      this class loader</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if data needed from the file cannot be read.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">Returns the classpath this classloader will consult.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classpath used for this classloader, with elements
         separated by the path separator for the system.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setIsolated(boolean)">
<h3>setIsolated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIsolated</span><wbr><span class="parameters">(boolean&nbsp;isolated)</span></div>
<div class="block">Sets whether this classloader should run in isolated mode. In
 isolated mode, classes not found on the given classpath will
 not be referred to the parent class loader but will cause a
 ClassNotFoundException.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>isolated</code> - Whether or not this classloader should run in
                 isolated mode.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initializeClass(java.lang.Class)">
<h3>initializeClass</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initializeClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;theClass)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             Use Class.forName with initialize=true instead.</div>
</div>
<div class="block">Forces initialization of a class in a JDK 1.1 compatible, albeit hacky
 way.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>theClass</code> - The class to initialize.
                 Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSystemPackageRoot(java.lang.String)">
<h3>addSystemPackageRoot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSystemPackageRoot</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageRoot)</span></div>
<div class="block">Adds a package root to the list of packages which must be loaded on the
 parent loader.

 All subpackages are also included.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>packageRoot</code> - The root of all packages to be included.
                    Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addLoaderPackageRoot(java.lang.String)">
<h3>addLoaderPackageRoot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addLoaderPackageRoot</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageRoot)</span></div>
<div class="block">Adds a package root to the list of packages which must be loaded using
 this loader.

 All subpackages are also included.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>packageRoot</code> - The root of all packages to be included.
                    Should not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forceLoadClass(java.lang.String)">
<h3>forceLoadClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">forceLoadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span>
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block">Loads a class through this class loader even if that class is available
 on the parent classpath.

 This ensures that any classes which are loaded by the returned class
 will use this classloader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - The name of the class to be loaded.
                  Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the required Class object</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the requested class does not exist
                                   on this loader's classpath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="forceLoadSystemClass(java.lang.String)">
<h3>forceLoadSystemClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">forceLoadSystemClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span>
                              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block">Loads a class through this class loader but defer to the parent class
 loader.

 This ensures that instances of the returned class will be compatible
 with instances which have already been loaded on the parent
 loader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classname</code> - The name of the class to be loaded.
                  Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the required Class object</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the requested class does not exist
 on this loader's classpath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourceAsStream(java.lang.String)">
<h3>getResourceAsStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">getResourceAsStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Returns a stream to read the requested resource name.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getResourceAsStream(java.lang.String)" title="class or interface in java.lang" class="external-link">getResourceAsStream</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the resource for which a stream is required.
             Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a stream to the required resource or <code>null</code> if the
         resource cannot be found on the loader's classpath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResource(java.lang.String)">
<h3>getResource</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></span>&nbsp;<span class="element-name">getResource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Finds the resource with the given name. A resource is
 some data (images, audio, text, etc) that can be accessed by class
 code in a way that is independent of the location of the code.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getResource(java.lang.String)" title="class or interface in java.lang" class="external-link">getResource</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the resource for which a stream is required.
             Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a URL for reading the resource, or <code>null</code> if the
         resource could not be found or the caller doesn't have
         adequate privileges to get the resource.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNamedResources(java.lang.String)">
<h3>getNamedResources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</span>&nbsp;<span class="element-name">getNamedResources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Finds all the resources with the given name. A resource is some
 data (images, audio, text, etc) that can be accessed by class
 code in a way that is independent of the location of the code.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - name of the resource</dd>
<dt>Returns:</dt>
<dd>possible URLs as enumeration</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if something goes wrong</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#findResources(java.lang.String,boolean)"><code>findResources(String, boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findResource(java.lang.String)">
<h3>findResource</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></span>&nbsp;<span class="element-name">findResource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Finds the resource with the given name.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findResource(java.lang.String)" title="class or interface in java.lang" class="external-link">findResource</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The resource name</dd>
<dt>Returns:</dt>
<dd>A <code>URL</code> object for reading the resource, or <code>null</code> if the
     resource could not be found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findResources(java.lang.String)">
<h3>findResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</span>&nbsp;<span class="element-name">findResources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findResources(java.lang.String)" title="class or interface in java.lang" class="external-link">findResources</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The resource name to search for.
             Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an enumeration of URLs for the resources</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if I/O errors occurs (can't happen)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findResources(java.lang.String,boolean)">
<h3>findResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</span>&nbsp;<span class="element-name">findResources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;skipParent)</span>
                                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The resource name to search for.
             Must not be <code>null</code>.</dd>
<dd><code>skipParent</code> - whether to skip searching the parent first - will be false if the method is
     invoked from <a href="#getResources(java.lang.String)"><code>getResources(String)</code></a> or <a href="#getNamedResources(java.lang.String)"><code>getNamedResources(String)</code></a> and true
     if the method is invoked from <a href="#findResources(java.lang.String)"><code>findResources(String)</code></a>.</dd>
<dt>Returns:</dt>
<dd>an enumeration of URLs for the resources</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if I/O errors occurs (can't happen)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourceURL(java.io.File,java.lang.String)">
<h3>getResourceURL</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a></span>&nbsp;<span class="element-name">getResourceURL</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resourceName)</span></div>
<div class="block">Returns the URL of a given resource in the given file which may
 either be a directory or a zip file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - The file (directory or jar) in which to search for
             the resource. Must not be <code>null</code>.</dd>
<dd><code>resourceName</code> - The name of the resource for which a stream
                     is required. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a stream to the required resource or <code>null</code> if the
         resource cannot be found in the given file object.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadClass(java.lang.String,boolean)">
<h3>loadClass</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 boolean&nbsp;resolve)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block">Loads a class with this class loader.

 This class attempts to load the class in an order determined by whether
 or not the class matches the system/loader package lists, with the
 loader package list taking priority. If the classloader is in isolated
 mode, failure to load the class in this loader will result in a
 ClassNotFoundException.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#loadClass(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">loadClass</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>classname</code> - The name of the class to be loaded.
                  Must not be <code>null</code>.</dd>
<dd><code>resolve</code> - <code>true</code> if all classes upon which this class
                depends are to be loaded.</dd>
<dt>Returns:</dt>
<dd>the required Class object</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the requested class does not exist
 on the system classpath (when not in isolated mode) or this loader's
 classpath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="defineClassFromData(java.io.File,byte[],java.lang.String)">
<h3>defineClassFromData</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">defineClassFromData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 byte[]&nbsp;classData,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname)</span>
                                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Define a class given its bytes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>container</code> - the container from which the class data has been read
                  may be a directory or a jar/zip file.</dd>
<dd><code>classData</code> - the bytecode data for the class</dd>
<dd><code>classname</code> - the name of the class</dd>
<dt>Returns:</dt>
<dd>the Class instance created from the given data</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the class data cannot be read.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="definePackage(java.io.File,java.lang.String)">
<h3>definePackage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">definePackage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Define the package information associated with a class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>container</code> - the file containing the class definition.</dd>
<dd><code>className</code> - the class name of for which the package information
        is to be determined.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the package information cannot be read from the
            container.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="definePackage(java.io.File,java.lang.String,java.util.jar.Manifest)">
<h3>definePackage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">definePackage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;container,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;packageName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</span></div>
<div class="block">Define the package information when the class comes from a
 jar with a manifest</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>container</code> - the jar file containing the manifest</dd>
<dd><code>packageName</code> - the name of the package being defined.</dd>
<dd><code>manifest</code> - the jar's manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findClass(java.lang.String)">
<h3>findClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">findClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block">Searches for and load a class on the classpath of this class loader.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#findClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findClass</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the class to be loaded. Must not be
             <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the required Class object</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the requested class does not exist
                                   on this loader's classpath.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isInPath(java.io.File)">
<h3>isInPath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isInPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;component)</span></div>
<div class="block">Indicate if the given file is in this loader's path</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>component</code> - the file which is to be checked</dd>
<dt>Returns:</dt>
<dd>true if the file is in the class path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanup()">
<h3>cleanup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanup</span>()</div>
<div class="block">Cleans up any resources held by this classloader. Any open archive
 files are closed.</div>
</section>
</li>
<li>
<section class="detail" id="getConfiguredParent()">
<h3>getConfiguredParent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getConfiguredParent</span>()</div>
<div class="block">Gets the parent as has been specified in the constructor or via
 setParent.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>classloader</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildStarted(org.apache.tools.ant.BuildEvent)">
<h3>buildStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the buildStarted event</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="buildFinished(org.apache.tools.ant.BuildEvent)">
<h3>buildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">buildFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Cleans up any resources held by this classloader at the end
 of a build.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the buildFinished event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subBuildFinished(org.apache.tools.ant.BuildEvent)">
<h3>subBuildFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">subBuildFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Cleans up any resources held by this classloader at the end of
 a subbuild if it has been created for the subbuild's project
 instance.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SubBuildListener.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</a></code>&nbsp;in interface&nbsp;<code><a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the buildFinished event</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="subBuildStarted(org.apache.tools.ant.BuildEvent)">
<h3>subBuildStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">subBuildStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SubBuildListener.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</a></code>&nbsp;in interface&nbsp;<code><a href="SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the buildStarted event</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetStarted(org.apache.tools.ant.BuildEvent)">
<h3>targetStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the targetStarted event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTarget()"><code>BuildEvent.getTarget()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="targetFinished(org.apache.tools.ant.BuildEvent)">
<h3>targetFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">targetFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the targetFinished event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskStarted(org.apache.tools.ant.BuildEvent)">
<h3>taskStarted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskStarted</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the taskStarted event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getTask()"><code>BuildEvent.getTask()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="taskFinished(org.apache.tools.ant.BuildEvent)">
<h3>taskFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">taskFinished</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the taskFinished event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="messageLogged(org.apache.tools.ant.BuildEvent)">
<h3>messageLogged</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">messageLogged</span><wbr><span class="parameters">(<a href="BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</a>&nbsp;event)</span></div>
<div class="block">Empty implementation to satisfy the BuildListener interface.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a></code>&nbsp;in interface&nbsp;<code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>event</code> - the messageLogged event</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BuildEvent.html#getMessage()"><code>BuildEvent.getMessage()</code></a></li>
<li><a href="BuildEvent.html#getException()"><code>BuildEvent.getException()</code></a></li>
<li><a href="BuildEvent.html#getPriority()"><code>BuildEvent.getPriority()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addJavaLibraries()">
<h3>addJavaLibraries</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addJavaLibraries</span>()</div>
<div class="block">add any libraries that come with different java versions
 here</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Returns a <code>String</code> representing this loader.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>the path that this classloader has.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResources(java.lang.String)">
<h3>getResources</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/net/URL.html" title="class or interface in java.net" class="external-link">URL</a>&gt;</span>&nbsp;<span class="element-name">getResources</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html#getResources(java.lang.String)" title="class or interface in java.lang" class="external-link">getResources</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="newAntClassLoader(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)">
<h3>newAntClassLoader</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></span>&nbsp;<span class="element-name">newAntClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 boolean&nbsp;parentFirst)</span></div>
<div class="block">Factory method</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - ClassLoader</dd>
<dd><code>project</code> - Project</dd>
<dd><code>path</code> - Path</dd>
<dd><code>parentFirst</code> - boolean</dd>
<dt>Returns:</dt>
<dd>AntClassLoader</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
