<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ResourceDecorator (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources, class: ResourceDecorator">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resources</a></div>
<h1 title="Class ResourceDecorator" class="title">Class ResourceDecorator</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../Resource.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.Resource</a>
<div class="inheritance">org.apache.tools.ant.types.resources.ResourceDecorator</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ContentTransformingResource.html" title="class in org.apache.tools.ant.types.resources">ContentTransformingResource</a></code>, <code><a href="MappedResource.html" title="class in org.apache.tools.ant.types.resources">MappedResource</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ResourceDecorator</span>
<span class="extends-implements">extends <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></span></div>
<div class="block">Abstract class that delegates all reading methods of Resource to
 its wrapped resource and deals with reference handling.

 <p>Overwrites all setters to throw exceptions.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.Resource">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#MAGIC">MAGIC</a>, <a href="../Resource.html#UNKNOWN_DATETIME">UNKNOWN_DATETIME</a>, <a href="../Resource.html#UNKNOWN_SIZE">UNKNOWN_SIZE</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ResourceDecorator</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">no arg constructor</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">ResourceDecorator</a><wbr>(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor with another resource to wrap.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfigured(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">addConfigured</a><wbr>(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the resource to wrap using a single-element collection.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;T</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#as(java.lang.Class)" class="member-name-link">as</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a view of this resource that implements the interface
 given as the argument or null if there is no such view.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compareTo(org.apache.tools.ant.types.Resource)" class="member-name-link">compareTo</a><wbr>(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;other)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delegates to a comparison of names.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stack,
 <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputStream()" class="member-name-link">getInputStream</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an InputStream for the Resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLastModified()" class="member-name-link">getLastModified</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells the modification time in milliseconds since 01.01.1970 .</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of the resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputStream()" class="member-name-link">getOutputStream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an OutputStream for the Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResource()" class="member-name-link">getResource</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">De-references refids if any, ensures a wrapped resource has
 been specified.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the size of this Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the hash code for this Resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells if the resource is a directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isExists()" class="member-name-link">isExists</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The exists attribute tells whether a file exists.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFilesystemOnly()" class="member-name-link">isFilesystemOnly</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fulfill the ResourceCollection contract.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDirectory(boolean)" class="member-name-link">setDirectory</a><wbr>(boolean&nbsp;directory)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override setDirectory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExists(boolean)" class="member-name-link">setExists</a><wbr>(boolean&nbsp;exists)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the exists attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLastModified(long)" class="member-name-link">setLastModified</a><wbr>(long&nbsp;lastmodified)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override setLastModified.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setName(java.lang.String)" class="member-name-link">setName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overridden, not allowed to set the name of the resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefid(org.apache.tools.ant.types.Reference)" class="member-name-link">setRefid</a><wbr>(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overrides the base version.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(long)" class="member-name-link">setSize</a><wbr>(long&nbsp;size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override setSize.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.Resource">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#asOptional(java.lang.Class)">asOptional</a>, <a href="../Resource.html#clone()">clone</a>, <a href="../Resource.html#equals(java.lang.Object)">equals</a>, <a href="../Resource.html#getMagicNumber(byte%5B%5D)">getMagicNumber</a>, <a href="../Resource.html#getRef()">getRef</a>, <a href="../Resource.html#iterator()">iterator</a>, <a href="../Resource.html#size()">size</a>, <a href="../Resource.html#toLongString()">toLongString</a>, <a href="../Resource.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ResourceDecorator</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ResourceDecorator</span>()</div>
<div class="block">no arg constructor</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.ResourceCollection)">
<h3>ResourceDecorator</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ResourceDecorator</span><wbr><span class="parameters">(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;other)</span></div>
<div class="block">Constructor with another resource to wrap.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the resource to wrap.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addConfigured(org.apache.tools.ant.types.ResourceCollection)">
<h3>addConfigured</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfigured</span><wbr><span class="parameters">(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;a)</span></div>
<div class="block">Sets the resource to wrap using a single-element collection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - the resource to wrap as a single element Resource collection.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">Get the name of the resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getName()">getName</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>the name of the wrapped resource.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isExists()">
<h3>isExists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isExists</span>()</div>
<div class="block">The exists attribute tells whether a file exists.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#isExists()">isExists</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>true if this resource exists.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLastModified()">
<h3>getLastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getLastModified</span>()</div>
<div class="block">Tells the modification time in milliseconds since 01.01.1970 .</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getLastModified()">getLastModified</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>0 if the resource does not exist to mirror the behavior
 of <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link"><code>File</code></a>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<div class="block">Tells if the resource is a directory.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#isDirectory()">isDirectory</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>boolean flag indicating if the resource is a directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getSize</span>()</div>
<div class="block">Get the size of this Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getSize()">getSize</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>the size, as a long, 0 if the Resource does not exist (for
         compatibility with java.io.File), or UNKNOWN_SIZE if not known.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInputStream()">
<h3>getInputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">getInputStream</span>()
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get an InputStream for the Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getInputStream()">getInputStream</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>an InputStream containing this Resource's content.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if unable to provide the content of this
         Resource as a stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/UnsupportedOperationException.html" title="class or interface in java.lang" class="external-link">UnsupportedOperationException</a></code> - if InputStreams are not
         supported for this Resource type.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputStream()">
<h3>getOutputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></span>&nbsp;<span class="element-name">getOutputStream</span>()
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get an OutputStream for the Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#getOutputStream()">getOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>an OutputStream to which content can be written.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if unable to provide the content of this
         Resource as a stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/UnsupportedOperationException.html" title="class or interface in java.lang" class="external-link">UnsupportedOperationException</a></code> - if OutputStreams are not
         supported for this Resource type.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFilesystemOnly()">
<h3>isFilesystemOnly</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFilesystemOnly</span>()</div>
<div class="block">Fulfill the ResourceCollection contract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</a></code>&nbsp;in interface&nbsp;<code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#isFilesystemOnly()">isFilesystemOnly</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>whether this Resource is a FileProvider.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRefid(org.apache.tools.ant.types.Reference)">
<h3>setRefid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefid</span><wbr><span class="parameters">(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Overrides the base version.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - the Reference to set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="as(java.lang.Class)">
<h3>as</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">as</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz)</span></div>
<div class="block">Returns a view of this resource that implements the interface
 given as the argument or null if there is no such view.

 <p>This allows extension interfaces to be added to resources
 without growing the number of permutations of interfaces
 decorators/adapters need to implement.</p>

 <p>This implementation of the method will return the current
 instance itself if it can be assigned to the given class.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#as(java.lang.Class)">as</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Type Parameters:</dt>
<dd><code>T</code> - desired type</dd>
<dt>Parameters:</dt>
<dd><code>clazz</code> - a class</dd>
<dt>Returns:</dt>
<dd>resource of a desired type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="compareTo(org.apache.tools.ant.types.Resource)">
<h3>compareTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">compareTo</span><wbr><span class="parameters">(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;other)</span></div>
<div class="block">Delegates to a comparison of names.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html#compareTo(T)" title="class or interface in java.lang" class="external-link">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#compareTo(org.apache.tools.ant.types.Resource)">compareTo</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>other</code> - the object to compare to.</dd>
<dt>Returns:</dt>
<dd>a negative integer, zero, or a positive integer as this Resource
         is less than, equal to, or greater than the specified Resource.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<div class="block">Get the hash code for this Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#hashCode()">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Returns:</dt>
<dd>hash code as int.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResource()">
<h3>getResource</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></span>&nbsp;<span class="element-name">getResource</span>()</div>
<div class="block">De-references refids if any, ensures a wrapped resource has
 been specified.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Resource</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stack,
 <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span>
                               throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).

 <p>If one is included, throw a BuildException created by <a href="../DataType.html#circularReference()"><code>circularReference</code></a>.</p>

 <p>This implementation is appropriate only for a DataType that
 cannot hold other DataTypes as children.</p>

 <p>The general contract of this method is that it shouldn't do
 anything if <a href="../DataType.html#checked"><code>DataType.checked</code></a> is true and
 set it to true on exit.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a></code>&nbsp;in class&nbsp;<code><a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stack</code> - the stack of references to check.</dd>
<dd><code>project</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setName(java.lang.String)">
<h3>setName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Overridden, not allowed to set the name of the resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setName(java.lang.String)">setName</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - not used.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - always.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExists(boolean)">
<h3>setExists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExists</span><wbr><span class="parameters">(boolean&nbsp;exists)</span></div>
<div class="block">Set the exists attribute.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setExists(boolean)">setExists</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>exists</code> - if true, this resource exists.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLastModified(long)">
<h3>setLastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLastModified</span><wbr><span class="parameters">(long&nbsp;lastmodified)</span>
                     throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Override setLastModified.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setLastModified(long)">setLastModified</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>lastmodified</code> - not used.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - always.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDirectory(boolean)">
<h3>setDirectory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDirectory</span><wbr><span class="parameters">(boolean&nbsp;directory)</span>
                  throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Override setDirectory.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setDirectory(boolean)">setDirectory</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>directory</code> - not used.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - always.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSize(long)">
<h3>setSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(long&nbsp;size)</span>
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Override setSize.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Resource.html#setSize(long)">setSize</a></code>&nbsp;in class&nbsp;<code><a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></code></dd>
<dt>Parameters:</dt>
<dd><code>size</code> - not used.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - always.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
