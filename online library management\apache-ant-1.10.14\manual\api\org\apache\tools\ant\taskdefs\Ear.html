<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Ear (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Ear">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Ear" class="title">Class Ear</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance"><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Zip</a>
<div class="inheritance"><a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Jar</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Ear</div>
</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Ear</span>
<span class="extends-implements">extends <a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></span></div>
<div class="block">Creates a EAR archive. Based on WAR task</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.Jar">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></h2>
<code><a href="Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</a>, <a href="Jar.StrictMode.html" title="class in org.apache.tools.ant.taskdefs">Jar.StrictMode</a></code></div>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h2>
<code><a href="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</a>, <a href="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</a>, <a href="Zip.UnicodeExtraField.html" title="class in org.apache.tools.ant.taskdefs">Zip.UnicodeExtraField</a>, <a href="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</a>, <a href="Zip.Zip64ModeAttribute.html" title="class in org.apache.tools.ant.taskdefs">Zip.Zip64ModeAttribute</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h3>
<code><a href="Zip.html#addedDirs">addedDirs</a>, <a href="Zip.html#archiveType">archiveType</a>, <a href="Zip.html#doubleFilePass">doubleFilePass</a>, <a href="Zip.html#duplicate">duplicate</a>, <a href="Zip.html#emptyBehavior">emptyBehavior</a>, <a href="Zip.html#entries">entries</a>, <a href="Zip.html#skipWriting">skipWriting</a>, <a href="Zip.html#zipFile">zipFile</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Ear</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Create an Ear task.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addArchives(org.apache.tools.ant.types.ZipFileSet)" class="member-name-link">addArchives</a><wbr>(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;fs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds zipfileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanUp()" class="member-name-link">cleanUp</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make sure we don't think we already have a application.xml next
 time this task gets executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)" class="member-name-link">initZipOutputStream</a><wbr>(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialize the output stream.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAppxml(java.io.File)" class="member-name-link">setAppxml</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;descr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">File to incorporate as application.xml.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setEarfile(java.io.File)" class="member-name-link">setEarfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;earFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)" class="member-name-link">zipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overridden from Zip class to deal with application.xml</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.Jar">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></h3>
<code><a href="Jar.html#addConfiguredIndexJars(org.apache.tools.ant.types.Path)">addConfiguredIndexJars</a>, <a href="Jar.html#addConfiguredIndexJarsMapper(org.apache.tools.ant.types.Mapper)">addConfiguredIndexJarsMapper</a>, <a href="Jar.html#addConfiguredManifest(org.apache.tools.ant.taskdefs.Manifest)">addConfiguredManifest</a>, <a href="Jar.html#addConfiguredService(org.apache.tools.ant.types.spi.Service)">addConfiguredService</a>, <a href="Jar.html#addMetainf(org.apache.tools.ant.types.ZipFileSet)">addMetainf</a>, <a href="Jar.html#createEmptyZip(java.io.File)">createEmptyZip</a>, <a href="Jar.html#finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)">finalizeZipOutputStream</a>, <a href="Jar.html#findJarName(java.lang.String,java.lang.String%5B%5D)">findJarName</a>, <a href="Jar.html#getIndexJarsMapper()">getIndexJarsMapper</a>, <a href="Jar.html#getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)">getResourcesToAdd</a>, <a href="Jar.html#grabFilesAndDirs(java.lang.String,java.util.List,java.util.List)">grabFilesAndDirs</a>, <a href="Jar.html#reset()">reset</a>, <a href="Jar.html#setFilesetmanifest(org.apache.tools.ant.taskdefs.Jar.FilesetManifestConfig)">setFilesetmanifest</a>, <a href="Jar.html#setFlattenAttributes(boolean)">setFlattenAttributes</a>, <a href="Jar.html#setIndex(boolean)">setIndex</a>, <a href="Jar.html#setIndexMetaInf(boolean)">setIndexMetaInf</a>, <a href="Jar.html#setJarfile(java.io.File)">setJarfile</a>, <a href="Jar.html#setManifest(java.io.File)">setManifest</a>, <a href="Jar.html#setManifestEncoding(java.lang.String)">setManifestEncoding</a>, <a href="Jar.html#setMergeClassPathAttributes(boolean)">setMergeClassPathAttributes</a>, <a href="Jar.html#setStrict(org.apache.tools.ant.taskdefs.Jar.StrictMode)">setStrict</a>, <a href="Jar.html#setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">setWhenempty</a>, <a href="Jar.html#setWhenmanifestonly(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">setWhenmanifestonly</a>, <a href="Jar.html#writeIndexLikeList(java.util.List,java.util.List,java.io.PrintWriter)">writeIndexLikeList</a>, <a href="Jar.html#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int)">zipFile</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.Zip">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></h3>
<code><a href="Zip.html#add(org.apache.tools.ant.types.ResourceCollection)">add</a>, <a href="Zip.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="Zip.html#addParentDirs(java.io.File,java.lang.String,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">addParentDirs</a>, <a href="Zip.html#addResources(org.apache.tools.ant.types.FileSet,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)">addResources</a>, <a href="Zip.html#addResources(org.apache.tools.ant.types.ResourceCollection,org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.zip.ZipOutputStream)">addResources</a>, <a href="Zip.html#addZipfileset(org.apache.tools.ant.types.ZipFileSet)">addZipfileset</a>, <a href="Zip.html#addZipGroupFileset(org.apache.tools.ant.types.FileSet)">addZipGroupFileset</a>, <a href="Zip.html#execute()">execute</a>, <a href="Zip.html#executeMain()">executeMain</a>, <a href="Zip.html#getComment()">getComment</a>, <a href="Zip.html#getCreateUnicodeExtraFields()">getCreateUnicodeExtraFields</a>, <a href="Zip.html#getCurrentExtraFields()">getCurrentExtraFields</a>, <a href="Zip.html#getDestFile()">getDestFile</a>, <a href="Zip.html#getEncoding()">getEncoding</a>, <a href="Zip.html#getFallBackToUTF8()">getFallBackToUTF8</a>, <a href="Zip.html#getLevel()">getLevel</a>, <a href="Zip.html#getModificationtime()">getModificationtime</a>, <a href="Zip.html#getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection%5B%5D,java.io.File,boolean)">getNonFileSetResourcesToAdd</a>, <a href="Zip.html#getPreserve0Permissions()">getPreserve0Permissions</a>, <a href="Zip.html#getResourcesToAdd(org.apache.tools.ant.types.FileSet%5B%5D,java.io.File,boolean)">getResourcesToAdd</a>, <a href="Zip.html#getUseLanguageEnodingFlag()">getUseLanguageEnodingFlag</a>, <a href="Zip.html#getZip64Mode()">getZip64Mode</a>, <a href="Zip.html#grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection%5B%5D)">grabNonFileSetResources</a>, <a href="Zip.html#grabResources(org.apache.tools.ant.types.FileSet%5B%5D)">grabResources</a>, <a href="Zip.html#hasUpdatedFile()">hasUpdatedFile</a>, <a href="Zip.html#isAddingNewFiles()">isAddingNewFiles</a>, <a href="Zip.html#isCompress()">isCompress</a>, <a href="Zip.html#isEmpty(org.apache.tools.ant.types.Resource%5B%5D%5B%5D)">isEmpty</a>, <a href="Zip.html#isFirstPass()">isFirstPass</a>, <a href="Zip.html#isInUpdateMode()">isInUpdateMode</a>, <a href="Zip.html#logWhenWriting(java.lang.String,int)">logWhenWriting</a>, <a href="Zip.html#selectDirectoryResources(org.apache.tools.ant.types.Resource%5B%5D)">selectDirectoryResources</a>, <a href="Zip.html#selectFileResources(org.apache.tools.ant.types.Resource%5B%5D)">selectFileResources</a>, <a href="Zip.html#selectResources(org.apache.tools.ant.types.Resource%5B%5D,org.apache.tools.ant.types.resources.selectors.ResourceSelector)">selectResources</a>, <a href="Zip.html#setBasedir(java.io.File)">setBasedir</a>, <a href="Zip.html#setComment(java.lang.String)">setComment</a>, <a href="Zip.html#setCompress(boolean)">setCompress</a>, <a href="Zip.html#setCreateUnicodeExtraFields(org.apache.tools.ant.taskdefs.Zip.UnicodeExtraField)">setCreateUnicodeExtraFields</a>, <a href="Zip.html#setCurrentExtraFields(org.apache.tools.zip.ZipExtraField%5B%5D)">setCurrentExtraFields</a>, <a href="Zip.html#setDestFile(java.io.File)">setDestFile</a>, <a href="Zip.html#setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)">setDuplicate</a>, <a href="Zip.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="Zip.html#setFallBackToUTF8(boolean)">setFallBackToUTF8</a>, <a href="Zip.html#setFile(java.io.File)">setFile</a>, <a href="Zip.html#setFilesonly(boolean)">setFilesonly</a>, <a href="Zip.html#setKeepCompression(boolean)">setKeepCompression</a>, <a href="Zip.html#setLevel(int)">setLevel</a>, <a href="Zip.html#setModificationtime(java.lang.String)">setModificationtime</a>, <a href="Zip.html#setPreserve0Permissions(boolean)">setPreserve0Permissions</a>, <a href="Zip.html#setRoundUp(boolean)">setRoundUp</a>, <a href="Zip.html#setUpdate(boolean)">setUpdate</a>, <a href="Zip.html#setUseLanguageEncodingFlag(boolean)">setUseLanguageEncodingFlag</a>, <a href="Zip.html#setZip64Mode(org.apache.tools.ant.taskdefs.Zip.Zip64ModeAttribute)">setZip64Mode</a>, <a href="Zip.html#setZipfile(java.io.File)">setZipfile</a>, <a href="Zip.html#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">zipDir</a>, <a href="Zip.html#zipDir(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipDir</a>, <a href="Zip.html#zipDir(org.apache.tools.ant.types.Resource,org.apache.tools.zip.ZipOutputStream,java.lang.String,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipDir</a>, <a href="Zip.html#zipFile(java.io.InputStream,org.apache.tools.zip.ZipOutputStream,java.lang.String,long,java.io.File,int,org.apache.tools.zip.ZipExtraField%5B%5D)">zipFile</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Ear</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Ear</span>()</div>
<div class="block">Create an Ear task.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setEarfile(java.io.File)">
<h3>setEarfile</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEarfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;earFile)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use setDestFile(destfile) instead.</div>
</div>
<div class="block">Set the destination file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>earFile</code> - the destination file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAppxml(java.io.File)">
<h3>setAppxml</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAppxml</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;descr)</span></div>
<div class="block">File to incorporate as application.xml.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>descr</code> - the descriptor file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addArchives(org.apache.tools.ant.types.ZipFileSet)">
<h3>addArchives</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addArchives</span><wbr><span class="parameters">(<a href="../types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</a>&nbsp;fs)</span></div>
<div class="block">Adds zipfileset.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fs</code> - zipfileset to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">
<h3>initZipOutputStream</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initZipOutputStream</span><wbr><span class="parameters">(<a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Initialize the output stream.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Jar.html#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">initZipOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></code></dd>
<dt>Parameters:</dt>
<dd><code>zOut</code> - the zip output stream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O errors</dd>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">
<h3>zipFile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">zipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file,
 <a href="../../zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a>&nbsp;zOut,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;vPath,
 int&nbsp;mode)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Overridden from Zip class to deal with application.xml</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Zip.html#zipFile(java.io.File,org.apache.tools.zip.ZipOutputStream,java.lang.String,int)">zipFile</a></code>&nbsp;in class&nbsp;<code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
<dt>Parameters:</dt>
<dd><code>file</code> - the file to add to the archive</dd>
<dd><code>zOut</code> - the stream to write to</dd>
<dd><code>vPath</code> - the name this entry shall have in the archive</dd>
<dd><code>mode</code> - the Unix permissions to set.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanUp()">
<h3>cleanUp</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanUp</span>()</div>
<div class="block">Make sure we don't think we already have a application.xml next
 time this task gets executed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Jar.html#cleanUp()">cleanUp</a></code>&nbsp;in class&nbsp;<code><a href="Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</a></code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="Zip.html#cleanUp()"><code>Zip.cleanUp()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
