<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
  <head>
    <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
    <title>WLJSPC Task</title>
  </head>
<body>
<h1>wljspc</h1>
<h3>Description</h3>
<p>Class to precompile JSP's using WebLogic JSP compiler (<code>weblogic.jspc</code>)</p>
<p>Tested only on WebLogic 4.5.1&mdash;NT 4.0 and Solaris 7 &amp; 8</p>
<h3>Parameters</h3>
<table class="attr">
  <thead>
    <tr>
      <th scope="col">Attribute</th>
      <th scope="col">Values</th>
      <th scope="col">Required</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>src</td>
      <td>root of source tree for JSP, ie, the document root for your WebLogic server</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>dest</td>
      <td>root of destination directory, what you have set as <code>WorkingDir</code> in the
        WebLogic properties</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>package</td>
      <td>start package name under which your JSPs would be compiled</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>classpath</td>
      <td>Class path to use when compiling JSPs</td>
      <td>Yes</td>
    </tr>
  </tbody>
</table>
<p>A classpath should be set which contains the WebLogic classes as well as all application classes
referenced by JSP. The system classpath is also appended when the <code>jspc</code> is called, so
you may choose to put everything in the classpath while calling Apache Ant. However, since
presumably JSPs will reference classes being build by Ant, it would be better to explicitly add the
classpath in the task.</p>
<p>The task checks timestamps on the JSP's and the generated classes, and compiles only those files
that have changed.</p>
<p>It follows the WebLogic naming convention of putting classes in <samp>_dirName/_fileName.class
for dirname/fileName.jsp</samp></p>
<h3>Example</h3>
<pre>
&lt;target name="jspcompile" depends="compile"&gt;
  &lt;wljspc src="c:\\weblogic\\myserver\\public_html" dest="c:\\weblogic\\myserver\\serverclasses" package="myapp.jsp"&gt;
    &lt;classpath&gt;
      &lt;pathelement location="${weblogic.classpath}"/&gt;
      &lt;pathelement path="${compile.dest}"/&gt;
    &lt;/classpath&gt;
  &lt;/wljspc&gt;
&lt;/target&gt;</pre>

<h3>Limitations</h3>
<ul>
  <li>This works only on WebLogic 4.5.1</li>
  <li>It compiles the files through the Classic compiler only.</li>
  <li>Since it is my experience that <code>weblogic.jspc</code> throws out of memory error on being
    given too many files at one go, it is called multiple times with one JSP file each.</li>
</ul>

</body>
</html>
