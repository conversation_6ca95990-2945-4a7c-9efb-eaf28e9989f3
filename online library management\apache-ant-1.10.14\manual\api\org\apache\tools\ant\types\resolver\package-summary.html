<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.types.resolver (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resolver">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li><a href="#package-description">Description</a></li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li><a href="#package-description">Description</a>&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.types.resolver" class="title">Package org.apache.tools.ant.types.resolver</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.types.resolver</span></div>
<section class="package-description" id="package-description">
<div class="block">Ant integration with xml-commons resolver.

<p>These classes enhance the <code>&lt;xmlcatalog&gt;</code> datatype
to support external catalog files using the xml-commons resolver, in
accordance with the
<a href="https://oasis-open.org/committees/entity/spec-2001-08-06.html">
OASIS "Open Catalog" standard</a>.  They will be used if and only if
the xml-commons resolver library is available on the classpath.</p></div>
<dl class="notes">
<dt>Author:</dt>
<dd><a href="mailto:<EMAIL>">Craeg Strong</a></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://xml.apache.org/commons">Apache xml-commons Project</a></li>
<li><a href="../XMLCatalog.html" title="class in org.apache.tools.ant.types"><code>XMLCatalog</code></a></li>
<li><a href="ApacheCatalogResolver.html" title="class in org.apache.tools.ant.types.resolver"><code>ApacheCatalogResolver</code></a></li>
<li><a href="ApacheCatalog.html" title="class in org.apache.tools.ant.types.resolver"><code>ApacheCatalog</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.types</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../mappers/package-summary.html">org.apache.tools.ant.types.mappers</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../optional/package-summary.html">org.apache.tools.ant.types.optional</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../resources/package-summary.html">org.apache.tools.ant.types.resources</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../selectors/package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../spi/package-summary.html">org.apache.tools.ant.types.spi</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ApacheCatalog.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalog</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class extends the Catalog class provided by Norman Walsh's
 resolver library in xml-commons in order to add classpath entity
 and URI resolution.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ApacheCatalogResolver.html" title="class in org.apache.tools.ant.types.resolver">ApacheCatalogResolver</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This class extends the CatalogResolver class provided by Norman
 Walsh's resolver library in xml-commons.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
