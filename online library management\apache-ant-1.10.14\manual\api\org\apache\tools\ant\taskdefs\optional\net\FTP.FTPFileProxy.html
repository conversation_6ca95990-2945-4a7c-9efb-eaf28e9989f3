<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>FTP.FTPFileProxy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.net, class: FTP, class: FTPFileProxy">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.net</a></div>
<h1 title="Class FTP.FTPFileProxy" class="title">Class FTP.FTPFileProxy</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">java.io.File</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.net.FTP.FTPFileProxy</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="FTP.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">protected static class </span><span class="element-name type-name-label">FTP.FTPFileProxy</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span></div>
<div class="block">internal class providing a File-like interface to some of the information
 available from the FTP server</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../serialized-form.html#org.apache.tools.ant.taskdefs.optional.net.FTP.FTPFileProxy">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.File">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#pathSeparator" title="class or interface in java.io" class="external-link">pathSeparator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#pathSeparatorChar" title="class or interface in java.io" class="external-link">pathSeparatorChar</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#separator" title="class or interface in java.io" class="external-link">separator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#separatorChar" title="class or interface in java.io" class="external-link">separatorChar</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String)" class="member-name-link">FTPFileProxy</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;completePath)</code></div>
<div class="col-last even-row-color">
<div class="block">creates a proxy to a FTP directory</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.commons.net.ftp.FTPFile)" class="member-name-link">FTPFileProxy</a><wbr>(org.apache.commons.net.ftp.FTPFile&nbsp;file)</code></div>
<div class="col-last odd-row-color">
<div class="block">creates a proxy to a FTP file</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exists()" class="member-name-link">exists</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsolutePath()" class="member-name-link">getAbsolutePath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParent()" class="member-name-link">getParent</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPath()" class="member-name-link">getPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAbsolute()" class="member-name-link">isAbsolute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">FTP files are stored as absolute paths</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFile()" class="member-name-link">isFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isHidden()" class="member-name-link">isHidden</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">FTP files cannot be hidden</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#lastModified()" class="member-name-link">lastModified</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#length()" class="member-name-link">length</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.File">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#canExecute()" title="class or interface in java.io" class="external-link">canExecute</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#canRead()" title="class or interface in java.io" class="external-link">canRead</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#canWrite()" title="class or interface in java.io" class="external-link">canWrite</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#compareTo(java.io.File)" title="class or interface in java.io" class="external-link">compareTo</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#createNewFile()" title="class or interface in java.io" class="external-link">createNewFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#createTempFile(java.lang.String,java.lang.String)" title="class or interface in java.io" class="external-link">createTempFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#createTempFile(java.lang.String,java.lang.String,java.io.File)" title="class or interface in java.io" class="external-link">createTempFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#delete()" title="class or interface in java.io" class="external-link">delete</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#deleteOnExit()" title="class or interface in java.io" class="external-link">deleteOnExit</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#equals(java.lang.Object)" title="class or interface in java.io" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getAbsoluteFile()" title="class or interface in java.io" class="external-link">getAbsoluteFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getCanonicalFile()" title="class or interface in java.io" class="external-link">getCanonicalFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getCanonicalPath()" title="class or interface in java.io" class="external-link">getCanonicalPath</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getFreeSpace()" title="class or interface in java.io" class="external-link">getFreeSpace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getParentFile()" title="class or interface in java.io" class="external-link">getParentFile</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getTotalSpace()" title="class or interface in java.io" class="external-link">getTotalSpace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getUsableSpace()" title="class or interface in java.io" class="external-link">getUsableSpace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#hashCode()" title="class or interface in java.io" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#list()" title="class or interface in java.io" class="external-link">list</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#list(java.io.FilenameFilter)" title="class or interface in java.io" class="external-link">list</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#listFiles()" title="class or interface in java.io" class="external-link">listFiles</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#listFiles(java.io.FileFilter)" title="class or interface in java.io" class="external-link">listFiles</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#listFiles(java.io.FilenameFilter)" title="class or interface in java.io" class="external-link">listFiles</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#listRoots()" title="class or interface in java.io" class="external-link">listRoots</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#mkdir()" title="class or interface in java.io" class="external-link">mkdir</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#mkdirs()" title="class or interface in java.io" class="external-link">mkdirs</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#renameTo(java.io.File)" title="class or interface in java.io" class="external-link">renameTo</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setExecutable(boolean)" title="class or interface in java.io" class="external-link">setExecutable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setExecutable(boolean,boolean)" title="class or interface in java.io" class="external-link">setExecutable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setLastModified(long)" title="class or interface in java.io" class="external-link">setLastModified</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setReadable(boolean)" title="class or interface in java.io" class="external-link">setReadable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setReadable(boolean,boolean)" title="class or interface in java.io" class="external-link">setReadable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setReadOnly()" title="class or interface in java.io" class="external-link">setReadOnly</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setWritable(boolean)" title="class or interface in java.io" class="external-link">setWritable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#setWritable(boolean,boolean)" title="class or interface in java.io" class="external-link">setWritable</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#toPath()" title="class or interface in java.io" class="external-link">toPath</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#toString()" title="class or interface in java.io" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#toURI()" title="class or interface in java.io" class="external-link">toURI</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#toURL()" title="class or interface in java.io" class="external-link">toURL</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.commons.net.ftp.FTPFile)">
<h3>FTPFileProxy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FTPFileProxy</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPFile&nbsp;file)</span></div>
<div class="block">creates a proxy to a FTP file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - FTPFile</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String)">
<h3>FTPFileProxy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FTPFileProxy</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;completePath)</span></div>
<div class="block">creates a proxy to a FTP directory</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>completePath</code> - the remote directory.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="exists()">
<h3>exists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">exists</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#exists()" title="class or interface in java.io" class="external-link">exists</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsolutePath()">
<h3>getAbsolutePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getAbsolutePath</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getAbsolutePath()" title="class or interface in java.io" class="external-link">getAbsolutePath</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getName()" title="class or interface in java.io" class="external-link">getName</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getParent()">
<h3>getParent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getParent</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getParent()" title="class or interface in java.io" class="external-link">getParent</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPath()">
<h3>getPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPath</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#getPath()" title="class or interface in java.io" class="external-link">getPath</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAbsolute()">
<h3>isAbsolute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAbsolute</span>()</div>
<div class="block">FTP files are stored as absolute paths</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#isAbsolute()" title="class or interface in java.io" class="external-link">isAbsolute</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
<dt>Returns:</dt>
<dd>true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#isDirectory()" title="class or interface in java.io" class="external-link">isDirectory</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFile()">
<h3>isFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFile</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#isFile()" title="class or interface in java.io" class="external-link">isFile</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isHidden()">
<h3>isHidden</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isHidden</span>()</div>
<div class="block">FTP files cannot be hidden</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#isHidden()" title="class or interface in java.io" class="external-link">isHidden</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
<dt>Returns:</dt>
<dd>false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="lastModified()">
<h3>lastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">lastModified</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#lastModified()" title="class or interface in java.io" class="external-link">lastModified</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="length()">
<h3>length</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">length</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html#length()" title="class or interface in java.io" class="external-link">length</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
