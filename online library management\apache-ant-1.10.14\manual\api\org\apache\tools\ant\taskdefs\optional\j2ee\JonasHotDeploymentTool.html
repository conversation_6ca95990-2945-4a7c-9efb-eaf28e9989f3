<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>JonasHotDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.j2ee, class: JonasHotDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.j2ee</a></div>
<h1 title="Class JonasHotDeploymentTool" class="title">Class JonasHotDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">org.apache.tools.ant.taskdefs.optional.j2ee.AbstractHotDeploymentTool</a>
<div class="inheritance"><a href="GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">org.apache.tools.ant.taskdefs.optional.j2ee.GenericHotDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.j2ee.JonasHotDeploymentTool</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">JonasHotDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a>
implements <a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></span></div>
<div class="block">An Ant wrapper task for the weblogic.deploy tool. This is used
  to hot-deploy J2EE applications to a running WebLogic server.
  This is <b>not</b> the same as creating the application
  archive. This task assumes the archive (EAR, JAR, or WAR) file
  has been assembled and is supplied as the "source" attribute.
  <p>

  In the end, this task assembles the commandline parameters and
  runs the weblogic.deploy tool in a separate JVM.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee"><code>HotDeploymentTool</code></a></li>
<li><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee"><code>AbstractHotDeploymentTool</code></a></li>
<li><a href="ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee"><code>ServerDeploy</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_ORB" class="member-name-link">DEFAULT_ORB</a></code></div>
<div class="col-last even-row-color">
<div class="block">Description of the Field</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></h3>
<code><a href="HotDeploymentTool.html#ACTION_DELETE">ACTION_DELETE</a>, <a href="HotDeploymentTool.html#ACTION_DEPLOY">ACTION_DEPLOY</a>, <a href="HotDeploymentTool.html#ACTION_LIST">ACTION_LIST</a>, <a href="HotDeploymentTool.html#ACTION_UNDEPLOY">ACTION_UNDEPLOY</a>, <a href="HotDeploymentTool.html#ACTION_UPDATE">ACTION_UPDATE</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">JonasHotDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">gets the classpath field.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isActionValid()" class="member-name-link">isActionValid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Determines if the action supplied is valid.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDavidhost(java.lang.String)" class="member-name-link">setDavidhost</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the host for the David ORB; required if
  ORB==david.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDavidport(int)" class="member-name-link">setDavidport</a><wbr>(int&nbsp;inValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the port for the David ORB; required if
  ORB==david.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJonasroot(java.io.File)" class="member-name-link">setJonasroot</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the jonas root directory (-Dinstall.root=).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOrb(java.lang.String)" class="member-name-link">setOrb</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose your ORB : RMI, JEREMIE, DAVID, ...; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateAttributes()" class="member-name-link">validateAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Validates the passed in attributes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.GenericHotDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a></h3>
<code><a href="GenericHotDeploymentTool.html#createArg()">createArg</a>, <a href="GenericHotDeploymentTool.html#createJvmarg()">createJvmarg</a>, <a href="GenericHotDeploymentTool.html#deploy()">deploy</a>, <a href="GenericHotDeploymentTool.html#getClassName()">getClassName</a>, <a href="GenericHotDeploymentTool.html#getJava()">getJava</a>, <a href="GenericHotDeploymentTool.html#setClassName(java.lang.String)">setClassName</a>, <a href="GenericHotDeploymentTool.html#setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)">setTask</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.AbstractHotDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></h3>
<code><a href="AbstractHotDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="AbstractHotDeploymentTool.html#getPassword()">getPassword</a>, <a href="AbstractHotDeploymentTool.html#getServer()">getServer</a>, <a href="AbstractHotDeploymentTool.html#getTask()">getTask</a>, <a href="AbstractHotDeploymentTool.html#getUserName()">getUserName</a>, <a href="AbstractHotDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="AbstractHotDeploymentTool.html#setPassword(java.lang.String)">setPassword</a>, <a href="AbstractHotDeploymentTool.html#setServer(java.lang.String)">setServer</a>, <a href="AbstractHotDeploymentTool.html#setUserName(java.lang.String)">setUserName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.j2ee.<a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></h3>
<code><a href="HotDeploymentTool.html#deploy()">deploy</a>, <a href="HotDeploymentTool.html#setTask(org.apache.tools.ant.taskdefs.optional.j2ee.ServerDeploy)">setTask</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_ORB">
<h3>DEFAULT_ORB</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_ORB</span></div>
<div class="block">Description of the Field</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.j2ee.JonasHotDeploymentTool.DEFAULT_ORB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>JonasHotDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">JonasHotDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDavidhost(java.lang.String)">
<h3>setDavidhost</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDavidhost</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</span></div>
<div class="block">Set the host for the David ORB; required if
  ORB==david.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - The new davidhost value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDavidport(int)">
<h3>setDavidport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDavidport</span><wbr><span class="parameters">(int&nbsp;inValue)</span></div>
<div class="block">Set the port for the David ORB; required if
  ORB==david.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - The new davidport value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJonasroot(java.io.File)">
<h3>setJonasroot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJonasroot</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inValue)</span></div>
<div class="block">set the jonas root directory (-Dinstall.root=). This
  element is required.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - The new jonasroot value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOrb(java.lang.String)">
<h3>setOrb</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOrb</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</span></div>
<div class="block">Choose your ORB : RMI, JEREMIE, DAVID, ...; optional.
 If omitted, it defaults
 to the one present in classpath. The corresponding JOnAS JAR is
 automatically added to the classpath. If your orb is DAVID (RMI/IIOP) you must
 specify davidhost and davidport properties.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - RMI, JEREMIE, DAVID,...</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">gets the classpath field.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractHotDeploymentTool.html#getClasspath()">getClasspath</a></code>&nbsp;in class&nbsp;<code><a href="AbstractHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">AbstractHotDeploymentTool</a></code></dd>
<dt>Returns:</dt>
<dd>A Path representing the "classpath" attribute.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validateAttributes()">
<h3>validateAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateAttributes</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Validates the passed in attributes.

 <p>The rules are:</p>
 <ol>
    <li> If action is "deploy" or "update" the "application"
    and "source" attributes must be supplied.</li>
    <li> If action is "delete" or "undeploy" the
    "application" attribute must be supplied.</li>
 </ol></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="HotDeploymentTool.html#validateAttributes()">validateAttributes</a></code>&nbsp;in interface&nbsp;<code><a href="HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="GenericHotDeploymentTool.html#validateAttributes()">validateAttributes</a></code>&nbsp;in class&nbsp;<code><a href="GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something goes wrong</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isActionValid()">
<h3>isActionValid</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isActionValid</span>()</div>
<div class="block">Determines if the action supplied is valid. <p>

  Valid actions are contained in the static array
  VALID_ACTIONS</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericHotDeploymentTool.html#isActionValid()">isActionValid</a></code>&nbsp;in class&nbsp;<code><a href="GenericHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">GenericHotDeploymentTool</a></code></dd>
<dt>Returns:</dt>
<dd>true if the action attribute is valid, false if
      not.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
