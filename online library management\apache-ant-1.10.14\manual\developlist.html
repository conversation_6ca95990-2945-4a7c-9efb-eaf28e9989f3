<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css"/>
<title>Apache Ant User Manual</title>
<base target="mainFrame"/>
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Developing with Apache Ant</h3>

<ul class="inlinelist">
<li><a href="https://ant.apache.org/ant_in_anger.html" target="_top">Ant in Anger</a> <small>(online)</small></li>
<li><a href="https://ant.apache.org/ant_task_guidelines.html" target="_top">Ant Task Guidelines</a> <small>(online)</small></li>
<li><a href="develop.html#writingowntask">Writing Your Own Task</a></li>
<li><a href="base_task_classes.html">Tasks Designed for Extension</a></li>
<li><a href="develop.html#buildevents">Build Events</a></li>
<li><a href="develop.html#integration">Source-code Integration</a></li>
<li><a href="inputhandler.html">InputHandler</a></li>
<li><a href="antexternal.html">Using Ant Tasks Outside of Ant</a></li>
<li><a href="projecthelper.html">The Ant frontend: ProjectHelper</a></li>
<li><a href="argumentprocessor.html">The Command Line Processor Plugin: ArgumentProcessor</a></li>
</ul>

<h3>Tutorials</h3>
<ul class="inlinelist">
<li><a href="tutorial-HelloWorldWithAnt.html">Hello World with Ant</a></li>
<li><a href="tutorial-writing-tasks.html">Writing Tasks</a></li>
<li><a href="tutorial-tasks-filesets-properties.html">Tasks using Properties, Filesets &amp; Paths</a></li>
</ul>

</body>
</html>
