<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ChainableReader (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters, interface: ChainableReader">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.filters</a></div>
<h1 title="Interface ChainableReader" class="title">Interface ChainableReader</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="ClassConstants.html" title="class in org.apache.tools.ant.filters">ClassConstants</a></code>, <code><a href="ConcatFilter.html" title="class in org.apache.tools.ant.filters">ConcatFilter</a></code>, <code><a href="EscapeUnicode.html" title="class in org.apache.tools.ant.filters">EscapeUnicode</a></code>, <code><a href="ExpandProperties.html" title="class in org.apache.tools.ant.filters">ExpandProperties</a></code>, <code><a href="../taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</a></code>, <code><a href="FixCrLfFilter.html" title="class in org.apache.tools.ant.filters">FixCrLfFilter</a></code>, <code><a href="HeadFilter.html" title="class in org.apache.tools.ant.filters">HeadFilter</a></code>, <code><a href="LineContains.html" title="class in org.apache.tools.ant.filters">LineContains</a></code>, <code><a href="LineContainsRegExp.html" title="class in org.apache.tools.ant.filters">LineContainsRegExp</a></code>, <code><a href="Native2AsciiFilter.html" title="class in org.apache.tools.ant.filters">Native2AsciiFilter</a></code>, <code><a href="PrefixLines.html" title="class in org.apache.tools.ant.filters">PrefixLines</a></code>, <code><a href="ReplaceTokens.html" title="class in org.apache.tools.ant.filters">ReplaceTokens</a></code>, <code><a href="../types/optional/ScriptFilter.html" title="class in org.apache.tools.ant.types.optional">ScriptFilter</a></code>, <code><a href="SortFilter.html" title="class in org.apache.tools.ant.filters">SortFilter</a></code>, <code><a href="StripJavaComments.html" title="class in org.apache.tools.ant.filters">StripJavaComments</a></code>, <code><a href="StripLineBreaks.html" title="class in org.apache.tools.ant.filters">StripLineBreaks</a></code>, <code><a href="StripLineComments.html" title="class in org.apache.tools.ant.filters">StripLineComments</a></code>, <code><a href="SuffixLines.html" title="class in org.apache.tools.ant.filters">SuffixLines</a></code>, <code><a href="TabsToSpaces.html" title="class in org.apache.tools.ant.filters">TabsToSpaces</a></code>, <code><a href="TailFilter.html" title="class in org.apache.tools.ant.filters">TailFilter</a></code>, <code><a href="TokenFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter</a></code>, <code><a href="TokenFilter.ChainableReaderFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a></code>, <code><a href="TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a></code>, <code><a href="TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a></code>, <code><a href="TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a></code>, <code><a href="TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a></code>, <code><a href="TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a></code>, <code><a href="TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a></code>, <code><a href="UniqFilter.html" title="class in org.apache.tools.ant.filters">UniqFilter</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">ChainableReader</span></div>
<div class="block">Interface indicating that a reader may be chained to another one.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Returns a reader with the same configuration as this one,
 but filtering input from the specified reader.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</span></div>
<div class="block">Returns a reader with the same configuration as this one,
 but filtering input from the specified reader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rdr</code> - the reader which the returned reader should be filtering</dd>
<dt>Returns:</dt>
<dd>a reader with the same configuration as this one, but
         filtering input from the specified reader</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
