<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>IntrospectionHelper (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: IntrospectionHelper">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class IntrospectionHelper" class="title">Class IntrospectionHelper</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.IntrospectionHelper</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">IntrospectionHelper</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Helper class that collects the methods a task or nested element
 holds to set attributes, create nested elements or hold PCDATA
 elements.

 It contains hashtables containing classes that use introspection
 to handle all the invocation of the project-component specific methods.

 This class is somewhat complex, as it implements the O/X mapping between
 Ant XML and Java class instances. This is not the best place for someone new
 to Ant to start contributing to the codebase, as a change here can break the
 entire system in interesting ways. Always run a full test of Ant before checking
 in/submitting changes to this file.

 The class is final and has a private constructor.
 To get an instance for a specific (class,project) combination,
 use <a href="#getHelper(org.apache.tools.ant.Project,java.lang.Class)"><code>getHelper(Project,Class)</code></a>.
 This may return an existing version, or a new one
 ...do not make any assumptions about its uniqueness, or its validity after the Project
 instance has finished its build.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="IntrospectionHelper.Creator.html" class="type-name-link" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></code></div>
<div class="col-last even-row-color">
<div class="block">creator - allows use of create/store external
 to IntrospectionHelper.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#NOT_SUPPORTED_CHILD_POSTFIX" class="member-name-link">NOT_SUPPORTED_CHILD_POSTFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">part of the error message created by <a href="#throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)"><code>throwNotSupported</code></a>.</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#NOT_SUPPORTED_CHILD_PREFIX" class="member-name-link">NOT_SUPPORTED_CHILD_PREFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">part of the error message created by <a href="#throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)"><code>throwNotSupported</code></a>.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addText(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)" class="member-name-link">addText</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds PCDATA to an element, using the element's
 <code>void addText(String)</code> method, if it has one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#clearCache()" class="member-name-link">clearCache</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Clears the static cache of on build finished.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#createElement(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)" class="member-name-link">createElement</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAddTextMethod()" class="member-name-link">getAddTextMethod</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the addText method when the introspected
 class supports nested text.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAttributeMap()" class="member-name-link">getAttributeMap</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a read-only map of attributes supported by the introspected class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAttributeMethod(java.lang.String)" class="member-name-link">getAttributeMethod</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the setter method of a named attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAttributes()" class="member-name-link">getAttributes</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumeration of the names of the attributes supported by the introspected class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAttributeType(java.lang.String)" class="member-name-link">getAttributeType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the type of a named attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getElementCreator(org.apache.tools.ant.Project,java.lang.String,java.lang.Object,java.lang.String,org.apache.tools.ant.UnknownElement)" class="member-name-link">getElementCreator</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName,
 <a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">returns an object that creates and stores an object
 for an element of a parent.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getElementMethod(java.lang.String)" class="member-name-link">getElementMethod</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the adder or creator method of a named nested element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getElementType(java.lang.String)" class="member-name-link">getElementType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the type of a named nested element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtensionPoints()" class="member-name-link">getExtensionPoints</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a read-only list of extension points supported
 by the introspected class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getHelper(java.lang.Class)" class="member-name-link">getHelper</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a helper for the given class, either from the cache
 or by creating a new instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getHelper(org.apache.tools.ant.Project,java.lang.Class)" class="member-name-link">getHelper</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a helper for the given class, either from the cache
 or by creating a new instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNestedElementMap()" class="member-name-link">getNestedElementMap</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a read-only map of nested elements supported
 by the introspected class.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNestedElements()" class="member-name-link">getNestedElements</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumeration of the names of the nested elements supported
 by the introspected class.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isContainer()" class="member-name-link">isContainer</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether the introspected class is a task container,
 supporting arbitrary nested tasks/types.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDynamic()" class="member-name-link">isDynamic</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether the introspected class is a dynamic one,
 supporting arbitrary nested elements and/or attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAttribute(org.apache.tools.ant.Project,java.lang.Object,java.lang.String,java.lang.Object)" class="member-name-link">setAttribute</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the named attribute in the given element, which is part of the
 given project.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAttribute(org.apache.tools.ant.Project,java.lang.Object,java.lang.String,java.lang.String)" class="member-name-link">setAttribute</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the named attribute in the given element, which is part of the
 given project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#storeElement(org.apache.tools.ant.Project,java.lang.Object,java.lang.Object,java.lang.String)" class="member-name-link">storeElement</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;child,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Stores a named nested element using a storage method determined
 by the initial introspection.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsCharacters()" class="member-name-link">supportsCharacters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns whether or not the introspected class supports PCDATA.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsNestedElement(java.lang.String)" class="member-name-link">supportsNestedElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates if this element supports a nested element of the
 given name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsNestedElement(java.lang.String,java.lang.String)" class="member-name-link">supportsNestedElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this element supports a nested element of the
 given name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsNestedElement(java.lang.String,java.lang.String,org.apache.tools.ant.Project,java.lang.Object)" class="member-name-link">supportsNestedElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate if this element supports a nested element of the
 given name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsReflectElement(java.lang.String,java.lang.String)" class="member-name-link">supportsReflectElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this element supports a nested element from reflection.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)" class="member-name-link">throwNotSupported</a><wbr>(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Utility method to throw a NotSupported exception</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="NOT_SUPPORTED_CHILD_PREFIX">
<h3>NOT_SUPPORTED_CHILD_PREFIX</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">NOT_SUPPORTED_CHILD_PREFIX</span></div>
<div class="block">part of the error message created by <a href="#throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)"><code>throwNotSupported</code></a>.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.IntrospectionHelper.NOT_SUPPORTED_CHILD_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NOT_SUPPORTED_CHILD_POSTFIX">
<h3>NOT_SUPPORTED_CHILD_POSTFIX</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">NOT_SUPPORTED_CHILD_POSTFIX</span></div>
<div class="block">part of the error message created by <a href="#throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)"><code>throwNotSupported</code></a>.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.IntrospectionHelper.NOT_SUPPORTED_CHILD_POSTFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getHelper(java.lang.Class)">
<h3>getHelper</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></span>&nbsp;<span class="element-name">getHelper</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</span></div>
<div class="block">Returns a helper for the given class, either from the cache
 or by creating a new instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>c</code> - The class for which a helper is required.
          Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a helper for the specified class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getHelper(org.apache.tools.ant.Project,java.lang.Class)">
<h3>getHelper</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</a></span>&nbsp;<span class="element-name">getHelper</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;c)</span></div>
<div class="block">Returns a helper for the given class, either from the cache
 or by creating a new instance.

 The method will make sure the helper will be cleaned up at the end of
 the project, and only one instance will be created for each class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - the project instance. Can be null, in which case the helper is not cached.</dd>
<dd><code>c</code> - The class for which a helper is required.
          Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a helper for the specified class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAttribute(org.apache.tools.ant.Project,java.lang.Object,java.lang.String,java.lang.Object)">
<h3>setAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAttribute</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span>
                  throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sets the named attribute in the given element, which is part of the
 given project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - The project containing the element. This is used when files
          need to be resolved. Must not be <code>null</code>.</dd>
<dd><code>element</code> - The element to set the attribute in. Must not be
                <code>null</code>.</dd>
<dd><code>attributeName</code> - The name of the attribute to set. Must not be
                      <code>null</code>.</dd>
<dd><code>value</code> - The value to set the attribute to. This may be interpreted
              or converted to the necessary type if the setter method
              doesn't accept an object of the supplied type.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class doesn't support
                           the given attribute, or if the setting
                           method fails.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAttribute(org.apache.tools.ant.Project,java.lang.Object,java.lang.String,java.lang.String)">
<h3>setAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAttribute</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span>
                  throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Sets the named attribute in the given element, which is part of the
 given project.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>p</code> - The project containing the element. This is used when files
          need to be resolved. Must not be <code>null</code>.</dd>
<dd><code>element</code> - The element to set the attribute in. Must not be
                <code>null</code>.</dd>
<dd><code>attributeName</code> - The name of the attribute to set. Must not be
                      <code>null</code>.</dd>
<dd><code>value</code> - The value to set the attribute to. This may be interpreted
              or converted to the necessary type if the setter method
              doesn't just take a string. Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class doesn't support
                           the given attribute, or if the setting
                           method fails.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addText(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">
<h3>addText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span>
             throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Adds PCDATA to an element, using the element's
 <code>void addText(String)</code> method, if it has one. If no
 such method is present, a BuildException is thrown if the
 given text contains non-whitespace.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - The project which the element is part of.
                Must not be <code>null</code>.</dd>
<dd><code>element</code> - The element to add the text to.
                Must not be <code>null</code>.</dd>
<dd><code>text</code> - The text to add.
                Must not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if non-whitespace text is provided and no
                           method is available to handle it, or if
                           the handling method fails.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="throwNotSupported(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">
<h3>throwNotSupported</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">throwNotSupported</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span></div>
<div class="block">Utility method to throw a NotSupported exception</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - the Project instance.</dd>
<dd><code>parent</code> - the object which doesn't support a requested element</dd>
<dd><code>elementName</code> - the name of the Element which is trying to be created.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createElement(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">
<h3>createElement</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">createElement</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span>
                     throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.6.x.
             This is not a namespace aware method.</div>
</div>
<div class="block">Creates a named nested element. Depending on the results of the
 initial introspection, either a method in the given parent instance
 or a simple no-arg constructor is used to create an instance of the
 specified element type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - Project to which the parent object belongs.
                Must not be <code>null</code>. If the resulting
                object is an instance of ProjectComponent, its
                Project reference is set to this parameter value.</dd>
<dd><code>parent</code> - Parent object used to create the instance.
                Must not be <code>null</code>.</dd>
<dd><code>elementName</code> - Name of the element to create an instance of.
                    Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an instance of the specified element type</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if no method is available to create the
                           element instance, or if the creating method fails.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getElementCreator(org.apache.tools.ant.Project,java.lang.String,java.lang.Object,java.lang.String,org.apache.tools.ant.UnknownElement)">
<h3>getElementCreator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</a></span>&nbsp;<span class="element-name">getElementCreator</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName,
 <a href="UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</a>&nbsp;ue)</span></div>
<div class="block">returns an object that creates and stores an object
 for an element of a parent.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - Project to which the parent object belongs.</dd>
<dd><code>parentUri</code> - The namespace uri of the parent object.</dd>
<dd><code>parent</code> - Parent object used to create the creator object to
                     create and store and instance of a subelement.</dd>
<dd><code>elementName</code> - Name of the element to create an instance of.</dd>
<dd><code>ue</code> - The unknown element associated with the element.</dd>
<dt>Returns:</dt>
<dd>a creator object to create and store the element instance.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isDynamic()">
<h3>isDynamic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDynamic</span>()</div>
<div class="block">Indicates whether the introspected class is a dynamic one,
 supporting arbitrary nested elements and/or attributes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><div><code>true</code> if the introspected class is dynamic;
         <code>false</code> otherwise.</div></dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="DynamicElement.html" title="interface in org.apache.tools.ant"><code>DynamicElement</code></a></li>
<li><a href="DynamicElementNS.html" title="interface in org.apache.tools.ant"><code>DynamicElementNS</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isContainer()">
<h3>isContainer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isContainer</span>()</div>
<div class="block">Indicates whether the introspected class is a task container,
 supporting arbitrary nested tasks/types.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if the introspected class is a container;
         <code>false</code> otherwise.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="TaskContainer.html" title="interface in org.apache.tools.ant"><code>TaskContainer</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsNestedElement(java.lang.String)">
<h3>supportsNestedElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsNestedElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span></div>
<div class="block">Indicates if this element supports a nested element of the
 given name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>elementName</code> - the name of the nested element being checked</dd>
<dt>Returns:</dt>
<dd>true if the given nested element is supported</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsNestedElement(java.lang.String,java.lang.String)">
<h3>supportsNestedElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsNestedElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span></div>
<div class="block">Indicate if this element supports a nested element of the
 given name.

 <p>Note that this method will always return true if the
 introspected class is <a href="#isDynamic()"><code>dynamic</code></a> or contains a
 method named "add" with void return type and a single argument.
 To ge a more thorough answer, use the four-arg version of this
 method instead.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentUri</code> - the uri of the parent</dd>
<dd><code>elementName</code> - the name of the nested element being checked</dd>
<dt>Returns:</dt>
<dd>true if the given nested element is supported</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsNestedElement(java.lang.String,java.lang.String,org.apache.tools.ant.Project,java.lang.Object)">
<h3>supportsNestedElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsNestedElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName,
 <a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent)</span></div>
<div class="block">Indicate if this element supports a nested element of the
 given name.

 <p>Note that this method will always return true if the
 introspected class is <a href="#isDynamic()"><code>dynamic</code></a>, so be
 prepared to catch an exception about unsupported children when
 calling <a href="#getElementCreator(org.apache.tools.ant.Project,java.lang.String,java.lang.Object,java.lang.String,org.apache.tools.ant.UnknownElement)"><code>getElementCreator</code></a>.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentUri</code> - the uri of the parent</dd>
<dd><code>elementName</code> - the name of the nested element being checked</dd>
<dd><code>project</code> - currently executing project instance</dd>
<dd><code>parent</code> - the parent element</dd>
<dt>Returns:</dt>
<dd>true if the given nested element is supported</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsReflectElement(java.lang.String,java.lang.String)">
<h3>supportsReflectElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsReflectElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;parentUri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span></div>
<div class="block">Check if this element supports a nested element from reflection.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentUri</code> - the uri of the parent</dd>
<dd><code>elementName</code> - the name of the nested element being checked</dd>
<dt>Returns:</dt>
<dd>true if the given nested element is supported</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="storeElement(org.apache.tools.ant.Project,java.lang.Object,java.lang.Object,java.lang.String)">
<h3>storeElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">storeElement</span><wbr><span class="parameters">(<a href="Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;child,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span>
                  throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Stores a named nested element using a storage method determined
 by the initial introspection. If no appropriate storage method
 is available, this method returns immediately.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>project</code> - Ignored in this implementation.
                May be <code>null</code>.</dd>
<dd><code>parent</code> - Parent instance to store the child in.
                Must not be <code>null</code>.</dd>
<dd><code>child</code> - Child instance to store in the parent.
                Should not be <code>null</code>.</dd>
<dd><code>elementName</code> - Name of the child element to store.
                     May be <code>null</code>, in which case
                     this method returns immediately.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the storage method fails.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getElementType(java.lang.String)">
<h3>getElementType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getElementType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span>
                        throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Returns the type of a named nested element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>elementName</code> - The name of the element to find the type of.
                    Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the type of the nested element with the specified name.
         This will never be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class does not
                           support the named nested element.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAttributeType(java.lang.String)">
<h3>getAttributeType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">getAttributeType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</span>
                          throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Returns the type of a named attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attributeName</code> - The name of the attribute to find the type of.
                      Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the type of the attribute with the specified name.
         This will never be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class does not
                           support the named attribute.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAddTextMethod()">
<h3>getAddTextMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></span>&nbsp;<span class="element-name">getAddTextMethod</span>()
                        throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Returns the addText method when the introspected
 class supports nested text.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the method on this introspected class that adds nested text.
         Cannot be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class does not
         support the nested text.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getElementMethod(java.lang.String)">
<h3>getElementMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></span>&nbsp;<span class="element-name">getElementMethod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;elementName)</span>
                        throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Returns the adder or creator method of a named nested element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>elementName</code> - The name of the attribute to find the setter
         method of. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the method on this introspected class that adds or creates this
         nested element. Can be <code>null</code> when the introspected
         class is a dynamic configurator!</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class does not
         support the named nested element.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAttributeMethod(java.lang.String)">
<h3>getAttributeMethod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a></span>&nbsp;<span class="element-name">getAttributeMethod</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</span>
                          throws <span class="exceptions"><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Returns the setter method of a named attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attributeName</code> - The name of the attribute to find the setter
         method of. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>the method on this introspected class that sets this attribute.
         This will never be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the introspected class does not
         support the named attribute.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsCharacters()">
<h3>supportsCharacters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsCharacters</span>()</div>
<div class="block">Returns whether or not the introspected class supports PCDATA.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether or not the introspected class supports PCDATA.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAttributes()">
<h3>getAttributes</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getAttributes</span>()</div>
<div class="block">Returns an enumeration of the names of the attributes supported by the introspected class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an enumeration of the names of the attributes supported by the introspected class.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getAttributeMap()"><code>getAttributeMap()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAttributeMap()">
<h3>getAttributeMap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getAttributeMap</span>()</div>
<div class="block">Returns a read-only map of attributes supported by the introspected class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an attribute name to attribute <code>Class</code>
         unmodifiable map. Can be empty, but never <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNestedElements()">
<h3>getNestedElements</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getNestedElements</span>()</div>
<div class="block">Returns an enumeration of the names of the nested elements supported
 by the introspected class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an enumeration of the names of the nested elements supported
         by the introspected class.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getNestedElementMap()"><code>getNestedElementMap()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNestedElementMap()">
<h3>getNestedElementMap</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&gt;</span>&nbsp;<span class="element-name">getNestedElementMap</span>()</div>
<div class="block">Returns a read-only map of nested elements supported
 by the introspected class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a nested-element name to nested-element <code>Class</code>
         unmodifiable map. Can be empty, but never <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExtensionPoints()">
<h3>getExtensionPoints</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/reflect/Method.html" title="class or interface in java.lang.reflect" class="external-link">Method</a>&gt;</span>&nbsp;<span class="element-name">getExtensionPoints</span>()</div>
<div class="block">Returns a read-only list of extension points supported
 by the introspected class.
 <p>
 A task/type or nested element with void methods named <code>add()</code>
 or <code>addConfigured()</code>, taking a single class or interface
 argument, supports extensions point. This method returns the list of
 all these <em>void add[Configured](type)</em> methods.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a list of void, single argument add() or addConfigured()
         <code>Method</code>s of all supported extension points.
         These methods are sorted such that if the argument type of a
         method derives from another type also an argument of a method
         of this list, the method with the most derived argument will
         always appear first. Can be empty, but never <code>null</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearCache()">
<h3>clearCache</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearCache</span>()</div>
<div class="block">Clears the static cache of on build finished.</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
