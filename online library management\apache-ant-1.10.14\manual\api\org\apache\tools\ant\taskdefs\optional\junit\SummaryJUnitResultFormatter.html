<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>SummaryJUnitResultFormatter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junit, class: SummaryJUnitResultFormatter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</a></div>
<h1 title="Class SummaryJUnitResultFormatter" class="title">Class SummaryJUnitResultFormatter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.junit.SummaryJUnitResultFormatter</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>junit.framework.TestListener</code>, <code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code>, <code><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a></code>, <code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="OutErrSummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">OutErrSummaryJUnitResultFormatter</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SummaryJUnitResultFormatter</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a>, <a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></span></div>
<div class="block">Prints short summary output of the test to Ant's logging system.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">SummaryJUnitResultFormatter</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addError(junit.framework.Test,java.lang.Throwable)" class="member-name-link">addError</a><wbr>(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFailure(junit.framework.Test,java.lang.Throwable)" class="member-name-link">addFailure</a><wbr>(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFailure(junit.framework.Test,junit.framework.AssertionFailedError)" class="member-name-link">addFailure</a><wbr>(junit.framework.Test&nbsp;test,
 junit.framework.AssertionFailedError&nbsp;t)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Interface TestListener for JUnit &gt; 3.4.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endTest(junit.framework.Test)" class="member-name-link">endTest</a><wbr>(junit.framework.Test&nbsp;test)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)" class="member-name-link">endTestSuite</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;suite)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The whole testsuite ended.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutput(java.io.OutputStream)" class="member-name-link">setOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the stream the formatter is supposed to write its results to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSystemError(java.lang.String)" class="member-name-link">setSystemError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;err)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is what the test has written to System.err</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSystemOutput(java.lang.String)" class="member-name-link">setSystemOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is what the test has written to System.out</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWithOutAndErr(boolean)" class="member-name-link">setWithOutAndErr</a><wbr>(boolean&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Should the output to System.out and System.err be written to
 the summary.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startTest(junit.framework.Test)" class="member-name-link">startTest</a><wbr>(junit.framework.Test&nbsp;t)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Empty</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)" class="member-name-link">startTestSuite</a><wbr>(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;suite)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The testsuite started.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>SummaryJUnitResultFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SummaryJUnitResultFormatter</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="startTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">
<h3>startTestSuite</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startTestSuite</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;suite)</span></div>
<div class="block">The testsuite started.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitResultFormatter.html#startTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">startTestSuite</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>suite</code> - the testsuite.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startTest(junit.framework.Test)">
<h3>startTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startTest</span><wbr><span class="parameters">(junit.framework.Test&nbsp;t)</span></div>
<div class="block">Empty</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>startTest</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>t</code> - not used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endTest(junit.framework.Test)">
<h3>endTest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endTest</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test)</span></div>
<div class="block">Empty</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>endTest</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - not used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFailure(junit.framework.Test,java.lang.Throwable)">
<h3>addFailure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFailure</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</span></div>
<div class="block">Empty</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>test</code> - not used.</dd>
<dd><code>t</code> - not used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFailure(junit.framework.Test,junit.framework.AssertionFailedError)">
<h3>addFailure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFailure</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 junit.framework.AssertionFailedError&nbsp;t)</span></div>
<div class="block">Interface TestListener for JUnit &gt; 3.4.

 <p>A Test failed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addFailure</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - not used.</dd>
<dd><code>t</code> - not used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addError(junit.framework.Test,java.lang.Throwable)">
<h3>addError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addError</span><wbr><span class="parameters">(junit.framework.Test&nbsp;test,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;t)</span></div>
<div class="block">Empty</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addError</code>&nbsp;in interface&nbsp;<code>junit.framework.TestListener</code></dd>
<dt>Parameters:</dt>
<dd><code>test</code> - not used.</dd>
<dd><code>t</code> - not used.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutput(java.io.OutputStream)">
<h3>setOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span></div>
<div class="block">Sets the stream the formatter is supposed to write its results to..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitResultFormatter.html#setOutput(java.io.OutputStream)">setOutput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html#setOutput(java.io.OutputStream)">setOutput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.JUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitResultFormatterMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>out</code> - the output stream to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSystemOutput(java.lang.String)">
<h3>setSystemOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSystemOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;out)</span></div>
<div class="block">This is what the test has written to System.out.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitResultFormatter.html#setSystemOutput(java.lang.String)">setSystemOutput</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>out</code> - the string to write.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSystemError(java.lang.String)">
<h3>setSystemError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSystemError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;err)</span></div>
<div class="block">This is what the test has written to System.err.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitResultFormatter.html#setSystemError(java.lang.String)">setSystemError</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>err</code> - the string to write.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setWithOutAndErr(boolean)">
<h3>setWithOutAndErr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWithOutAndErr</span><wbr><span class="parameters">(boolean&nbsp;value)</span></div>
<div class="block">Should the output to System.out and System.err be written to
 the summary.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html#setWithOutAndErr(boolean)">setWithOutAndErr</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitTaskMirror.SummaryJUnitResultFormatterMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.SummaryJUnitResultFormatterMirror</a></code></dd>
<dt>Parameters:</dt>
<dd><code>value</code> - if true write System.out and System.err to the summary.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">
<h3>endTestSuite</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endTestSuite</span><wbr><span class="parameters">(<a href="JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</a>&nbsp;suite)</span>
                  throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">The whole testsuite ended.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JUnitResultFormatter.html#endTestSuite(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">endTestSuite</a></code>&nbsp;in interface&nbsp;<code><a href="JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitResultFormatter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>suite</code> - the testsuite.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
