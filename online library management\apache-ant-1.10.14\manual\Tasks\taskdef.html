<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>TaskDef Task</title>
</head>

<body>

<h2 id="taskdef">Taskdef</h2>

<h3>Description</h3>
<p>Adds a task definition to the current project, such that this new task can be used in the current
project.</p>
<p>This task is a form of <a href="typedef.html">Typedef</a> with the attributes <var>adapter</var>
and <var>adaptto</var> set to the values <q>org.apache.tools.ant.TaskAdapter</q>
and <q>org.apache.tools.ant.Task</q> respectively. Anything said in
the <a href="typedef.html">manual page of typedef</a> applies to <code>taskdef</code> as well.</p>

<h3>Examples</h3>

<p>Make a task called <code>myjavadoc</code> available to Apache Ant. The
class <code>com.mydomain.JavadocTask</code> implements the task.</p>

<pre>&lt;taskdef name=&quot;myjavadoc&quot; classname=&quot;com.mydomain.JavadocTask&quot;/&gt;</pre>

</body>
</html>
