<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional.ejb (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.optional.ejb" class="title">Package org.apache.tools.ant.taskdefs.optional.ejb</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.optional.ejb</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">BorlandDeploymentTool is dedicated to the Borland Application Server 4.5 and 4.5.1
 This task generates and compiles the stubs and skeletons for all ejb described into the
 Deployment Descriptor, builds the jar file including the support files and verify
 whether the produced jar is valid or not.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="BorlandGenerateClient.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandGenerateClient</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Generates a Borland Application Server 4.5 client JAR using as
 input the EJB JAR file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Inner class used by EjbJar to facilitate the parsing of deployment
 descriptors and the capture of appropriate information.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">The interface to implement for deployment tools.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="EjbJar.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Provides automated EJB JAR file creation.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="EjbJar.CMPVersion.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">CMP versions supported
 valid CMP versions are 1.0 and 2.0</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="EjbJar.DTDLocation.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Inner class used to record information about the location of a local DTD</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="EjbJar.NamingScheme.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An EnumeratedAttribute class for handling different EJB jar naming
 schemes</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A deployment tool which creates generic EJB jars.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="InnerClassFilenameFilter.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">InnerClassFilenameFilter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A filename filter for inner class files of a particular class.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IPlanetDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class is used to generate iPlanet Application Server (iAS) 6.0 stubs and
 skeletons and build an EJB Jar file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbc</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Compiles EJB stubs and skeletons for the iPlanet Application
 Server (iAS).</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="IPlanetEjbcTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbcTask</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Compiles EJB stubs and skeletons for the iPlanet Application Server.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The deployment tool to add the jboss specific deployment descriptor to the ejb jar file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The deployment tool to add the jonas specific deployment descriptors to the
 ejb JAR file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="OrionDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The deployment tool to add the orion specific deployment descriptor to the
 ejb jar file.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The weblogic element is used to control the weblogic.ejbc compiler for
    generating WebLogic EJB jars.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Deployment tool for WebLogic TOPLink.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">WebSphere deployment tool that augments the ejbjar task.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
