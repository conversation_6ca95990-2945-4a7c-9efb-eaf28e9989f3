<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Sound Task</title>
</head>

<body>

<h2 id="sound">Sound</h2>
<h3>Description</h3>
<p>Plays a sound file at the end of the build, according to whether the build failed or
succeeded. You can specify either a specific sound file to play, or, if a directory is specified,
the <code>&lt;sound&gt;</code> task will randomly select a file to play. <strong>Note</strong>: At
this point, the random selection is based on all the files in the directory, not just those ending
in appropriate suffixes for sound files, so be sure you only have sound files in the directory you
specify.</p>
<p>More precisely <code>&lt;sound&gt;</code> registers a hook that is triggered when the build
finishes. Therefore you have to place this task as top level or inside a target which is always
executed.</p>
<p>Unless you are running on Java 1.3 or later, you need the Java Media Framework on the classpath
(<code class="code">javax.sound</code>).</p>

<h3>Parameters specified as nested elements</h3>
<h4>success</h4>
<p>Specifies the sound to be played if the build succeeded.</p>

<h4>fail</h4>
<p>Specifies the sound to be played if the build failed.</p>

<h4>Nested element parameters</h4>
<p>The following attributes may be used on the <code>&lt;success&gt;</code>
and <code>&lt;fail&gt;</code> elements:</p>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>source</td>
    <td>the path to a sound file directory, or the name of a specific sound file, to be played. If
      this file does not exist, an error message will be logged.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>loops</td>
    <td>the number of extra times to play the sound file.</td>
    <td>No; default is <q>0</q></td>
  </tr>
  <tr>
    <td>duration</td>
    <td>the amount of time (in milliseconds) to play the sound file.</td>
    <td>No</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Play the <samp>bell.wav</samp> sound file if the build succeeded, or the <samp>ohno.wav</samp>
sound file if the build failed, three times, if the <code>fun</code> property is set to <q>true</q>.
If the target is a dependency of an "initialization" target that other targets depend on,
the <code>fun.done</code> property prevents the target from being executed more than once.</p>
<pre>
&lt;target name=&quot;fun&quot; if=&quot;fun&quot; unless=&quot;fun.done&quot;&gt;
  &lt;sound&gt;
    &lt;success source=&quot;${user.home}/sounds/bell.wav&quot;/&gt;
    &lt;fail source=&quot;${user.home}/sounds/ohno.wav&quot; loops=&quot;2&quot;/&gt;
  &lt;/sound&gt;
  &lt;property name=&quot;fun.done&quot; value=&quot;true&quot;/&gt;
&lt;/target&gt;</pre>

<p>Randomly select a sound file to play when the build succeeds or fails.</p>
<pre>
&lt;target name=&quot;fun&quot; if=&quot;fun&quot; unless=&quot;fun.done&quot;&gt;
  &lt;sound&gt;
    &lt;success source=&quot;//intranet/sounds/success&quot;/&gt;
    &lt;fail source=&quot;//intranet/sounds/failure&quot;/&gt;
  &lt;/sound&gt;
  &lt;property name=&quot;fun.done&quot; value=&quot;true&quot;/&gt;
&lt;/target&gt;
</pre>

</body>
</html>
