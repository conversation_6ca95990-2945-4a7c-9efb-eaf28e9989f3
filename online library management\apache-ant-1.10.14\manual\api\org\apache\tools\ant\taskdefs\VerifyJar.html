<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>VerifyJar (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: VerifyJar">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class VerifyJar" class="title">Class VerifyJar</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.AbstractJarSignerTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.VerifyJar</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">VerifyJar</span>
<span class="extends-implements">extends <a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></span></div>
<div class="block">JAR verification task.
 For every JAR passed in, we fork jarsigner to verify
 that it is correctly signed. This is more rigorous than just checking for
 the existence of a signature; the entire certification chain is tested</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_NO_FILE" class="member-name-link">ERROR_NO_FILE</a></code></div>
<div class="col-last even-row-color">
<div class="block">no file message "Not found :"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ERROR_NO_VERIFY" class="member-name-link">ERROR_NO_VERIFY</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Error output if there is a failure to verify the jar.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractJarSignerTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></h3>
<code><a href="AbstractJarSignerTask.html#alias">alias</a>, <a href="AbstractJarSignerTask.html#ERROR_NO_SOURCE">ERROR_NO_SOURCE</a>, <a href="AbstractJarSignerTask.html#filesets">filesets</a>, <a href="AbstractJarSignerTask.html#jar">jar</a>, <a href="AbstractJarSignerTask.html#JARSIGNER_COMMAND">JARSIGNER_COMMAND</a>, <a href="AbstractJarSignerTask.html#keypass">keypass</a>, <a href="AbstractJarSignerTask.html#keystore">keystore</a>, <a href="AbstractJarSignerTask.html#maxMemory">maxMemory</a>, <a href="AbstractJarSignerTask.html#storepass">storepass</a>, <a href="AbstractJarSignerTask.html#storetype">storetype</a>, <a href="AbstractJarSignerTask.html#strict">strict</a>, <a href="AbstractJarSignerTask.html#verbose">verbose</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">VerifyJar</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beginExecution()" class="member-name-link">beginExecution</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">init processing logic; this is retained through our execution(s)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endExecution()" class="member-name-link">endExecution</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">any cleanup logic</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">verify our jar files</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCertificates(boolean)" class="member-name-link">setCertificates</a><wbr>(boolean&nbsp;certificates)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ask for certificate information to be printed</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.AbstractJarSignerTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></h3>
<code><a href="AbstractJarSignerTask.html#addArg(org.apache.tools.ant.types.Commandline.Argument)">addArg</a>, <a href="AbstractJarSignerTask.html#addArgument(org.apache.tools.ant.taskdefs.ExecTask,org.apache.tools.ant.types.Commandline.Argument)">addArgument</a>, <a href="AbstractJarSignerTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="AbstractJarSignerTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</a>, <a href="AbstractJarSignerTask.html#addValue(org.apache.tools.ant.taskdefs.ExecTask,java.lang.String)">addValue</a>, <a href="AbstractJarSignerTask.html#bindToKeystore(org.apache.tools.ant.taskdefs.ExecTask)">bindToKeystore</a>, <a href="AbstractJarSignerTask.html#createJarSigner()">createJarSigner</a>, <a href="AbstractJarSignerTask.html#createPath()">createPath</a>, <a href="AbstractJarSignerTask.html#createUnifiedSourcePath()">createUnifiedSourcePath</a>, <a href="AbstractJarSignerTask.html#createUnifiedSources()">createUnifiedSources</a>, <a href="AbstractJarSignerTask.html#declareSysProperty(org.apache.tools.ant.taskdefs.ExecTask,org.apache.tools.ant.types.Environment.Variable)">declareSysProperty</a>, <a href="AbstractJarSignerTask.html#getRedirector()">getRedirector</a>, <a href="AbstractJarSignerTask.html#hasResources()">hasResources</a>, <a href="AbstractJarSignerTask.html#setAlias(java.lang.String)">setAlias</a>, <a href="AbstractJarSignerTask.html#setCommonOptions(org.apache.tools.ant.taskdefs.ExecTask)">setCommonOptions</a>, <a href="AbstractJarSignerTask.html#setExecutable(java.lang.String)">setExecutable</a>, <a href="AbstractJarSignerTask.html#setJar(java.io.File)">setJar</a>, <a href="AbstractJarSignerTask.html#setKeypass(java.lang.String)">setKeypass</a>, <a href="AbstractJarSignerTask.html#setKeystore(java.lang.String)">setKeystore</a>, <a href="AbstractJarSignerTask.html#setMaxmemory(java.lang.String)">setMaxmemory</a>, <a href="AbstractJarSignerTask.html#setProviderArg(java.lang.String)">setProviderArg</a>, <a href="AbstractJarSignerTask.html#setProviderClass(java.lang.String)">setProviderClass</a>, <a href="AbstractJarSignerTask.html#setProviderName(java.lang.String)">setProviderName</a>, <a href="AbstractJarSignerTask.html#setStorepass(java.lang.String)">setStorepass</a>, <a href="AbstractJarSignerTask.html#setStoretype(java.lang.String)">setStoretype</a>, <a href="AbstractJarSignerTask.html#setStrict(boolean)">setStrict</a>, <a href="AbstractJarSignerTask.html#setVerbose(boolean)">setVerbose</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ERROR_NO_FILE">
<h3>ERROR_NO_FILE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_FILE</span></div>
<div class="block">no file message "Not found :"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.VerifyJar.ERROR_NO_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ERROR_NO_VERIFY">
<h3>ERROR_NO_VERIFY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_NO_VERIFY</span></div>
<div class="block">Error output if there is a failure to verify the jar.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.VerifyJar.ERROR_NO_VERIFY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>VerifyJar</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">VerifyJar</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setCertificates(boolean)">
<h3>setCertificates</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCertificates</span><wbr><span class="parameters">(boolean&nbsp;certificates)</span></div>
<div class="block">Ask for certificate information to be printed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>certificates</code> - if true print certificates.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">verify our jar files</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="beginExecution()">
<h3>beginExecution</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">beginExecution</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="AbstractJarSignerTask.html#beginExecution()">AbstractJarSignerTask</a></code></span></div>
<div class="block">init processing logic; this is retained through our execution(s)</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractJarSignerTask.html#beginExecution()">beginExecution</a></code>&nbsp;in class&nbsp;<code><a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></code></dd>
<dt>Since:</dt>
<dd>1.10.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endExecution()">
<h3>endExecution</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endExecution</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="AbstractJarSignerTask.html#endExecution()">AbstractJarSignerTask</a></code></span></div>
<div class="block">any cleanup logic</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="AbstractJarSignerTask.html#endExecution()">endExecution</a></code>&nbsp;in class&nbsp;<code><a href="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</a></code></dd>
<dt>Since:</dt>
<dd>1.10.3</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
