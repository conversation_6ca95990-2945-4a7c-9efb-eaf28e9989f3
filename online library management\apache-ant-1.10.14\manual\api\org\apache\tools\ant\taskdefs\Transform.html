<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Transform (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Transform">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Transform" class="title">Class Transform</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecTask</a>
<div class="inheritance"><a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecuteOn</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Transform</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Transform</span>
<span class="extends-implements">extends <a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></span></div>
<div class="block">Has been merged into ExecuteOn, empty class for backwards compatibility.
 We leave that in case that external programs access this class directly,
 for example via
   <code> Transform tr = (Transform) getProject().createTask("apply") </code></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.taskdefs.ExecuteOn">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></h2>
<code><a href="ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.ExecuteOn">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></h3>
<code><a href="ExecuteOn.html#destDir">destDir</a>, <a href="ExecuteOn.html#filesets">filesets</a>, <a href="ExecuteOn.html#mapper">mapper</a>, <a href="ExecuteOn.html#mapperElement">mapperElement</a>, <a href="ExecuteOn.html#srcFilePos">srcFilePos</a>, <a href="ExecuteOn.html#srcIsFirst">srcIsFirst</a>, <a href="ExecuteOn.html#targetFilePos">targetFilePos</a>, <a href="ExecuteOn.html#type">type</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.ExecTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></h3>
<code><a href="ExecTask.html#cmdl">cmdl</a>, <a href="ExecTask.html#failOnError">failOnError</a>, <a href="ExecTask.html#newEnvironment">newEnvironment</a>, <a href="ExecTask.html#redirector">redirector</a>, <a href="ExecTask.html#redirectorElement">redirectorElement</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Transform</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.ExecuteOn">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></h3>
<code><a href="ExecuteOn.html#add(org.apache.tools.ant.types.ResourceCollection)">add</a>, <a href="ExecuteOn.html#add(org.apache.tools.ant.util.FileNameMapper)">add</a>, <a href="ExecuteOn.html#addDirset(org.apache.tools.ant.types.DirSet)">addDirset</a>, <a href="ExecuteOn.html#addFilelist(org.apache.tools.ant.types.FileList)">addFilelist</a>, <a href="ExecuteOn.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="ExecuteOn.html#checkConfiguration()">checkConfiguration</a>, <a href="ExecuteOn.html#createHandler()">createHandler</a>, <a href="ExecuteOn.html#createMapper()">createMapper</a>, <a href="ExecuteOn.html#createSrcfile()">createSrcfile</a>, <a href="ExecuteOn.html#createTargetfile()">createTargetfile</a>, <a href="ExecuteOn.html#getCommandline(java.lang.String%5B%5D,java.io.File%5B%5D)">getCommandline</a>, <a href="ExecuteOn.html#getCommandline(java.lang.String,java.io.File)">getCommandline</a>, <a href="ExecuteOn.html#getDirs(java.io.File,org.apache.tools.ant.DirectoryScanner)">getDirs</a>, <a href="ExecuteOn.html#getFiles(java.io.File,org.apache.tools.ant.DirectoryScanner)">getFiles</a>, <a href="ExecuteOn.html#getFilesAndDirs(org.apache.tools.ant.types.FileList)">getFilesAndDirs</a>, <a href="ExecuteOn.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</a>, <a href="ExecuteOn.html#runParallel(org.apache.tools.ant.taskdefs.Execute,java.util.Vector,java.util.Vector)">runParallel</a>, <a href="ExecuteOn.html#setAddsourcefile(boolean)">setAddsourcefile</a>, <a href="ExecuteOn.html#setDest(java.io.File)">setDest</a>, <a href="ExecuteOn.html#setForce(boolean)">setForce</a>, <a href="ExecuteOn.html#setForwardslash(boolean)">setForwardslash</a>, <a href="ExecuteOn.html#setIgnoremissing(boolean)">setIgnoremissing</a>, <a href="ExecuteOn.html#setMaxParallel(int)">setMaxParallel</a>, <a href="ExecuteOn.html#setParallel(boolean)">setParallel</a>, <a href="ExecuteOn.html#setRelative(boolean)">setRelative</a>, <a href="ExecuteOn.html#setSkipEmptyFilesets(boolean)">setSkipEmptyFilesets</a>, <a href="ExecuteOn.html#setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)">setType</a>, <a href="ExecuteOn.html#setupRedirector()">setupRedirector</a>, <a href="ExecuteOn.html#setVerbose(boolean)">setVerbose</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.ExecTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</a></h3>
<code><a href="ExecTask.html#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">addConfiguredRedirector</a>, <a href="ExecTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</a>, <a href="ExecTask.html#createArg()">createArg</a>, <a href="ExecTask.html#createWatchdog()">createWatchdog</a>, <a href="ExecTask.html#execute()">execute</a>, <a href="ExecTask.html#getOs()">getOs</a>, <a href="ExecTask.html#getOsFamily()">getOsFamily</a>, <a href="ExecTask.html#getResolveExecutable()">getResolveExecutable</a>, <a href="ExecTask.html#isValidOs()">isValidOs</a>, <a href="ExecTask.html#logFlush()">logFlush</a>, <a href="ExecTask.html#maybeSetResultPropertyValue(int)">maybeSetResultPropertyValue</a>, <a href="ExecTask.html#prepareExec()">prepareExec</a>, <a href="ExecTask.html#resolveExecutable(java.lang.String,boolean)">resolveExecutable</a>, <a href="ExecTask.html#runExecute(org.apache.tools.ant.taskdefs.Execute)">runExecute</a>, <a href="ExecTask.html#setAppend(boolean)">setAppend</a>, <a href="ExecTask.html#setCommand(org.apache.tools.ant.types.Commandline)">setCommand</a>, <a href="ExecTask.html#setDir(java.io.File)">setDir</a>, <a href="ExecTask.html#setDiscardError(boolean)">setDiscardError</a>, <a href="ExecTask.html#setDiscardOutput(boolean)">setDiscardOutput</a>, <a href="ExecTask.html#setError(java.io.File)">setError</a>, <a href="ExecTask.html#setErrorProperty(java.lang.String)">setErrorProperty</a>, <a href="ExecTask.html#setExecutable(java.lang.String)">setExecutable</a>, <a href="ExecTask.html#setFailIfExecutionFails(boolean)">setFailIfExecutionFails</a>, <a href="ExecTask.html#setFailonerror(boolean)">setFailonerror</a>, <a href="ExecTask.html#setInput(java.io.File)">setInput</a>, <a href="ExecTask.html#setInputString(java.lang.String)">setInputString</a>, <a href="ExecTask.html#setLogError(boolean)">setLogError</a>, <a href="ExecTask.html#setNewenvironment(boolean)">setNewenvironment</a>, <a href="ExecTask.html#setOs(java.lang.String)">setOs</a>, <a href="ExecTask.html#setOsFamily(java.lang.String)">setOsFamily</a>, <a href="ExecTask.html#setOutput(java.io.File)">setOutput</a>, <a href="ExecTask.html#setOutputproperty(java.lang.String)">setOutputproperty</a>, <a href="ExecTask.html#setResolveExecutable(boolean)">setResolveExecutable</a>, <a href="ExecTask.html#setResultProperty(java.lang.String)">setResultProperty</a>, <a href="ExecTask.html#setSearchPath(boolean)">setSearchPath</a>, <a href="ExecTask.html#setSpawn(boolean)">setSpawn</a>, <a href="ExecTask.html#setTimeout(java.lang.Integer)">setTimeout</a>, <a href="ExecTask.html#setTimeout(java.lang.Long)">setTimeout</a>, <a href="ExecTask.html#setVMLauncher(boolean)">setVMLauncher</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Transform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Transform</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
