<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Augment Task</title>
</head>

<body>

<h2>Augment</h2>
<p><em>Since Apache Ant 1.8.1</em></p>
<h3>Description</h3>
<p>Modify an existing reference by adding nested elements or (re-)assigning properties mapped as XML
attributes. This is an unusual task that makes use of <PERSON><PERSON>'s internal processing mechanisms to reload
a previously declared reference by means of the <var>id</var> attribute, then treats the
declared <code>augment</code> element as though it were the original element.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>id</td>
    <td>The <var>id</var> of the reference to augment. If no such reference has been declared
      a <code>BuildException</code> is thrown.</td>
    <td>Yes</td>
  </tr>
</table>
<p>Additional permissible attributes are dependent on the reference to be modified.</p>

<h3>Parameters specified as nested elements</h3>

<p>Permissible nested elements are dependent on the reference to be modified.</p>

<h3>Examples</h3>

<p>Given</p>

<pre>&lt;fileset id="input-fs" dir="${basedir}"/&gt;</pre>

<p>invocation</p>

<pre>&lt;augment id="input-fs" excludes="foo"/&gt;</pre>

<p>modifies the <var>excludes</var> attribute of <samp>input-fs</samp>, whereas</p>

<pre>
&lt;augment id="input-fs"&gt;
  &lt;filename name="bar"/&gt;
&lt;/augment&gt;</pre>

<p>adds a <code>filename</code> selector to <samp>input-fs</samp>.</p>

</body>
</html>
