<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>jarlib-display Task</title>
</head>

<body>

<h2 id="jarlib-display">jarlib-display</h2>
<h3>Description</h3>
<p>Display the "Optional Package" and "Package Specification" information contained within the
specified jars.</p>

<p>Note that this task works with extensions as defined by the "Optional Package" specification.
For more information about optional packages, see the document <em>Optional Package Versioning</em>
in the documentation bundle for your Java Standard Edition package, in
file <samp>guide/extensions/versioning.html</samp> or the
online <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/extensions/versioning.html"
target="_top">Extension and ExtensionSet documentation</a> for further details.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The file to display extension information about</td>
    <td>Yes, unless a nested <code>&lt;fileset&gt;</code> is specified</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>fileset</h4>
<p><a href="../Types/fileset.html">FileSet</a>s contain list of files to display Extension
information about.</p>

<h3>Examples</h3>
<p>Display Extension info for a single file</p>
<pre>&lt;jarlib-display file=&quot;myfile.jar&quot;&gt;</pre>

<p>Display Extension info for a fileset</p>
<pre>
&lt;jarlib-display&gt;
  &lt;fileset dir="lib"&gt;
    &lt;include name="*.jar"/&gt;
  &lt;/fileset&gt;
&lt;/jarlib-display&gt;</pre>

</body>
</html>
