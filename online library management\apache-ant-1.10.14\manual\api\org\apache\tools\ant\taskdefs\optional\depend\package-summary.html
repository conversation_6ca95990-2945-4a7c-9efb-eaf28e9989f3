<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.taskdefs.optional.depend (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.depend">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.taskdefs.optional.depend" class="title">Package org.apache.tools.ant.taskdefs.optional.depend</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.taskdefs.optional.depend</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="constantpool/package-summary.html">org.apache.tools.ant.taskdefs.optional.depend.constantpool</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AntAnalyzer.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">AntAnalyzer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An analyzer which uses the depend task's bytecode classes to analyze
 dependencies</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ClassFile.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFile</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A ClassFile object stores information about a Java class.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="ClassFileIterator.html" title="interface in org.apache.tools.ant.taskdefs.optional.depend">ClassFileIterator</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Iterator interface for iterating over a set of class files</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ClassFileUtils.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">ClassFileUtils</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Utility class file routines.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Depend.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">Depend</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Generates a dependency file for a given set of classes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DirectoryIterator.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">DirectoryIterator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">An iterator which iterates through the contents of a java directory.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JarFileIterator.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">JarFileIterator</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A class file iterator which iterates through the contents of a Java jar
 file.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
