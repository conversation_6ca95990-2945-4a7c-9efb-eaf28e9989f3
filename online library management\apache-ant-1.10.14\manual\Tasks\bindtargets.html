<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Bindtargets Task</title>
</head>

<body>

<h2 id="ant">Bindtargets</h2>
<p><em>Since Apache Ant 1.8.2</em></p>
<h3>Description</h3>

<p>Make some target the extension of some
defined <a href="../targets.html#extension-points">extension point</a>. It will make the list of
targets dependencies of the extension point.</p>

<p>This target is useful when you want to have a target to participate in another build workflow
which explicitly exposes an extension point for that kind of insertion. Thus the target to bind and
the extension point to bind to are both declared in some imported build files. But directly
modifying the target dependency graph of these external build files may have a side effect on some
other project which imports them. This task helps to modify the target dependencies but only in your
context.</p>

<p><strong>Note</strong>: this task is quite equivalent to the definition of an intermediate target
which will be the bridge between the target to bind and the extension point. For instance:</p>
<pre>&lt;bindtargets targets="jar,javadoc" extensionPoint="dist"/&gt;</pre>
<p>is quite equivalent to:</p>
<pre>&lt;target name="bind-to-dist" depends="jar,javadoc" extensionOf="dist"/&gt;</pre>
<p>This task basically avoids the creation of a target.</p>

<p>The <code>bindtargets</code> task may only be used as a top-level task. This means that it may
not be used in a target. This is making the target dependency graph static and predictable as soon
as every build file is loaded.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>targets</td>
    <td>a comma separated list of target names to bind.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>extensionPoint</td>
    <td>the name of the extension point to bind the targets to.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>onMissingExtensionPoint</td>
    <td>What to do if this target tries to extend a
    missing <a href="../targets.html#extension-points">extension-point</a>: <q>fail</q>, <q>warn</q>, <q>ignore</q>.</td>
    <td>No; defaults to <q>fail</q></td>
  </tr>
</table>

<h3>Examples</h3>
<pre>&lt;bindtargets targets=&quot;build-jar,build-src-jar&quot; extensionPoint=&quot;dist&quot;/&gt;</pre>

</body>
</html>
