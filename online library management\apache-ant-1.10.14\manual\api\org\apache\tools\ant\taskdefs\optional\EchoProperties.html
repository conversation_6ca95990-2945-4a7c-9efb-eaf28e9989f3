<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>EchoProperties (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional, class: EchoProperties">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<h1 title="Class EchoProperties" class="title">Class EchoProperties</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.EchoProperties</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">EchoProperties</span>
<span class="extends-implements">extends <a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Displays all the current properties in the build. The output can be sent to
 a file if desired.
 <p>
 Attribute "destfile" defines a file to send the properties to. This can be
 processed as a standard property file later.
 </p>
 <p>
 Attribute "prefix" defines a prefix which is used to filter the properties
 only those properties starting with this prefix will be echoed.
 </p>
 <p>
 By default, the "failonerror" attribute is enabled. If an error occurs while
 writing the properties to a file, and this attribute is enabled, then a
 BuildException will be thrown. If disabled, then IO errors will be reported
 as a log statement, but no error will be thrown.
 </p>
 <p>Examples:</p><pre>
  &lt;echoproperties  /&gt;
 </pre>
 Report the current properties to the log.
 <pre>
  &lt;echoproperties destfile="my.properties" /&gt;
 </pre>
 Report the current properties to the file "my.properties", and will
 fail the build if the file could not be created or written to.
 <pre>
  &lt;echoproperties destfile="my.properties" failonerror="false"
      prefix="ant" /&gt;
 </pre>
 Report all properties beginning with 'ant' to the file
 "my.properties", and will log a message if the file could not be created or
 written to, but will still allow the build to continue.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="EchoProperties.FormatAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a></code></div>
<div class="col-last even-row-color">
<div class="block">A enumerated type for the format attribute.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">EchoProperties</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPropertyset(org.apache.tools.ant.types.PropertySet)" class="member-name-link">addPropertyset</a><wbr>(<a href="../../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;ps)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A set of properties to write.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#jdkSaveProperties(java.util.Properties,java.io.OutputStream,java.lang.String)" class="member-name-link">jdkSaveProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a>&nbsp;props,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;header)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">JDK 1.2 allows for the safer method
 <code>Properties.store(OutputStream, String)</code>, which throws an
 <code>IOException</code> on an output error.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveProperties(java.util.Hashtable,java.io.OutputStream)" class="member-name-link">saveProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;allProps,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send the key/value pairs in the hashtable to the given output stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestfile(java.io.File)" class="member-name-link">setDestfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destfile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a file to store the property output.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnError(boolean)" class="member-name-link">setFailOnError</a><wbr>(boolean&nbsp;failonerror)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, the task will fail if an error occurs writing the properties
 file, otherwise errors are just logged.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFormat(org.apache.tools.ant.taskdefs.optional.EchoProperties.FormatAttribute)" class="member-name-link">setFormat</a><wbr>(<a href="EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a>&nbsp;ea)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output format - xml or text.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPrefix(java.lang.String)" class="member-name-link">setPrefix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If the prefix is set, then only properties which start with this
 prefix string will be recorded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRegex(java.lang.String)" class="member-name-link">setRegex</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;regex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If the regex is set, then only properties whose names match it
 will be recorded.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcfile(java.io.File)" class="member-name-link">setSrcfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the input file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xmlSaveProperties(java.util.Properties,java.io.OutputStream)" class="member-name-link">xmlSaveProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a>&nbsp;props,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Output the properties as xml output.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#init()">init</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>EchoProperties</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">EchoProperties</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSrcfile(java.io.File)">
<h3>setSrcfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Sets the input file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the input file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestfile(java.io.File)">
<h3>setDestfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destfile)</span></div>
<div class="block">Set a file to store the property output.  If this is never specified,
  then the output will be sent to the Ant log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destfile</code> - file to store the property output</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnError(boolean)">
<h3>setFailOnError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnError</span><wbr><span class="parameters">(boolean&nbsp;failonerror)</span></div>
<div class="block">If true, the task will fail if an error occurs writing the properties
 file, otherwise errors are just logged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failonerror</code> - <code>true</code> if IO exceptions are reported as build
      exceptions, or <code>false</code> if IO exceptions are ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPrefix(java.lang.String)">
<h3>setPrefix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPrefix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">If the prefix is set, then only properties which start with this
 prefix string will be recorded. If regex is not set and  if this
 is never set, or it is set to an empty string or <code>null</code>,
 then all properties will be recorded.

 <p>For example, if the attribute is set as:</p>
 <pre>&lt;echoproperties  prefix="ant." /&gt;</pre>
 then the property "ant.home" will be recorded, but "ant-example"
 will not.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - The new prefix value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRegex(java.lang.String)">
<h3>setRegex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRegex</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;regex)</span></div>
<div class="block">If the regex is set, then only properties whose names match it
 will be recorded.  If prefix is not set and if this is never set,
 or it is set to an empty string or <code>null</code>, then all
 properties will be recorded.

 <p>For example, if the attribute is set as:</p>
 <pre>&lt;echoproperties  prefix=".*ant.*" /&gt;</pre>
 then the properties "ant.home" and "user.variant" will be recorded,
 but "ant-example" will not.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>regex</code> - The new regex value</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addPropertyset(org.apache.tools.ant.types.PropertySet)">
<h3>addPropertyset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPropertyset</span><wbr><span class="parameters">(<a href="../../types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</a>&nbsp;ps)</span></div>
<div class="block">A set of properties to write.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ps</code> - the property set to write</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFormat(org.apache.tools.ant.taskdefs.optional.EchoProperties.FormatAttribute)">
<h3>setFormat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFormat</span><wbr><span class="parameters">(<a href="EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties.FormatAttribute</a>&nbsp;ea)</span></div>
<div class="block">Set the output format - xml or text.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ea</code> - an enumerated <code>FormatAttribute</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - trouble, probably file IO</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveProperties(java.util.Hashtable,java.io.OutputStream)">
<h3>saveProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;allProps,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</span>
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Send the key/value pairs in the hashtable to the given output stream.
 Only those properties matching the <code>prefix</code> constraint will be
 sent to the output stream.
 The output stream will be closed when this method returns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>allProps</code> - propfile to save</dd>
<dd><code>os</code> - output stream</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on output errors</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xmlSaveProperties(java.util.Properties,java.io.OutputStream)">
<h3>xmlSaveProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">xmlSaveProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a>&nbsp;props,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</span>
                          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Output the properties as xml output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>props</code> - the properties to save</dd>
<dd><code>os</code> - the output stream to write to (Note this gets closed)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error in writing to the stream</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="jdkSaveProperties(java.util.Properties,java.io.OutputStream,java.lang.String)">
<h3>jdkSaveProperties</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">jdkSaveProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Properties.html" title="class or interface in java.util" class="external-link">Properties</a>&nbsp;props,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;header)</span>
                          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">JDK 1.2 allows for the safer method
 <code>Properties.store(OutputStream, String)</code>, which throws an
 <code>IOException</code> on an output error.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>props</code> - the properties to record</dd>
<dd><code>os</code> - record the properties to this output stream</dd>
<dd><code>header</code> - prepend this header to the property output</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on an I/O error during a write.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
