<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Retry Task</title>
</head>
<body>
<h2>Retry</h2>
<p><em>Since Apache Ant 1.7.1</em></p>
<h3>Description</h3>
<p><code>Retry</code> is a container which executes a single nested task until either: there is no
failure; or: its <var>retrycount</var> has been exceeded. If this happens
a <code>BuildException</code> is thrown.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>retrycount</td>
    <td>number of times to attempt to execute the nested task</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>retrydelay</td>
    <td>number of milliseconds to wait between retry attempts task. <em>Since Apache Ant
    1.8.3</em></td>
    <td>No; defaults to no delay</td>
  </tr>
</table>
<p>Any valid Ant task may be embedded within the retry task.</p>

<h3>Example</h3>
<p>This example shows how to use <code>&lt;retry&gt;</code> to wrap a task which must interact with
an unreliable network resource.</p>
<pre>
&lt;retry retrycount="3"&gt;
  &lt;get src="https://www.unreliable-server.com/unreliable.tar.gz"
       dest="/home/<USER>/unreliable.tar.gz"/&gt;
&lt;/retry&gt;</pre>
</body>
</html>
