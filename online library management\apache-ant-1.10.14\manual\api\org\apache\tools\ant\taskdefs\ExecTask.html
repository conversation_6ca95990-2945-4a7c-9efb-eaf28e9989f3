<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ExecTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: ExecTask">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class ExecTask" class="title">Class ExecTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.ExecTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ExecTask</span>
<span class="extends-implements">extends <a href="../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Executes a given command if the os platform is appropriate.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color"><code><a href="#cmdl" class="member-name-link">cmdl</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#failOnError" class="member-name-link">failOnError</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#newEnvironment" class="member-name-link">newEnvironment</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a></code></div>
<div class="col-second odd-row-color"><code><a href="#redirector" class="member-name-link">redirector</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a></code></div>
<div class="col-second even-row-color"><code><a href="#redirectorElement" class="member-name-link">redirectorElement</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ExecTask</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Create an instance.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Task)" class="member-name-link">ExecTask</a><wbr>(<a href="../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;owner)</code></div>
<div class="col-last odd-row-color">
<div class="block">create an instance that is helping another task.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)" class="member-name-link">addConfiguredRedirector</a><wbr>(<a href="../types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a>&nbsp;redirectorElement)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a <code>RedirectorElement</code> to this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addEnv(org.apache.tools.ant.types.Environment.Variable)" class="member-name-link">addEnv</a><wbr>(<a href="../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;var)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an environment variable to the launched process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkConfiguration()" class="member-name-link">checkConfiguration</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Has the user set all necessary attributes?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createArg()" class="member-name-link">createArg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a command-line argument.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createHandler()" class="member-name-link">createHandler</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the StreamHandler to use with our Execute instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWatchdog()" class="member-name-link">createWatchdog</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create the Watchdog to kill a runaway process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do the work.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOs()" class="member-name-link">getOs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">List of operating systems on which the command may be executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOsFamily()" class="member-name-link">getOsFamily</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Restrict this execution to a single OS Family</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResolveExecutable()" class="member-name-link">getResolveExecutable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether to attempt to resolve the executable to a
 file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isValidOs()" class="member-name-link">isValidOs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is this the OS the user wanted?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logFlush()" class="member-name-link">logFlush</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flush the output stream - if there is one.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#maybeSetResultPropertyValue(int)" class="member-name-link">maybeSetResultPropertyValue</a><wbr>(int&nbsp;result)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Helper method to set result property to the
 passed in value if appropriate.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#prepareExec()" class="member-name-link">prepareExec</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an Execute instance with the correct working directory set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveExecutable(java.lang.String,boolean)" class="member-name-link">resolveExecutable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;exec,
 boolean&nbsp;mustSearchPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The method attempts to figure out where the executable is so that we can feed
 the full path.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runExec(org.apache.tools.ant.taskdefs.Execute)" class="member-name-link">runExec</a><wbr>(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the command using the given Execute instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runExecute(org.apache.tools.ant.taskdefs.Execute)" class="member-name-link">runExecute</a><wbr>(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">A Utility method for this classes and subclasses to run an
 Execute instance (an external command).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAppend(boolean)" class="member-name-link">setAppend</a><wbr>(boolean&nbsp;append)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether output should be appended to or overwrite an existing file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCommand(org.apache.tools.ant.types.Commandline)" class="member-name-link">setCommand</a><wbr>(<a href="../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmdl)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a command line.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDir(java.io.File)" class="member-name-link">setDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the working directory of the process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDiscardError(boolean)" class="member-name-link">setDiscardError</a><wbr>(boolean&nbsp;discard)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether error output should be discarded.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDiscardOutput(boolean)" class="member-name-link">setDiscardOutput</a><wbr>(boolean&nbsp;discard)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether output should be discarded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setError(java.io.File)" class="member-name-link">setError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;error)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the File to which the error stream of the process should be redirected.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty(java.lang.String)" class="member-name-link">setErrorProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the property whose value should be set to the error of
 the process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExecutable(java.lang.String)" class="member-name-link">setExecutable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the name of the executable program.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailIfExecutionFails(boolean)" class="member-name-link">setFailIfExecutionFails</a><wbr>(boolean&nbsp;flag)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to stop the build if program cannot be started.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailonerror(boolean)" class="member-name-link">setFailonerror</a><wbr>(boolean&nbsp;fail)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fail if the command exits with a non-zero return code.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(java.io.File)" class="member-name-link">setInput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;input)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the input file to use for the task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputString(java.lang.String)" class="member-name-link">setInputString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the string to use as input.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogError(boolean)" class="member-name-link">setLogError</a><wbr>(boolean&nbsp;logError)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Controls whether error output of exec is logged.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewenvironment(boolean)" class="member-name-link">setNewenvironment</a><wbr>(boolean&nbsp;newenv)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do not propagate old environment when new environment variables are specified.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOs(java.lang.String)" class="member-name-link">setOs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;os)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">List of operating systems on which the command may be executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOsFamily(java.lang.String)" class="member-name-link">setOsFamily</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;osFamily)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Restrict this execution to a single OS Family</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutput(java.io.File)" class="member-name-link">setOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;out)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">File the output of the process is redirected to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputproperty(java.lang.String)" class="member-name-link">setOutputproperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputProp)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the property name whose value should be set to the output of
 the process.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResolveExecutable(boolean)" class="member-name-link">setResolveExecutable</a><wbr>(boolean&nbsp;resolveExecutable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to attempt to resolve the executable to a file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResultProperty(java.lang.String)" class="member-name-link">setResultProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resultProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of a property in which the return code of the
 command should be stored.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSearchPath(boolean)" class="member-name-link">setSearchPath</a><wbr>(boolean&nbsp;searchPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to search nested, then
 system PATH environment variables for the executable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSpawn(boolean)" class="member-name-link">setSpawn</a><wbr>(boolean&nbsp;spawn)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not you want the process to be spawned.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeout(java.lang.Integer)" class="member-name-link">setTimeout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timeout in milliseconds after which the process will be killed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeout(java.lang.Long)" class="member-name-link">setTimeout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timeout in milliseconds after which the process will be killed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupRedirector()" class="member-name-link">setupRedirector</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set up properties on the redirector that we needed to store locally.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVMLauncher(boolean)" class="member-name-link">setVMLauncher</a><wbr>(boolean&nbsp;vmLauncher)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to launch new process with VM, otherwise use the OS's shell.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="failOnError">
<h3>failOnError</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">failOnError</span></div>
</section>
</li>
<li>
<section class="detail" id="newEnvironment">
<h3>newEnvironment</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">newEnvironment</span></div>
</section>
</li>
<li>
<section class="detail" id="cmdl">
<h3>cmdl</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">cmdl</span></div>
</section>
</li>
<li>
<section class="detail" id="redirector">
<h3>redirector</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a></span>&nbsp;<span class="element-name">redirector</span></div>
</section>
</li>
<li>
<section class="detail" id="redirectorElement">
<h3>redirectorElement</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a></span>&nbsp;<span class="element-name">redirectorElement</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ExecTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExecTask</span>()</div>
<div class="block">Create an instance.
 Needs to be configured by binding to a project.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Task)">
<h3>ExecTask</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExecTask</span><wbr><span class="parameters">(<a href="../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;owner)</span></div>
<div class="block">create an instance that is helping another task.
 Project, OwningTarget, TaskName and description are all
 pulled out</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>owner</code> - task that we belong to</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setSpawn(boolean)">
<h3>setSpawn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSpawn</span><wbr><span class="parameters">(boolean&nbsp;spawn)</span></div>
<div class="block">Set whether or not you want the process to be spawned.
 Default is false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>spawn</code> - if true you do not want Ant to wait for the end of the process.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeout(java.lang.Long)">
<h3>setTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeout</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;value)</span></div>
<div class="block">Set the timeout in milliseconds after which the process will be killed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - timeout in milliseconds.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeout(java.lang.Integer)">
<h3>setTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeout</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;value)</span></div>
<div class="block">Set the timeout in milliseconds after which the process will be killed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - timeout in milliseconds.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setExecutable(java.lang.String)">
<h3>setExecutable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExecutable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set the name of the executable program.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - the name of the executable program.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDir(java.io.File)">
<h3>setDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;d)</span></div>
<div class="block">Set the working directory of the process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the working directory of the process.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOs(java.lang.String)">
<h3>setOs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;os)</span></div>
<div class="block">List of operating systems on which the command may be executed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>os</code> - list of operating systems on which the command may be executed.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOs()">
<h3>getOs</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOs</span>()</div>
<div class="block">List of operating systems on which the command may be executed.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCommand(org.apache.tools.ant.types.Commandline)">
<h3>setCommand</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCommand</span><wbr><span class="parameters">(<a href="../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmdl)</span></div>
<div class="block">Sets a command line.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmdl</code> - command line.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutput(java.io.File)">
<h3>setOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;out)</span></div>
<div class="block">File the output of the process is redirected to. If error is not
 redirected, it too will appear in the output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - name of a file to which output should be sent.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInput(java.io.File)">
<h3>setInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;input)</span></div>
<div class="block">Set the input file to use for the task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - name of a file from which to get input.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInputString(java.lang.String)">
<h3>setInputString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputString)</span></div>
<div class="block">Set the string to use as input.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputString</code> - the string which is used as the input source.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogError(boolean)">
<h3>setLogError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogError</span><wbr><span class="parameters">(boolean&nbsp;logError)</span></div>
<div class="block">Controls whether error output of exec is logged. This is only useful when
 output is being redirected and error output is desired in the Ant log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>logError</code> - set to true to log error output in the normal ant log.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setError(java.io.File)">
<h3>setError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;error)</span></div>
<div class="block">Set the File to which the error stream of the process should be redirected.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>error</code> - a file to which stderr should be sent.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputproperty(java.lang.String)">
<h3>setOutputproperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputproperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputProp)</span></div>
<div class="block">Sets the property name whose value should be set to the output of
 the process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputProp</code> - name of property.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty(java.lang.String)">
<h3>setErrorProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</span></div>
<div class="block">Sets the name of the property whose value should be set to the error of
 the process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorProperty</code> - name of property.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailonerror(boolean)">
<h3>setFailonerror</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailonerror</span><wbr><span class="parameters">(boolean&nbsp;fail)</span></div>
<div class="block">Fail if the command exits with a non-zero return code.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fail</code> - if true fail the command on non-zero return code.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNewenvironment(boolean)">
<h3>setNewenvironment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewenvironment</span><wbr><span class="parameters">(boolean&nbsp;newenv)</span></div>
<div class="block">Do not propagate old environment when new environment variables are specified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newenv</code> - if true, do not propagate old environment
 when new environment variables are specified.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setResolveExecutable(boolean)">
<h3>setResolveExecutable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResolveExecutable</span><wbr><span class="parameters">(boolean&nbsp;resolveExecutable)</span></div>
<div class="block">Set whether to attempt to resolve the executable to a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>resolveExecutable</code> - if true, attempt to resolve the
 path of the executable.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSearchPath(boolean)">
<h3>setSearchPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSearchPath</span><wbr><span class="parameters">(boolean&nbsp;searchPath)</span></div>
<div class="block">Set whether to search nested, then
 system PATH environment variables for the executable.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>searchPath</code> - if true, search PATHs.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResolveExecutable()">
<h3>getResolveExecutable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getResolveExecutable</span>()</div>
<div class="block">Indicates whether to attempt to resolve the executable to a
 file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the resolveExecutable flag</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addEnv(org.apache.tools.ant.types.Environment.Variable)">
<h3>addEnv</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addEnv</span><wbr><span class="parameters">(<a href="../types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</a>&nbsp;var)</span></div>
<div class="block">Add an environment variable to the launched process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>var</code> - new environment variable.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createArg()">
<h3>createArg</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</a></span>&nbsp;<span class="element-name">createArg</span>()</div>
<div class="block">Adds a command-line argument.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new command line argument created.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setResultProperty(java.lang.String)">
<h3>setResultProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResultProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resultProperty)</span></div>
<div class="block">Sets the name of a property in which the return code of the
 command should be stored. Only of interest if failonerror=false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>resultProperty</code> - name of property.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="maybeSetResultPropertyValue(int)">
<h3>maybeSetResultPropertyValue</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">maybeSetResultPropertyValue</span><wbr><span class="parameters">(int&nbsp;result)</span></div>
<div class="block">Helper method to set result property to the
 passed in value if appropriate.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>result</code> - value desired for the result property value.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailIfExecutionFails(boolean)">
<h3>setFailIfExecutionFails</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailIfExecutionFails</span><wbr><span class="parameters">(boolean&nbsp;flag)</span></div>
<div class="block">Set whether to stop the build if program cannot be started.
 Defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - stop the build if program cannot be started.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAppend(boolean)">
<h3>setAppend</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAppend</span><wbr><span class="parameters">(boolean&nbsp;append)</span></div>
<div class="block">Set whether output should be appended to or overwrite an existing file.
 Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>append</code> - if true append is desired.</dd>
<dt>Since:</dt>
<dd>1.30, Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDiscardOutput(boolean)">
<h3>setDiscardOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDiscardOutput</span><wbr><span class="parameters">(boolean&nbsp;discard)</span></div>
<div class="block">Whether output should be discarded.

 <p>Defaults to false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>discard</code> - if true output streams are discarded.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.10</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDiscardError(boolean)"><code>setDiscardError(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDiscardError(boolean)">
<h3>setDiscardError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDiscardError</span><wbr><span class="parameters">(boolean&nbsp;discard)</span></div>
<div class="block">Whether error output should be discarded.

 <p>Defaults to false.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>discard</code> - if true error streams are discarded.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.10</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDiscardOutput(boolean)"><code>setDiscardOutput(boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">
<h3>addConfiguredRedirector</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredRedirector</span><wbr><span class="parameters">(<a href="../types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</a>&nbsp;redirectorElement)</span></div>
<div class="block">Add a <code>RedirectorElement</code> to this task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>redirectorElement</code> - <code>RedirectorElement</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOsFamily(java.lang.String)">
<h3>setOsFamily</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOsFamily</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;osFamily)</span></div>
<div class="block">Restrict this execution to a single OS Family</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>osFamily</code> - the family to restrict to.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOsFamily()">
<h3>getOsFamily</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOsFamily</span>()</div>
<div class="block">Restrict this execution to a single OS Family</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the family to restrict to.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveExecutable(java.lang.String,boolean)">
<h3>resolveExecutable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">resolveExecutable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;exec,
 boolean&nbsp;mustSearchPath)</span></div>
<div class="block">The method attempts to figure out where the executable is so that we can feed
 the full path. We first try basedir, then the exec dir, and then
 fallback to the straight executable name (i.e. on the path).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exec</code> - the name of the executable.</dd>
<dd><code>mustSearchPath</code> - if true, the executable will be looked up in
 the PATH environment and the absolute path is returned.</dd>
<dt>Returns:</dt>
<dd>the executable as a full path if it can be determined.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Do the work.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - in a number of circumstances:
 <ul>
 <li>if failIfExecFails is set to true and the process cannot be started</li>
 <li>the java13command launcher can send build exceptions</li>
 <li>this list is not exhaustive or limitative</li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkConfiguration()">
<h3>checkConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkConfiguration</span>()
                           throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Has the user set all necessary attributes?</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there are missing required parameters.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupRedirector()">
<h3>setupRedirector</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setupRedirector</span>()</div>
<div class="block">Set up properties on the redirector that we needed to store locally.</div>
</section>
</li>
<li>
<section class="detail" id="isValidOs()">
<h3>isValidOs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isValidOs</span>()</div>
<div class="block">Is this the OS the user wanted?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean.
 <ul>
 <li>
 <li><code>true</code> if the os and osfamily attributes are null.</li>
 <li><code>true</code> if osfamily is set, and the os family and must match
 that of the current OS, according to the logic of
 <a href="condition/Os.html#isOs(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"><code>Os.isOs(String, String, String, String)</code></a>, and the result of the
 <code>os</code> attribute must also evaluate true.
 </li>
 <li>
 <code>true</code> if os is set, and the system.property os.name
 is found in the os attribute,</li>
 <li><code>false</code> otherwise.</li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVMLauncher(boolean)">
<h3>setVMLauncher</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVMLauncher</span><wbr><span class="parameters">(boolean&nbsp;vmLauncher)</span></div>
<div class="block">Set whether to launch new process with VM, otherwise use the OS's shell.
 Default value is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>vmLauncher</code> - true if we want to launch new process with VM,
 false if we want to use the OS's shell.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="prepareExec()">
<h3>prepareExec</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a></span>&nbsp;<span class="element-name">prepareExec</span>()
                       throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create an Execute instance with the correct working directory set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an instance of the Execute class.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - under unknown circumstances.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runExecute(org.apache.tools.ant.taskdefs.Execute)">
<h3>runExecute</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">runExecute</span><wbr><span class="parameters">(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</span>
                         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">A Utility method for this classes and subclasses to run an
 Execute instance (an external command).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exe</code> - instance of the execute class.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - in case of problem to attach to the stdin/stdout/stderr
 streams of the process.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="runExec(org.apache.tools.ant.taskdefs.Execute)">
<h3>runExec</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">runExec</span><wbr><span class="parameters">(<a href="Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</a>&nbsp;exe)</span>
                throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the command using the given Execute instance. This may be
 overridden by subclasses.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exe</code> - instance of Execute to run.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the new process could not be started
 only if failIfExecFails is set to true (the default).</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createHandler()">
<h3>createHandler</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</a></span>&nbsp;<span class="element-name">createHandler</span>()
                                      throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the StreamHandler to use with our Execute instance.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>instance of ExecuteStreamHandler.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - under unknown circumstances.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createWatchdog()">
<h3>createWatchdog</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</a></span>&nbsp;<span class="element-name">createWatchdog</span>()
                                  throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Create the Watchdog to kill a runaway process.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>instance of ExecuteWatchdog.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - under unknown circumstances.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logFlush()">
<h3>logFlush</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logFlush</span>()</div>
<div class="block">Flush the output stream - if there is one.</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
