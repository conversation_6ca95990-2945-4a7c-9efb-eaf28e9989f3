<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Sync.MyCopy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Sync, class: MyCopy">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Sync.MyCopy" class="title">Class Sync.MyCopy</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copy</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Sync.MyCopy</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">Sync.MyCopy</span>
<span class="extends-implements">extends <a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></span></div>
<div class="block">Subclass Copy in order to access it's file/dir maps.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.Copy">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></h3>
<code><a href="Copy.html#completeDirMap">completeDirMap</a>, <a href="Copy.html#destDir">destDir</a>, <a href="Copy.html#destFile">destFile</a>, <a href="Copy.html#dirCopyMap">dirCopyMap</a>, <a href="Copy.html#failonerror">failonerror</a>, <a href="Copy.html#file">file</a>, <a href="Copy.html#fileCopyMap">fileCopyMap</a>, <a href="Copy.html#filesets">filesets</a>, <a href="Copy.html#fileUtils">fileUtils</a>, <a href="Copy.html#filtering">filtering</a>, <a href="Copy.html#flatten">flatten</a>, <a href="Copy.html#forceOverwrite">forceOverwrite</a>, <a href="Copy.html#includeEmpty">includeEmpty</a>, <a href="Copy.html#mapperElement">mapperElement</a>, <a href="Copy.html#preserveLastModified">preserveLastModified</a>, <a href="Copy.html#rcs">rcs</a>, <a href="Copy.html#verbosity">verbosity</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">MyCopy</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeEmptyDirs()" class="member-name-link">getIncludeEmptyDirs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the includeEmptyDirs attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getToDir()" class="member-name-link">getToDir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the destination directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scan(java.io.File,java.io.File,java.lang.String%5B%5D,java.lang.String%5B%5D)" class="member-name-link">scan</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;toDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;dirs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Compares source files to destination files to see if they should be
 copied.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scan(org.apache.tools.ant.types.Resource%5B%5D,java.io.File)" class="member-name-link">scan</a><wbr>(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;toDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Compares source resources to destination files to see if they
 should be copied.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#supportsNonFileResources()" class="member-name-link">supportsNonFileResources</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Yes, we can.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.Copy">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></h3>
<code><a href="Copy.html#add(org.apache.tools.ant.types.ResourceCollection)">add</a>, <a href="Copy.html#add(org.apache.tools.ant.util.FileNameMapper)">add</a>, <a href="Copy.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</a>, <a href="Copy.html#buildMap(java.io.File,java.io.File,java.lang.String%5B%5D,org.apache.tools.ant.util.FileNameMapper,java.util.Hashtable)">buildMap</a>, <a href="Copy.html#buildMap(org.apache.tools.ant.types.Resource%5B%5D,java.io.File,org.apache.tools.ant.util.FileNameMapper)">buildMap</a>, <a href="Copy.html#createFilterChain()">createFilterChain</a>, <a href="Copy.html#createFilterSet()">createFilterSet</a>, <a href="Copy.html#createMapper()">createMapper</a>, <a href="Copy.html#doFileOperations()">doFileOperations</a>, <a href="Copy.html#doResourceOperations(java.util.Map)">doResourceOperations</a>, <a href="Copy.html#execute()">execute</a>, <a href="Copy.html#getEncoding()">getEncoding</a>, <a href="Copy.html#getFileUtils()">getFileUtils</a>, <a href="Copy.html#getFilterChains()">getFilterChains</a>, <a href="Copy.html#getFilterSets()">getFilterSets</a>, <a href="Copy.html#getForce()">getForce</a>, <a href="Copy.html#getOutputEncoding()">getOutputEncoding</a>, <a href="Copy.html#getPreserveLastModified()">getPreserveLastModified</a>, <a href="Copy.html#isEnableMultipleMapping()">isEnableMultipleMapping</a>, <a href="Copy.html#setEnableMultipleMappings(boolean)">setEnableMultipleMappings</a>, <a href="Copy.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="Copy.html#setFailOnError(boolean)">setFailOnError</a>, <a href="Copy.html#setFile(java.io.File)">setFile</a>, <a href="Copy.html#setFiltering(boolean)">setFiltering</a>, <a href="Copy.html#setFlatten(boolean)">setFlatten</a>, <a href="Copy.html#setForce(boolean)">setForce</a>, <a href="Copy.html#setGranularity(long)">setGranularity</a>, <a href="Copy.html#setIncludeEmptyDirs(boolean)">setIncludeEmptyDirs</a>, <a href="Copy.html#setOutputEncoding(java.lang.String)">setOutputEncoding</a>, <a href="Copy.html#setOverwrite(boolean)">setOverwrite</a>, <a href="Copy.html#setPreserveLastModified(boolean)">setPreserveLastModified</a>, <a href="Copy.html#setPreserveLastModified(java.lang.String)">setPreserveLastModified</a>, <a href="Copy.html#setQuiet(boolean)">setQuiet</a>, <a href="Copy.html#setTodir(java.io.File)">setTodir</a>, <a href="Copy.html#setTofile(java.io.File)">setTofile</a>, <a href="Copy.html#setVerbose(boolean)">setVerbose</a>, <a href="Copy.html#validateAttributes()">validateAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>MyCopy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MyCopy</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="scan(java.io.File,java.io.File,java.lang.String[],java.lang.String[])">
<h3>scan</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scan</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;fromDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;toDir,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;dirs)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="Copy.html#scan(java.io.File,java.io.File,java.lang.String%5B%5D,java.lang.String%5B%5D)">Copy</a></code></span></div>
<div class="block">Compares source files to destination files to see if they should be
 copied.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Copy.html#scan(java.io.File,java.io.File,java.lang.String%5B%5D,java.lang.String%5B%5D)">scan</a></code>&nbsp;in class&nbsp;<code><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code></dd>
<dt>Parameters:</dt>
<dd><code>fromDir</code> - The source directory.</dd>
<dd><code>toDir</code> - The destination directory.</dd>
<dd><code>files</code> - A list of files to copy.</dd>
<dd><code>dirs</code> - A list of directories to copy.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="Copy.html#scan(java.io.File,java.io.File,java.lang.String%5B%5D,java.lang.String%5B%5D)"><code>Copy.scan(File, File, String[], String[])</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="scan(org.apache.tools.ant.types.Resource[],java.io.File)">
<h3>scan</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&gt;</span>&nbsp;<span class="element-name">scan</span><wbr><span class="parameters">(<a href="../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>[]&nbsp;resources,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;toDir)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="Copy.html#scan(org.apache.tools.ant.types.Resource%5B%5D,java.io.File)">Copy</a></code></span></div>
<div class="block">Compares source resources to destination files to see if they
 should be copied.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Copy.html#scan(org.apache.tools.ant.types.Resource%5B%5D,java.io.File)">scan</a></code>&nbsp;in class&nbsp;<code><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code></dd>
<dt>Parameters:</dt>
<dd><code>resources</code> - The source resources.</dd>
<dd><code>toDir</code> - The destination directory.</dd>
<dt>Returns:</dt>
<dd>a Map with the out-of-date resources as keys and an
 array of target file names as values.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="Copy.html#scan(org.apache.tools.ant.types.Resource%5B%5D,java.io.File)"><code>Copy.scan(Resource[], File)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getToDir()">
<h3>getToDir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getToDir</span>()</div>
<div class="block">Get the destination directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the destination directory</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIncludeEmptyDirs()">
<h3>getIncludeEmptyDirs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeEmptyDirs</span>()</div>
<div class="block">Get the includeEmptyDirs attribute.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if emptyDirs are to be included</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="supportsNonFileResources()">
<h3>supportsNonFileResources</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">supportsNonFileResources</span>()</div>
<div class="block">Yes, we can.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Copy.html#supportsNonFileResources()">supportsNonFileResources</a></code>&nbsp;in class&nbsp;<code><a href="Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</a></code></dd>
<dt>Returns:</dt>
<dd>true always.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
