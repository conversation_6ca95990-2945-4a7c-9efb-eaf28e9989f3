<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>TraXLiaison (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional, class: TraXLiaison">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional</a></div>
<h1 title="Class TraXLiaison" class="title">Class TraXLiaison</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.TraXLiaison</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a></code>, <code><a href="../XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code>, <code><a href="../XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></code>, <code><a href="../XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></code>, <code><a href="../XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a></code>, <code><a href="../XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TraXLiaison</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="../XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a>, <a href="../XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></span></div>
<div class="block">Concrete liaison for XSLT processor implementing TraX. (ie JAXP 1.1)</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.3</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.XSLTLiaison">Fields inherited from interface&nbsp;org.apache.tools.ant.taskdefs.<a href="../XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></h3>
<code><a href="../XSLTLiaison.html#FILE_PROTOCOL_PREFIX">FILE_PROTOCOL_PREFIX</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TraXLiaison</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for TraXLiaison.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addParam(java.lang.String,java.lang.Object)" class="member-name-link">addParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a parameter.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addParam(java.lang.String,java.lang.String)" class="member-name-link">addParam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a parameter.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(org.apache.tools.ant.taskdefs.XSLTProcess)" class="member-name-link">configure</a><wbr>(<a href="../XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a>&nbsp;xsltTask)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specific configuration for the TRaX liaison.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#error(javax.xml.transform.TransformerException)" class="member-name-link">error</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log an error.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fatalError(javax.xml.transform.TransformerException)" class="member-name-link">fatalError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log a fatal error.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getSystemId(java.io.File)" class="member-name-link">getSystemId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAttribute(java.lang.String,java.lang.Object)" class="member-name-link">setAttribute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a custom attribute for the JAXP factory implementation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEntityResolver(org.xml.sax.EntityResolver)" class="member-name-link">setEntityResolver</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>&nbsp;aResolver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the class to resolve entities during the transformation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFactory(java.lang.String)" class="member-name-link">setFactory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the factory name to use instead of JAXP default lookup.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFeature(java.lang.String,boolean)" class="member-name-link">setFeature</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a custom feature for the JAXP factory implementation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogger(org.apache.tools.ant.taskdefs.XSLTLogger)" class="member-name-link">setLogger</a><wbr>(<a href="../XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a>&nbsp;l)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a logger.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputProperty(java.lang.String,java.lang.String)" class="member-name-link">setOutputProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output property for the current transformer.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStylesheet(java.io.File)" class="member-name-link">setStylesheet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;stylesheet)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the stylesheet file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStylesheet(org.apache.tools.ant.types.Resource)" class="member-name-link">setStylesheet</a><wbr>(<a href="../../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;stylesheet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the stylesheet file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setURIResolver(javax.xml.transform.URIResolver)" class="member-name-link">setURIResolver</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/URIResolver.html" title="class or interface in javax.xml.transform" class="external-link">URIResolver</a>&nbsp;aResolver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the class to resolve URIs during the transformation</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#transform(java.io.File,java.io.File)" class="member-name-link">transform</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;infile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Transform an input file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#warning(javax.xml.transform.TransformerException)" class="member-name-link">warning</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log a warning.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TraXLiaison</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TraXLiaison</span>()
            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Constructor for TraXLiaison.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - never</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setStylesheet(java.io.File)">
<h3>setStylesheet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStylesheet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;stylesheet)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Set the stylesheet file.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison.html#setStylesheet(java.io.File)">setStylesheet</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stylesheet</code> - a <code>File</code> value</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStylesheet(org.apache.tools.ant.types.Resource)">
<h3>setStylesheet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStylesheet</span><wbr><span class="parameters">(<a href="../../types/Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;stylesheet)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Set the stylesheet file.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison3.html#setStylesheet(org.apache.tools.ant.types.Resource)">setStylesheet</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stylesheet</code> - a <a href="../../types/Resource.html" title="class in org.apache.tools.ant.types"><code>Resource</code></a> value</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="transform(java.io.File,java.io.File)">
<h3>transform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">transform</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;infile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outfile)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Transform an input file.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison.html#transform(java.io.File,java.io.File)">transform</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code></dd>
<dt>Parameters:</dt>
<dd><code>infile</code> - the file to transform</dd>
<dd><code>outfile</code> - the result file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code> - on error</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../XSLTLiaison.html#setStylesheet(java.io.File)"><code>XSLTLiaison.setStylesheet(File)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFactory(java.lang.String)">
<h3>setFactory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFactory</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set the factory name to use instead of JAXP default lookup.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the fully qualified class name of the factory to use
 or null for the default JAXP look up mechanism.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAttribute(java.lang.String,java.lang.Object)">
<h3>setAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAttribute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Set a custom attribute for the JAXP factory implementation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the attribute name.</dd>
<dd><code>value</code> - the value of the attribute, usually a boolean
 string or object.</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFeature(java.lang.String,boolean)">
<h3>setFeature</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFeature</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 boolean&nbsp;value)</span></div>
<div class="block">Set a custom feature for the JAXP factory implementation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the feature name.</dd>
<dd><code>value</code> - the value of the feature</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputProperty(java.lang.String,java.lang.String)">
<h3>setOutputProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Set the output property for the current transformer.
 Note that the stylesheet must be set prior to calling
 this method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the output property name.</dd>
<dd><code>value</code> - the output property value.</dd>
<dt>Since:</dt>
<dd>Ant 1.5, Ant 1.5</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEntityResolver(org.xml.sax.EntityResolver)">
<h3>setEntityResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEntityResolver</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a>&nbsp;aResolver)</span></div>
<div class="block">Set the class to resolve entities during the transformation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aResolver</code> - the resolver class.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setURIResolver(javax.xml.transform.URIResolver)">
<h3>setURIResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setURIResolver</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/URIResolver.html" title="class or interface in javax.xml.transform" class="external-link">URIResolver</a>&nbsp;aResolver)</span></div>
<div class="block">Set the class to resolve URIs during the transformation</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aResolver</code> - a <code>EntityResolver</code> value</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addParam(java.lang.String,java.lang.String)">
<h3>addParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">Add a parameter.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison.html#addParam(java.lang.String,java.lang.String)">addParam</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the parameter</dd>
<dd><code>value</code> - the value of the parameter</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../XSLTLiaison4.html#addParam(java.lang.String,java.lang.Object)"><code>XSLTLiaison4.addParam(java.lang.String, java.lang.Object)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addParam(java.lang.String,java.lang.Object)">
<h3>addParam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addParam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Add a parameter.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison4.html#addParam(java.lang.String,java.lang.Object)">addParam</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison4.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison4</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the parameter</dd>
<dd><code>value</code> - the value of the parameter</dd>
<dt>Since:</dt>
<dd>Ant 1.9.3</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/Transformer.html#setParameter(java.lang.String,java.lang.Object)" title="class or interface in javax.xml.transform" class="external-link"><code>Transformer.setParameter(java.lang.String, java.lang.Object)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogger(org.apache.tools.ant.taskdefs.XSLTLogger)">
<h3>setLogger</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogger</span><wbr><span class="parameters">(<a href="../XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</a>&nbsp;l)</span></div>
<div class="block">Set a logger.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLoggerAware.html#setLogger(org.apache.tools.ant.taskdefs.XSLTLogger)">setLogger</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</a></code></dd>
<dt>Parameters:</dt>
<dd><code>l</code> - a logger.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="error(javax.xml.transform.TransformerException)">
<h3>error</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">error</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</span></div>
<div class="block">Log an error.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html#error(javax.xml.transform.TransformerException)" title="class or interface in javax.xml.transform" class="external-link">error</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>e</code> - the exception to log.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fatalError(javax.xml.transform.TransformerException)">
<h3>fatalError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fatalError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</span></div>
<div class="block">Log a fatal error.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html#fatalError(javax.xml.transform.TransformerException)" title="class or interface in javax.xml.transform" class="external-link">fatalError</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>e</code> - the exception to log.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="warning(javax.xml.transform.TransformerException)">
<h3>warning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">warning</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/TransformerException.html" title="class or interface in javax.xml.transform" class="external-link">TransformerException</a>&nbsp;e)</span></div>
<div class="block">Log a warning.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html#warning(javax.xml.transform.TransformerException)" title="class or interface in javax.xml.transform" class="external-link">warning</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/transform/ErrorListener.html" title="class or interface in javax.xml.transform" class="external-link">ErrorListener</a></code></dd>
<dt>Parameters:</dt>
<dd><code>e</code> - the exception to log.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSystemId(java.io.File)">
<h3>getSystemId</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSystemId</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.5.x.
             Use org.apache.tools.ant.util.JAXPUtils#getSystemId instead.</div>
</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the filename to use for the systemid</dd>
<dt>Returns:</dt>
<dd>the systemid</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="configure(org.apache.tools.ant.taskdefs.XSLTProcess)">
<h3>configure</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="../XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a>&nbsp;xsltTask)</span></div>
<div class="block">Specific configuration for the TRaX liaison.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../XSLTLiaison2.html#configure(org.apache.tools.ant.taskdefs.XSLTProcess)">configure</a></code>&nbsp;in interface&nbsp;<code><a href="../XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</a></code></dd>
<dt>Parameters:</dt>
<dd><code>xsltTask</code> - the XSLTProcess task instance from which this liaison
        is to be configured.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
