<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>TokenFilter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.filters, class: TokenFilter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.filters</a></div>
<h1 title="Class TokenFilter" class="title">Class TokenFilter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">java.io.Reader</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">java.io.FilterReader</a>
<div class="inheritance"><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseFilterReader</a>
<div class="inheritance">org.apache.tools.ant.filters.TokenFilter</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Readable.html" title="class or interface in java.lang" class="external-link">Readable</a></code>, <code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TokenFilter</span>
<span class="extends-implements">extends <a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a>
implements <a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></span></div>
<div class="block">This splits up input into tokens and passes
 the tokens to a sequence of filters.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters"><code>BaseFilterReader</code></a></li>
<li><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters"><code>ChainableReader</code></a></li>
<li><a href="../DynamicConfigurator.html" title="interface in org.apache.tools.ant"><code>DynamicConfigurator</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.ChainableReaderFilter.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</a></code></div>
<div class="col-last even-row-color">
<div class="block">Abstract class that converts derived filter classes into
 ChainableReaderFilter's</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TokenFilter.ContainsRegex.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a></code></div>
<div class="col-last odd-row-color">
<div class="block">filter to filter tokens matching regular expressions.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.ContainsString.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a></code></div>
<div class="col-last even-row-color">
<div class="block">Simple filter to filter lines contains strings</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TokenFilter.DeleteCharacters.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Filter to delete characters</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.FileTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a></code></div>
<div class="col-last even-row-color">
<div class="block">class to read the complete input into a string</div>
</div>
<div class="col-first odd-row-color"><code>static interface&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TokenFilter.Filter.html" class="type-name-link" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a></code></div>
<div class="col-last odd-row-color">
<div class="block">string filters implement this interface</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.IgnoreBlank.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a></code></div>
<div class="col-last even-row-color">
<div class="block">Filter remove empty tokens</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TokenFilter.ReplaceRegex.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a></code></div>
<div class="col-last odd-row-color">
<div class="block">filter to replace regex.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.ReplaceString.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a></code></div>
<div class="col-last even-row-color">
<div class="block">Simple replace string filter.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="TokenFilter.StringTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a></code></div>
<div class="col-last odd-row-color">
<div class="block">class to tokenize the input as areas separated
 by white space, or by a specified list of
 delim characters.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TokenFilter.Trim.html" class="type-name-link" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a></code></div>
<div class="col-last even-row-color">
<div class="block">Filter to trim white space</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.FilterReader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#in" title="class or interface in java.io" class="external-link">in</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.io.Reader">Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#lock" title="class or interface in java.io" class="external-link">lock</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TokenFilter</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for "dummy" instances.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.Reader)" class="member-name-link">TokenFilter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new filtered reader.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.filters.TokenFilter.Filter)" class="member-name-link">add</a><wbr>(<a href="TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a>&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an arbitrary filter</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.util.Tokenizer)" class="member-name-link">add</a><wbr>(<a href="../util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>&nbsp;tokenizer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an arbitrary tokenizer</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContainsRegex(org.apache.tools.ant.filters.TokenFilter.ContainsRegex)" class="member-name-link">addContainsRegex</a><wbr>(<a href="TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a>&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">contains regex filter</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContainsString(org.apache.tools.ant.filters.TokenFilter.ContainsString)" class="member-name-link">addContainsString</a><wbr>(<a href="TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a>&nbsp;filter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">contains string filter</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDeleteCharacters(org.apache.tools.ant.filters.TokenFilter.DeleteCharacters)" class="member-name-link">addDeleteCharacters</a><wbr>(<a href="TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a>&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">delete chars</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFileTokenizer(org.apache.tools.ant.filters.TokenFilter.FileTokenizer)" class="member-name-link">addFileTokenizer</a><wbr>(<a href="TokenFilter.FileTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a>&nbsp;tokenizer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a file tokenizer</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIgnoreBlank(org.apache.tools.ant.filters.TokenFilter.IgnoreBlank)" class="member-name-link">addIgnoreBlank</a><wbr>(<a href="TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a>&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">ignore blank filter</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addLineTokenizer(org.apache.tools.ant.util.LineTokenizer)" class="member-name-link">addLineTokenizer</a><wbr>(<a href="../util/LineTokenizer.html" title="class in org.apache.tools.ant.util">LineTokenizer</a>&nbsp;tokenizer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a line tokenizer - this is the default.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReplaceRegex(org.apache.tools.ant.filters.TokenFilter.ReplaceRegex)" class="member-name-link">addReplaceRegex</a><wbr>(<a href="TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a>&nbsp;filter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">replace regex filter</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReplaceString(org.apache.tools.ant.filters.TokenFilter.ReplaceString)" class="member-name-link">addReplaceString</a><wbr>(<a href="TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a>&nbsp;filter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">replace string filter</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addStringTokenizer(org.apache.tools.ant.filters.TokenFilter.StringTokenizer)" class="member-name-link">addStringTokenizer</a><wbr>(<a href="TokenFilter.StringTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a>&nbsp;tokenizer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a string tokenizer</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTrim(org.apache.tools.ant.filters.TokenFilter.Trim)" class="member-name-link">addTrim</a><wbr>(<a href="TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a>&nbsp;filter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">trim filter</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new TokenFilter using the passed in
 Reader for instantiation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#convertRegexOptions(java.lang.String)" class="member-name-link">convertRegexOptions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">convert regex option flag characters to regex options
 
   g -  Regexp.REPLACE_ALL
   i -  Regexp.MATCH_CASE_INSENSITIVE
   m -  Regexp.MATCH_MULTILINE
   s -  Regexp.MATCH_SINGLELINE
 </div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#read()" class="member-name-link">read</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the next character in the filtered stream, only including
 lines from the original stream which match all of the specified
 regular expressions.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#resolveBackSlash(java.lang.String)" class="member-name-link">resolveBackSlash</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">xml does not do "c" like interpretation of strings.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDelimOutput(java.lang.String)" class="member-name-link">setDelimOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;delimOutput)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the output delimiter.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.filters.BaseFilterReader">Methods inherited from class&nbsp;org.apache.tools.ant.filters.<a href="BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</a></h3>
<code><a href="BaseFilterReader.html#getInitialized()">getInitialized</a>, <a href="BaseFilterReader.html#getProject()">getProject</a>, <a href="BaseFilterReader.html#read(char%5B%5D,int,int)">read</a>, <a href="BaseFilterReader.html#readFully()">readFully</a>, <a href="BaseFilterReader.html#readLine()">readLine</a>, <a href="BaseFilterReader.html#setInitialized(boolean)">setInitialized</a>, <a href="BaseFilterReader.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="BaseFilterReader.html#skip(long)">skip</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.FilterReader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#close()" title="class or interface in java.io" class="external-link">close</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#mark(int)" title="class or interface in java.io" class="external-link">mark</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#markSupported()" title="class or interface in java.io" class="external-link">markSupported</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#ready()" title="class or interface in java.io" class="external-link">ready</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#reset()" title="class or interface in java.io" class="external-link">reset</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.Reader">Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#nullReader()" title="class or interface in java.io" class="external-link">nullReader</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(char%5B%5D)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#read(java.nio.CharBuffer)" title="class or interface in java.io" class="external-link">read</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html#transferTo(java.io.Writer)" title="class or interface in java.io" class="external-link">transferTo</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TokenFilter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TokenFilter</span>()</div>
<div class="block">Constructor for "dummy" instances.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="BaseFilterReader.html#%3Cinit%3E()"><code>BaseFilterReader()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.Reader)">
<h3>TokenFilter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TokenFilter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;in)</span></div>
<div class="block">Creates a new filtered reader.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - A Reader object providing the underlying stream.
           Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="read()">
<h3>read</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">read</span>()
         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns the next character in the filtered stream, only including
 lines from the original stream which match all of the specified
 regular expressions.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html#read()" title="class or interface in java.io" class="external-link">read</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/FilterReader.html" title="class or interface in java.io" class="external-link">FilterReader</a></code></dd>
<dt>Returns:</dt>
<dd>the next character in the resulting stream, or -1
 if the end of the resulting stream has been reached</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if the underlying stream throws an IOException
 during reading</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;reader)</span></div>
<div class="block">Creates a new TokenFilter using the passed in
 Reader for instantiation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ChainableReader.html#chain(java.io.Reader)">chain</a></code>&nbsp;in interface&nbsp;<code><a href="ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>reader</code> - A Reader object providing the underlying stream.</dd>
<dt>Returns:</dt>
<dd>a new filter based on this configuration</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDelimOutput(java.lang.String)">
<h3>setDelimOutput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDelimOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;delimOutput)</span></div>
<div class="block">set the output delimiter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>delimOutput</code> - replaces the delim string returned by the
                    tokenizer, if present.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addLineTokenizer(org.apache.tools.ant.util.LineTokenizer)">
<h3>addLineTokenizer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addLineTokenizer</span><wbr><span class="parameters">(<a href="../util/LineTokenizer.html" title="class in org.apache.tools.ant.util">LineTokenizer</a>&nbsp;tokenizer)</span></div>
<div class="block">add a line tokenizer - this is the default.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tokenizer</code> - the line tokenizer</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addStringTokenizer(org.apache.tools.ant.filters.TokenFilter.StringTokenizer)">
<h3>addStringTokenizer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addStringTokenizer</span><wbr><span class="parameters">(<a href="TokenFilter.StringTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.StringTokenizer</a>&nbsp;tokenizer)</span></div>
<div class="block">add a string tokenizer</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tokenizer</code> - the string tokenizer</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addFileTokenizer(org.apache.tools.ant.filters.TokenFilter.FileTokenizer)">
<h3>addFileTokenizer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFileTokenizer</span><wbr><span class="parameters">(<a href="TokenFilter.FileTokenizer.html" title="class in org.apache.tools.ant.filters">TokenFilter.FileTokenizer</a>&nbsp;tokenizer)</span></div>
<div class="block">add a file tokenizer</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tokenizer</code> - the file tokenizer</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.util.Tokenizer)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../util/Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>&nbsp;tokenizer)</span></div>
<div class="block">add an arbitrary tokenizer</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tokenizer</code> - the tokenizer to all, only one allowed</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReplaceString(org.apache.tools.ant.filters.TokenFilter.ReplaceString)">
<h3>addReplaceString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReplaceString</span><wbr><span class="parameters">(<a href="TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</a>&nbsp;filter)</span></div>
<div class="block">replace string filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the replace string filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addContainsString(org.apache.tools.ant.filters.TokenFilter.ContainsString)">
<h3>addContainsString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContainsString</span><wbr><span class="parameters">(<a href="TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</a>&nbsp;filter)</span></div>
<div class="block">contains string filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the contains string filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReplaceRegex(org.apache.tools.ant.filters.TokenFilter.ReplaceRegex)">
<h3>addReplaceRegex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReplaceRegex</span><wbr><span class="parameters">(<a href="TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</a>&nbsp;filter)</span></div>
<div class="block">replace regex filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the replace regex filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addContainsRegex(org.apache.tools.ant.filters.TokenFilter.ContainsRegex)">
<h3>addContainsRegex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContainsRegex</span><wbr><span class="parameters">(<a href="TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</a>&nbsp;filter)</span></div>
<div class="block">contains regex filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the contains regex filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTrim(org.apache.tools.ant.filters.TokenFilter.Trim)">
<h3>addTrim</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTrim</span><wbr><span class="parameters">(<a href="TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</a>&nbsp;filter)</span></div>
<div class="block">trim filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the trim filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addIgnoreBlank(org.apache.tools.ant.filters.TokenFilter.IgnoreBlank)">
<h3>addIgnoreBlank</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIgnoreBlank</span><wbr><span class="parameters">(<a href="TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</a>&nbsp;filter)</span></div>
<div class="block">ignore blank filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the ignore blank filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDeleteCharacters(org.apache.tools.ant.filters.TokenFilter.DeleteCharacters)">
<h3>addDeleteCharacters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDeleteCharacters</span><wbr><span class="parameters">(<a href="TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</a>&nbsp;filter)</span></div>
<div class="block">delete chars</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the delete characters filter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.filters.TokenFilter.Filter)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="TokenFilter.Filter.html" title="interface in org.apache.tools.ant.filters">TokenFilter.Filter</a>&nbsp;filter)</span></div>
<div class="block">Add an arbitrary filter</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filter</code> - the filter to add</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveBackSlash(java.lang.String)">
<h3>resolveBackSlash</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">resolveBackSlash</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</span></div>
<div class="block">xml does not do "c" like interpretation of strings.
 i.e. \n\r\t etc.
 this method processes \n, \r, \t, \f, \\
 also subs \s with " \n\r\t\f"
 a trailing '\' will be ignored</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - raw string with possible embedded '\'s</dd>
<dt>Returns:</dt>
<dd>converted string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="convertRegexOptions(java.lang.String)">
<h3>convertRegexOptions</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">convertRegexOptions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;flags)</span></div>
<div class="block">convert regex option flag characters to regex options
 <ul>
   <li>g -  Regexp.REPLACE_ALL</li>
   <li>i -  Regexp.MATCH_CASE_INSENSITIVE</li>
   <li>m -  Regexp.MATCH_MULTILINE</li>
   <li>s -  Regexp.MATCH_SINGLELINE</li>
 </ul></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flags</code> - the string containing the flags</dd>
<dt>Returns:</dt>
<dd>the Regexp option bits</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
