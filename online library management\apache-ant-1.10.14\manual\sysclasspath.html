<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>build.sysclasspath</title>
</head>

<body>

<h2 id="sysclasspath">build.sysclasspath</h2>
<p>The value of the <code>build.sysclasspath</code> property
controls how the system classpath, i.e. the classpath in effect when
Apache Ant is run, affects the behavior of classpaths in Ant.
The default behavior varies from task to task.</p>

The values and their meanings are:

<table>
<tr><th scope="col">value</th><th scope="col">meaning</th></tr>
<tr>
<td>only</td>
<td>Only the system classpath is used and classpaths specified in build files,
etc are ignored. This situation could be considered as the person running
the build file knows more about the environment than the person writing the
build file.
</td>
</tr>

<tr>
<td>ignore</td>
<td>
The system classpath is ignored. This situation is the reverse of the
above. The person running the build trusts the build file writer to get the
build file right. This mode is recommended for portable scripts.
</td>
</tr>

<tr>
<td>last</td>
<td>
The classpath is concatenated to any specified classpaths at the end. This
is a compromise, where the build file writer has priority.
</td>
</tr>

<tr>
<td>first</td>
<td>
Any specified classpaths are concatenated to the system classpath. This is
the other form of compromise where the build runner has priority.
</td>
</tr>
</table>

<p><em>Since Ant 1.7</em> the value of this property also affects the
bootclasspath settings&mdash;it combines the bootclasspath that has been
specified for a task with the bootclasspath of the JVM running
Ant.  If the property has not been set, it defaults to <q>ignore</q> in
this case.</p>

</body>
</html>
