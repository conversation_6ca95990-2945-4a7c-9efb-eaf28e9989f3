<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectHelperImpl (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.helper, class: ProjectHelperImpl">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.helper</a></div>
<h1 title="Class ProjectHelperImpl" class="title">Class ProjectHelperImpl</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectHelper.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectHelper</a>
<div class="inheritance">org.apache.tools.ant.helper.ProjectHelperImpl</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ProjectHelperImpl</span>
<span class="extends-implements">extends <a href="../ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></span></div>
<div class="block">Original helper.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.apache.tools.ant.ProjectHelper">Nested classes/interfaces inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></h2>
<code><a href="../ProjectHelper.OnMissingExtensionPoint.html" title="class in org.apache.tools.ant">ProjectHelper.OnMissingExtensionPoint</a></code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectHelper">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></h3>
<code><a href="../ProjectHelper.html#ANT_ATTRIBUTE_URI">ANT_ATTRIBUTE_URI</a>, <a href="../ProjectHelper.html#ANT_CORE_URI">ANT_CORE_URI</a>, <a href="../ProjectHelper.html#ANT_CURRENT_URI">ANT_CURRENT_URI</a>, <a href="../ProjectHelper.html#ANT_TYPE">ANT_TYPE</a>, <a href="../ProjectHelper.html#ANTLIB_URI">ANTLIB_URI</a>, <a href="../ProjectHelper.html#HELPER_PROPERTY">HELPER_PROPERTY</a>, <a href="../ProjectHelper.html#PROJECTHELPER_REFERENCE">PROJECTHELPER_REFERENCE</a>, <a href="../ProjectHelper.html#SERVICE_ID">SERVICE_ID</a>, <a href="../ProjectHelper.html#USE_PROJECT_NAME_AS_TARGET_PREFIX">USE_PROJECT_NAME_AS_TARGET_PREFIX</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ProjectHelperImpl</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">default constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parse(org.apache.tools.ant.Project,java.lang.Object)" class="member-name-link">parse</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parses the project file, configuring the project as it goes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectHelper">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></h3>
<code><a href="../ProjectHelper.html#addLocationToBuildException(org.apache.tools.ant.BuildException,org.apache.tools.ant.Location)">addLocationToBuildException</a>, <a href="../ProjectHelper.html#addText(org.apache.tools.ant.Project,java.lang.Object,char%5B%5D,int,int)">addText</a>, <a href="../ProjectHelper.html#addText(org.apache.tools.ant.Project,java.lang.Object,java.lang.String)">addText</a>, <a href="../ProjectHelper.html#canParseAntlibDescriptor(org.apache.tools.ant.types.Resource)">canParseAntlibDescriptor</a>, <a href="../ProjectHelper.html#canParseBuildFile(org.apache.tools.ant.types.Resource)">canParseBuildFile</a>, <a href="../ProjectHelper.html#configure(java.lang.Object,org.xml.sax.AttributeList,org.apache.tools.ant.Project)">configure</a>, <a href="../ProjectHelper.html#configureProject(org.apache.tools.ant.Project,java.io.File)">configureProject</a>, <a href="../ProjectHelper.html#extractNameFromComponentName(java.lang.String)">extractNameFromComponentName</a>, <a href="../ProjectHelper.html#extractUriFromComponentName(java.lang.String)">extractUriFromComponentName</a>, <a href="../ProjectHelper.html#genComponentName(java.lang.String,java.lang.String)">genComponentName</a>, <a href="../ProjectHelper.html#getContextClassLoader()">getContextClassLoader</a>, <a href="../ProjectHelper.html#getCurrentPrefixSeparator()">getCurrentPrefixSeparator</a>, <a href="../ProjectHelper.html#getCurrentTargetPrefix()">getCurrentTargetPrefix</a>, <a href="../ProjectHelper.html#getDefaultBuildFile()">getDefaultBuildFile</a>, <a href="../ProjectHelper.html#getExtensionStack()">getExtensionStack</a>, <a href="../ProjectHelper.html#getImportStack()">getImportStack</a>, <a href="../ProjectHelper.html#getProjectHelper()">getProjectHelper</a>, <a href="../ProjectHelper.html#isInIncludeMode()">isInIncludeMode</a>, <a href="../ProjectHelper.html#nsToComponentName(java.lang.String)">nsToComponentName</a>, <a href="../ProjectHelper.html#parseAntlibDescriptor(org.apache.tools.ant.Project,org.apache.tools.ant.types.Resource)">parseAntlibDescriptor</a>, <a href="../ProjectHelper.html#parsePropertyString(java.lang.String,java.util.Vector,java.util.Vector)">parsePropertyString</a>, <a href="../ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project,java.lang.String)">replaceProperties</a>, <a href="../ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project,java.lang.String,java.util.Hashtable)">replaceProperties</a>, <a href="../ProjectHelper.html#resolveExtensionOfAttributes(org.apache.tools.ant.Project)">resolveExtensionOfAttributes</a>, <a href="../ProjectHelper.html#setCurrentPrefixSeparator(java.lang.String)">setCurrentPrefixSeparator</a>, <a href="../ProjectHelper.html#setCurrentTargetPrefix(java.lang.String)">setCurrentTargetPrefix</a>, <a href="../ProjectHelper.html#setInIncludeMode(boolean)">setInIncludeMode</a>, <a href="../ProjectHelper.html#storeChild(org.apache.tools.ant.Project,java.lang.Object,java.lang.Object,java.lang.String)">storeChild</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ProjectHelperImpl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ProjectHelperImpl</span>()</div>
<div class="block">default constructor</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="parse(org.apache.tools.ant.Project,java.lang.Object)">
<h3>parse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parse</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;source)</span>
           throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Parses the project file, configuring the project as it goes.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../ProjectHelper.html#parse(org.apache.tools.ant.Project,java.lang.Object)">parse</a></code>&nbsp;in class&nbsp;<code><a href="../ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - project instance to be configured.</dd>
<dd><code>source</code> - the source from which the project is read.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if the configuration is invalid or cannot
                           be read.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
