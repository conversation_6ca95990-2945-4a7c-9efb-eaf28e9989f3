<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Truncate Task</title>
</head>

<body>

<h2 id="touch">Truncate</h2>
<p><em>Since Apache Ant 1.7.1</em></p>
<h3>Description</h3>

<p>Set the length of one or more files, as the <code>truncate</code> Unix function or GNU
utility. In addition to working with a single file, this Task can also work
on <a href="../Types/resources.html">resources</a> and resource collections.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>file</td>
    <td>The name of the file.</td>
    <td>Unless a nested resource collection element has been specified</td>
  </tr>
  <tr>
    <td>length</td>
    <td>Specifies the new file length (in bytes) to set.  The following suffixes are supported:
      <ul>
        <li><q>K</q> : Kilobytes (1024 bytes)</li>
        <li><q>M</q> : Megabytes (1024 K)</li>
        <li><q>G</q> : Gigabytes (1024 M)</li>
        <li><q>T</q> : Terabytes (1024 G)</li>
        <li><q>P</q> : Petabytes (1024 T)</li>
      </ul>
    </td>
    <td rowspan="2">At most one of these; omitting both implies <var>length</var>=<q>0</q>
    </td>
  </tr>
  <tr>
    <td>adjust</td>
    <td class="left">Specifies the number of bytes (and positive/negative direction) by which to
      adjust file lengths.  The same suffixes are supported for this attribute as for
      the <var>length</var> attribute.
    </td>
  </tr>
  <tr>
    <td>create</td>
    <td>Whether to create nonexistent files.</td>
    <td>No; default <q>true</q></td>
  </tr>
  <tr>
    <td>mkdirs</td>
    <td>Whether to create nonexistent parent directories when creating new files.</td>
    <td>No; default <q>false</q></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>any resource collection</h4>

<p>You can use any number of nested resource collection elements to define the resources for this
task and refer to resources defined elsewhere.  <strong>Note</strong>: resources passed to this task
are expected to be filesystem-based.</p>

<h3>Examples</h3>

<p>Set the length of file <samp>foo</samp> to zero.</p>
<pre>&lt;truncate file="foo"/&gt;</pre>

<p>Set the length of file <samp>foo</samp> to 1 kilobyte (1024 bytes).</p>
<pre>&lt;truncate file="foo" length="1K"/&gt;</pre>

<p>Adjust the length of file <samp>foo</samp> upward by 1 kilobyte.</p>
<pre>&lt;truncate file="foo" adjust="1K"/&gt;</pre>

<p>Adjust the length of file <samp>foo</samp> downward by 1 megabyte.</p>
<pre>&lt;truncate file="foo" adjust="-1M"/&gt;</pre>

</body>
</html>
