<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectHelper2.<PERSON><PERSON><PERSON><PERSON><PERSON> (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.helper, class: ProjectHelper2, class: AntHandler">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.helper</a></div>
<h1 title="Class ProjectHelper2.AntHandler" class="title">Class ProjectHelper2.AntHandler</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.helper.ProjectHelper2.AntHandler</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ProjectHelper2.ElementHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.ElementHandler</a></code>, <code><a href="ProjectHelper2.MainHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.MainHandler</a></code>, <code><a href="ProjectHelper2.ProjectHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.ProjectHandler</a></code>, <code><a href="ProjectHelper2.TargetHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.TargetHandler</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">ProjectHelper2.AntHandler</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">The common superclass for all SAX event handlers used to parse
 the configuration file.

 The context will hold all state information. At each time
 there is one active handler for the current element. It can
 use onStartChild() to set an alternate handler for the child.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">AntHandler</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#characters(char%5B%5D,int,int,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">characters</a><wbr>(char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles text within an element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkNamespace(java.lang.String)" class="member-name-link">checkNamespace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Will be called every time a namespace is reached.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onEndChild(java.lang.String,java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onEndChild</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle the end of a element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onEndElement(java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onEndElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method is called when this element and all elements nested into it have been
 handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStartChild(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onStartChild</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the start of an element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onStartElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)" class="member-name-link">onStartElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the start of an element.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>AntHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntHandler</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="onStartElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onStartElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onStartElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handles the start of an element. This base implementation does
 nothing.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - the namespace URI for the tag</dd>
<dd><code>tag</code> - The name of the element being started.
            Will not be <code>null</code>.</dd>
<dd><code>qname</code> - The qualified name of the element.</dd>
<dd><code>attrs</code> - Attributes of the element being started.
              Will not be <code>null</code>.</dd>
<dd><code>context</code> - The context that this element is in.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if this method is not overridden, or in
                              case of error in an overridden version</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onStartChild(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onStartChild</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></span>&nbsp;<span class="element-name">onStartChild</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handles the start of an element. This base implementation just
 throws an exception - you must override this method if you expect
 child elements.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace uri for this element.</dd>
<dd><code>tag</code> - The name of the element being started.
            Will not be <code>null</code>.</dd>
<dd><code>qname</code> - The qualified name for this element.</dd>
<dd><code>attrs</code> - Attributes of the element being started.
              Will not be <code>null</code>.</dd>
<dd><code>context</code> - The current context.</dd>
<dt>Returns:</dt>
<dd>a handler (in the derived classes)</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if this method is not overridden, or in
                              case of error in an overridden version</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onEndChild(java.lang.String,java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onEndChild</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onEndChild</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handle the end of a element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - the namespace uri of the element</dd>
<dd><code>tag</code> - the tag of the element</dd>
<dd><code>qname</code> - the qualified name of the element</dd>
<dd><code>context</code> - the current context</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if an error occurs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onEndElement(java.lang.String,java.lang.String,org.apache.tools.ant.helper.AntXMLContext)">
<h3>onEndElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onEndElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span></div>
<div class="block">This method is called when this element and all elements nested into it have been
 handled. I.e., this happens at the &lt;/end_tag_of_the_element&gt;.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - the namespace uri for this element</dd>
<dd><code>tag</code> - the element name</dd>
<dd><code>context</code> - the current context</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="characters(char[],int,int,org.apache.tools.ant.helper.AntXMLContext)">
<h3>characters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">characters</span><wbr><span class="parameters">(char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count,
 <a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handles text within an element. This base implementation just
 throws an exception, you must override it if you expect content.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>buf</code> - A character array of the text within the element.
            Will not be <code>null</code>.</dd>
<dd><code>start</code> - The start element in the array.</dd>
<dd><code>count</code> - The number of characters to read from the array.</dd>
<dd><code>context</code> - The current context.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if this method is not overridden, or in
                              case of error in an overridden version</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkNamespace(java.lang.String)">
<h3>checkNamespace</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkNamespace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</span></div>
<div class="block">Will be called every time a namespace is reached.
 It'll verify if the ns was processed, and if not load the task definitions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace uri.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
