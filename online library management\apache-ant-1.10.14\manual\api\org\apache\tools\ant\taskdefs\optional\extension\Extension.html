<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Extension (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.extension, class: Extension">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.extension</a></div>
<h1 title="Class Extension" class="title">Class Extension</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.extension.Extension</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">Extension</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block"><p>Utility class that represents either an available "Optional Package"
 (formerly known as "Standard Extension") as described in the manifest
 of a JAR file, or the requirement for such an optional package.</p>

 <p>For more information about optional packages, see the document
 <em>Optional Package Versioning</em> in the documentation bundle for your
 Java2 Standard Edition package, in file
 <code>guide/extensions/versioning.html</code>.</p></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMPATIBLE" class="member-name-link">COMPATIBLE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enum indicating that extension is compatible with other extension.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second odd-row-color"><code><a href="#EXTENSION_LIST" class="member-name-link">EXTENSION_LIST</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Manifest Attribute Name object for EXTENSION_LIST.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second even-row-color"><code><a href="#EXTENSION_NAME" class="member-name-link">EXTENSION_NAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">Manifest Attribute Name object for EXTENSION_NAME.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second odd-row-color"><code><a href="#IMPLEMENTATION_URL" class="member-name-link">IMPLEMENTATION_URL</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_URL.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second even-row-color"><code><a href="#IMPLEMENTATION_VENDOR" class="member-name-link">IMPLEMENTATION_VENDOR</a></code></div>
<div class="col-last even-row-color">
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VENDOR.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second odd-row-color"><code><a href="#IMPLEMENTATION_VENDOR_ID" class="member-name-link">IMPLEMENTATION_VENDOR_ID</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VENDOR_ID.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second even-row-color"><code><a href="#IMPLEMENTATION_VERSION" class="member-name-link">IMPLEMENTATION_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VERSION.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second odd-row-color"><code><a href="#INCOMPATIBLE" class="member-name-link">INCOMPATIBLE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enum indicating that extension is incompatible with
 other extension in ways other than other enums
 indicate).</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second even-row-color"><code><a href="#OPTIONAL_EXTENSION_LIST" class="member-name-link">OPTIONAL_EXTENSION_LIST</a></code></div>
<div class="col-last even-row-color">
<div class="block"><code>Name</code> object for <code>Optional-Extension-List</code>
 manifest attribute used for declaring optional dependencies on
 installed extensions.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REQUIRE_IMPLEMENTATION_UPGRADE" class="member-name-link">REQUIRE_IMPLEMENTATION_UPGRADE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enum indicating that extension requires an upgrade
 of implementation to be compatible with other extension.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second even-row-color"><code><a href="#REQUIRE_SPECIFICATION_UPGRADE" class="member-name-link">REQUIRE_SPECIFICATION_UPGRADE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enum indicating that extension requires an upgrade
 of specification to be compatible with other extension.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REQUIRE_VENDOR_SWITCH" class="member-name-link">REQUIRE_VENDOR_SWITCH</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enum indicating that extension requires a vendor
 switch to be compatible with other extension.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second even-row-color"><code><a href="#SPECIFICATION_VENDOR" class="member-name-link">SPECIFICATION_VENDOR</a></code></div>
<div class="col-last even-row-color">
<div class="block">Manifest Attribute Name object for SPECIFICATION_VENDOR.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SPECIFICATION_VERSION" class="member-name-link">SPECIFICATION_VERSION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Manifest Attribute Name object for SPECIFICATION_VERSION.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">Extension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;specificationVersion,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;specificationVendor,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVersion,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVendor,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVendorId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationURL)</code></div>
<div class="col-last even-row-color">
<div class="block">The constructor to create Extension object.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addExtension(org.apache.tools.ant.taskdefs.optional.extension.Extension,java.lang.String,java.util.jar.Attributes)" class="member-name-link">addExtension</a><wbr>(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;extension,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.html" title="class or interface in java.util.jar" class="external-link">Attributes</a>&nbsp;attributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Add Extension to the specified manifest Attributes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addExtension(org.apache.tools.ant.taskdefs.optional.extension.Extension,java.util.jar.Attributes)" class="member-name-link">addExtension</a><wbr>(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;extension,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.html" title="class or interface in java.util.jar" class="external-link">Attributes</a>&nbsp;attributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Add Extension to the specified manifest Attributes.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getAvailable(java.util.jar.Manifest)" class="member-name-link">getAvailable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Return an array of <code>Extension</code> objects representing optional
 packages that are available in the JAR file associated with the
 specified <code>Manifest</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompatibilityWith(org.apache.tools.ant.taskdefs.optional.extension.Extension)" class="member-name-link">getCompatibilityWith</a><wbr>(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;required)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a Compatibility enum indicating the relationship of this
 <code>Extension</code> with the specified <code>Extension</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtensionName()" class="member-name-link">getExtensionName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the name of the extension.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplementationURL()" class="member-name-link">getImplementationURL</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the url of the extensions implementation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplementationVendor()" class="member-name-link">getImplementationVendor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the vendor of the extensions implementation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplementationVendorID()" class="member-name-link">getImplementationVendorID</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the vendorID of the extensions implementation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../util/DeweyDecimal.html" title="class in org.apache.tools.ant.util">DeweyDecimal</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplementationVersion()" class="member-name-link">getImplementationVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the version of the extensions implementation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getOptions(java.util.jar.Manifest)" class="member-name-link">getOptions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Return the set of <code>Extension</code> objects representing "Optional
 Packages" that the application declares they will use if present.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getRequired(java.util.jar.Manifest)" class="member-name-link">getRequired</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Return the set of <code>Extension</code> objects representing optional
 packages that are required by the application contained in the JAR
 file associated with the specified <code>Manifest</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSpecificationVendor()" class="member-name-link">getSpecificationVendor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the vendor of the extensions specification.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../util/DeweyDecimal.html" title="class in org.apache.tools.ant.util">DeweyDecimal</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSpecificationVersion()" class="member-name-link">getSpecificationVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the version of the extensions specification.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCompatibleWith(org.apache.tools.ant.taskdefs.optional.extension.Extension)" class="member-name-link">isCompatibleWith</a><wbr>(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;required)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return <code>true</code> if the specified <code>Extension</code>
 (which represents an optional package required by an application)
 is satisfied by this <code>Extension</code> (which represents an
 optional package that is already installed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a String representation of this object.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="EXTENSION_LIST">
<h3>EXTENSION_LIST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">EXTENSION_LIST</span></div>
<div class="block">Manifest Attribute Name object for EXTENSION_LIST.</div>
</section>
</li>
<li>
<section class="detail" id="OPTIONAL_EXTENSION_LIST">
<h3>OPTIONAL_EXTENSION_LIST</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">OPTIONAL_EXTENSION_LIST</span></div>
<div class="block"><code>Name</code> object for <code>Optional-Extension-List</code>
 manifest attribute used for declaring optional dependencies on
 installed extensions. Note that the dependencies declared by this method
 are not required for the library to operate but if present will be used.
 It is NOT part of the official "Optional Package" specification.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/javase/8/docs/technotes/guides/extensions/spec.html#dependency">
      Installed extension dependency</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="EXTENSION_NAME">
<h3>EXTENSION_NAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">EXTENSION_NAME</span></div>
<div class="block">Manifest Attribute Name object for EXTENSION_NAME.</div>
</section>
</li>
<li>
<section class="detail" id="SPECIFICATION_VERSION">
<h3>SPECIFICATION_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">SPECIFICATION_VERSION</span></div>
<div class="block">Manifest Attribute Name object for SPECIFICATION_VERSION.</div>
</section>
</li>
<li>
<section class="detail" id="SPECIFICATION_VENDOR">
<h3>SPECIFICATION_VENDOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">SPECIFICATION_VENDOR</span></div>
<div class="block">Manifest Attribute Name object for SPECIFICATION_VENDOR.</div>
</section>
</li>
<li>
<section class="detail" id="IMPLEMENTATION_VERSION">
<h3>IMPLEMENTATION_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">IMPLEMENTATION_VERSION</span></div>
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VERSION.</div>
</section>
</li>
<li>
<section class="detail" id="IMPLEMENTATION_VENDOR">
<h3>IMPLEMENTATION_VENDOR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">IMPLEMENTATION_VENDOR</span></div>
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VENDOR.</div>
</section>
</li>
<li>
<section class="detail" id="IMPLEMENTATION_URL">
<h3>IMPLEMENTATION_URL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">IMPLEMENTATION_URL</span></div>
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_URL.</div>
</section>
</li>
<li>
<section class="detail" id="IMPLEMENTATION_VENDOR_ID">
<h3>IMPLEMENTATION_VENDOR_ID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.Name.html" title="class or interface in java.util.jar" class="external-link">Attributes.Name</a></span>&nbsp;<span class="element-name">IMPLEMENTATION_VENDOR_ID</span></div>
<div class="block">Manifest Attribute Name object for IMPLEMENTATION_VENDOR_ID.</div>
</section>
</li>
<li>
<section class="detail" id="COMPATIBLE">
<h3>COMPATIBLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">COMPATIBLE</span></div>
<div class="block">Enum indicating that extension is compatible with other extension.</div>
</section>
</li>
<li>
<section class="detail" id="REQUIRE_SPECIFICATION_UPGRADE">
<h3>REQUIRE_SPECIFICATION_UPGRADE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">REQUIRE_SPECIFICATION_UPGRADE</span></div>
<div class="block">Enum indicating that extension requires an upgrade
 of specification to be compatible with other extension.</div>
</section>
</li>
<li>
<section class="detail" id="REQUIRE_VENDOR_SWITCH">
<h3>REQUIRE_VENDOR_SWITCH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">REQUIRE_VENDOR_SWITCH</span></div>
<div class="block">Enum indicating that extension requires a vendor
 switch to be compatible with other extension.</div>
</section>
</li>
<li>
<section class="detail" id="REQUIRE_IMPLEMENTATION_UPGRADE">
<h3>REQUIRE_IMPLEMENTATION_UPGRADE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">REQUIRE_IMPLEMENTATION_UPGRADE</span></div>
<div class="block">Enum indicating that extension requires an upgrade
 of implementation to be compatible with other extension.</div>
</section>
</li>
<li>
<section class="detail" id="INCOMPATIBLE">
<h3>INCOMPATIBLE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">INCOMPATIBLE</span></div>
<div class="block">Enum indicating that extension is incompatible with
 other extension in ways other than other enums
 indicate). For example the other extension may have
 a different ID.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)">
<h3>Extension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Extension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;specificationVersion,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;specificationVendor,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVersion,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVendor,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationVendorId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;implementationURL)</span></div>
<div class="block">The constructor to create Extension object.
 Note that every component is allowed to be specified
 but only the extensionName is mandatory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extensionName</code> - the name of extension.</dd>
<dd><code>specificationVersion</code> - the specification Version of extension.</dd>
<dd><code>specificationVendor</code> - the specification Vendor of extension.</dd>
<dd><code>implementationVersion</code> - the implementation Version of extension.</dd>
<dd><code>implementationVendor</code> - the implementation Vendor of extension.</dd>
<dd><code>implementationVendorId</code> - the implementation VendorId of extension.</dd>
<dd><code>implementationURL</code> - the implementation URL of extension.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getAvailable(java.util.jar.Manifest)">
<h3>getAvailable</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</span>&nbsp;<span class="element-name">getAvailable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</span></div>
<div class="block">Return an array of <code>Extension</code> objects representing optional
 packages that are available in the JAR file associated with the
 specified <code>Manifest</code>.  If there are no such optional
 packages, a zero-length array is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifest</code> - Manifest to be parsed</dd>
<dt>Returns:</dt>
<dd>the "available" extensions in specified manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRequired(java.util.jar.Manifest)">
<h3>getRequired</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</span>&nbsp;<span class="element-name">getRequired</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</span></div>
<div class="block">Return the set of <code>Extension</code> objects representing optional
 packages that are required by the application contained in the JAR
 file associated with the specified <code>Manifest</code>.  If there
 are no such optional packages, a zero-length list is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifest</code> - Manifest to be parsed</dd>
<dt>Returns:</dt>
<dd>the dependencies that are specified in manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOptions(java.util.jar.Manifest)">
<h3>getOptions</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>[]</span>&nbsp;<span class="element-name">getOptions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Manifest.html" title="class or interface in java.util.jar" class="external-link">Manifest</a>&nbsp;manifest)</span></div>
<div class="block">Return the set of <code>Extension</code> objects representing "Optional
 Packages" that the application declares they will use if present. If
 there are no such optional packages, a zero-length list is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifest</code> - Manifest to be parsed</dd>
<dt>Returns:</dt>
<dd>the optional dependencies that are specified in manifest</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addExtension(org.apache.tools.ant.taskdefs.optional.extension.Extension,java.util.jar.Attributes)">
<h3>addExtension</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExtension</span><wbr><span class="parameters">(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;extension,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.html" title="class or interface in java.util.jar" class="external-link">Attributes</a>&nbsp;attributes)</span></div>
<div class="block">Add Extension to the specified manifest Attributes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extension</code> - the extension</dd>
<dd><code>attributes</code> - the attributes of manifest to add to</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addExtension(org.apache.tools.ant.taskdefs.optional.extension.Extension,java.lang.String,java.util.jar.Attributes)">
<h3>addExtension</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExtension</span><wbr><span class="parameters">(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;extension,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/jar/Attributes.html" title="class or interface in java.util.jar" class="external-link">Attributes</a>&nbsp;attributes)</span></div>
<div class="block">Add Extension to the specified manifest Attributes.
 Use the specified prefix so that dependencies can added
 with a prefix such as "java3d-" etc.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extension</code> - the extension</dd>
<dd><code>prefix</code> - the name to prefix to extension</dd>
<dd><code>attributes</code> - the attributes of manifest to add to</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExtensionName()">
<h3>getExtensionName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getExtensionName</span>()</div>
<div class="block">Get the name of the extension.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the extension</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSpecificationVendor()">
<h3>getSpecificationVendor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSpecificationVendor</span>()</div>
<div class="block">Get the vendor of the extensions specification.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the vendor of the extensions specification.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSpecificationVersion()">
<h3>getSpecificationVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../util/DeweyDecimal.html" title="class in org.apache.tools.ant.util">DeweyDecimal</a></span>&nbsp;<span class="element-name">getSpecificationVersion</span>()</div>
<div class="block">Get the version of the extensions specification.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the version of the extensions specification.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImplementationURL()">
<h3>getImplementationURL</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getImplementationURL</span>()</div>
<div class="block">Get the url of the extensions implementation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the url of the extensions implementation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImplementationVendor()">
<h3>getImplementationVendor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getImplementationVendor</span>()</div>
<div class="block">Get the vendor of the extensions implementation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the vendor of the extensions implementation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImplementationVendorID()">
<h3>getImplementationVendorID</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getImplementationVendorID</span>()</div>
<div class="block">Get the vendorID of the extensions implementation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the vendorID of the extensions implementation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImplementationVersion()">
<h3>getImplementationVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../util/DeweyDecimal.html" title="class in org.apache.tools.ant.util">DeweyDecimal</a></span>&nbsp;<span class="element-name">getImplementationVersion</span>()</div>
<div class="block">Get the version of the extensions implementation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the version of the extensions implementation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompatibilityWith(org.apache.tools.ant.taskdefs.optional.extension.Extension)">
<h3>getCompatibilityWith</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Compatibility.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Compatibility</a></span>&nbsp;<span class="element-name">getCompatibilityWith</span><wbr><span class="parameters">(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;required)</span></div>
<div class="block">Return a Compatibility enum indicating the relationship of this
 <code>Extension</code> with the specified <code>Extension</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>required</code> - Description of the required optional package</dd>
<dt>Returns:</dt>
<dd>the enum indicating the compatibility (or lack thereof)
         of specified extension</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCompatibleWith(org.apache.tools.ant.taskdefs.optional.extension.Extension)">
<h3>isCompatibleWith</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCompatibleWith</span><wbr><span class="parameters">(<a href="Extension.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">Extension</a>&nbsp;required)</span></div>
<div class="block">Return <code>true</code> if the specified <code>Extension</code>
 (which represents an optional package required by an application)
 is satisfied by this <code>Extension</code> (which represents an
 optional package that is already installed.  Otherwise, return
 <code>false</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>required</code> - Description of the required optional package</dd>
<dt>Returns:</dt>
<dd>true if the specified extension is compatible with this extension</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Return a String representation of this object.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>string representation of object.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
