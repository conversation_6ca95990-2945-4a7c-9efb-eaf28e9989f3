<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DefaultJspCompilerAdapter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.jsp.compilers, class: DefaultJspCompilerAdapter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.jsp.compilers</a></div>
<h1 title="Class DefaultJspCompilerAdapter" class="title">Class DefaultJspCompilerAdapter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.jsp.compilers.DefaultJspCompilerAdapter</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="JasperC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JasperC</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">DefaultJspCompilerAdapter</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></span></div>
<div class="block">This is the default implementation for the JspCompilerAdapter interface.
 This is currently very light on the ground since only one compiler type is
 supported.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></code></div>
<div class="col-second even-row-color"><code><a href="#owner" class="member-name-link">owner</a></code></div>
<div class="col-last even-row-color">
<div class="block">our owner</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DefaultJspCompilerAdapter</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String)" class="member-name-link">addArg</a><wbr>(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a single argument to the argument list, if the value isn't null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String,java.io.File)" class="member-name-link">addArg</a><wbr>(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an argument tuple to the arg list, if the file parameter isn't null</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String,java.lang.String)" class="member-name-link">addArg</a><wbr>(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an argument tuple to the argument list, if the value isn't null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJspc()" class="member-name-link">getJspc</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the owner</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../../Project.html" title="class in org.apache.tools.ant">Project</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProject()" class="member-name-link">getProject</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get our project</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#implementsOwnDependencyChecking()" class="member-name-link">implementsOwnDependencyChecking</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">ask if compiler can sort out its own dependencies</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logAndAddFilesToCompile(org.apache.tools.ant.taskdefs.optional.jsp.JspC,java.util.Vector,org.apache.tools.ant.types.CommandlineJava)" class="member-name-link">logAndAddFilesToCompile</a><wbr>(<a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a>&nbsp;jspc,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;compileList,
 <a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJspc(org.apache.tools.ant.taskdefs.optional.jsp.JspC)" class="member-name-link">setJspc</a><wbr>(<a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a>&nbsp;owner)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the owner</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.jsp.compilers.JspCompilerAdapter">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.optional.jsp.compilers.<a href="JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></h3>
<code><a href="JspCompilerAdapter.html#createMangler()">createMangler</a>, <a href="JspCompilerAdapter.html#execute()">execute</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="owner">
<h3>owner</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></span>&nbsp;<span class="element-name">owner</span></div>
<div class="block">our owner</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DefaultJspCompilerAdapter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DefaultJspCompilerAdapter</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="logAndAddFilesToCompile(org.apache.tools.ant.taskdefs.optional.jsp.JspC,java.util.Vector,org.apache.tools.ant.types.CommandlineJava)">
<h3>logAndAddFilesToCompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logAndAddFilesToCompile</span><wbr><span class="parameters">(<a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a>&nbsp;jspc,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;compileList,
 <a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd)</span></div>
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jspc</code> - the compiler task for logging</dd>
<dd><code>compileList</code> - the list of files to compile</dd>
<dd><code>cmd</code> - the command line used</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJspc(org.apache.tools.ant.taskdefs.optional.jsp.JspC)">
<h3>setJspc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJspc</span><wbr><span class="parameters">(<a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a>&nbsp;owner)</span></div>
<div class="block">set the owner</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JspCompilerAdapter.html#setJspc(org.apache.tools.ant.taskdefs.optional.jsp.JspC)">setJspc</a></code>&nbsp;in interface&nbsp;<code><a href="JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>owner</code> - the owner JspC compiler</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJspc()">
<h3>getJspc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></span>&nbsp;<span class="element-name">getJspc</span>()</div>
<div class="block">get the owner</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the owner; should never be null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String)">
<h3>addArg</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addArg</span><wbr><span class="parameters">(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument)</span></div>
<div class="block">add a single argument to the argument list, if the value isn't null</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dd><code>argument</code> - The argument</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String,java.lang.String)">
<h3>addArg</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addArg</span><wbr><span class="parameters">(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</span></div>
<div class="block">add an argument tuple to the argument list, if the value isn't null</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dd><code>argument</code> - The argument</dd>
<dd><code>value</code> - the parameter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addArg(org.apache.tools.ant.types.CommandlineJava,java.lang.String,java.io.File)">
<h3>addArg</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addArg</span><wbr><span class="parameters">(<a href="../../../../types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</a>&nbsp;cmd,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;argument,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">add an argument tuple to the arg list, if the file parameter isn't null</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the command line</dd>
<dd><code>argument</code> - The argument</dd>
<dd><code>file</code> - the parameter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="implementsOwnDependencyChecking()">
<h3>implementsOwnDependencyChecking</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">implementsOwnDependencyChecking</span>()</div>
<div class="block">ask if compiler can sort out its own dependencies</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="JspCompilerAdapter.html#implementsOwnDependencyChecking()">implementsOwnDependencyChecking</a></code>&nbsp;in interface&nbsp;<code><a href="JspCompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp.compilers">JspCompilerAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>true if the compiler wants to do its own
 depends</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProject()">
<h3>getProject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../../Project.html" title="class in org.apache.tools.ant">Project</a></span>&nbsp;<span class="element-name">getProject</span>()</div>
<div class="block">get our project</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>owner project data</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
