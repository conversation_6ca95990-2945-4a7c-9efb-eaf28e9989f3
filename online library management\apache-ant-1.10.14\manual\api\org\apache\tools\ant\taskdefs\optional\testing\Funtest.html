<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Funtest (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.testing, class: Funtest">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.testing</a></div>
<h1 title="Class Funtest" class="title">Class Funtest</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.testing.Funtest</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Funtest</span>
<span class="extends-implements">extends <a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Task to provide functional testing under Ant, with a fairly complex workflow of:

 <ul>
 <li>Conditional execution</li>
 <li>Application to start</li>
 <li>A probe to "waitfor" before running tests</li>
 <li>A tests sequence</li>
 <li>A reporting sequence that runs after the tests have finished</li>
 <li>A "teardown" clause that runs after the rest.</li>
 <li>Automated termination of the program it executes, if a timeout is not met</li>
 <li>Checking of a failure property and automatic raising of a fault
     (with the text in failureText)
 if test shutdown and reporting succeeded</li>
  </ul>

 The task is designed to be framework neutral; it will work with JUnit,
  TestNG and other test frameworks That can be
 executed from Ant. It bears a resemblance to the FunctionalTest task from
 SmartFrog, as the attribute names were
 chosen to make migration easier. However, this task benefits from the
 ability to tweak Ant's internals, and so
 simplify the workflow, and from the experience of using the SmartFrog task.
 No code has been shared.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#APPLICATION_EXCEPTION" class="member-name-link">APPLICATION_EXCEPTION</a></code></div>
<div class="col-last even-row-color">
<div class="block">Application exception : "Application Exception"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#APPLICATION_FORCIBLY_SHUT_DOWN" class="member-name-link">APPLICATION_FORCIBLY_SHUT_DOWN</a></code></div>
<div class="col-last odd-row-color">
<div class="block">"Application forcibly shut down"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#SHUTDOWN_INTERRUPTED" class="member-name-link">SHUTDOWN_INTERRUPTED</a></code></div>
<div class="col-last even-row-color">
<div class="block">"Shutdown interrupted"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SKIPPING_TESTS" class="member-name-link">SKIPPING_TESTS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">"Condition failed -skipping tests"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TEARDOWN_EXCEPTION" class="member-name-link">TEARDOWN_EXCEPTION</a></code></div>
<div class="col-last even-row-color">
<div class="block">Teardown exception : "Teardown Exception"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WARN_OVERRIDING" class="member-name-link">WARN_OVERRIDING</a></code></div>
<div class="col-last odd-row-color">
<div class="block">"Overriding previous definition of "</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Funtest</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addApplication(org.apache.tools.ant.taskdefs.Sequential)" class="member-name-link">addApplication</a><wbr>(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an application.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBlock(org.apache.tools.ant.taskdefs.optional.testing.BlockFor)" class="member-name-link">addBlock</a><wbr>(<a href="BlockFor.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">BlockFor</a>&nbsp;sequence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a block.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReporting(org.apache.tools.ant.taskdefs.Sequential)" class="member-name-link">addReporting</a><wbr>(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set reporting sequence of tasks.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSetup(org.apache.tools.ant.taskdefs.Sequential)" class="member-name-link">addSetup</a><wbr>(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a setup sequence.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTeardown(org.apache.tools.ant.taskdefs.Sequential)" class="member-name-link">addTeardown</a><wbr>(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set teardown sequence of tasks.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTests(org.apache.tools.ant.taskdefs.Sequential)" class="member-name-link">addTests</a><wbr>(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add tests.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCondition()" class="member-name-link">createCondition</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a condition element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run the functional test sequence.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getApplicationException()" class="member-name-link">getApplicationException</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the application exception.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskException()" class="member-name-link">getTaskException</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the task exception.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeardownException()" class="member-name-link">getTeardownException</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the teardown exception.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTestException()" class="member-name-link">getTestException</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the test exception.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ignoringThrowable(java.lang.String,java.lang.Throwable)" class="member-name-link">ignoringThrowable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;thrown)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">log that we are ignoring something rather than rethrowing it.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processExceptions()" class="member-name-link">processExceptions</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Now faults are analysed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailOnTeardownErrors(boolean)" class="member-name-link">setFailOnTeardownErrors</a><wbr>(boolean&nbsp;failOnTeardownErrors)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the failOnTeardownErrors attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailureMessage(java.lang.String)" class="member-name-link">setFailureMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureMessage)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the failureMessage attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailureProperty(java.lang.String)" class="member-name-link">setFailureProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the failureProperty attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShutdownTime(long)" class="member-name-link">setShutdownTime</a><wbr>(long&nbsp;shutdownTime)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the shutdownTime attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShutdownUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)" class="member-name-link">setShutdownUnit</a><wbr>(<a href="../../WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the shutdownunit attribute.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeout(long)" class="member-name-link">setTimeout</a><wbr>(long&nbsp;timeout)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timeout attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeoutUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)" class="member-name-link">setTimeoutUnit</a><wbr>(<a href="../../WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the timeoutunit attribute.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="WARN_OVERRIDING">
<h3>WARN_OVERRIDING</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WARN_OVERRIDING</span></div>
<div class="block">"Overriding previous definition of "</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.WARN_OVERRIDING">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="APPLICATION_FORCIBLY_SHUT_DOWN">
<h3>APPLICATION_FORCIBLY_SHUT_DOWN</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">APPLICATION_FORCIBLY_SHUT_DOWN</span></div>
<div class="block">"Application forcibly shut down"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.APPLICATION_FORCIBLY_SHUT_DOWN">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SHUTDOWN_INTERRUPTED">
<h3>SHUTDOWN_INTERRUPTED</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SHUTDOWN_INTERRUPTED</span></div>
<div class="block">"Shutdown interrupted"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.SHUTDOWN_INTERRUPTED">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SKIPPING_TESTS">
<h3>SKIPPING_TESTS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SKIPPING_TESTS</span></div>
<div class="block">"Condition failed -skipping tests"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.SKIPPING_TESTS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="APPLICATION_EXCEPTION">
<h3>APPLICATION_EXCEPTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">APPLICATION_EXCEPTION</span></div>
<div class="block">Application exception : "Application Exception"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.APPLICATION_EXCEPTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TEARDOWN_EXCEPTION">
<h3>TEARDOWN_EXCEPTION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TEARDOWN_EXCEPTION</span></div>
<div class="block">Teardown exception : "Teardown Exception"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.testing.Funtest.TEARDOWN_EXCEPTION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Funtest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Funtest</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createCondition()">
<h3>createCondition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</a></span>&nbsp;<span class="element-name">createCondition</span>()</div>
<div class="block">Add a condition element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>ConditionBase</code>.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addApplication(org.apache.tools.ant.taskdefs.Sequential)">
<h3>addApplication</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addApplication</span><wbr><span class="parameters">(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</span></div>
<div class="block">Add an application.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - the application to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSetup(org.apache.tools.ant.taskdefs.Sequential)">
<h3>addSetup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSetup</span><wbr><span class="parameters">(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</span></div>
<div class="block">Add a setup sequence.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - the setup sequence to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addBlock(org.apache.tools.ant.taskdefs.optional.testing.BlockFor)">
<h3>addBlock</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBlock</span><wbr><span class="parameters">(<a href="BlockFor.html" title="class in org.apache.tools.ant.taskdefs.optional.testing">BlockFor</a>&nbsp;sequence)</span></div>
<div class="block">Add a block.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - the block for to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTests(org.apache.tools.ant.taskdefs.Sequential)">
<h3>addTests</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTests</span><wbr><span class="parameters">(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</span></div>
<div class="block">add tests.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - a sequence to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addReporting(org.apache.tools.ant.taskdefs.Sequential)">
<h3>addReporting</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReporting</span><wbr><span class="parameters">(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</span></div>
<div class="block">set reporting sequence of tasks.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - a reporting sequence to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addTeardown(org.apache.tools.ant.taskdefs.Sequential)">
<h3>addTeardown</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTeardown</span><wbr><span class="parameters">(<a href="../../Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</a>&nbsp;sequence)</span></div>
<div class="block">set teardown sequence of tasks.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sequence</code> - a teardown sequence to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailOnTeardownErrors(boolean)">
<h3>setFailOnTeardownErrors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailOnTeardownErrors</span><wbr><span class="parameters">(boolean&nbsp;failOnTeardownErrors)</span></div>
<div class="block">Set the failOnTeardownErrors attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failOnTeardownErrors</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailureMessage(java.lang.String)">
<h3>setFailureMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailureMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureMessage)</span></div>
<div class="block">Set the failureMessage attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failureMessage</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFailureProperty(java.lang.String)">
<h3>setFailureProperty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailureProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;failureProperty)</span></div>
<div class="block">Set the failureProperty attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>failureProperty</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShutdownTime(long)">
<h3>setShutdownTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShutdownTime</span><wbr><span class="parameters">(long&nbsp;shutdownTime)</span></div>
<div class="block">Set the shutdownTime attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shutdownTime</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeout(long)">
<h3>setTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeout</span><wbr><span class="parameters">(long&nbsp;timeout)</span></div>
<div class="block">Set the timeout attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timeout</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTimeoutUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">
<h3>setTimeoutUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeoutUnit</span><wbr><span class="parameters">(<a href="../../WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</span></div>
<div class="block">Set the timeoutunit attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unit</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShutdownUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">
<h3>setShutdownUnit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShutdownUnit</span><wbr><span class="parameters">(<a href="../../WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</a>&nbsp;unit)</span></div>
<div class="block">Set the shutdownunit attribute.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unit</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getApplicationException()">
<h3>getApplicationException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">getApplicationException</span>()</div>
<div class="block">Get the application exception.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the application exception.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeardownException()">
<h3>getTeardownException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">getTeardownException</span>()</div>
<div class="block">Get the teardown exception.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the teardown exception.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTestException()">
<h3>getTestException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">getTestException</span>()</div>
<div class="block">Get the test exception.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the test exception.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTaskException()">
<h3>getTaskException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span>&nbsp;<span class="element-name">getTaskException</span>()</div>
<div class="block">Get the task exception.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the task exception.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Run the functional test sequence.
 <p>
 This is a fairly complex workflow -what is going on is that we try to clean up
 no matter how the run ended, and to retain the innermost exception that got thrown
 during cleanup. That is, if teardown fails after the tests themselves failed, it is the
 test failing that is more important.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if something was caught during the run or teardown.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processExceptions()">
<h3>processExceptions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processExceptions</span>()</div>
<div class="block">Now faults are analysed.
 <p>The priority is</p>
 <ol>
 <li>testexceptions, except those indicating a build timeout when the application itself
 failed. (Because often it is the application fault that is more interesting than the probe
 failure, which is usually triggered by the application not starting.)</li>
 <li>Application exceptions (above test timeout exceptions)</li>
 <li>Teardown exceptions -except when they are being ignored</li>
 <li>Test failures as indicated by the failure property</li>
 </ol></div>
</section>
</li>
<li>
<section class="detail" id="ignoringThrowable(java.lang.String,java.lang.Throwable)">
<h3>ignoringThrowable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">ignoringThrowable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;thrown)</span></div>
<div class="block">log that we are ignoring something rather than rethrowing it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>type</code> - name of exception</dd>
<dd><code>thrown</code> - what was thrown</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
