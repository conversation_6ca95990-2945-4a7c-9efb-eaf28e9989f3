<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>IPlanetDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: IPlanetDeploymentTool">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class IPlanetDeploymentTool" class="title">Class IPlanetDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.IPlanetDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">IPlanetDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></span></div>
<div class="block">This class is used to generate iPlanet Application Server (iAS) 6.0 stubs and
 skeletons and build an EJB Jar file.  It is designed to be used with the Ant
 <code>ejbjar</code> task.  If only stubs and skeletons need to be generated
 (in other words, if no JAR file needs to be created), refer to the
 <code>iplanet-ejbc</code> task and the <code>IPlanetEjbcTask</code> class.
 <p>The following attributes may be specified by the user:</p>
 <ul>
     <li><i>destdir</i> -- The base directory into which the generated JAR
                           files will be written.  Each JAR file is written
                           in directories which correspond to their location
                           within the "descriptordir" namespace.  This is a
                           required attribute.
     <li><i>classpath</i> -- The classpath used when generating EJB stubs and
                             skeletons.  This is an optional attribute (if
                             omitted, the classpath specified in the "ejbjar"
                             parent task will be used).  If specified, the
                             classpath elements will be prepended to the
                             classpath specified in the parent "ejbjar" task.
                             Note that nested "classpath" elements may also be
                             used.
     <li><i>keepgenerated</i> -- Indicates whether or not the Java source
                                 files which are generated by ejbc will be
                                 saved or automatically deleted.  If "yes",
                                 the source files will be retained.  This is
                                 an optional attribute (if omitted, it
                                 defaults to "no").
     <li><i>debug</i> -- Indicates whether or not the ejbc utility should
                         log additional debugging statements to the standard
                         output.  If "yes", the additional debugging statements
                         will be generated (if omitted, it defaults to "no").
     <li><i>iashome</i> -- May be used to specify the "home" directory for
                           this iPlanet Application server installation.  This
                           is used to find the ejbc utility if it isn't
                           included in the user's system path.  This is an
                           optional attribute (if specified, it should refer
                           to the <code>[install-location]/iplanet/ias6/ias
                           </code> directory).  If omitted, the ejbc utility
                           must be on the user's system path.
     <li><i>suffix</i> -- String value appended to the JAR filename when
                          creating each JAR.  This attribute is not required
                          (if omitted, it defaults to ".jar").
 </ul>
 <p>For each EJB descriptor found in the "ejbjar" parent task, this deployment
 tool will locate the three classes that comprise the EJB.  If these class
 files cannot be located in the specified <code>srcdir</code> directory, the
 task will fail.  The task will also attempt to locate the EJB stubs and
 skeletons in this directory.  If found, the timestamps on the stubs and
 skeletons will be checked to ensure they are up to date.  Only if these files
 cannot be found or if they are out of date will ejbc be called.</p></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><code>IPlanetEjbc</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</a>, <a href="GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</a>, <a href="GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</a>, <a href="GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</a>, <a href="GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</a>, <a href="GenericDeploymentTool.html#EJB_DD">EJB_DD</a>, <a href="GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</a>, <a href="GenericDeploymentTool.html#MANIFEST">MANIFEST</a>, <a href="GenericDeploymentTool.html#META_DIR">META_DIR</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">IPlanetDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the iAS-specific EJB descriptor to the list of files which will be
 written to the JAR file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">checkConfiguration</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Verifies that the user selections are valid.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPublicId()" class="member-name-link">getPublicId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The iAS ejbc utility doesn't require the Public ID of the descriptor's
 DTD for it to process correctly--this method always returns <code>null
 </code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">parseEjbFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)" class="member-name-link">processDescriptor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether or not debugging output will be generated when ejbc is
 executed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGenericJarSuffix(java.lang.String)" class="member-name-link">setGenericJarSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Since iAS doesn't generate a "generic" JAR as part of its processing,
 this attribute is ignored and a warning message is displayed to the user.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIashome(java.io.File)" class="member-name-link">setIashome</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;iashome)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter method used to store the "home" directory of the user's iAS
 installation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgenerated(boolean)" class="member-name-link">setKeepgenerated</a><wbr>(boolean&nbsp;keepgenerated)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter method used to specify whether the Java source files generated by
 the ejbc utility should be saved or automatically deleted.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuffix(java.lang.String)" class="member-name-link">setSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jarSuffix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter method used to specify the filename suffix (for example, ".jar")
 for the JAR files to be created.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">addFileToJar</a>, <a href="GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</a>, <a href="GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</a>, <a href="GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a>, <a href="GenericDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</a>, <a href="GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</a>, <a href="GenericDeploymentTool.html#getConfig()">getConfig</a>, <a href="GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</a>, <a href="GenericDeploymentTool.html#getDestDir()">getDestDir</a>, <a href="GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</a>, <a href="GenericDeploymentTool.html#getLocation()">getLocation</a>, <a href="GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</a>, <a href="GenericDeploymentTool.html#getTask()">getTask</a>, <a href="GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String,java.lang.String)">getVendorDDPrefix</a>, <a href="GenericDeploymentTool.html#log(java.lang.String,int)">log</a>, <a href="GenericDeploymentTool.html#needToRebuild(java.util.Hashtable,java.io.File)">needToRebuild</a>, <a href="GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</a>, <a href="GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</a>, <a href="GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a>, <a href="GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</a>, <a href="GenericDeploymentTool.html#validateConfigured()">validateConfigured</a>, <a href="GenericDeploymentTool.html#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">writeJar</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>IPlanetDeploymentTool</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">IPlanetDeploymentTool</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setIashome(java.io.File)">
<h3>setIashome</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIashome</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;iashome)</span></div>
<div class="block">Setter method used to store the "home" directory of the user's iAS
 installation.  The directory specified should typically be
 <code>[install-location]/iplanet/ias6/ias</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>iashome</code> - The home directory for the user's iAS installation.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setKeepgenerated(boolean)">
<h3>setKeepgenerated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgenerated</span><wbr><span class="parameters">(boolean&nbsp;keepgenerated)</span></div>
<div class="block">Setter method used to specify whether the Java source files generated by
 the ejbc utility should be saved or automatically deleted.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>keepgenerated</code> - boolean which, if <code>true</code>, indicates that
                      Java source files generated by ejbc for the stubs
                      and skeletons should be kept.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">Sets whether or not debugging output will be generated when ejbc is
 executed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - A boolean indicating if debugging output should be generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSuffix(java.lang.String)">
<h3>setSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jarSuffix)</span></div>
<div class="block">Setter method used to specify the filename suffix (for example, ".jar")
 for the JAR files to be created.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jarSuffix</code> - The string to use as the JAR filename suffix.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setGenericJarSuffix(java.lang.String)">
<h3>setGenericJarSuffix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGenericJarSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Since iAS doesn't generate a "generic" JAR as part of its processing,
 this attribute is ignored and a warning message is displayed to the user.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.  This parameter is
                 ignored.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>processDescriptor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processDescriptor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span></div>
<div class="block">Process a deployment descriptor, generating the necessary vendor specific
 deployment files...</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorName</code> - the name of the deployment descriptor</dd>
<dd><code>saxParser</code> - a SAX parser which can be used to parse the deployment descriptor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>checkConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkConfiguration</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span>
                           throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Verifies that the user selections are valid.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">checkConfiguration</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dd><code>saxParser</code> - SAXParser which may be used to parse the XML
                           descriptor</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - If the user selections are invalid.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">
<h3>parseEjbFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</span>&nbsp;<span class="element-name">parseEjbFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;descriptorFileName,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/javax/xml/parsers/SAXParser.html" title="class or interface in javax.xml.parsers" class="external-link">SAXParser</a>&nbsp;saxParser)</span>
                                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">parseEjbFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>descriptorFileName</code> - String representing the file name of an EJB
                           descriptor to be processed</dd>
<dd><code>saxParser</code> - SAXParser which may be used to parse the XML
                           descriptor</dd>
<dt>Returns:</dt>
<dd>Hashtable of EJB class (and other) files to be
                           added to the completed JAR file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - An IOException from the parser, possibly from
                           the byte stream or character stream</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - Any SAX exception, possibly wrapping another
                           exception</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ddPrefix)</span></div>
<div class="block">Add the iAS-specific EJB descriptor to the list of files which will be
 written to the JAR file.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable,java.lang.String)">addVendorFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - Hashtable of EJB class (and other) files to be added to
                 the completed JAR file.</dd>
<dd><code>ddPrefix</code> - not used</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPublicId()">
<h3>getPublicId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPublicId</span>()</div>
<div class="block">The iAS ejbc utility doesn't require the Public ID of the descriptor's
 DTD for it to process correctly--this method always returns <code>null
 </code>.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#getPublicId()">getPublicId</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Returns:</dt>
<dd><code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
