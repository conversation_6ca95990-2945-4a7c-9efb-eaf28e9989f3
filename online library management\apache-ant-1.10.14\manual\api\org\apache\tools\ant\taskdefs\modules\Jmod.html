<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Jmod (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.modules, class: Jmod">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.modules</a></div>
<h1 title="Class Jmod" class="title">Class Jmod</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.modules.Jmod</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Jmod</span>
<span class="extends-implements">extends <a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></span></div>
<div class="block">Creates a linkable .jmod file from a modular jar file, and optionally from
 other resource files such as native libraries and documents.  Equivalent
 to the JDK's
 <a href="https://docs.oracle.com/en/java/javase/11/tools/jmod.html">jmod</a>
 tool.
 <p>
 Supported attributes:
 <dl>
 <dt><code>destFile</code>
 <dd>Required, jmod file to create.
 <dt><code>classpath</code>
 <dt><code>classpathref</code>
 <dd>Where to locate files to be placed in the jmod file.
 <dt><code>modulepath</code>
 <dt><code>modulepathref</code>
 <dd>Where to locate dependencies.
 <dt><code>commandpath</code>
 <dt><code>commandpathref</code>
 <dd>Directories containing native commands to include in jmod.
 <dt><code>headerpath</code>
 <dt><code>headerpathref</code>
 <dd>Directories containing header files to include in jmod.
 <dt><code>configpath</code>
 <dt><code>configpathref</code>
 <dd>Directories containing user-editable configuration files
     to include in jmod.
 <dt><code>legalpath</code>
 <dt><code>legalpathref</code>
 <dd>Directories containing legal licenses and notices to include in jmod.
 <dt><code>nativelibpath</code>
 <dt><code>nativelibpathref</code>
 <dd>Directories containing native libraries to include in jmod.
 <dt><code>manpath</code>
 <dt><code>manpathref</code>
 <dd>Directories containing man pages to include in jmod.
 <dt><code>version</code>
 <dd>Module <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">version</a>.
 <dt><code>mainclass</code>
 <dd>Main class of module.
 <dt><code>platform</code>
 <dd>The target platform for the jmod.  A particular JDK's platform
 can be seen by running
 <code>jmod describe $JDK_HOME/jmods/java.base.jmod | grep -i platform</code>.
 <dt><code>hashModulesPattern</code>
 <dd>Regular expression for names of modules in the module path
     which depend on the jmod being created, and which should have
     hashes generated for them and included in the new jmod.
 <dt><code>resolveByDefault</code>
 <dd>Boolean indicating whether the jmod should be one of
     the default resolved modules in an application.  Default is true.
 <dt><code>moduleWarnings</code>
 <dd>Whether to emit warnings when resolving modules which are
     not recommended for use.  Comma-separated list of one of more of
     the following:
     <dl>
     <dt><code>deprecated</code>
     <dd>Warn if module is deprecated
     <dt><code>leaving</code>
     <dd>Warn if module is deprecated for removal
     <dt><code>incubating</code>
     <dd>Warn if module is an incubating (not yet official) module
     </dl>
 </dl>

 <p>
 Supported nested elements:
 <dl>
 <dt><code>&lt;classpath&gt;</code>
 <dd>Path indicating where to locate files to be placed in the jmod file.
 <dt><code>&lt;modulepath&gt;</code>
 <dd>Path indicating where to locate dependencies.
 <dt><code>&lt;commandpath&gt;</code>
 <dd>Path of directories containing native commands to include in jmod.
 <dt><code>&lt;headerpath&gt;</code>
 <dd>Path of directories containing header files to include in jmod.
 <dt><code>&lt;configpath&gt;</code>
 <dd>Path of directories containing user-editable configuration files
     to include in jmod.
 <dt><code>&lt;legalpath&gt;</code>
 <dd>Path of directories containing legal notices to include in jmod.
 <dt><code>&lt;nativelibpath&gt;</code>
 <dd>Path of directories containing native libraries to include in jmod.
 <dt><code>&lt;manpath&gt;</code>
 <dd>Path of directories containing man pages to include in jmod.
 <dt><code>&lt;version&gt;</code>
 <dd><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">Module version</a> of jmod.
     Must have a required <code>number</code> attribute.  May also have optional
     <code>preRelease</code> and <code>build</code> attributes.
 <dt><code>&lt;moduleWarning&gt;</code>
 <dd>Has one required attribute, <code>reason</code>.  See <code>moduleWarnings</code>
     attribute above.  This element may be specified multiple times.
 </dl>
 <p>
 destFile and classpath are required data.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.10.6</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Jmod.ResolutionWarningReason.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningReason</a></code></div>
<div class="col-last even-row-color">
<div class="block">Permissible reasons for jmod creation to emit warnings.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="Jmod.ResolutionWarningSpec.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningSpec</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Child element which enables jmod tool warnings.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#target">target</a>, <a href="../../Task.html#taskName">taskName</a>, <a href="../../Task.html#taskType">taskType</a>, <a href="../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Jmod</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an unconfigured <code>&lt;classpath&gt;</code> child element which can
 specify the files which will comprise the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCommandPath()" class="member-name-link">createCommandPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing native executable files to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createConfigPath()" class="member-name-link">createConfigPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing user configuration files to include in the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createHeaderPath()" class="member-name-link">createHeaderPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing compile-time header files for third party use, to include
 in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createLegalPath()" class="member-name-link">createLegalPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing license files to include in the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createManPath()" class="member-name-link">createManPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing man pages (program manuals, typically in troff format)
 to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModulePath()" class="member-name-link">createModulePath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child <code>&lt;modulePath&gt;</code> element which can contain a
 path of directories containing modules upon which modules in the
 <a href="#setClasspath(org.apache.tools.ant.types.Path)">classpath</a> depend.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Jmod.ResolutionWarningSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningSpec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModuleWarning()" class="member-name-link">createModuleWarning</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can specify the circumstances
 under which jmod creation emits warnings.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createNativeLibPath()" class="member-name-link">createNativeLibPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a child element which can contain a list of directories
 containing native libraries to include in the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/ModuleVersion.html" title="class in org.apache.tools.ant.types">ModuleVersion</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createVersion()" class="member-name-link">createVersion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates an uninitialized child element representing the version of
 the module represented by the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a jmod file according to this task's properties
 and child elements.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute which specifies the files (usually modular .jar files)
 which will comprise the created jmod file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCommandPath()" class="member-name-link">getCommandPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing path of directories which contain native
 executable files to include in the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getConfigPath()" class="member-name-link">getConfigPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing list of directories which contain
 user configuration files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestFile()" class="member-name-link">getDestFile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing the location of the jmod file to create.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHashModulesPattern()" class="member-name-link">getHashModulesPattern</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing a regular expression which specifies which
 of the modules that depend on the jmod being created should have
 hashes generated and added to the jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeaderPath()" class="member-name-link">getHeaderPath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing a path of directories which hold compile-time
 header files for third party use, all of which will be included in the
 created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLegalPath()" class="member-name-link">getLegalPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing list of directories which hold license files
 to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMainClass()" class="member-name-link">getMainClass</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing the class that acts as the executable entry point
 of the created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getManPath()" class="member-name-link">getManPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing list of directories containing man pages
 to include in created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulePath()" class="member-name-link">getModulePath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing path of directories which contain modules on which
 the created jmod's <a href="#setClasspath(org.apache.tools.ant.types.Path)">constituent modules</a>
 depend.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeLibPath()" class="member-name-link">getNativeLibPath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing list of directories which hold native libraries
 to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlatform()" class="member-name-link">getPlatform</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute containing the platform for which the jmod
 will be built.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResolveByDefault()" class="member-name-link">getResolveByDefault</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute indicating whether the created jmod should be visible
 in a module path, even when not specified explicitly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVersion()" class="member-name-link">getVersion</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Attribute which specifies
 a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">module version</a>
 for created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute specifying the files that will comprise the created jmod
 file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setClasspath(org.apache.tools.ant.types.Path)">classpath attribute</a> from a
 path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCommandPath(org.apache.tools.ant.types.Path)" class="member-name-link">setCommandPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing path of directories which contain native
 executable files to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCommandPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setCommandPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setCommandPath(org.apache.tools.ant.types.Path)">command path</a>
 from a path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConfigPath(org.apache.tools.ant.types.Path)" class="member-name-link">setConfigPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing list of directories which contain
 user configuration files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setConfigPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setConfigPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setConfigPath(org.apache.tools.ant.types.Path)">configuration file path</a>
 from a path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestFile(java.io.File)" class="member-name-link">setDestFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing the location of the jmod file to create.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHashModulesPattern(java.lang.String)" class="member-name-link">setHashModulesPattern</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a regular expression which specifies which
 of the modules that depend on the jmod being created should have
 hashes generated and added to the jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHeaderPath(org.apache.tools.ant.types.Path)" class="member-name-link">setHeaderPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a path of directories which hold compile-time
 header files for third party use, all of which will be included in the
 created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHeaderPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setHeaderPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setHeaderPath(org.apache.tools.ant.types.Path)">header path</a>
 from a path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLegalPath(org.apache.tools.ant.types.Path)" class="member-name-link">setLegalPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing list of directories which hold license files
 to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLegalPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setLegalPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setLegalPath(org.apache.tools.ant.types.Path)">legal licenses path</a>
 from a path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMainClass(java.lang.String)" class="member-name-link">setMainClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing the class that acts as the
 executable entry point of the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManPath(org.apache.tools.ant.types.Path)" class="member-name-link">setManPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing list of directories containing man pages
 to include in created jmod.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setManPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setManPath(org.apache.tools.ant.types.Path)">man pages path</a>
 from a path reference.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulePath(org.apache.tools.ant.types.Path)" class="member-name-link">setModulePath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing path of directories which contain modules
 on which the created jmod's
 <a href="#setClasspath(org.apache.tools.ant.types.Path)">constituent modules</a> depend.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulePathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setModulePathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setModulePath(org.apache.tools.ant.types.Path)">module path</a>
 from a path reference.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModuleWarnings(java.lang.String)" class="member-name-link">setModuleWarnings</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;warningList)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing a comma-separated list of reasons for
 jmod creation to emit warnings.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNativeLibPath(org.apache.tools.ant.types.Path)" class="member-name-link">setNativeLibPath</a><wbr>(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing list of directories which hold native libraries
 to include in the created jmod.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNativeLibPathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setNativeLibPathRef</a><wbr>(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets <a href="#setNativeLibPath(org.apache.tools.ant.types.Path)">native library path</a>
 from a path reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPlatform(java.lang.String)" class="member-name-link">setPlatform</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;platform)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute containing the platform for which the jmod
 will be built.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setResolveByDefault(boolean)" class="member-name-link">setResolveByDefault</a><wbr>(boolean&nbsp;resolve)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets attribute indicating whether the created jmod should be visible
 in a module path, even when not specified explicitly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVersion(java.lang.String)" class="member-name-link">setVersion</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">module version</a>
 for the created jmod.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../Task.html#getTaskName()">getTaskName</a>, <a href="../../Task.html#getTaskType()">getTaskType</a>, <a href="../../Task.html#getWrapper()">getWrapper</a>, <a href="../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../Task.html#init()">init</a>, <a href="../../Task.html#isInvalid()">isInvalid</a>, <a href="../../Task.html#log(java.lang.String)">log</a>, <a href="../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../Task.html#perform()">perform</a>, <a href="../../Task.html#reconfigure()">reconfigure</a>, <a href="../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#clone()">clone</a>, <a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Jmod</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Jmod</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getDestFile()">
<h3>getDestFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestFile</span>()</div>
<div class="block">Attribute containing the location of the jmod file to create.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>location of jmod file</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setDestFile(java.io.File)"><code>setDestFile(File)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestFile(java.io.File)">
<h3>setDestFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Sets attribute containing the location of the jmod file to create.
 This value is required.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - location where jmod file will be created.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Adds an unconfigured <code>&lt;classpath&gt;</code> child element which can
 specify the files which will comprise the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setClasspath(org.apache.tools.ant.types.Path)"><code>setClasspath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">Attribute which specifies the files (usually modular .jar files)
 which will comprise the created jmod file.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path of constituent files</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setClasspath(org.apache.tools.ant.types.Path)"><code>setClasspath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute specifying the files that will comprise the created jmod
 file.  Usually this contains a single modular .jar file.
 <p>
 The classpath is required and must not be empty.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path of files that will comprise jmod</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createClasspath()"><code>createClasspath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setClasspath(org.apache.tools.ant.types.Path)">classpath attribute</a> from a
 path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createModulePath()">
<h3>createModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createModulePath</span>()</div>
<div class="block">Creates a child <code>&lt;modulePath&gt;</code> element which can contain a
 path of directories containing modules upon which modules in the
 <a href="#setClasspath(org.apache.tools.ant.types.Path)">classpath</a> depend.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModulePath(org.apache.tools.ant.types.Path)"><code>setModulePath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModulePath()">
<h3>getModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulePath</span>()</div>
<div class="block">Attribute containing path of directories which contain modules on which
 the created jmod's <a href="#setClasspath(org.apache.tools.ant.types.Path)">constituent modules</a>
 depend.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path of directories containing modules needed by
         classpath modules</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModulePath(org.apache.tools.ant.types.Path)"><code>setModulePath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModulePath(org.apache.tools.ant.types.Path)">
<h3>setModulePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulePath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing path of directories which contain modules
 on which the created jmod's
 <a href="#setClasspath(org.apache.tools.ant.types.Path)">constituent modules</a> depend.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path of directories containing modules needed by
             classpath modules</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createModulePath()"><code>createModulePath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModulePathRef(org.apache.tools.ant.types.Reference)">
<h3>setModulePathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulePathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setModulePath(org.apache.tools.ant.types.Path)">module path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as module path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCommandPath()">
<h3>createCommandPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createCommandPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing native executable files to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setCommandPath(org.apache.tools.ant.types.Path)"><code>setCommandPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCommandPath()">
<h3>getCommandPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getCommandPath</span>()</div>
<div class="block">Attribute containing path of directories which contain native
 executable files to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>list of directories containing native executables</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setCommandPath(org.apache.tools.ant.types.Path)"><code>setCommandPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCommandPath(org.apache.tools.ant.types.Path)">
<h3>setCommandPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCommandPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing path of directories which contain native
 executable files to include in the created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - list of directories containing native executables</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createCommandPath()"><code>createCommandPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCommandPathRef(org.apache.tools.ant.types.Reference)">
<h3>setCommandPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCommandPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setCommandPath(org.apache.tools.ant.types.Path)">command path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as command path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createConfigPath()">
<h3>createConfigPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createConfigPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing user configuration files to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setConfigPath(org.apache.tools.ant.types.Path)"><code>setConfigPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getConfigPath()">
<h3>getConfigPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getConfigPath</span>()</div>
<div class="block">Attribute containing list of directories which contain
 user configuration files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>list of directories containing user configuration files</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setConfigPath(org.apache.tools.ant.types.Path)"><code>setConfigPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setConfigPath(org.apache.tools.ant.types.Path)">
<h3>setConfigPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setConfigPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing list of directories which contain
 user configuration files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - list of directories containing user configuration files</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createConfigPath()"><code>createConfigPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setConfigPathRef(org.apache.tools.ant.types.Reference)">
<h3>setConfigPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setConfigPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setConfigPath(org.apache.tools.ant.types.Path)">configuration file path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as configuration file path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createHeaderPath()">
<h3>createHeaderPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createHeaderPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing compile-time header files for third party use, to include
 in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setHeaderPath(org.apache.tools.ant.types.Path)"><code>setHeaderPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getHeaderPath()">
<h3>getHeaderPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getHeaderPath</span>()</div>
<div class="block">Attribute containing a path of directories which hold compile-time
 header files for third party use, all of which will be included in the
 created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path of directories containing header files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHeaderPath(org.apache.tools.ant.types.Path)">
<h3>setHeaderPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHeaderPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing a path of directories which hold compile-time
 header files for third party use, all of which will be included in the
 created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path of directories containing header files</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createHeaderPath()"><code>createHeaderPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHeaderPathRef(org.apache.tools.ant.types.Reference)">
<h3>setHeaderPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHeaderPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setHeaderPath(org.apache.tools.ant.types.Path)">header path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as header path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createLegalPath()">
<h3>createLegalPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createLegalPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing license files to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setLegalPath(org.apache.tools.ant.types.Path)"><code>setLegalPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLegalPath()">
<h3>getLegalPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getLegalPath</span>()</div>
<div class="block">Attribute containing list of directories which hold license files
 to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path containing directories which hold license files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLegalPath(org.apache.tools.ant.types.Path)">
<h3>setLegalPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLegalPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing list of directories which hold license files
 to include in the created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path containing directories which hold license files</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createLegalPath()"><code>createLegalPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLegalPathRef(org.apache.tools.ant.types.Reference)">
<h3>setLegalPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLegalPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setLegalPath(org.apache.tools.ant.types.Path)">legal licenses path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as legal path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createNativeLibPath()">
<h3>createNativeLibPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createNativeLibPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing native libraries to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setNativeLibPath(org.apache.tools.ant.types.Path)"><code>setNativeLibPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNativeLibPath()">
<h3>getNativeLibPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getNativeLibPath</span>()</div>
<div class="block">Attribute containing list of directories which hold native libraries
 to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path of directories containing native libraries</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNativeLibPath(org.apache.tools.ant.types.Path)">
<h3>setNativeLibPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNativeLibPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing list of directories which hold native libraries
 to include in the created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path of directories containing native libraries</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createNativeLibPath()"><code>createNativeLibPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNativeLibPathRef(org.apache.tools.ant.types.Reference)">
<h3>setNativeLibPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNativeLibPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setNativeLibPath(org.apache.tools.ant.types.Path)">native library path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as native library path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createManPath()">
<h3>createManPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createManPath</span>()</div>
<div class="block">Creates a child element which can contain a list of directories
 containing man pages (program manuals, typically in troff format)
 to include in the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setManPath(org.apache.tools.ant.types.Path)"><code>setManPath(Path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getManPath()">
<h3>getManPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getManPath</span>()</div>
<div class="block">Attribute containing list of directories containing man pages
 to include in created jmod.  Man pages are textual program manuals,
 typically in troff format.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>path containing directories which hold man pages to include
         in jmod</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setManPath(org.apache.tools.ant.types.Path)">
<h3>setManPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManPath</span><wbr><span class="parameters">(<a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path)</span></div>
<div class="block">Sets attribute containing list of directories containing man pages
 to include in created jmod.  Man pages are textual program manuals,
 typically in troff format.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - path containing directories which hold man pages to include
             in jmod</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createManPath()"><code>createManPath()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setManPathRef(org.apache.tools.ant.types.Reference)">
<h3>setManPathRef</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManPathRef</span><wbr><span class="parameters">(<a href="../../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;ref)</span></div>
<div class="block">Sets <a href="#setManPath(org.apache.tools.ant.types.Path)">man pages path</a>
 from a path reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ref</code> - reference to path which will act as module path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createVersion()">
<h3>createVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/ModuleVersion.html" title="class in org.apache.tools.ant.types">ModuleVersion</a></span>&nbsp;<span class="element-name">createVersion</span>()</div>
<div class="block">Creates an uninitialized child element representing the version of
 the module represented by the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setVersion(java.lang.String)"><code>setVersion(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVersion()">
<h3>getVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getVersion</span>()</div>
<div class="block">Attribute which specifies
 a <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">module version</a>
 for created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>module version for created jmod</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setVersion(java.lang.String)">
<h3>setVersion</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVersion</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;version)</span></div>
<div class="block">Sets the <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/module/ModuleDescriptor.Version.html">module version</a>
 for the created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>version</code> - module version of created jmod</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createVersion()"><code>createVersion()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMainClass()">
<h3>getMainClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMainClass</span>()</div>
<div class="block">Attribute containing the class that acts as the executable entry point
 of the created jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>fully-qualified name of jmod's main class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMainClass(java.lang.String)">
<h3>setMainClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMainClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span></div>
<div class="block">Sets attribute containing the class that acts as the
 executable entry point of the created jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>className</code> - fully-qualified name of jmod's main class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlatform()">
<h3>getPlatform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPlatform</span>()</div>
<div class="block">Attribute containing the platform for which the jmod
 will be built.  Platform values are defined in the
 <code>java.base.jmod</code> of JDKs, and usually take the form
 <var>OS</var><code>-</code><var>architecture</var>.  If unset,
 current platform is used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>OS and architecture for which jmod will be built, or <code>null</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPlatform(java.lang.String)">
<h3>setPlatform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPlatform</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;platform)</span></div>
<div class="block">Sets attribute containing the platform for which the jmod
 will be built.  Platform values are defined in the
 <code>java.base.jmod</code> of JDKs, and usually take the form
 <var>OS</var><code>-</code><var>architecture</var>.  If unset,
 current platform is used.
 <p>
 A JDK's platform can be viewed with a command like:
 <code>jmod describe $JDK_HOME/jmods/java.base.jmod | grep -i platform</code>.
o    *</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>platform</code> - platform for which jmod will be created, or <code>null</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getHashModulesPattern()">
<h3>getHashModulesPattern</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getHashModulesPattern</span>()</div>
<div class="block">Attribute containing a regular expression which specifies which
 of the modules that depend on the jmod being created should have
 hashes generated and added to the jmod.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>regex specifying which dependent modules should have
         their generated hashes included</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHashModulesPattern(java.lang.String)">
<h3>setHashModulesPattern</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHashModulesPattern</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern)</span></div>
<div class="block">Sets attribute containing a regular expression which specifies which
 of the modules that depend on the jmod being created should have
 hashes generated and added to the jmod.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - regex specifying which dependent modules should have
         their generated hashes included</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResolveByDefault()">
<h3>getResolveByDefault</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getResolveByDefault</span>()</div>
<div class="block">Attribute indicating whether the created jmod should be visible
 in a module path, even when not specified explicitly.  True by default.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether jmod should be visible in module paths</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setResolveByDefault(boolean)">
<h3>setResolveByDefault</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setResolveByDefault</span><wbr><span class="parameters">(boolean&nbsp;resolve)</span></div>
<div class="block">Sets attribute indicating whether the created jmod should be visible
 in a module path, even when not specified explicitly.  True by default.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>resolve</code> - whether jmod should be visible in module paths</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createModuleWarning()">
<h3>createModuleWarning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Jmod.ResolutionWarningSpec.html" title="class in org.apache.tools.ant.taskdefs.modules">Jmod.ResolutionWarningSpec</a></span>&nbsp;<span class="element-name">createModuleWarning</span>()</div>
<div class="block">Creates a child element which can specify the circumstances
 under which jmod creation emits warnings.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>new, unconfigured child element</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setModuleWarnings(java.lang.String)"><code>setModuleWarnings(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setModuleWarnings(java.lang.String)">
<h3>setModuleWarnings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModuleWarnings</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;warningList)</span></div>
<div class="block">Sets attribute containing a comma-separated list of reasons for
 jmod creation to emit warnings.  Valid values in list are:
 <code>deprecated</code>, <code>leaving</code>, <code>incubating</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>warningList</code> - list containing one or more of the above values,
                    separated by commas</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#createModuleWarning()"><code>createModuleWarning()</code></a></li>
<li><a href="Jmod.ResolutionWarningReason.html" title="class in org.apache.tools.ant.taskdefs.modules"><code>Jmod.ResolutionWarningReason</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Creates a jmod file according to this task's properties
 and child elements.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if destFile is not set</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if classpath is not set or is empty</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if any path other than classpath refers to an
                        existing file which is not a directory</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if both <code>version</code> attribute and
                        <code>&lt;version&gt;</code> child element are present</dd>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if <code>hashModulesPattern</code> is set, but
                        module path is not defined</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
