<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ContentTransformingResource (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.resources, class: ContentTransformingResource">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.resources</a></div>
<h1 title="Class ContentTransformingResource" class="title">Class ContentTransformingResource</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../Resource.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.Resource</a>
<div class="inheritance"><a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">org.apache.tools.ant.types.resources.ResourceDecorator</a>
<div class="inheritance">org.apache.tools.ant.types.resources.ContentTransformingResource</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;</code>, <code><a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">ContentTransformingResource</span>
<span class="extends-implements">extends <a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></span></div>
<div class="block">A resource that transforms the content of another resource.

 <p>Wraps around another resource, delegates all queries (except
 getSize) to that other resource but transforms stream content
 on the fly.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.Resource">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#MAGIC">MAGIC</a>, <a href="../Resource.html#UNKNOWN_DATETIME">UNKNOWN_DATETIME</a>, <a href="../Resource.html#UNKNOWN_SIZE">UNKNOWN_SIZE</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ContentTransformingResource</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">no arg constructor</div>
</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.types.ResourceCollection)" class="member-name-link">ContentTransformingResource</a><wbr>(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor with another resource to wrap.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#as(java.lang.Class)" class="member-name-link">as</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Suppress FileProvider, re-implement Appendable</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputStream()" class="member-name-link">getInputStream</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an InputStream for the Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOutputStream()" class="member-name-link">getOutputStream</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an OutputStream for the Resource.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the size of this Resource.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAppendSupported()" class="member-name-link">isAppendSupported</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Learn whether the transformation performed allows appends.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>protected abstract <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#wrapStream(java.io.InputStream)" class="member-name-link">wrapStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Get a content-filtering/transforming InputStream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>protected abstract <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#wrapStream(java.io.OutputStream)" class="member-name-link">wrapStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Get a content-filtering/transforming OutputStream.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.resources.ResourceDecorator">Methods inherited from class&nbsp;org.apache.tools.ant.types.resources.<a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></h3>
<code><a href="ResourceDecorator.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</a>, <a href="ResourceDecorator.html#compareTo(org.apache.tools.ant.types.Resource)">compareTo</a>, <a href="ResourceDecorator.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="ResourceDecorator.html#getLastModified()">getLastModified</a>, <a href="ResourceDecorator.html#getName()">getName</a>, <a href="ResourceDecorator.html#getResource()">getResource</a>, <a href="ResourceDecorator.html#hashCode()">hashCode</a>, <a href="ResourceDecorator.html#isDirectory()">isDirectory</a>, <a href="ResourceDecorator.html#isExists()">isExists</a>, <a href="ResourceDecorator.html#isFilesystemOnly()">isFilesystemOnly</a>, <a href="ResourceDecorator.html#setDirectory(boolean)">setDirectory</a>, <a href="ResourceDecorator.html#setExists(boolean)">setExists</a>, <a href="ResourceDecorator.html#setLastModified(long)">setLastModified</a>, <a href="ResourceDecorator.html#setName(java.lang.String)">setName</a>, <a href="ResourceDecorator.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="ResourceDecorator.html#setSize(long)">setSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.Resource">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a></h3>
<code><a href="../Resource.html#asOptional(java.lang.Class)">asOptional</a>, <a href="../Resource.html#clone()">clone</a>, <a href="../Resource.html#equals(java.lang.Object)">equals</a>, <a href="../Resource.html#getMagicNumber(byte%5B%5D)">getMagicNumber</a>, <a href="../Resource.html#getRef()">getRef</a>, <a href="../Resource.html#iterator()">iterator</a>, <a href="../Resource.html#size()">size</a>, <a href="../Resource.html#toLongString()">toLongString</a>, <a href="../Resource.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Iterable">Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#forEach(java.util.function.Consumer)" title="class or interface in java.lang" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Iterable.html#spliterator()" title="class or interface in java.lang" class="external-link">spliterator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ResourceCollection">Methods inherited from interface&nbsp;org.apache.tools.ant.types.<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a></h3>
<code><a href="../ResourceCollection.html#isEmpty()">isEmpty</a>, <a href="../ResourceCollection.html#stream()">stream</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ContentTransformingResource</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ContentTransformingResource</span>()</div>
<div class="block">no arg constructor</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.types.ResourceCollection)">
<h3>ContentTransformingResource</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">ContentTransformingResource</span><wbr><span class="parameters">(<a href="../ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</a>&nbsp;other)</span></div>
<div class="block">Constructor with another resource to wrap.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the resource to wrap.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getSize</span>()</div>
<div class="block">Get the size of this Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ResourceDecorator.html#getSize()">getSize</a></code>&nbsp;in class&nbsp;<code><a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></code></dd>
<dt>Returns:</dt>
<dd>the size, as a long, 0 if the Resource does not exist (for
         compatibility with java.io.File), or UNKNOWN_SIZE if not known.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInputStream()">
<h3>getInputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">getInputStream</span>()
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get an InputStream for the Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ResourceDecorator.html#getInputStream()">getInputStream</a></code>&nbsp;in class&nbsp;<code><a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></code></dd>
<dt>Returns:</dt>
<dd>an InputStream containing this Resource's content.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if unable to provide the content of this
         Resource as a stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/UnsupportedOperationException.html" title="class or interface in java.lang" class="external-link">UnsupportedOperationException</a></code> - if InputStreams are not
         supported for this Resource type.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOutputStream()">
<h3>getOutputStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></span>&nbsp;<span class="element-name">getOutputStream</span>()
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get an OutputStream for the Resource.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ResourceDecorator.html#getOutputStream()">getOutputStream</a></code>&nbsp;in class&nbsp;<code><a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></code></dd>
<dt>Returns:</dt>
<dd>an OutputStream to which content can be written.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if unable to provide the content of this
         Resource as a stream.</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/UnsupportedOperationException.html" title="class or interface in java.lang" class="external-link">UnsupportedOperationException</a></code> - if OutputStreams are not
         supported for this Resource type.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="as(java.lang.Class)">
<h3>as</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">as</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;clazz)</span></div>
<div class="block">Suppress FileProvider, re-implement Appendable</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="ResourceDecorator.html#as(java.lang.Class)">as</a></code>&nbsp;in class&nbsp;<code><a href="ResourceDecorator.html" title="class in org.apache.tools.ant.types.resources">ResourceDecorator</a></code></dd>
<dt>Type Parameters:</dt>
<dd><code>T</code> - desired type</dd>
<dt>Parameters:</dt>
<dd><code>clazz</code> - a class</dd>
<dt>Returns:</dt>
<dd>resource of a desired type</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAppendSupported()">
<h3>isAppendSupported</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAppendSupported</span>()</div>
<div class="block">Learn whether the transformation performed allows appends.

 <p>In general compressed outputs will become invalid if they
 are appended to, for example.</p>

 <p>This implementations returns false.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>boolean false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="wrapStream(java.io.InputStream)">
<h3>wrapStream</h3>
<div class="member-signature"><span class="modifiers">protected abstract</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a></span>&nbsp;<span class="element-name">wrapStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in)</span>
                                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get a content-filtering/transforming InputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - InputStream to wrap, will never be null.</dd>
<dt>Returns:</dt>
<dd>a compressed InputStream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="wrapStream(java.io.OutputStream)">
<h3>wrapStream</h3>
<div class="member-signature"><span class="modifiers">protected abstract</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a></span>&nbsp;<span class="element-name">wrapStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span>
                                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Get a content-filtering/transforming OutputStream.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - OutputStream to wrap, will never be null.</dd>
<dt>Returns:</dt>
<dd>a compressed OutputStream.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is a problem.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
