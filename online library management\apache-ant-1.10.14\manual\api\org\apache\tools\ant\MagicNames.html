<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>MagicNames (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: MagicNames">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class MagicNames" class="title">Class MagicNames</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.MagicNames</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">MagicNames</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Magic names used within Ant.

 Not all magic names are here yet.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_CORE_PACKAGE" class="member-name-link">ANT_CORE_PACKAGE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of Ant core package
 Value: "org.apache.tools.ant"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_EXECUTOR_CLASSNAME" class="member-name-link">ANT_EXECUTOR_CLASSNAME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Property defining the classname of an executor.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_EXECUTOR_REFERENCE" class="member-name-link">ANT_EXECUTOR_REFERENCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Reference to the current Ant executor.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_FILE" class="member-name-link">ANT_FILE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">property for ant file name.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_FILE_TYPE" class="member-name-link">ANT_FILE_TYPE</a></code></div>
<div class="col-last even-row-color">
<div class="block">property for type of ant build file (either file or url)
 Value: "ant.file.type"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_FILE_TYPE_FILE" class="member-name-link">ANT_FILE_TYPE_FILE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">ant build file of type file
 Value: "file"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_FILE_TYPE_URL" class="member-name-link">ANT_FILE_TYPE_URL</a></code></div>
<div class="col-last even-row-color">
<div class="block">ant build file of type url
 Value: "url"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_HOME" class="member-name-link">ANT_HOME</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Property used to store the location of ant.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_JAVA_VERSION" class="member-name-link">ANT_JAVA_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">Property used to store the java version ant is running in.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_LIB" class="member-name-link">ANT_LIB</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Property used to store the location of the ant library (typically the ant.jar file.)
 Value: "ant.core.lib"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_SHELL_LAUNCHER_REF_ID" class="member-name-link">ANT_SHELL_LAUNCHER_REF_ID</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the project reference holding an instance of <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to use
 when executing commands with the help of an external script.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANT_VERSION" class="member-name-link">ANT_VERSION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Ant version property.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ANT_VM_LAUNCHER_REF_ID" class="member-name-link">ANT_VM_LAUNCHER_REF_ID</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the project reference holding an instance of <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to use
 when executing commands without the help of an external script.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ANTLIB_PREFIX" class="member-name-link">ANTLIB_PREFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">prefix for antlib URIs:
 "antlib:"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ATTRIBUTE_NAMESPACE" class="member-name-link">ATTRIBUTE_NAMESPACE</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the namespace "type".</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#AUTO_TMPDIR" class="member-name-link">AUTO_TMPDIR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Magic property that will be set to override java.io.tmpdir
 system property as the location for Ant's default temporary
 directory if a temp file is created and <a href="#TMPDIR"><code>TMPDIR</code></a> is not
 set.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#BUILD_JAVAC_SOURCE" class="member-name-link">BUILD_JAVAC_SOURCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">property that provides the default value for javac's and
 javadoc's source attribute.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#BUILD_JAVAC_TARGET" class="member-name-link">BUILD_JAVAC_TARGET</a></code></div>
<div class="col-last odd-row-color">
<div class="block">property that provides the default value for javac's target attribute.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#BUILD_SYSCLASSPATH" class="member-name-link">BUILD_SYSCLASSPATH</a></code></div>
<div class="col-last even-row-color">
<div class="block">System classpath policy.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#DISABLE_NASHORN_COMPAT" class="member-name-link">DISABLE_NASHORN_COMPAT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Magic property that can be used to disable Nashorn compatibility mode when using GraalVM JavaScript as script
 engine.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#HTTP_AGENT_PROPERTY" class="member-name-link">HTTP_AGENT_PROPERTY</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the property which can provide an override of the
 User-Agent used in &lt;get&gt; tasks.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PROJECT_BASEDIR" class="member-name-link">PROJECT_BASEDIR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">property name for basedir of the project.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROJECT_DEFAULT_TARGET" class="member-name-link">PROJECT_DEFAULT_TARGET</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the property holding the default target of the
 currently executing project, if one has been specified.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PROJECT_HELPER_CLASS" class="member-name-link">PROJECT_HELPER_CLASS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Name of JVM system property which provides the name of the ProjectHelper class to use.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROJECT_HELPER_SERVICE" class="member-name-link">PROJECT_HELPER_SERVICE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The service identifier in jars which provide ProjectHelper implementations.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PROJECT_INVOKED_TARGETS" class="member-name-link">PROJECT_INVOKED_TARGETS</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Name of the property holding a comma separated list of targets
 that have been invoked (from the command line).</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PROJECT_NAME" class="member-name-link">PROJECT_NAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the property holding the name of the currently
 executing project, if one has been specified.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REFID_CLASSPATH_LOADER_PREFIX" class="member-name-link">REFID_CLASSPATH_LOADER_PREFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Prefix used to store classloader references.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#REFID_CLASSPATH_REUSE_LOADER" class="member-name-link">REFID_CLASSPATH_REUSE_LOADER</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the magic property that controls classloader reuse.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REFID_LOCAL_PROPERTIES" class="member-name-link">REFID_LOCAL_PROPERTIES</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Reference used to store the local properties.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#REFID_PROJECT_HELPER" class="member-name-link">REFID_PROJECT_HELPER</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of ProjectHelper reference that we add to a project.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REFID_PROPERTY_HELPER" class="member-name-link">REFID_PROPERTY_HELPER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Reference used to store the property helper.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#REGEXP_IMPL" class="member-name-link">REGEXP_IMPL</a></code></div>
<div class="col-last even-row-color">
<div class="block">property for regular expression implementation.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#REPOSITORY_DIR_PROPERTY" class="member-name-link">REPOSITORY_DIR_PROPERTY</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Name of the property which can provide an override of the repository dir.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#REPOSITORY_URL_PROPERTY" class="member-name-link">REPOSITORY_URL_PROPERTY</a></code></div>
<div class="col-last even-row-color">
<div class="block">Name of the property which can provide an override of the repository URL.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SCRIPT_CACHE" class="member-name-link">SCRIPT_CACHE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The name of the script cache used by the script runner.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#SCRIPT_REPOSITORY" class="member-name-link">SCRIPT_REPOSITORY</a></code></div>
<div class="col-last even-row-color">
<div class="block">The name of the script repository used by the script repo task.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#SYSTEM_LOADER_REF" class="member-name-link">SYSTEM_LOADER_REF</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The name of the reference to the System Class Loader.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TASKDEF_PROPERTIES_RESOURCE" class="member-name-link">TASKDEF_PROPERTIES_RESOURCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">name of the resource that taskdefs are stored under.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TMPDIR" class="member-name-link">TMPDIR</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Magic property that can be set to override the java.io.tmpdir
 system property as the location for Ant's default temporary
 directory.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TSTAMP_NOW" class="member-name-link">TSTAMP_NOW</a></code></div>
<div class="col-last even-row-color">
<div class="block">Magic property that can be set to contain a value for tstamp's
 "now" in order to make builds that use the task create
 reproducible results.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#TSTAMP_NOW_ISO" class="member-name-link">TSTAMP_NOW_ISO</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Magic property that can be set to contain a value for tstamp's
 "now" in order to make builds that use the task create
 reproducible results.</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#TYPEDEFS_PROPERTIES_RESOURCE" class="member-name-link">TYPEDEFS_PROPERTIES_RESOURCE</a></code></div>
<div class="col-last even-row-color">
<div class="block">name of the resource that typedefs are stored under.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WARN_SECURITY_MANAGER_USAGE" class="member-name-link">WARN_SECURITY_MANAGER_USAGE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">When running on Java 18 or higher runtime, Ant will throw a <a href="BuildException.html" title="class in org.apache.tools.ant"><code>BuildException</code></a>
 if the <a href="types/Permissions.html" title="class in org.apache.tools.ant.types"><permissions></a> type is used.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ANTLIB_PREFIX">
<h3>ANTLIB_PREFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANTLIB_PREFIX</span></div>
<div class="block">prefix for antlib URIs:
 "antlib:"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANTLIB_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_VERSION">
<h3>ANT_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_VERSION</span></div>
<div class="block">Ant version property.
 Value: "ant.version"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BUILD_SYSCLASSPATH">
<h3>BUILD_SYSCLASSPATH</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">BUILD_SYSCLASSPATH</span></div>
<div class="block">System classpath policy.
 Value: "build.sysclasspath"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_SYSCLASSPATH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SCRIPT_REPOSITORY">
<h3>SCRIPT_REPOSITORY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SCRIPT_REPOSITORY</span></div>
<div class="block">The name of the script repository used by the script repo task.
 Value "org.apache.ant.scriptrepo"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.SCRIPT_REPOSITORY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SCRIPT_CACHE">
<h3>SCRIPT_CACHE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SCRIPT_CACHE</span></div>
<div class="block">The name of the script cache used by the script runner.
 Value "org.apache.ant.scriptcache"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.SCRIPT_CACHE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SYSTEM_LOADER_REF">
<h3>SYSTEM_LOADER_REF</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SYSTEM_LOADER_REF</span></div>
<div class="block">The name of the reference to the System Class Loader.
 Value "ant.coreLoader"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.SYSTEM_LOADER_REF">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REPOSITORY_DIR_PROPERTY">
<h3>REPOSITORY_DIR_PROPERTY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REPOSITORY_DIR_PROPERTY</span></div>
<div class="block">Name of the property which can provide an override of the repository dir.
 for the libraries task
 Value "ant.maven.repository.dir"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REPOSITORY_DIR_PROPERTY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REPOSITORY_URL_PROPERTY">
<h3>REPOSITORY_URL_PROPERTY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REPOSITORY_URL_PROPERTY</span></div>
<div class="block">Name of the property which can provide an override of the repository URL.
 for the libraries task
 Value "ant.maven.repository.url"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REPOSITORY_URL_PROPERTY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TASKDEF_PROPERTIES_RESOURCE">
<h3>TASKDEF_PROPERTIES_RESOURCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TASKDEF_PROPERTIES_RESOURCE</span></div>
<div class="block">name of the resource that taskdefs are stored under.
 Value: "/org/apache/tools/ant/taskdefs/defaults.properties"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TASKDEF_PROPERTIES_RESOURCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TYPEDEFS_PROPERTIES_RESOURCE">
<h3>TYPEDEFS_PROPERTIES_RESOURCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TYPEDEFS_PROPERTIES_RESOURCE</span></div>
<div class="block">name of the resource that typedefs are stored under.
 Value: "/org/apache/tools/ant/types/defaults.properties"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TYPEDEFS_PROPERTIES_RESOURCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_EXECUTOR_REFERENCE">
<h3>ANT_EXECUTOR_REFERENCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_EXECUTOR_REFERENCE</span></div>
<div class="block">Reference to the current Ant executor.
 Value: "ant.executor"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_EXECUTOR_REFERENCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_EXECUTOR_CLASSNAME">
<h3>ANT_EXECUTOR_CLASSNAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_EXECUTOR_CLASSNAME</span></div>
<div class="block">Property defining the classname of an executor.
 Value: "ant.executor.class"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_EXECUTOR_CLASSNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_BASEDIR">
<h3>PROJECT_BASEDIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_BASEDIR</span></div>
<div class="block">property name for basedir of the project.
 Value: "basedir"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_BASEDIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_FILE">
<h3>ANT_FILE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_FILE</span></div>
<div class="block">property for ant file name.
 Value: "ant.file"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_FILE_TYPE">
<h3>ANT_FILE_TYPE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_FILE_TYPE</span></div>
<div class="block">property for type of ant build file (either file or url)
 Value: "ant.file.type"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_FILE_TYPE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_FILE_TYPE_FILE">
<h3>ANT_FILE_TYPE_FILE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_FILE_TYPE_FILE</span></div>
<div class="block">ant build file of type file
 Value: "file"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_FILE_TYPE_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_FILE_TYPE_URL">
<h3>ANT_FILE_TYPE_URL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_FILE_TYPE_URL</span></div>
<div class="block">ant build file of type url
 Value: "url"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_FILE_TYPE_URL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_JAVA_VERSION">
<h3>ANT_JAVA_VERSION</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_JAVA_VERSION</span></div>
<div class="block">Property used to store the java version ant is running in.
 Value: "ant.java.version"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_JAVA_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_HOME">
<h3>ANT_HOME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_HOME</span></div>
<div class="block">Property used to store the location of ant.
 Value: "ant.home"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_HOME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_LIB">
<h3>ANT_LIB</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_LIB</span></div>
<div class="block">Property used to store the location of the ant library (typically the ant.jar file.)
 Value: "ant.core.lib"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_LIB">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REGEXP_IMPL">
<h3>REGEXP_IMPL</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REGEXP_IMPL</span></div>
<div class="block">property for regular expression implementation.
 Value: "ant.regexp.regexpimpl"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REGEXP_IMPL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BUILD_JAVAC_SOURCE">
<h3>BUILD_JAVAC_SOURCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">BUILD_JAVAC_SOURCE</span></div>
<div class="block">property that provides the default value for javac's and
 javadoc's source attribute.
 Value: "ant.build.javac.source"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_JAVAC_SOURCE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="BUILD_JAVAC_TARGET">
<h3>BUILD_JAVAC_TARGET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">BUILD_JAVAC_TARGET</span></div>
<div class="block">property that provides the default value for javac's target attribute.
 Value: "ant.build.javac.target"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_JAVAC_TARGET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REFID_CLASSPATH_REUSE_LOADER">
<h3>REFID_CLASSPATH_REUSE_LOADER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REFID_CLASSPATH_REUSE_LOADER</span></div>
<div class="block">Name of the magic property that controls classloader reuse.
 Value: "ant.reuse.loader"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_CLASSPATH_REUSE_LOADER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REFID_CLASSPATH_LOADER_PREFIX">
<h3>REFID_CLASSPATH_LOADER_PREFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REFID_CLASSPATH_LOADER_PREFIX</span></div>
<div class="block">Prefix used to store classloader references.
 Value: "ant.loader."</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_CLASSPATH_LOADER_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REFID_PROPERTY_HELPER">
<h3>REFID_PROPERTY_HELPER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REFID_PROPERTY_HELPER</span></div>
<div class="block">Reference used to store the property helper.
 Value: "ant.PropertyHelper"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_PROPERTY_HELPER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REFID_LOCAL_PROPERTIES">
<h3>REFID_LOCAL_PROPERTIES</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REFID_LOCAL_PROPERTIES</span></div>
<div class="block">Reference used to store the local properties.
 Value: "ant.LocalProperties"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_LOCAL_PROPERTIES">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_CORE_PACKAGE">
<h3>ANT_CORE_PACKAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_CORE_PACKAGE</span></div>
<div class="block">Name of Ant core package
 Value: "org.apache.tools.ant"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.9</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_CORE_PACKAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_HELPER_CLASS">
<h3>PROJECT_HELPER_CLASS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_HELPER_CLASS</span></div>
<div class="block">Name of JVM system property which provides the name of the ProjectHelper class to use.
 Value: "org.apache.tools.ant.ProjectHelper"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_HELPER_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_HELPER_SERVICE">
<h3>PROJECT_HELPER_SERVICE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_HELPER_SERVICE</span></div>
<div class="block">The service identifier in jars which provide ProjectHelper implementations.
 Value: "META-INF/services/org.apache.tools.ant.ProjectHelper"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_HELPER_SERVICE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="REFID_PROJECT_HELPER">
<h3>REFID_PROJECT_HELPER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">REFID_PROJECT_HELPER</span></div>
<div class="block">Name of ProjectHelper reference that we add to a project.
 Value: "ant.projectHelper"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_PROJECT_HELPER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_NAME">
<h3>PROJECT_NAME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_NAME</span></div>
<div class="block">Name of the property holding the name of the currently
 executing project, if one has been specified.

 Value: "ant.project.name"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_NAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_DEFAULT_TARGET">
<h3>PROJECT_DEFAULT_TARGET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_DEFAULT_TARGET</span></div>
<div class="block">Name of the property holding the default target of the
 currently executing project, if one has been specified.

 Value: "ant.project.default-target"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_DEFAULT_TARGET">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="PROJECT_INVOKED_TARGETS">
<h3>PROJECT_INVOKED_TARGETS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_INVOKED_TARGETS</span></div>
<div class="block">Name of the property holding a comma separated list of targets
 that have been invoked (from the command line).

 Value: "ant.project.invoked-targets"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_INVOKED_TARGETS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_SHELL_LAUNCHER_REF_ID">
<h3>ANT_SHELL_LAUNCHER_REF_ID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_SHELL_LAUNCHER_REF_ID</span></div>
<div class="block">Name of the project reference holding an instance of <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to use
 when executing commands with the help of an external script.

 <p>Alternatively this is the name of a system property holding
 the fully qualified class name of a <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a>.</p>

 Value: "ant.shellLauncher"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_SHELL_LAUNCHER_REF_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ANT_VM_LAUNCHER_REF_ID">
<h3>ANT_VM_LAUNCHER_REF_ID</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ANT_VM_LAUNCHER_REF_ID</span></div>
<div class="block">Name of the project reference holding an instance of <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a> to use
 when executing commands without the help of an external script.

 <p>Alternatively this is the name of a system property holding
 the fully qualified class name of a <a href="taskdefs/launcher/CommandLauncher.html" title="class in org.apache.tools.ant.taskdefs.launcher"><code>CommandLauncher</code></a>.</p>

 Value: "ant.vmLauncher"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_VM_LAUNCHER_REF_ID">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ATTRIBUTE_NAMESPACE">
<h3>ATTRIBUTE_NAMESPACE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ATTRIBUTE_NAMESPACE</span></div>
<div class="block">Name of the namespace "type".
 (Note: cannot be used as an element.)</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.9.1</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ATTRIBUTE_NAMESPACE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="HTTP_AGENT_PROPERTY">
<h3>HTTP_AGENT_PROPERTY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">HTTP_AGENT_PROPERTY</span></div>
<div class="block">Name of the property which can provide an override of the
 User-Agent used in &lt;get&gt; tasks.
 Value "ant.http.agent"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.HTTP_AGENT_PROPERTY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TSTAMP_NOW">
<h3>TSTAMP_NOW</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TSTAMP_NOW</span></div>
<div class="block">Magic property that can be set to contain a value for tstamp's
 "now" in order to make builds that use the task create
 reproducible results.

 <p>The value is expected to be a number representing the date
 as seconds since the epoch.</p>

 Value: "ant.tstamp.now"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TSTAMP_NOW">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TSTAMP_NOW_ISO">
<h3>TSTAMP_NOW_ISO</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TSTAMP_NOW_ISO</span></div>
<div class="block">Magic property that can be set to contain a value for tstamp's
 "now" in order to make builds that use the task create
 reproducible results.

 <p>The value is expected to be in ISO time format
 (<i>1972-04-17T08:07</i>)</p>

 Value: "ant.tstamp.now.iso"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TSTAMP_NOW_ISO">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="TMPDIR">
<h3>TMPDIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">TMPDIR</span></div>
<div class="block">Magic property that can be set to override the java.io.tmpdir
 system property as the location for Ant's default temporary
 directory.
 Value: "ant.tmpdir"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.8</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TMPDIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="AUTO_TMPDIR">
<h3>AUTO_TMPDIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">AUTO_TMPDIR</span></div>
<div class="block">Magic property that will be set to override java.io.tmpdir
 system property as the location for Ant's default temporary
 directory if a temp file is created and <a href="#TMPDIR"><code>TMPDIR</code></a> is not
 set.
 Value: "ant.auto.tmpdir"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.9</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.AUTO_TMPDIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DISABLE_NASHORN_COMPAT">
<h3>DISABLE_NASHORN_COMPAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DISABLE_NASHORN_COMPAT</span></div>
<div class="block">Magic property that can be used to disable Nashorn compatibility mode when using GraalVM JavaScript as script
 engine.

 <p>Set this to "true" if you want to disable Nashorn compatibility mode.</p>

 Value: "ant.disable.graal.nashorn.compat"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.9</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.DISABLE_NASHORN_COMPAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="WARN_SECURITY_MANAGER_USAGE">
<h3>WARN_SECURITY_MANAGER_USAGE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WARN_SECURITY_MANAGER_USAGE</span></div>
<div class="block">When running on Java 18 or higher runtime, Ant will throw a <a href="BuildException.html" title="class in org.apache.tools.ant"><code>BuildException</code></a>
 if the <a href="types/Permissions.html" title="class in org.apache.tools.ant.types"><permissions></a> type is used.
 Set this property to <code>true</code> to disable throwing an exception and instead just log a
 warning message.

 Value: "ant.securitymanager.usage.warn"</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.10.14</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#org.apache.tools.ant.MagicNames.WARN_SECURITY_MANAGER_USAGE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
