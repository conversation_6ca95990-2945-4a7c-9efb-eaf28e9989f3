

<%@ page language="java" import="java.sql.*" %>

<% 

    Connection con;

    PreparedStatement ps;
    ResultSet rs;
   
   String name=request.getParameter("name");

   String author=request.getParameter("author");

   String subject=request.getParameter("subject");
   
   String nob=request.getParameter("nob");
   
   String price=request.getParameter("price");   

   Class.forName("org.sqlite.JDBC");
 con=DriverManager.getConnection(
"jdbc:sqlite:kan.db");
 
 
 
  
  ps=con.prepareStatement("select * from books where name=?");
   ps.setString(1,name);
  rs=ps.executeQuery();
  if(rs.next())
  {
  out.println("Book name Already present");
  }
  else
  {
    ps=con.prepareStatement("insert into books(name,author,subject,nob,price) values(?,?,?,?,?)");
   
   ps.setString(1,name);

   ps.setString(2,author);

   ps.setString(3,subject);
   ps.setString(4,nob);
   ps.setString(5,price);
  
   ps.execute();
   out.println("Book Inserted Successfully");   
  }
   
   
   %>
  
<html>
       <body>
   <style>
   a {
  background-color: #04AA6D;
  color: white;
  padding: 14px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  opacity: 0.8;
}
</style>
   <br>
   </br>
   </br>
   <a href="welcome.jsp">Back To Home</a>
</body>
</html>
