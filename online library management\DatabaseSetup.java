import java.sql.*;

public class DatabaseSetup {
    public static void main(String[] args) {
        String url = "jdbc:sqlite:kan.db";
        
        try {
            // Load SQLite JDBC driver
            Class.forName("org.sqlite.JDBC");
            
            // Create connection
            Connection conn = DriverManager.getConnection(url);
            System.out.println("Connected to SQLite database successfully!");
            
            // Create Statement
            Statement stmt = conn.createStatement();
            
            // Create users table
            String createUsersTable = """
                CREATE TABLE IF NOT EXISTS users (
                    name TEXT,
                    gender TEXT,
                    address TEXT,
                    city TEXT,
                    mno TEXT,
                    emailid TEXT,
                    username TEXT PRIMARY KEY,
                    password TEXT
                )
            """;
            
            stmt.execute(createUsersTable);
            System.out.println("Users table created successfully!");
            
            // Create books table
            String createBooksTable = """
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT,
                    author TEXT,
                    subject TEXT,
                    nob INTEGER,
                    price REAL
                )
            """;
            
            stmt.execute(createBooksTable);
            System.out.println("Books table created successfully!");
            
            // Insert some sample data
            String insertUser = "INSERT OR IGNORE INTO users VALUES ('Admin User', 'Male', '123 Main St', 'City', '1234567890', '<EMAIL>', 'admin', 'admin123')";
            stmt.execute(insertUser);
            
            String insertBook = "INSERT INTO books (name, author, subject, nob, price) VALUES ('Sample Book', 'Sample Author', 'Computer Science', 5, 29.99)";
            stmt.execute(insertBook);
            
            System.out.println("Sample data inserted successfully!");
            
            // Close connections
            stmt.close();
            conn.close();
            
            System.out.println("Database setup completed successfully!");
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
