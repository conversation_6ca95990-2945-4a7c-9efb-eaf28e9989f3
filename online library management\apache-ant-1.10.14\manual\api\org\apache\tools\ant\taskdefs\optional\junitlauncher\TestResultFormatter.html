<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>TestResultFormatter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.junitlauncher, interface: TestResultFormatter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.junitlauncher</a></div>
<h1 title="Interface TestResultFormatter" class="title">Interface TestResultFormatter</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code>org.junit.platform.launcher.TestExecutionListener</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">TestResultFormatter</span><span class="extends-implements">
extends org.junit.platform.launcher.TestExecutionListener, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></span></div>
<div class="block">A <code>TestExecutionListener</code> which lets implementing classes
 format and write out the test execution results.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab5" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab5', 3)" class="table-tab">Default Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setContext(org.apache.tools.ant.taskdefs.optional.junitlauncher.TestExecutionContext)" class="member-name-link">setContext</a><wbr>(<a href="TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestExecutionContext</a>&nbsp;context)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">This method will be invoked by the <code>junitlauncher</code> and will be passed a
 <a href="TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestExecutionContext</code></a>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setDestination(java.io.OutputStream)" class="member-name-link">setDestination</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">This method will be invoked by the <code>junitlauncher</code> and will be passed the
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link"><code>OutputStream</code></a> to a file, to which the formatted result is expected to be written
 to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setUseLegacyReportingName(boolean)" class="member-name-link">setUseLegacyReportingName</a><wbr>(boolean&nbsp;useLegacyReportingName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">This method will be invoked by the <code>junitlauncher</code> to let the result formatter implementation
 know whether or not to use JUnit 4 style, legacy reporting names for test identifiers that get
 displayed in the test reports.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#sysErrAvailable(byte%5B%5D)" class="member-name-link">sysErrAvailable</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">This method will be invoked by the <code>junitlauncher</code>, <strong>regularly/multiple times</strong>,
 as and when any content is generated on the standard error stream during the test execution.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#sysOutAvailable(byte%5B%5D)" class="member-name-link">sysOutAvailable</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">This method will be invoked by the <code>junitlauncher</code>, <strong>regularly/multiple times</strong>,
 as and when any content is generated on the standard output stream during the test execution.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.io.Closeable">Methods inherited from interface&nbsp;java.io.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Closeable.html#close()" title="class or interface in java.io" class="external-link">close</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.junit.platform.launcher.TestExecutionListener">Methods inherited from interface&nbsp;org.junit.platform.launcher.TestExecutionListener</h3>
<code>dynamicTestRegistered, executionFinished, executionSkipped, executionStarted, reportingEntryPublished, testPlanExecutionFinished, testPlanExecutionStarted</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setDestination(java.io.OutputStream)">
<h3>setDestination</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setDestination</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</span></div>
<div class="block">This method will be invoked by the <code>junitlauncher</code> and will be passed the
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link"><code>OutputStream</code></a> to a file, to which the formatted result is expected to be written
 to.
 <p>
 This method will be called once, early on, during the initialization of this
 <a href="TestResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestResultFormatter</code></a>, typically before the test execution itself has started.
 </p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>os</code> - The output stream to which to write out the result</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setContext(org.apache.tools.ant.taskdefs.optional.junitlauncher.TestExecutionContext)">
<h3>setContext</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setContext</span><wbr><span class="parameters">(<a href="TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher">TestExecutionContext</a>&nbsp;context)</span></div>
<div class="block">This method will be invoked by the <code>junitlauncher</code> and will be passed a
 <a href="TestExecutionContext.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestExecutionContext</code></a>. This allows the <a href="TestResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestResultFormatter</code></a> to have access
 to any additional contextual information to use in the test reports.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>context</code> - The context of the execution of the test</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUseLegacyReportingName(boolean)">
<h3>setUseLegacyReportingName</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setUseLegacyReportingName</span><wbr><span class="parameters">(boolean&nbsp;useLegacyReportingName)</span></div>
<div class="block">This method will be invoked by the <code>junitlauncher</code> to let the result formatter implementation
 know whether or not to use JUnit 4 style, legacy reporting names for test identifiers that get
 displayed in the test reports. Result formatter implementations are allowed to default to a specific
 reporting style for test identifiers, if this method isn't invoked.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>useLegacyReportingName</code> - <code>true</code> if legacy reporting name is to be used, <code>false</code>
                               otherwise.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.10</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sysOutAvailable(byte[])">
<h3>sysOutAvailable</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sysOutAvailable</span><wbr><span class="parameters">(byte[]&nbsp;data)</span></div>
<div class="block">This method will be invoked by the <code>junitlauncher</code>, <strong>regularly/multiple times</strong>,
 as and when any content is generated on the standard output stream during the test execution.
 This method will be only be called if the <code>sendSysOut</code> attribute of the <code>listener</code>,
 to which this <a href="TestResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestResultFormatter</code></a> is configured for, is enabled</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - The content generated on standard output stream</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sysErrAvailable(byte[])">
<h3>sysErrAvailable</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sysErrAvailable</span><wbr><span class="parameters">(byte[]&nbsp;data)</span></div>
<div class="block">This method will be invoked by the <code>junitlauncher</code>, <strong>regularly/multiple times</strong>,
 as and when any content is generated on the standard error stream during the test execution.
 This method will be only be called if the <code>sendSysErr</code> attribute of the <code>listener</code>,
 to which this <a href="TestResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junitlauncher"><code>TestResultFormatter</code></a> is configured for, is enabled</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - The content generated on standard error stream</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
