<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>Draw (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.optional.imageio, class: Draw">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.optional.imageio</a></div>
<h1 title="Class Draw" class="title">Class Draw</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="ImageOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">org.apache.tools.ant.types.optional.imageio.ImageOperation</a>
<div class="inheritance"><a href="TransformOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">org.apache.tools.ant.types.optional.imageio.TransformOperation</a>
<div class="inheritance">org.apache.tools.ant.types.optional.imageio.Draw</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Draw</span>
<span class="extends-implements">extends <a href="TransformOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">TransformOperation</a></span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../taskdefs/optional/image/ImageIOTask.html" title="class in org.apache.tools.ant.taskdefs.optional.image"><code>ImageIOTask</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.optional.imageio.ImageOperation">Fields inherited from class&nbsp;org.apache.tools.ant.types.optional.imageio.<a href="ImageOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">ImageOperation</a></h3>
<code><a href="ImageOperation.html#instructions">instructions</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checked">checked</a>, <a href="../../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Draw</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addArc(org.apache.tools.ant.types.optional.imageio.Arc)" class="member-name-link">addArc</a><wbr>(<a href="Arc.html" title="class in org.apache.tools.ant.types.optional.imageio">Arc</a>&nbsp;arc)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an arc.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addEllipse(org.apache.tools.ant.types.optional.imageio.Ellipse)" class="member-name-link">addEllipse</a><wbr>(<a href="Ellipse.html" title="class in org.apache.tools.ant.types.optional.imageio">Ellipse</a>&nbsp;elip)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an ellipse.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addRectangle(org.apache.tools.ant.types.optional.imageio.Rectangle)" class="member-name-link">addRectangle</a><wbr>(<a href="Rectangle.html" title="class in org.apache.tools.ant.types.optional.imageio">Rectangle</a>&nbsp;rect)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a rectangle to the operation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addText(org.apache.tools.ant.types.optional.imageio.Text)" class="member-name-link">addText</a><wbr>(<a href="Text.html" title="class in org.apache.tools.ant.types.optional.imageio">Text</a>&nbsp;text)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add text to the operation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executeTransformOperation(java.awt.image.BufferedImage)" class="member-name-link">executeTransformOperation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a>&nbsp;bi)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs the transformations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setXloc(int)" class="member-name-link">setXloc</a><wbr>(int&nbsp;x)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the X location.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setYloc(int)" class="member-name-link">setYloc</a><wbr>(int&nbsp;y)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Y location.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.optional.imageio.ImageOperation">Methods inherited from class&nbsp;org.apache.tools.ant.types.optional.imageio.<a href="ImageOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">ImageOperation</a></h3>
<code><a href="ImageOperation.html#addDraw(org.apache.tools.ant.types.optional.imageio.Draw)">addDraw</a>, <a href="ImageOperation.html#addRotate(org.apache.tools.ant.types.optional.imageio.Rotate)">addRotate</a>, <a href="ImageOperation.html#addScale(org.apache.tools.ant.types.optional.imageio.Scale)">addScale</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../../DataType.html#circularReference()">circularReference</a>, <a href="../../DataType.html#clone()">clone</a>, <a href="../../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../../DataType.html#getRefid()">getRefid</a>, <a href="../../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../../DataType.html#isChecked()">isChecked</a>, <a href="../../DataType.html#isReference()">isReference</a>, <a href="../../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../../DataType.html#tooManyAttributes()">tooManyAttributes</a>, <a href="../../DataType.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Draw</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Draw</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setXloc(int)">
<h3>setXloc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setXloc</span><wbr><span class="parameters">(int&nbsp;x)</span></div>
<div class="block">Set the X location.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>x</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setYloc(int)">
<h3>setYloc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setYloc</span><wbr><span class="parameters">(int&nbsp;y)</span></div>
<div class="block">Set the Y location.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>y</code> - the value to use.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addText(org.apache.tools.ant.types.optional.imageio.Text)">
<h3>addText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="Text.html" title="class in org.apache.tools.ant.types.optional.imageio">Text</a>&nbsp;text)</span></div>
<div class="block">Add text to the operation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - the text to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addRectangle(org.apache.tools.ant.types.optional.imageio.Rectangle)">
<h3>addRectangle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addRectangle</span><wbr><span class="parameters">(<a href="Rectangle.html" title="class in org.apache.tools.ant.types.optional.imageio">Rectangle</a>&nbsp;rect)</span></div>
<div class="block">Add a rectangle to the operation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rect</code> - the rectangle to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addEllipse(org.apache.tools.ant.types.optional.imageio.Ellipse)">
<h3>addEllipse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addEllipse</span><wbr><span class="parameters">(<a href="Ellipse.html" title="class in org.apache.tools.ant.types.optional.imageio">Ellipse</a>&nbsp;elip)</span></div>
<div class="block">Add an ellipse.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>elip</code> - the ellipse to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addArc(org.apache.tools.ant.types.optional.imageio.Arc)">
<h3>addArc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addArc</span><wbr><span class="parameters">(<a href="Arc.html" title="class in org.apache.tools.ant.types.optional.imageio">Arc</a>&nbsp;arc)</span></div>
<div class="block">Add an arc.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>arc</code> - the arc to add.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="executeTransformOperation(java.awt.image.BufferedImage)">
<h3>executeTransformOperation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a></span>&nbsp;<span class="element-name">executeTransformOperation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.desktop/java/awt/image/BufferedImage.html" title="class or interface in java.awt.image" class="external-link">BufferedImage</a>&nbsp;bi)</span></div>
<div class="block">Performs the transformations..</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="TransformOperation.html#executeTransformOperation(java.awt.image.BufferedImage)">executeTransformOperation</a></code>&nbsp;in class&nbsp;<code><a href="TransformOperation.html" title="class in org.apache.tools.ant.types.optional.imageio">TransformOperation</a></code></dd>
<dt>Parameters:</dt>
<dd><code>bi</code> - The image to perform the transformation on.</dd>
<dt>Returns:</dt>
<dd>the transformed image.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
