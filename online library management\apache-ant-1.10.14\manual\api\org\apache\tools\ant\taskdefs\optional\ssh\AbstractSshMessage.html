<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>AbstractSshMessage (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ssh, class: AbstractSshMessage">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ssh</a></div>
<h1 title="Class AbstractSshMessage" class="title">Class AbstractSshMessage</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ssh.AbstractSshMessage</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ScpFromMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpFromMessage</a></code>, <code><a href="ScpToMessage.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">ScpToMessage</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">AbstractSshMessage</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Abstract class for ssh upload and download</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(boolean,boolean,com.jcraft.jsch.Session)" class="member-name-link">AbstractSshMessage</a><wbr>(boolean&nbsp;verbose,
 boolean&nbsp;compressed,
 com.jcraft.jsch.Session&nbsp;session)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for AbstractSshMessage</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(boolean,com.jcraft.jsch.Session)" class="member-name-link">AbstractSshMessage</a><wbr>(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructor for AbstractSshMessage</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(com.jcraft.jsch.Session)" class="member-name-link">AbstractSshMessage</a><wbr>(com.jcraft.jsch.Session&nbsp;session)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for AbstractSshMessage</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>abstract void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Carry out the transfer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompressed()" class="member-name-link">getCompressed</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is the compressed attribute set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected com.jcraft.jsch.SftpProgressMonitor</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProgressMonitor()" class="member-name-link">getProgressMonitor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the progress monitor.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVerbose()" class="member-name-link">getVerbose</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is the verbose attribute set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#log(java.lang.String)" class="member-name-link">log</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log a message to the log listener.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logStats(long,long,long)" class="member-name-link">logStats</a><wbr>(long&nbsp;timeStarted,
 long&nbsp;timeEnded,
 long&nbsp;totalLength)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Log transfer stats to the log listener.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected com.jcraft.jsch.Channel</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#openExecChannel(java.lang.String)" class="member-name-link">openExecChannel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Open an ssh channel.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected com.jcraft.jsch.ChannelSftp</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#openSftpChannel()" class="member-name-link">openSftpChannel</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Open an ssh sftp channel.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sendAck(java.io.OutputStream)" class="member-name-link">sendAck</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Send an ack.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogListener(org.apache.tools.ant.taskdefs.optional.ssh.LogListener)" class="member-name-link">setLogListener</a><wbr>(<a href="LogListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a>&nbsp;aListener)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a log listener.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#trackProgress(long,long,int)" class="member-name-link">trackProgress</a><wbr>(long&nbsp;filesize,
 long&nbsp;totalLength,
 int&nbsp;percentTransmitted)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Track progress every 10% if 100kb &lt; filesize &lt; 1Mb.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitForAck(java.io.InputStream)" class="member-name-link">waitForAck</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Reads the response, throws a BuildException if the response
 indicates an error.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(com.jcraft.jsch.Session)">
<h3>AbstractSshMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AbstractSshMessage</span><wbr><span class="parameters">(com.jcraft.jsch.Session&nbsp;session)</span></div>
<div class="block">Constructor for AbstractSshMessage</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>session</code> - the ssh session to use</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(boolean,com.jcraft.jsch.Session)">
<h3>AbstractSshMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AbstractSshMessage</span><wbr><span class="parameters">(boolean&nbsp;verbose,
 com.jcraft.jsch.Session&nbsp;session)</span></div>
<div class="block">Constructor for AbstractSshMessage</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - if true do verbose logging</dd>
<dd><code>session</code> - the ssh session to use</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(boolean,boolean,com.jcraft.jsch.Session)">
<h3>AbstractSshMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AbstractSshMessage</span><wbr><span class="parameters">(boolean&nbsp;verbose,
 boolean&nbsp;compressed,
 com.jcraft.jsch.Session&nbsp;session)</span></div>
<div class="block">Constructor for AbstractSshMessage</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - if true do verbose logging</dd>
<dd><code>compressed</code> - if true use compression</dd>
<dd><code>session</code> - the ssh session to use</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="openExecChannel(java.lang.String)">
<h3>openExecChannel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">com.jcraft.jsch.Channel</span>&nbsp;<span class="element-name">openExecChannel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command)</span>
                                           throws <span class="exceptions">com.jcraft.jsch.JSchException</span></div>
<div class="block">Open an ssh channel.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>command</code> - the command to use</dd>
<dt>Returns:</dt>
<dd>the channel</dd>
<dt>Throws:</dt>
<dd><code>com.jcraft.jsch.JSchException</code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="openSftpChannel()">
<h3>openSftpChannel</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">com.jcraft.jsch.ChannelSftp</span>&nbsp;<span class="element-name">openSftpChannel</span>()
                                               throws <span class="exceptions">com.jcraft.jsch.JSchException</span></div>
<div class="block">Open an ssh sftp channel.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the channel</dd>
<dt>Throws:</dt>
<dd><code>com.jcraft.jsch.JSchException</code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sendAck(java.io.OutputStream)">
<h3>sendAck</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sendAck</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;out)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Send an ack.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - the output stream to use</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitForAck(java.io.InputStream)">
<h3>waitForAck</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitForAck</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;in)</span>
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Reads the response, throws a BuildException if the response
 indicates an error.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>in</code> - the input stream to use</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O error</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on other errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public abstract</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
com.jcraft.jsch.JSchException</span></div>
<div class="block">Carry out the transfer.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - on I/O errors</dd>
<dd><code>com.jcraft.jsch.JSchException</code> - on ssh errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLogListener(org.apache.tools.ant.taskdefs.optional.ssh.LogListener)">
<h3>setLogListener</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogListener</span><wbr><span class="parameters">(<a href="LogListener.html" title="interface in org.apache.tools.ant.taskdefs.optional.ssh">LogListener</a>&nbsp;aListener)</span></div>
<div class="block">Set a log listener.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aListener</code> - the log listener</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="log(java.lang.String)">
<h3>log</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">log</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">Log a message to the log listener.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - the message to log</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logStats(long,long,long)">
<h3>logStats</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logStats</span><wbr><span class="parameters">(long&nbsp;timeStarted,
 long&nbsp;timeEnded,
 long&nbsp;totalLength)</span></div>
<div class="block">Log transfer stats to the log listener.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timeStarted</code> - the time started</dd>
<dd><code>timeEnded</code> - the finishing time</dd>
<dd><code>totalLength</code> - the total length</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getVerbose()">
<h3>getVerbose</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getVerbose</span>()</div>
<div class="block">Is the verbose attribute set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the verbose attribute is set</dd>
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompressed()">
<h3>getCompressed</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getCompressed</span>()</div>
<div class="block">Is the compressed attribute set.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the compressed attribute is set</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="trackProgress(long,long,int)">
<h3>trackProgress</h3>
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">trackProgress</span><wbr><span class="parameters">(long&nbsp;filesize,
 long&nbsp;totalLength,
 int&nbsp;percentTransmitted)</span></div>
<div class="block">Track progress every 10% if 100kb &lt; filesize &lt; 1Mb. For larger
 files track progress for every percent transmitted.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filesize</code> - the size of the file been transmitted</dd>
<dd><code>totalLength</code> - the total transmission size</dd>
<dd><code>percentTransmitted</code> - the current percent transmitted</dd>
<dt>Returns:</dt>
<dd>the percent that the file is of the total</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getProgressMonitor()">
<h3>getProgressMonitor</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">com.jcraft.jsch.SftpProgressMonitor</span>&nbsp;<span class="element-name">getProgressMonitor</span>()</div>
<div class="block">Get the progress monitor.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the progress monitor.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
