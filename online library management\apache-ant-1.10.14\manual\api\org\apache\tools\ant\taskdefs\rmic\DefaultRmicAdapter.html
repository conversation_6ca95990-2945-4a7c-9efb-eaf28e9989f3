<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>DefaultRmicAdapter (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.rmic, class: DefaultRmicAdapter">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.rmic</a></div>
<h1 title="Class DefaultRmicAdapter" class="title">Class DefaultRmicAdapter</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="ForkingSunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">ForkingSunRmic</a></code>, <code><a href="KaffeRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">KaffeRmic</a></code>, <code><a href="SunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">SunRmic</a></code>, <code><a href="WLRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">WLRmic</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">DefaultRmicAdapter</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></span></div>
<div class="block">This is the default implementation for the RmicAdapter interface.
 Currently, this is a cut-and-paste of the original rmic task and
 DefaultCompilerAdapter.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.4</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#RMI_SKEL_SUFFIX" class="member-name-link">RMI_SKEL_SUFFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">suffix denoting a skel file: "_Skel"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#RMI_STUB_SUFFIX" class="member-name-link">RMI_STUB_SUFFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">suffix denoting a stub file: "_Stub"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#RMI_TIE_SUFFIX" class="member-name-link">RMI_TIE_SUFFIX</a></code></div>
<div class="col-last even-row-color">
<div class="block">suffix denoting a tie file: "_Tie"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#STUB_1_1" class="member-name-link">STUB_1_1</a></code></div>
<div class="col-last odd-row-color">
<div class="block">arg for 1.1: "-v1.1"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#STUB_1_2" class="member-name-link">STUB_1_2</a></code></div>
<div class="col-last even-row-color">
<div class="block">arg for 1.2: "-v1.2"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#STUB_COMPAT" class="member-name-link">STUB_COMPAT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">arg for compat: "-vcompat"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#STUB_OPTION_1_1" class="member-name-link">STUB_OPTION_1_1</a></code></div>
<div class="col-last even-row-color">
<div class="block">option for stub 1.1 in the rmic task: "1.1"</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#STUB_OPTION_1_2" class="member-name-link">STUB_OPTION_1_2</a></code></div>
<div class="col-last odd-row-color">
<div class="block">option for stub 1.2 in the rmic task: "1.2"</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#STUB_OPTION_COMPAT" class="member-name-link">STUB_OPTION_COMPAT</a></code></div>
<div class="col-last even-row-color">
<div class="block">option for stub compat in the rmic task: "compat"</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">DefaultRmicAdapter</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addStubVersionOptions()" class="member-name-link">addStubVersionOptions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This is an override point; get the stub version off the rmic command and
 translate that into a compiler-specific argument</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#areIiopAndIdlSupported()" class="member-name-link">areIiopAndIdlSupported</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether the iiop and idl switches are supported.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#filterJvmCompilerArgs(java.lang.String%5B%5D)" class="member-name-link">filterJvmCompilerArgs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Strip out all -J args from the command list.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the CLASSPATH this rmic process will use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompileClasspath()" class="member-name-link">getCompileClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Builds the compilation classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMapper()" class="member-name-link">getMapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This implementation returns a mapper that may return up to two
 file names.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRmic()" class="member-name-link">getRmic</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Rmic attributes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSkelClassSuffix()" class="member-name-link">getSkelClassSuffix</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the skeleton class suffix</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStubClassSuffix()" class="member-name-link">getStubClassSuffix</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the stub class suffix</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTieClassSuffix()" class="member-name-link">getTieClassSuffix</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the tie class suffix</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)" class="member-name-link">logAndAddFilesToCompile</a><wbr>(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#preprocessCompilerArgs(java.lang.String%5B%5D)" class="member-name-link">preprocessCompilerArgs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Preprocess the compiler arguments in any way you see fit.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRmic(org.apache.tools.ant.taskdefs.Rmic)" class="member-name-link">setRmic</a><wbr>(<a href="../Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a>&nbsp;attributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets Rmic attributes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupRmicCommand()" class="member-name-link">setupRmicCommand</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setup rmic argument for rmic.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setupRmicCommand(java.lang.String%5B%5D)" class="member-name-link">setupRmicCommand</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;options)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setup rmic argument for rmic.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.rmic.RmicAdapter">Methods inherited from interface&nbsp;org.apache.tools.ant.taskdefs.rmic.<a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></h3>
<code><a href="RmicAdapter.html#execute()">execute</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="RMI_STUB_SUFFIX">
<h3>RMI_STUB_SUFFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">RMI_STUB_SUFFIX</span></div>
<div class="block">suffix denoting a stub file: "_Stub"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_STUB_SUFFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RMI_SKEL_SUFFIX">
<h3>RMI_SKEL_SUFFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">RMI_SKEL_SUFFIX</span></div>
<div class="block">suffix denoting a skel file: "_Skel"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_SKEL_SUFFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RMI_TIE_SUFFIX">
<h3>RMI_TIE_SUFFIX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">RMI_TIE_SUFFIX</span></div>
<div class="block">suffix denoting a tie file: "_Tie"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_TIE_SUFFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_COMPAT">
<h3>STUB_COMPAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_COMPAT</span></div>
<div class="block">arg for compat: "-vcompat"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_COMPAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_1_1">
<h3>STUB_1_1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_1_1</span></div>
<div class="block">arg for 1.1: "-v1.1"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_1_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_1_2">
<h3>STUB_1_2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_1_2</span></div>
<div class="block">arg for 1.2: "-v1.2"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_1_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_OPTION_1_1">
<h3>STUB_OPTION_1_1</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_OPTION_1_1</span></div>
<div class="block">option for stub 1.1 in the rmic task: "1.1"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_OPTION_1_1">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_OPTION_1_2">
<h3>STUB_OPTION_1_2</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_OPTION_1_2</span></div>
<div class="block">option for stub 1.2 in the rmic task: "1.2"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_OPTION_1_2">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STUB_OPTION_COMPAT">
<h3>STUB_OPTION_COMPAT</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STUB_OPTION_COMPAT</span></div>
<div class="block">option for stub compat in the rmic task: "compat"</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_OPTION_COMPAT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>DefaultRmicAdapter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DefaultRmicAdapter</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setRmic(org.apache.tools.ant.taskdefs.Rmic)">
<h3>setRmic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRmic</span><wbr><span class="parameters">(<a href="../Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a>&nbsp;attributes)</span></div>
<div class="block">Sets Rmic attributes</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="RmicAdapter.html#setRmic(org.apache.tools.ant.taskdefs.Rmic)">setRmic</a></code>&nbsp;in interface&nbsp;<code><a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></code></dd>
<dt>Parameters:</dt>
<dd><code>attributes</code> - the rmic attributes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRmic()">
<h3>getRmic</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a></span>&nbsp;<span class="element-name">getRmic</span>()</div>
<div class="block">Get the Rmic attributes</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the attributes as a Rmic taskdef</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStubClassSuffix()">
<h3>getStubClassSuffix</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStubClassSuffix</span>()</div>
<div class="block">Gets the stub class suffix</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the stub suffix &quot;_Stub&quot;</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSkelClassSuffix()">
<h3>getSkelClassSuffix</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSkelClassSuffix</span>()</div>
<div class="block">Gets the skeleton class suffix</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the skeleton suffix &quot;_Skel&quot;</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTieClassSuffix()">
<h3>getTieClassSuffix</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTieClassSuffix</span>()</div>
<div class="block">Gets the tie class suffix</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tie suffix &quot;_Tie&quot;</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMapper()">
<h3>getMapper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a></span>&nbsp;<span class="element-name">getMapper</span>()</div>
<div class="block">This implementation returns a mapper that may return up to two
 file names.

 <ul>
   <li>for JRMP it will return *_getStubClassSuffix (and
   *_getSkelClassSuffix if JDK 1.1 is used)</li>

   <li>for IDL it will return a random name, causing &lt;rmic&gt; to
     always recompile.</li>

   <li>for IIOP it will return _*_getStubClassSuffix for
   interfaces and _*_getStubClassSuffix for non-interfaces (and
   determine the interface and create _*_Stub from that).</li>
 </ul></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="RmicAdapter.html#getMapper()">getMapper</a></code>&nbsp;in interface&nbsp;<code><a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>a <code>FileNameMapper</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">Gets the CLASSPATH this rmic process will use.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="RmicAdapter.html#getClasspath()">getClasspath</a></code>&nbsp;in interface&nbsp;<code><a href="RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</a></code></dd>
<dt>Returns:</dt>
<dd>the classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCompileClasspath()">
<h3>getCompileClasspath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getCompileClasspath</span>()</div>
<div class="block">Builds the compilation classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the classpath</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="areIiopAndIdlSupported()">
<h3>areIiopAndIdlSupported</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">areIiopAndIdlSupported</span>()</div>
<div class="block">Whether the iiop and idl switches are supported.

 <p>This implementation returns false if running on Java 11
 onwards and true otherwise.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the iiop and idl switches are supported</dd>
<dt>Since:</dt>
<dd>Ant 1.10.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupRmicCommand()">
<h3>setupRmicCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupRmicCommand</span>()</div>
<div class="block">Setup rmic argument for rmic.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setupRmicCommand(java.lang.String[])">
<h3>setupRmicCommand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a></span>&nbsp;<span class="element-name">setupRmicCommand</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;options)</span></div>
<div class="block">Setup rmic argument for rmic.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>options</code> - additional parameters needed by a specific
                implementation.</dd>
<dt>Returns:</dt>
<dd>the command line</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addStubVersionOptions()">
<h3>addStubVersionOptions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">addStubVersionOptions</span>()</div>
<div class="block">This is an override point; get the stub version off the rmic command and
 translate that into a compiler-specific argument</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a string to use for the stub version; can be null</dd>
<dt>Since:</dt>
<dd>Ant1.7.1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="preprocessCompilerArgs(java.lang.String[])">
<h3>preprocessCompilerArgs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">preprocessCompilerArgs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</span></div>
<div class="block">Preprocess the compiler arguments in any way you see fit.
 This is to allow compiler adapters to validate or filter the arguments.
 The base implementation returns the original compiler arguments unchanged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compilerArgs</code> - the original compiler arguments</dd>
<dt>Returns:</dt>
<dd>the filtered set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="filterJvmCompilerArgs(java.lang.String[])">
<h3>filterJvmCompilerArgs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">filterJvmCompilerArgs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;compilerArgs)</span></div>
<div class="block">Strip out all -J args from the command list. Invoke this from
 <a href="#preprocessCompilerArgs(java.lang.String%5B%5D)"><code>preprocessCompilerArgs(String[])</code></a> if you have a non-forking
 compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compilerArgs</code> - the original compiler arguments</dd>
<dt>Returns:</dt>
<dd>the filtered set.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">
<h3>logAndAddFilesToCompile</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logAndAddFilesToCompile</span><wbr><span class="parameters">(<a href="../../types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</a>&nbsp;cmd)</span></div>
<div class="block">Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cmd</code> - the commandline args</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
