<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>FixCRLF (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: FixCRLF">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class FixCRLF" class="title">Class FixCRLF</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.FixCRLF</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FixCRLF</span>
<span class="extends-implements">extends <a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a>
implements <a href="../filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></span></div>
<div class="block">Converts text source files to local OS formatting conventions, as
 well as repair text files damaged by misconfigured or misguided editors or
 file transfer programs.
 <p>
 This task can take the following arguments:
 <ul>
 <li>srcdir
 <li>destdir
 <li>include
 <li>exclude
 <li>cr
 <li>eol
 <li>tab
 <li>eof
 <li>encoding
 <li>targetencoding
 </ul>
 Of these arguments, only <b>sourcedir</b> is required.
 <p>
 When this task executes, it will scan the srcdir based on the include
 and exclude properties.
 <p>
 This version generalises the handling of EOL characters, and allows
 for CR-only line endings (the standard on Mac systems prior to OS X).
 Tab handling has also been generalised to accommodate any tabwidth
 from 2 to 80, inclusive.  Importantly, it will leave untouched any
 literal TAB characters embedded within string or character constants.
 <p>
 <em>Warning:</em> do not run on binary files.
 <em>Caution:</em> run with care on carefully formatted files.
 This may sound obvious, but if you don't specify asis, presume that
 your files are going to be modified.  If "tabs" is "add" or "remove",
 whitespace characters may be added or removed as necessary.  Similarly,
 for CR's - in fact "eol"="crlf" or cr="add" can result in cr
 characters being removed in one special case accommodated, i.e.,
 CRCRLF is regarded as a single EOL to handle cases where other
 programs have converted CRLF into CRCRLF.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FixCRLF.AddAsisRemove.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerated attribute with the values "asis", "add" and "remove".</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="FixCRLF.CrLf.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enumerated attribute with the values "asis", "cr", "lf", "crlf", "mac", "unix" and "dos.</div>
</div>
<div class="col-first even-row-color"><code>protected class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="FixCRLF.OneLiner.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">FixCRLF.OneLiner</a></code></div>
<div class="col-last even-row-color">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.7.0.</div>
</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ERROR_FILE_AND_SRCDIR" class="member-name-link">ERROR_FILE_AND_SRCDIR</a></code></div>
<div class="col-last even-row-color">
<div class="block">error string for using srcdir and file</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">FixCRLF</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#chain(java.io.Reader)" class="member-name-link">chain</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Chain this task as a reader.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setCr(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)" class="member-name-link">setCr</a><wbr>(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x.</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination where the fixed files should be placed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the encoding Ant expects the files to be
 in--defaults to the platforms default encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEof(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)" class="member-name-link">setEof</a><wbr>(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how DOS EOF (control-z) characters are to be handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEol(org.apache.tools.ant.taskdefs.FixCRLF.CrLf)" class="member-name-link">setEol</a><wbr>(<a href="FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a>&nbsp;attr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how EndOfLine characters are to be handled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFile(java.io.File)" class="member-name-link">setFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a single file to convert.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFixlast(boolean)" class="member-name-link">setFixlast</a><wbr>(boolean&nbsp;fixlast)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify whether a missing EOL will be added
 to the final line of a file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJavafiles(boolean)" class="member-name-link">setJavafiles</a><wbr>(boolean&nbsp;javafiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set to true if modifying Java source files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputEncoding(java.lang.String)" class="member-name-link">setOutputEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specifies the encoding that the files are
 to be written in--same as input encoding by default.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPreserveLastModified(boolean)" class="member-name-link">setPreserveLastModified</a><wbr>(boolean&nbsp;preserve)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to preserve the last modified time as the original files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcdir(java.io.File)" class="member-name-link">setSrcdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source dir to find the source text files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTab(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)" class="member-name-link">setTab</a><wbr>(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify how tab characters are to be handled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTablength(int)" class="member-name-link">setTablength</a><wbr>(int&nbsp;tlength)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Specify tab length in characters.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ERROR_FILE_AND_SRCDIR">
<h3>ERROR_FILE_AND_SRCDIR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ERROR_FILE_AND_SRCDIR</span></div>
<div class="block">error string for using srcdir and file</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.FixCRLF.ERROR_FILE_AND_SRCDIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>FixCRLF</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">FixCRLF</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="chain(java.io.Reader)">
<h3>chain</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a></span>&nbsp;<span class="element-name">chain</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/Reader.html" title="class or interface in java.io" class="external-link">Reader</a>&nbsp;rdr)</span></div>
<div class="block">Chain this task as a reader.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../filters/ChainableReader.html#chain(java.io.Reader)">chain</a></code>&nbsp;in interface&nbsp;<code><a href="../filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>rdr</code> - Reader to chain.</dd>
<dt>Returns:</dt>
<dd>a Reader.</dd>
<dt>Since:</dt>
<dd>Ant 1.7?</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSrcdir(java.io.File)">
<h3>setSrcdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Set the source dir to find the source text files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</span></div>
<div class="block">Set the destination where the fixed files should be placed.
 Default is to replace the original file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destDir</code> - the destination directory.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJavafiles(boolean)">
<h3>setJavafiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJavafiles</span><wbr><span class="parameters">(boolean&nbsp;javafiles)</span></div>
<div class="block">Set to true if modifying Java source files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>javafiles</code> - whether modifying Java files.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFile(java.io.File)">
<h3>setFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Set a single file to convert.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to convert.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEol(org.apache.tools.ant.taskdefs.FixCRLF.CrLf)">
<h3>setEol</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEol</span><wbr><span class="parameters">(<a href="FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</a>&nbsp;attr)</span></div>
<div class="block">Specify how EndOfLine characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
 <ul>
 <li>asis: leave line endings alone
 <li>cr: convert line endings to CR
 <li>lf: convert line endings to LF
 <li>crlf: convert line endings to CRLF
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCr(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)">
<h3>setCr</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCr</span><wbr><span class="parameters">(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">since 1.4.x.
             Use <a href="#setEol(org.apache.tools.ant.taskdefs.FixCRLF.CrLf)"><code>setEol</code></a> instead.</div>
</div>
<div class="block">Specify how carriage return (CR) characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
 <ul>
 <li>add: ensure that there is a CR before every LF
 <li>asis: leave CR characters alone
 <li>remove: remove all CR characters
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTab(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)">
<h3>setTab</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTab</span><wbr><span class="parameters">(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</span></div>
<div class="block">Specify how tab characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
 <ul>
 <li>add: convert sequences of spaces which span a tab stop to tabs
 <li>asis: leave tab and space characters alone
 <li>remove: convert tabs to spaces
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTablength(int)">
<h3>setTablength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTablength</span><wbr><span class="parameters">(int&nbsp;tlength)</span>
                  throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Specify tab length in characters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tlength</code> - specify the length of tab in spaces.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEof(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)">
<h3>setEof</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEof</span><wbr><span class="parameters">(<a href="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</a>&nbsp;attr)</span></div>
<div class="block">Specify how DOS EOF (control-z) characters are to be handled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - valid values:
 <ul>
 <li>add: ensure that there is an eof at the end of the file
 <li>asis: leave eof characters alone
 <li>remove: remove any eof character found at the end
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Specifies the encoding Ant expects the files to be
 in--defaults to the platforms default encoding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - String encoding name.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputEncoding(java.lang.String)">
<h3>setOutputEncoding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</span></div>
<div class="block">Specifies the encoding that the files are
 to be written in--same as input encoding by default.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputEncoding</code> - String outputEncoding name.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFixlast(boolean)">
<h3>setFixlast</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFixlast</span><wbr><span class="parameters">(boolean&nbsp;fixlast)</span></div>
<div class="block">Specify whether a missing EOL will be added
 to the final line of a file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fixlast</code> - whether to fix the last line.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPreserveLastModified(boolean)">
<h3>setPreserveLastModified</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPreserveLastModified</span><wbr><span class="parameters">(boolean&nbsp;preserve)</span></div>
<div class="block">Set whether to preserve the last modified time as the original files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>preserve</code> - true if timestamps should be preserved.</dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
