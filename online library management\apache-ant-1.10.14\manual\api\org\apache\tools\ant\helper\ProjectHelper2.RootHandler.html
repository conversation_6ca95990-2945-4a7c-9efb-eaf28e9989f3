<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ProjectHelper2.RootHandler (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.helper, class: ProjectHelper2, class: RootHandler">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.helper</a></div>
<h1 title="Class ProjectHelper2.RootHandler" class="title">Class ProjectHelper2.RootHandler</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">org.xml.sax.helpers.DefaultHandler</a>
<div class="inheritance">org.apache.tools.ant.helper.ProjectHelper2.RootHandler</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DTDHandler.html" title="class or interface in org.xml.sax" class="external-link">DTDHandler</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">ProjectHelper2.RootHandler</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></span></div>
<div class="block">Handler for ant processing. Uses a stack of AntHandlers to
 implement each element (the original parser used a recursive behavior,
 with the implicit execution stack)</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.helper.AntXMLContext,org.apache.tools.ant.helper.ProjectHelper2.AntHandler)" class="member-name-link">RootHandler</a><wbr>(<a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context,
 <a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a>&nbsp;rootHandler)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new RootHandler instance.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#characters(char%5B%5D,int,int)" class="member-name-link">characters</a><wbr>(char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handle text within an element, calls currentHandler.characters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endElement(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">endElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the end of an element.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endPrefixMapping(java.lang.String)" class="member-name-link">endPrefixMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">End a namespace prefix to uri mapping</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentAntHandler()" class="member-name-link">getCurrentAntHandler</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the current ant handler object.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/InputSource.html" title="class or interface in org.xml.sax" class="external-link">InputSource</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveEntity(java.lang.String,java.lang.String)" class="member-name-link">resolveEntity</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;systemId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resolves file: URIs relative to the build file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDocumentLocator(org.xml.sax.Locator)" class="member-name-link">setDocumentLocator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a>&nbsp;locator)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the locator in the project helper for future reference.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes)" class="member-name-link">startElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the start of a project element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startPrefixMapping(java.lang.String,java.lang.String)" class="member-name-link">startPrefixMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start a namespace prefix to uri mapping</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.xml.sax.helpers.DefaultHandler">Methods inherited from class&nbsp;org.xml.sax.helpers.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#endDocument()" title="class or interface in org.xml.sax.helpers" class="external-link">endDocument</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#error(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax.helpers" class="external-link">error</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#fatalError(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax.helpers" class="external-link">fatalError</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#ignorableWhitespace(char%5B%5D,int,int)" title="class or interface in org.xml.sax.helpers" class="external-link">ignorableWhitespace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#notationDecl(java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">notationDecl</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#processingInstruction(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">processingInstruction</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#skippedEntity(java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">skippedEntity</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#startDocument()" title="class or interface in org.xml.sax.helpers" class="external-link">startDocument</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#unparsedEntityDecl(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">unparsedEntityDecl</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#warning(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax.helpers" class="external-link">warning</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.xml.sax.ContentHandler">Methods inherited from interface&nbsp;org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#declaration(java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">declaration</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.helper.AntXMLContext,org.apache.tools.ant.helper.ProjectHelper2.AntHandler)">
<h3>RootHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">RootHandler</span><wbr><span class="parameters">(<a href="AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</a>&nbsp;context,
 <a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a>&nbsp;rootHandler)</span></div>
<div class="block">Creates a new RootHandler instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>context</code> - The context for the handler.</dd>
<dd><code>rootHandler</code> - The handler for the root element.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getCurrentAntHandler()">
<h3>getCurrentAntHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</a></span>&nbsp;<span class="element-name">getCurrentAntHandler</span>()</div>
<div class="block">Returns the current ant handler object.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the current ant handler.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveEntity(java.lang.String,java.lang.String)">
<h3>resolveEntity</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/InputSource.html" title="class or interface in org.xml.sax" class="external-link">InputSource</a></span>&nbsp;<span class="element-name">resolveEntity</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;systemId)</span></div>
<div class="block">Resolves file: URIs relative to the build file.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html#resolveEntity(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">resolveEntity</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#resolveEntity(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">resolveEntity</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>publicId</code> - The public identifier, or <code>null</code>
                 if none is available. Ignored in this
                 implementation.</dd>
<dd><code>systemId</code> - The system identifier provided in the XML
                 document. Will not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an inputsource for this identifier</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes)">
<h3>startElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tag,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qname,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Attributes.html" title="class or interface in org.xml.sax" class="external-link">Attributes</a>&nbsp;attrs)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handles the start of a project element. A project handler is created
 and initialised with the element name and attributes.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#startElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes)" title="class or interface in org.xml.sax" class="external-link">startElement</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#startElement(java.lang.String,java.lang.String,java.lang.String,org.xml.sax.Attributes)" title="class or interface in org.xml.sax.helpers" class="external-link">startElement</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace uri for this element.</dd>
<dd><code>tag</code> - The name of the element being started.
            Will not be <code>null</code>.</dd>
<dd><code>qname</code> - The qualified name for this element.</dd>
<dd><code>attrs</code> - Attributes of the element being started.
              Will not be <code>null</code>.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if the tag given is not
                              <code>"project"</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setDocumentLocator(org.xml.sax.Locator)">
<h3>setDocumentLocator</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDocumentLocator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/Locator.html" title="class or interface in org.xml.sax" class="external-link">Locator</a>&nbsp;locator)</span></div>
<div class="block">Sets the locator in the project helper for future reference.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#setDocumentLocator(org.xml.sax.Locator)" title="class or interface in org.xml.sax" class="external-link">setDocumentLocator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#setDocumentLocator(org.xml.sax.Locator)" title="class or interface in org.xml.sax.helpers" class="external-link">setDocumentLocator</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>locator</code> - The locator used by the parser.
                Will not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endElement(java.lang.String,java.lang.String,java.lang.String)">
<h3>endElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;qName)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">Handles the end of an element. Any required clean-up is performed
 by the onEndElement() method and then the original handler is restored to the parser.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#endElement(java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">endElement</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#endElement(java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">endElement</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>uri</code> - The namespace URI for this element.</dd>
<dd><code>name</code> - The name of the element which is ending.
             Will not be <code>null</code>.</dd>
<dd><code>qName</code> - The qualified name for this element.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - in case of error (not thrown in this implementation)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="characters(char[],int,int)">
<h3>characters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">characters</span><wbr><span class="parameters">(char[]&nbsp;buf,
 int&nbsp;start,
 int&nbsp;count)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></span></div>
<div class="block">Handle text within an element, calls currentHandler.characters.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#characters(char%5B%5D,int,int)" title="class or interface in org.xml.sax" class="external-link">characters</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#characters(char%5B%5D,int,int)" title="class or interface in org.xml.sax.helpers" class="external-link">characters</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>buf</code> - A character array of the test.</dd>
<dd><code>start</code> - The start offset in the array.</dd>
<dd><code>count</code> - The number of characters to read.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXParseException.html" title="class or interface in org.xml.sax" class="external-link">SAXParseException</a></code> - if an error occurs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startPrefixMapping(java.lang.String,java.lang.String)">
<h3>startPrefixMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startPrefixMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;uri)</span></div>
<div class="block">Start a namespace prefix to uri mapping</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#startPrefixMapping(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">startPrefixMapping</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#startPrefixMapping(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">startPrefixMapping</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>prefix</code> - the namespace prefix</dd>
<dd><code>uri</code> - the namespace uri</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endPrefixMapping(java.lang.String)">
<h3>endPrefixMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endPrefixMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">End a namespace prefix to uri mapping</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html#endPrefixMapping(java.lang.String)" title="class or interface in org.xml.sax" class="external-link">endPrefixMapping</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ContentHandler.html" title="class or interface in org.xml.sax" class="external-link">ContentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html#endPrefixMapping(java.lang.String)" title="class or interface in org.xml.sax.helpers" class="external-link">endPrefixMapping</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/helpers/DefaultHandler.html" title="class or interface in org.xml.sax.helpers" class="external-link">DefaultHandler</a></code></dd>
<dt>Parameters:</dt>
<dd><code>prefix</code> - the prefix that is not mapped anymore</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
