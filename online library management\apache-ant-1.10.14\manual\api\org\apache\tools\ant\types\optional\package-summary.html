<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>org.apache.tools.ant.types.optional (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.optional">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.types.optional" class="title">Package org.apache.tools.ant.types.optional</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.types.optional</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant.types</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="depend/package-summary.html">org.apache.tools.ant.types.optional.depend</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="image/package-summary.html">org.apache.tools.ant.types.optional.image</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="imageio/package-summary.html">org.apache.tools.ant.types.optional.imageio</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="xz/package-summary.html">org.apache.tools.ant.types.optional.xz</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../mappers/package-summary.html">org.apache.tools.ant.types.mappers</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../resolver/package-summary.html">org.apache.tools.ant.types.resolver</a></div>
<div class="col-last even-row-color">
<div class="block">Ant integration with xml-commons resolver.</div>
</div>
<div class="col-first odd-row-color"><a href="../resources/package-summary.html">org.apache.tools.ant.types.resources</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../selectors/package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../spi/package-summary.html">org.apache.tools.ant.types.spi</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractScriptComponent.html" title="class in org.apache.tools.ant.types.optional">AbstractScriptComponent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a <a href="../../ProjectComponent.html" title="class in org.apache.tools.ant"><code>ProjectComponent</code></a> that has script support built in
 Use it as a foundation for scriptable things.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ScriptCondition.html" title="class in org.apache.tools.ant.types.optional">ScriptCondition</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A condition that lets you include script.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ScriptFilter.html" title="class in org.apache.tools.ant.types.optional">ScriptFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Most of this is CAP (Cut And Paste) from the Script task
 ScriptFilter class, implements TokenFilter.Filter
 for scripts to use.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ScriptMapper.html" title="class in org.apache.tools.ant.types.optional">ScriptMapper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Script support at map time.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ScriptSelector.html" title="class in org.apache.tools.ant.types.optional">ScriptSelector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Selector that lets you run a script with selection logic inline</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
