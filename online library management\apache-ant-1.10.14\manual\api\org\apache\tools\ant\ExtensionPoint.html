<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>ExtensionPoint (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, class: ExtensionPoint">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Class ExtensionPoint" class="title">Class ExtensionPoint</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="Target.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Target</a>
<div class="inheritance">org.apache.tools.ant.ExtensionPoint</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ExtensionPoint</span>
<span class="extends-implements">extends <a href="Target.html" title="class in org.apache.tools.ant">Target</a></span></div>
<div class="block">An extension point build files can provide as a place where other
 build files can add new dependencies.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ExtensionPoint</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Target)" class="member-name-link">ExtensionPoint</a><wbr>(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color">
<div class="block">Cloning constructor.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDataType(org.apache.tools.ant.RuntimeConfigurable)" class="member-name-link">addDataType</a><wbr>(<a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Throws an exception.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>final void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addTask(org.apache.tools.ant.Task)" class="member-name-link">addTask</a><wbr>(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Throws an exception.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Target">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="Target.html" title="class in org.apache.tools.ant">Target</a></h3>
<code><a href="Target.html#addDependency(java.lang.String)">addDependency</a>, <a href="Target.html#dependsOn(java.lang.String)">dependsOn</a>, <a href="Target.html#execute()">execute</a>, <a href="Target.html#getDependencies()">getDependencies</a>, <a href="Target.html#getDescription()">getDescription</a>, <a href="Target.html#getIf()">getIf</a>, <a href="Target.html#getLocation()">getLocation</a>, <a href="Target.html#getName()">getName</a>, <a href="Target.html#getProject()">getProject</a>, <a href="Target.html#getTasks()">getTasks</a>, <a href="Target.html#getUnless()">getUnless</a>, <a href="Target.html#parseDepends(java.lang.String,java.lang.String,java.lang.String)">parseDepends</a>, <a href="Target.html#performTasks()">performTasks</a>, <a href="Target.html#setDepends(java.lang.String)">setDepends</a>, <a href="Target.html#setDescription(java.lang.String)">setDescription</a>, <a href="Target.html#setIf(java.lang.String)">setIf</a>, <a href="Target.html#setIf(org.apache.tools.ant.taskdefs.condition.Condition)">setIf</a>, <a href="Target.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="Target.html#setName(java.lang.String)">setName</a>, <a href="Target.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="Target.html#setUnless(java.lang.String)">setUnless</a>, <a href="Target.html#setUnless(org.apache.tools.ant.taskdefs.condition.Condition)">setUnless</a>, <a href="Target.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ExtensionPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExtensionPoint</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Target)">
<h3>ExtensionPoint</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ExtensionPoint</span><wbr><span class="parameters">(<a href="Target.html" title="class in org.apache.tools.ant">Target</a>&nbsp;other)</span></div>
<div class="block">Cloning constructor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>other</code> - the Target to clone.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addTask(org.apache.tools.ant.Task)">
<h3>addTask</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addTask</span><wbr><span class="parameters">(<a href="Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task)</span></div>
<div class="block">Throws an exception.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="TaskContainer.html#addTask(org.apache.tools.ant.Task)">addTask</a></code>&nbsp;in interface&nbsp;<code><a href="TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="Target.html#addTask(org.apache.tools.ant.Task)">addTask</a></code>&nbsp;in class&nbsp;<code><a href="Target.html" title="class in org.apache.tools.ant">Target</a></code></dd>
<dt>Parameters:</dt>
<dd><code>task</code> - The task to be added. Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDataType(org.apache.tools.ant.RuntimeConfigurable)">
<h3>addDataType</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDataType</span><wbr><span class="parameters">(<a href="RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</a>&nbsp;r)</span></div>
<div class="block">Throws an exception.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="Target.html#addDataType(org.apache.tools.ant.RuntimeConfigurable)">addDataType</a></code>&nbsp;in class&nbsp;<code><a href="Target.html" title="class in org.apache.tools.ant">Target</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - The wrapper for the data type element to be added.
          Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
