<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>StreamPumper (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: StreamPumper">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class StreamPumper" class="title">Class StreamPumper</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.StreamPumper</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">StreamPumper</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a></span></div>
<div class="block">Copies all data from an input stream to an output stream.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.2</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,java.io.OutputStream)" class="member-name-link">StreamPumper</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</code></div>
<div class="col-last even-row-color">
<div class="block">Create a new StreamPumper.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,java.io.OutputStream,boolean)" class="member-name-link">StreamPumper</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 boolean&nbsp;closeWhenExhausted)</code></div>
<div class="col-last odd-row-color">
<div class="block">Create a new StreamPumper.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.io.InputStream,java.io.OutputStream,boolean,boolean)" class="member-name-link">StreamPumper</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 boolean&nbsp;closeWhenExhausted,
 boolean&nbsp;useAvailable)</code></div>
<div class="col-last even-row-color">
<div class="block">Create a new StreamPumper.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBufferSize()" class="member-name-link">getBufferSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the size in bytes of the read buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getException()" class="member-name-link">getException</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the exception encountered, if any.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFinished()" class="member-name-link">isFinished</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tells whether the end of the stream has been reached.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#run()" class="member-name-link">run</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies data from the input stream to the output stream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBufferSize(int)" class="member-name-link">setBufferSize</a><wbr>(int&nbsp;bufferSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the size in bytes of the read buffer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitFor()" class="member-name-link">waitFor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method blocks until the StreamPumper finishes.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,java.io.OutputStream,boolean)">
<h3>StreamPumper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StreamPumper</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 boolean&nbsp;closeWhenExhausted)</span></div>
<div class="block">Create a new StreamPumper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - input stream to read data from</dd>
<dd><code>os</code> - output stream to write data to.</dd>
<dd><code>closeWhenExhausted</code> - if true, the output stream will be closed when
        the input is exhausted.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,java.io.OutputStream,boolean,boolean)">
<h3>StreamPumper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StreamPumper</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os,
 boolean&nbsp;closeWhenExhausted,
 boolean&nbsp;useAvailable)</span></div>
<div class="block">Create a new StreamPumper.
 <p><b>Note:</b> If you set useAvailable to true, you must
 explicitly invoke <code>stop</code> or interrupt the
 corresponding Thread when you are done or the run method will
 never finish on some JVMs (namely those where available returns
 0 on a closed stream).  Setting it to true may also impact
 performance negatively.  This flag should only be set to true
 if you intend to stop the pumper before the input stream gets
 closed.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - input stream to read data from</dd>
<dd><code>os</code> - output stream to write data to.</dd>
<dd><code>closeWhenExhausted</code> - if true, the output stream will be closed when
        the input is exhausted.</dd>
<dd><code>useAvailable</code> - whether the pumper should use <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html#available()" title="class or interface in java.io" class="external-link"><code>available</code></a> to determine
        whether input is ready, thus trying to emulate
        non-blocking behavior.</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.io.InputStream,java.io.OutputStream)">
<h3>StreamPumper</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">StreamPumper</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;is,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;os)</span></div>
<div class="block">Create a new StreamPumper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>is</code> - input stream to read data from</dd>
<dd><code>os</code> - output stream to write data to.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="run()">
<h3>run</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">run</span>()</div>
<div class="block">Copies data from the input stream to the output stream.

 Terminates as soon as the input stream is closed or an error occurs.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Runnable.html#run()" title="class or interface in java.lang" class="external-link">run</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFinished()">
<h3>isFinished</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFinished</span>()</div>
<div class="block">Tells whether the end of the stream has been reached.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true is the stream has been exhausted.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitFor()">
<h3>waitFor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitFor</span>()
             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InterruptedException.html" title="class or interface in java.lang" class="external-link">InterruptedException</a></span></div>
<div class="block">This method blocks until the StreamPumper finishes.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/InterruptedException.html" title="class or interface in java.lang" class="external-link">InterruptedException</a></code> - if interrupted.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#isFinished()"><code>isFinished()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBufferSize(int)">
<h3>setBufferSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBufferSize</span><wbr><span class="parameters">(int&nbsp;bufferSize)</span></div>
<div class="block">Set the size in bytes of the read buffer.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bufferSize</code> - the buffer size to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></code> - if the StreamPumper is already running.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBufferSize()">
<h3>getBufferSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getBufferSize</span>()</div>
<div class="block">Get the size in bytes of the read buffer.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the int size of the read buffer.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getException()">
<h3>getException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span>&nbsp;<span class="element-name">getException</span>()</div>
<div class="block">Get the exception encountered, if any.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the Exception encountered.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
