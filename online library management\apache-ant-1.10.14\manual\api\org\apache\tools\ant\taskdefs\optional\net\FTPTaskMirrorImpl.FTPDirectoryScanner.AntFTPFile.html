<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.net, class: FTPTaskMirrorImpl, class: FTPDirectoryScanner, class: AntFTPFile">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.net</a></div>
<h1 title="Class FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile" class="title">Class FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPRootFile</a></code></dd>
</dl>
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="FTPTaskMirrorImpl.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">protected class </span><span class="element-name type-name-label">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">an AntFTPFile is a representation of a remote file</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.commons.net.ftp.FTPClient,org.apache.commons.net.ftp.FTPFile,java.lang.String)" class="member-name-link">AntFTPFile</a><wbr>(org.apache.commons.net.ftp.FTPClient&nbsp;client,
 org.apache.commons.net.ftp.FTPFile&nbsp;ftpFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;curpwd)</code></div>
<div class="col-last even-row-color">
<div class="block">constructor</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile,java.lang.String)" class="member-name-link">AntFTPFile</a><wbr>(<a href="FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color">
<div class="block">other constructor</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exists()" class="member-name-link">exists</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out if the file exists</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsolutePath()" class="member-name-link">getAbsolutePath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the absolute path of the file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected org.apache.commons.net.ftp.FTPClient</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClient()" class="member-name-link">getClient</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">return the attached FTP client object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurpwd()" class="member-name-link">getCurpwd</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">returns the path of the directory containing the AntFTPFile.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurpwdPlusFileSep()" class="member-name-link">getCurpwdPlusFileSep</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">returns the path of the directory containing the AntFTPFile.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFastRelativePath()" class="member-name-link">getFastRelativePath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the relative path assuming that the path used to construct
 this AntFTPFile was spelled properly with regards to case.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.apache.commons.net.ftp.FTPFile</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFile(org.apache.commons.net.ftp.FTPFile%5B%5D,java.lang.String)" class="member-name-link">getFile</a><wbr>(org.apache.commons.net.ftp.FTPFile[]&nbsp;theFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;lastpathelement)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find a file matching a string in an array of FTPFile.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLink()" class="member-name-link">getLink</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">if the file is a symbolic link, find out to what it is pointing</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the name of the file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRelativePath()" class="member-name-link">getRelativePath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out the relative path to the rootPath of the enclosing scanner.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isDirectory()" class="member-name-link">isDirectory</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">tell if a file is a directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSymbolicLink()" class="member-name-link">isSymbolicLink</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">tell if a file is a symbolic link</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isTraverseSymlinks()" class="member-name-link">isTraverseSymlinks</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">find out if a symbolic link is encountered in the relative path of this file
 from rootPath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCurpwd(java.lang.String)" class="member-name-link">setCurpwd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;curpwd)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sets the current path of an AntFTPFile</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a string rep of this object.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.commons.net.ftp.FTPClient,org.apache.commons.net.ftp.FTPFile,java.lang.String)">
<h3>AntFTPFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntFTPFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPClient&nbsp;client,
 org.apache.commons.net.ftp.FTPFile&nbsp;ftpFile,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;curpwd)</span></div>
<div class="block">constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>client</code> - ftp client variable</dd>
<dd><code>ftpFile</code> - the file</dd>
<dd><code>curpwd</code> - absolute remote path where the file is found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.taskdefs.optional.net.FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile,java.lang.String)">
<h3>AntFTPFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">AntFTPFile</span><wbr><span class="parameters">(<a href="FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTPTaskMirrorImpl.FTPDirectoryScanner.AntFTPFile</a>&nbsp;parent,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">other constructor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - the parent file</dd>
<dd><code>path</code> - a relative path to the parent file</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="exists()">
<h3>exists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">exists</span>()</div>
<div class="block">find out if the file exists</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the file exists</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLink()">
<h3>getLink</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLink</span>()</div>
<div class="block">if the file is a symbolic link, find out to what it is pointing</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the target of the symbolic link</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<div class="block">get the name of the file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsolutePath()">
<h3>getAbsolutePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getAbsolutePath</span>()</div>
<div class="block">find out the absolute path of the file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>absolute path as string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFastRelativePath()">
<h3>getFastRelativePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFastRelativePath</span>()</div>
<div class="block">find out the relative path assuming that the path used to construct
 this AntFTPFile was spelled properly with regards to case.
 This is OK on a case sensitive system such as UNIX</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>relative path</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRelativePath()">
<h3>getRelativePath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRelativePath</span>()
                       throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">find out the relative path to the rootPath of the enclosing scanner.
 this relative path is spelled exactly like on disk,
 for instance if the AntFTPFile has been instantiated as ALPHA,
 but the file is really called alpha, this method will return alpha.
 If a symbolic link is encountered, it is followed, but the name of the link
 rather than the name of the target is returned.
 (ie does not behave like File.getCanonicalPath())</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>relative path, separated by remoteFileSep</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if a change directory fails, ...</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if one of the components of the relative path cannot
 be found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFile(org.apache.commons.net.ftp.FTPFile[],java.lang.String)">
<h3>getFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.apache.commons.net.ftp.FTPFile</span>&nbsp;<span class="element-name">getFile</span><wbr><span class="parameters">(org.apache.commons.net.ftp.FTPFile[]&nbsp;theFiles,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;lastpathelement)</span></div>
<div class="block">find a file matching a string in an array of FTPFile.
 This method will find "alpha" when requested for "ALPHA"
 if and only if the caseSensitive attribute is set to false.
 When caseSensitive is set to true, only the exact match is returned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>theFiles</code> - array of files</dd>
<dd><code>lastpathelement</code> - the file name being sought</dd>
<dt>Returns:</dt>
<dd>null if the file cannot be found, otherwise return the matching file.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isDirectory()">
<h3>isDirectory</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isDirectory</span>()</div>
<div class="block">tell if a file is a directory.
 note that it will return false for symbolic links pointing to directories.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> for directories</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSymbolicLink()">
<h3>isSymbolicLink</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSymbolicLink</span>()</div>
<div class="block">tell if a file is a symbolic link</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> for symbolic links</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClient()">
<h3>getClient</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">org.apache.commons.net.ftp.FTPClient</span>&nbsp;<span class="element-name">getClient</span>()</div>
<div class="block">return the attached FTP client object.
 Warning : this instance is really shared with the enclosing class.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>FTP client</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCurpwd(java.lang.String)">
<h3>setCurpwd</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCurpwd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;curpwd)</span></div>
<div class="block">sets the current path of an AntFTPFile</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>curpwd</code> - the current path one wants to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurpwd()">
<h3>getCurpwd</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurpwd</span>()</div>
<div class="block">returns the path of the directory containing the AntFTPFile.
 of the full path of the file itself in case of AntFTPRootFile</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>parent directory of the AntFTPFile</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurpwdPlusFileSep()">
<h3>getCurpwdPlusFileSep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurpwdPlusFileSep</span>()</div>
<div class="block">returns the path of the directory containing the AntFTPFile.
 of the full path of the file itself in case of AntFTPRootFile
 and appends the remote file separator if necessary.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>parent directory of the AntFTPFile</dd>
<dt>Since:</dt>
<dd>Ant 1.8.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isTraverseSymlinks()">
<h3>isTraverseSymlinks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isTraverseSymlinks</span>()
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a>,
<a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">find out if a symbolic link is encountered in the relative path of this file
 from rootPath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>true</code> if a symbolic link is encountered in the relative path.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if one of the change directory or directory listing operations
 fails</dd>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if a path component in the relative path cannot be found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Get a string rep of this object.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
<dt>Returns:</dt>
<dd>a string containing the pwd and the file.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
