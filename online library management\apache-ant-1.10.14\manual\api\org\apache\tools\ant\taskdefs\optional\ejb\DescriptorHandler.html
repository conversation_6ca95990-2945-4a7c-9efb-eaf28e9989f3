<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title><PERSON><PERSON>or<PERSON><PERSON><PERSON> (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: DescriptorHandler">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class DescriptorHandler" class="title">Class DescriptorHandler</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">org.xml.sax.HandlerBase</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DTDHandler.html" title="class or interface in org.xml.sax" class="external-link">DTDHandler</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/ErrorHandler.html" title="class or interface in org.xml.sax" class="external-link">ErrorHandler</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DescriptorHandler</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></span></div>
<div class="block">Inner class used by EjbJar to facilitate the parsing of deployment
 descriptors and the capture of appropriate information. Extends
 HandlerBase so it only implements the methods needed. During parsing
 creates a hashtable consisting of entries mapping the name it should be
 inserted into an EJB jar as to a File representing the file on disk. This
 list can then be accessed through the getFiles() method.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#currentElement" class="member-name-link">currentElement</a></code></div>
<div class="col-last even-row-color">
<div class="block">Instance variable used to store the name of the current element being
 processed by the SAX parser.</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#currentText" class="member-name-link">currentText</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The text of the current element</div>
</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#ejbFiles" class="member-name-link">ejbFiles</a></code></div>
<div class="col-last even-row-color">
<div class="block">Instance variable that stores the names of the files as they will be
 put into the jar file, mapped to File objects  Accessed by the SAX
 parser call-back method characters().</div>
</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ejbName" class="member-name-link">ejbName</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Instance variable that stores the value found in the &lt;ejb-name&gt; element</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.Task,java.io.File)" class="member-name-link">DescriptorHandler</a><wbr>(<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor for DescriptorHandler.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#characters(char%5B%5D,int,int)" class="member-name-link">characters</a><wbr>(char[]&nbsp;ch,
 int&nbsp;start,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SAX parser call-back method invoked whenever characters are located within
 an element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endElement(java.lang.String)" class="member-name-link">endElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SAX parser call-back method that is invoked when an element is exited.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEjbName()" class="member-name-link">getEjbName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Getter method that returns the value of the &lt;ejb-name&gt; element.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFiles()" class="member-name-link">getFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Getter method that returns the set of files to include in the EJB jar.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPublicId()" class="member-name-link">getPublicId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the publicId of the DTD</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#processElement()" class="member-name-link">processElement</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called when an endelement is seen.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerDTD(java.lang.String,java.lang.String)" class="member-name-link">registerDTD</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;location)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register a dtd with a location.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/InputSource.html" title="class or interface in org.xml.sax" class="external-link">InputSource</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolveEntity(java.lang.String,java.lang.String)" class="member-name-link">resolveEntity</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;systemId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resolve the entity.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startDocument()" class="member-name-link">startDocument</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SAX parser call-back method that is used to initialize the values of some
 instance variables to ensure safe operation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startElement(java.lang.String,org.xml.sax.AttributeList)" class="member-name-link">startElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/AttributeList.html" title="class or interface in org.xml.sax" class="external-link">AttributeList</a>&nbsp;attrs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">SAX parser call-back method that is invoked when a new element is entered
 into.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.xml.sax.HandlerBase">Methods inherited from class&nbsp;org.xml.sax.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#endDocument()" title="class or interface in org.xml.sax" class="external-link">endDocument</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#error(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax" class="external-link">error</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#fatalError(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax" class="external-link">fatalError</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#ignorableWhitespace(char%5B%5D,int,int)" title="class or interface in org.xml.sax" class="external-link">ignorableWhitespace</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#notationDecl(java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">notationDecl</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#processingInstruction(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">processingInstruction</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#setDocumentLocator(org.xml.sax.Locator)" title="class or interface in org.xml.sax" class="external-link">setDocumentLocator</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#unparsedEntityDecl(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">unparsedEntityDecl</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#warning(org.xml.sax.SAXParseException)" title="class or interface in org.xml.sax" class="external-link">warning</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="currentElement">
<h3>currentElement</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">currentElement</span></div>
<div class="block">Instance variable used to store the name of the current element being
 processed by the SAX parser.  Accessed by the SAX parser call-back methods
 startElement() and endElement().</div>
</section>
</li>
<li>
<section class="detail" id="currentText">
<h3>currentText</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">currentText</span></div>
<div class="block">The text of the current element</div>
</section>
</li>
<li>
<section class="detail" id="ejbFiles">
<h3>ejbFiles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</span>&nbsp;<span class="element-name">ejbFiles</span></div>
<div class="block">Instance variable that stores the names of the files as they will be
 put into the jar file, mapped to File objects  Accessed by the SAX
 parser call-back method characters().</div>
</section>
</li>
<li>
<section class="detail" id="ejbName">
<h3>ejbName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ejbName</span></div>
<div class="block">Instance variable that stores the value found in the &lt;ejb-name&gt; element</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.Task,java.io.File)">
<h3>DescriptorHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DescriptorHandler</span><wbr><span class="parameters">(<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a>&nbsp;task,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Constructor for DescriptorHandler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>task</code> - the task that owns this descriptor</dd>
<dd><code>srcDir</code> - the source directory</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="registerDTD(java.lang.String,java.lang.String)">
<h3>registerDTD</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerDTD</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;location)</span></div>
<div class="block">Register a dtd with a location.
 The location is one of a filename, a resource name in the classpath, or
 a URL.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>publicId</code> - the public identity of the dtd</dd>
<dd><code>location</code> - the location of the dtd</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resolveEntity(java.lang.String,java.lang.String)">
<h3>resolveEntity</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/InputSource.html" title="class or interface in org.xml.sax" class="external-link">InputSource</a></span>&nbsp;<span class="element-name">resolveEntity</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;systemId)</span>
                          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">Resolve the entity.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html#resolveEntity(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">resolveEntity</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html" title="class or interface in org.xml.sax" class="external-link">EntityResolver</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#resolveEntity(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link">resolveEntity</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></code></dd>
<dt>Parameters:</dt>
<dd><code>publicId</code> - The public identifier, or <code>null</code>
                 if none is available.</dd>
<dd><code>systemId</code> - The system identifier provided in the XML
                 document. Will not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>an inputsource for this identifier</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - if there is a problem.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/EntityResolver.html#resolveEntity(java.lang.String,java.lang.String)" title="class or interface in org.xml.sax" class="external-link"><code>EntityResolver.resolveEntity(String, String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFiles()">
<h3>getFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;</span>&nbsp;<span class="element-name">getFiles</span>()</div>
<div class="block">Getter method that returns the set of files to include in the EJB jar.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the map of files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPublicId()">
<h3>getPublicId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPublicId</span>()</div>
<div class="block">Get the publicId of the DTD</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the public id</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEjbName()">
<h3>getEjbName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEjbName</span>()</div>
<div class="block">Getter method that returns the value of the &lt;ejb-name&gt; element.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the ejb name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startDocument()">
<h3>startDocument</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startDocument</span>()
                   throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">SAX parser call-back method that is used to initialize the values of some
 instance variables to ensure safe operation.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html#startDocument()" title="class or interface in org.xml.sax" class="external-link">startDocument</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#startDocument()" title="class or interface in org.xml.sax" class="external-link">startDocument</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startElement(java.lang.String,org.xml.sax.AttributeList)">
<h3>startElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">startElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/AttributeList.html" title="class or interface in org.xml.sax" class="external-link">AttributeList</a>&nbsp;attrs)</span>
                  throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">SAX parser call-back method that is invoked when a new element is entered
 into.  Used to store the context (attribute name) in the currentAttribute
 instance variable.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html#startElement(java.lang.String,org.xml.sax.AttributeList)" title="class or interface in org.xml.sax" class="external-link">startElement</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#startElement(java.lang.String,org.xml.sax.AttributeList)" title="class or interface in org.xml.sax" class="external-link">startElement</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the element being entered.</dd>
<dd><code>attrs</code> - Attributes associated to the element.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="endElement(java.lang.String)">
<h3>endElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">endElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">SAX parser call-back method that is invoked when an element is exited.
 Used to blank out (set to the empty string, not nullify) the name of
 the currentAttribute.  A better method would be to use a stack as an
 instance variable, however since we are only interested in leaf-node
 data this is a simpler and workable solution.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html#endElement(java.lang.String)" title="class or interface in org.xml.sax" class="external-link">endElement</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#endElement(java.lang.String)" title="class or interface in org.xml.sax" class="external-link">endElement</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></code></dd>
<dt>Parameters:</dt>
<dd><code>name</code> - The name of the attribute being exited. Ignored
        in this implementation.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="characters(char[],int,int)">
<h3>characters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">characters</span><wbr><span class="parameters">(char[]&nbsp;ch,
 int&nbsp;start,
 int&nbsp;length)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></span></div>
<div class="block">SAX parser call-back method invoked whenever characters are located within
 an element.  currentAttribute (modified by startElement and endElement)
 tells us whether we are in an interesting element (one of the up to four
 classes of an EJB).  If so then converts the classname from the format
 org.apache.tools.ant.Parser to the convention for storing such a class,
 org/apache/tools/ant/Parser.class.  This is then resolved into a file
 object under the srcdir which is stored in a Hashtable.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html#characters(char%5B%5D,int,int)" title="class or interface in org.xml.sax" class="external-link">characters</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/DocumentHandler.html" title="class or interface in org.xml.sax" class="external-link">DocumentHandler</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html#characters(char%5B%5D,int,int)" title="class or interface in org.xml.sax" class="external-link">characters</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/HandlerBase.html" title="class or interface in org.xml.sax" class="external-link">HandlerBase</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ch</code> - A character array containing all the characters in
        the element, and maybe others that should be ignored.</dd>
<dd><code>start</code> - An integer marking the position in the char
        array to start reading from.</dd>
<dd><code>length</code> - An integer representing an offset into the
        char array where the current data terminates.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.xml/org/xml/sax/SAXException.html" title="class or interface in org.xml.sax" class="external-link">SAXException</a></code> - on error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="processElement()">
<h3>processElement</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">processElement</span>()</div>
<div class="block">Called when an endelement is seen.
 This may be overridden in derived classes.
 This updates the ejbfiles if the element is an interface or a bean class.
 This updates the ejbname if the element is an ejb name.</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
