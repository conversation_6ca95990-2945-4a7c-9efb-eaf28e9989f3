<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (20) on Wed Aug 16 17:12:28 IST 2023 -->
<title>BuildLogger (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2023-08-16">
<meta name="description" content="declaration: package: org.apache.tools.ant, interface: BuildLogger">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant</a></div>
<h1 title="Interface BuildLogger" class="title">Interface BuildLogger</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Superinterfaces:</dt>
<dd><code><a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code></dd>
</dl>
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</a></code>, <code><a href="listener/BigProjectLogger.html" title="class in org.apache.tools.ant.listener">BigProjectLogger</a></code>, <code><a href="listener/CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</a></code>, <code><a href="DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</a></code>, <code><a href="listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</a></code>, <code><a href="NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</a></code>, <code><a href="listener/ProfileLogger.html" title="class in org.apache.tools.ant.listener">ProfileLogger</a></code>, <code><a href="taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</a></code>, <code><a href="listener/SilentLogger.html" title="class in org.apache.tools.ant.listener">SilentLogger</a></code>, <code><a href="listener/SimpleBigProjectLogger.html" title="class in org.apache.tools.ant.listener">SimpleBigProjectLogger</a></code>, <code><a href="listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</a></code>, <code><a href="XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">BuildLogger</span><span class="extends-implements">
extends <a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></span></div>
<div class="block">Interface used by Ant to log the build output.

 A build logger is a build listener which has the 'right' to send output to
 the ant log, which is usually <code>System.out</code> unless redirected by
 the <code>-logfile</code> option.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab5" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab5', 3)" class="table-tab">Default Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#getMessageOutputLevel()" class="member-name-link">getMessageOutputLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setEmacsMode(boolean)" class="member-name-link">setEmacsMode</a><wbr>(boolean&nbsp;emacsMode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets this logger to produce emacs (and other editor) friendly output.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setErrorPrintStream(java.io.PrintStream)" class="member-name-link">setErrorPrintStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;err)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the output stream to which this logger is to send error messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setMessageOutputLevel(int)" class="member-name-link">setMessageOutputLevel</a><wbr>(int&nbsp;level)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the highest level of message this logger should respond to.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#setOutputPrintStream(java.io.PrintStream)" class="member-name-link">setOutputPrintStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;output)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Sets the output stream to which this logger is to send its output.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.BuildListener">Methods inherited from interface&nbsp;org.apache.tools.ant.<a href="BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></h3>
<code><a href="BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a>, <a href="BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a>, <a href="BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a>, <a href="BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a>, <a href="BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a>, <a href="BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a>, <a href="BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setMessageOutputLevel(int)">
<h3>setMessageOutputLevel</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setMessageOutputLevel</span><wbr><span class="parameters">(int&nbsp;level)</span></div>
<div class="block">Sets the highest level of message this logger should respond to.

 Only messages with a message level lower than or equal to the
 given level should be written to the log.
 <P>
 Constants for the message levels are in the
 <a href="Project.html" title="class in org.apache.tools.ant"><code>Project</code></a> class. The order of the levels, from least
 to most verbose, is <code>MSG_ERR</code>, <code>MSG_WARN</code>,
 <code>MSG_INFO</code>, <code>MSG_VERBOSE</code>,
 <code>MSG_DEBUG</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>level</code> - the logging level for the logger.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMessageOutputLevel()">
<h3>getMessageOutputLevel</h3>
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMessageOutputLevel</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the <a href="#setMessageOutputLevel(int)"><code>currently set</code></a> message output level.
 The <code>default</code> implementation of this method returns <code>MSG_INFO</code>.</dd>
<dt>Since:</dt>
<dd>1.10.13</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setOutputPrintStream(java.io.PrintStream)">
<h3>setOutputPrintStream</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setOutputPrintStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;output)</span></div>
<div class="block">Sets the output stream to which this logger is to send its output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>output</code> - The output stream for the logger.
               Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setEmacsMode(boolean)">
<h3>setEmacsMode</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setEmacsMode</span><wbr><span class="parameters">(boolean&nbsp;emacsMode)</span></div>
<div class="block">Sets this logger to produce emacs (and other editor) friendly output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>emacsMode</code> - <code>true</code> if output is to be unadorned so that
                  emacs and other editors can parse files names, etc.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setErrorPrintStream(java.io.PrintStream)">
<h3>setErrorPrintStream</h3>
<div class="member-signature"><span class="return-type">void</span>&nbsp;<span class="element-name">setErrorPrintStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/20/docs/api/java.base/java/io/PrintStream.html" title="class or interface in java.io" class="external-link">PrintStream</a>&nbsp;err)</span></div>
<div class="block">Sets the output stream to which this logger is to send error messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>err</code> - The error stream for the logger.
            Must not be <code>null</code>.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
